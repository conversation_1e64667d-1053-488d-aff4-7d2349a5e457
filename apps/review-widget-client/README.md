# Review Widget Client

## Developing Locally
`npm run start review-widget-client`
Use a widgetID from production in `index.html`, the widget only calls production microservice APIs

## Test Widget on Demo/Prod with Code snippet

Login to Reputation Management and copy Review Widget HTML snippet which looks like:

```
<script
    src="https://cdn.apigateway.co/review-widget-client..${Your Env to test}/sdk.js"
    defer
  ></script>
  <review-widget widget-id="${Your widget ID}"></review-widget>
```
* To test on DEMO, get snippet from Reputation Management PROD and change `${Your Env}` from `prod` to `demo`:
  
```
<script
    src="https://cdn.apigateway.co/review-widget-client..${Your Env}/sdk.js"
    defer
  ></script>
  <review-widget widget-id="${Your Widget ID}"></review-widget>
```

Render on local or your sample website or using Website Pro.

> [!NOTE]  
> Test style contamination by outer html loading it at different environments e.g. Load widget in Website Pro (WorldPress) sites to check style overrides from accessability styles on mat-menu focus color. 

## Add Review Widget to your Website with Website Pro: 
A Review Widget could be added by Custom HTML -> paste code from RM which is in format:


As in video:

https://github.com/vendasta/galaxy/assets/32747843/0c092449-6b46-4d41-a5cb-9d39ba4cbad6


