<mat-toolbar class="menu-topbar" color="primary">
  <button mat-icon-button (click)="nav.toggle()">
    <mat-icon>menu</mat-icon>
  </button>
</mat-toolbar>
<glxy-nav #nav fixedTopGap="40" appName="wsp-admin-center" [navAutoSize]="true" class="glxy-nav--light-theme">
  <glxy-nav-panel>
    <glxy-nav-header>
      <div class="glxy-product-nav-header">
        <img src="{{ logoUrl }}" alt="logo" class="app-logo" />
        <span class="app-name">Website Pro Admin</span>
      </div>
    </glxy-nav-header>

    <div class="glxy-nav-items">
      <ng-container *ngIf="navItems$ | async as navItems">
        <ng-container *ngFor="let navItem of navItems">
          <glxy-nav-item
            *ngIf="navItem.children === null"
            [route]="navItem.url"
            [icon]="navItem.icon"
            [queryParams]="navItem.url"
          >
            <div class="glxy-nav-items__label">
              {{ navItem.label | translate }}
              <span class="tab-upgrade-chip" *ngIf="navItem.showUpgradeChip">Pro</span>
            </div>
          </glxy-nav-item>

          <glxy-nav-item-list *ngIf="navItem.children !== null" [icon]="navItem.icon">
            <div class="glxy-nav-items__label">
              {{ navItem.label | translate }}
              <span class="tab-upgrade-chip" *ngIf="navItem.showUpgradeChip">Pro</span>
            </div>
            <glxy-nav-item-list-items>
              <ng-container *ngFor="let childNavItem of navItem.children">
                <glxy-nav-item [route]="childNavItem.url" [queryParams]="childNavItem.url">
                  <div class="glxy-nav-items__label">
                    {{ childNavItem.label | translate }}
                  </div>
                </glxy-nav-item>
              </ng-container>
            </glxy-nav-item-list-items>
          </glxy-nav-item-list>
        </ng-container>
      </ng-container>
    </div>
  </glxy-nav-panel>

  <!-- Page Content -->
  <router-outlet></router-outlet>
</glxy-nav>
