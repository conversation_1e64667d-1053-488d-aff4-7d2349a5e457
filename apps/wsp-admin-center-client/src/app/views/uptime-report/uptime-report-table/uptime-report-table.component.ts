import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { PartnerRouteParams } from '../../../core/routeparams';
import { TranslateService } from '@ngx-translate/core';
import { MatTable } from '@angular/material/table';
import { UptimeDataSource } from '../../../core/uptime/uptime-data-source';
import { WordPressSite } from '../../../core/wsp_admin_center_sdk/_internal/objects';
import { UptimeService } from '../../../core/uptime/uptime.service';
import { getImageSrc } from '../../../core/image-utilities';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Filters, FilterService } from '@vendasta/uikit';
import { AdminCenterService } from '../../../core/wsp_admin_center_sdk/admin-center.service';
import { TableComprehensiveService } from '../table-comprehensive-service';
import { GalaxyFilterInterface, GalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import { MonitorApiService } from '@vendasta/wsp-monitor';
import { BrandingV2Service } from '@galaxy/partner';
import jsonData from '../../../../assets/i18n/en_devel.json';
@Component({
  selector: 'app-uptime-report-table',
  templateUrl: './uptime-report-table.component.html',
  styleUrls: ['./uptime-report-table.component.scss'],
  providers: [
    TableComprehensiveService,
    { provide: GalaxyFilterChipInjectionToken, useExisting: TableComprehensiveService },
  ],
  standalone: false,
})
export class UptimeReportTableComponent implements OnInit, OnDestroy {
  displayedColumns = ['account', 'status', '24hours', '7days', '30days', 'links'];
  columns = [
    {
      id: 'account',
      title: 'Account',
      sticky: true,
    },
    {
      id: 'status',
      title: 'Status',
    },
    {
      id: '24hours',
      title: '24hours',
    },
    {
      id: '7days',
      title: '7days',
    },
    {
      id: '30days',
      title: '30days',
    },
    {
      id: 'links',
      title: 'Links',
      stickyEnd: true,
    },
  ];

  dataSource: UptimeDataSource;
  partnerId: string;
  public sideData: any;
  totalSitesCount: number;
  upSitesCount: number;
  downSitesCount: number;
  deactivateSitesCount: number;
  appliedFilter: string;

  subscriptions: Subscription[] = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatTable, { static: true }) table: MatTable<any>;

  checkImage = getImageSrc('images/check-circle-green.svg');
  warningImage = getImageSrc('images/warning-yellow.svg');

  logoImage = getImageSrc('website-pro.svg');
  wordPressImage = getImageSrc('wordpress-logo.svg');
  currentSelection = 'ALL';
  filters: Filters;

  constructor(
    private route: ActivatedRoute,
    private uptimeService: UptimeService,
    private environmentService: EnvironmentService,
    readonly filterService: FilterService,
    private translateService: TranslateService,
    private monitorApiService: MonitorApiService,
    private brandingService: BrandingV2Service,
    private adminCenterService: AdminCenterService,
  ) {
    this.setPartnerId();
  }

  ngOnInit(): void {
    this.dataSource = new UptimeDataSource(this.uptimeService, this.partnerId);
    this.dataSource.getSummaryCardData();
    this.subscriptions.push(
      this.filterService.fieldValueChanges.subscribe((changedFilters) => {
        this.dataSource.updateFilter(changedFilters);
      }),
      this.dataSource.totalSitesCount$.subscribe((totalSitesCount: number) => {
        this.totalSitesCount = totalSitesCount;
      }),
      this.dataSource.upSitesCount$.subscribe((upSitesCount: number) => {
        this.upSitesCount = upSitesCount;
      }),
      this.dataSource.downSitesCount$.subscribe((downSitesCount: number) => {
        this.downSitesCount = downSitesCount;
      }),
      this.dataSource.deactivateSitesCount$.subscribe((deactivateSitesCount: number) => {
        this.deactivateSitesCount = deactivateSitesCount;
      }),
      this.dataSource.appliedFilter$.subscribe((appliedFilter: string) => {
        this.appliedFilter = appliedFilter;
      }),
    );
  }

  setCurrent(selection): void {
    this.currentSelection = selection;
    this.dataSource.filterData(selection);
  }
  setPartnerId(): void {
    this.partnerId = (this.route.snapshot.params as PartnerRouteParams).partnerId;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  trackById(index: number, wordPressSite: WordPressSite): string {
    return wordPressSite.siteId;
  }

  websiteProDashboardClicked(site: WordPressSite): void {
    const environ = this.environmentService.getEnvironment();
    if (environ === Environment.PROD) {
      window.open(`https://www.websiteprodashboard.com/site/${site.siteId}/overview`, '_blank');
    } else {
      window.open(`https://websiteprodashboard-demo.com/site/${site.siteId}/overview`, '_blank');
    }
  }

  wordPressDashboardClicked(site: any): void {
    this.getSearchData(site.accountGroupName, site.partnerId).subscribe((res: any) => {
      const primaryDomain = res.sites[0].primaryDomain;
      window.open(`https://${primaryDomain}/wp-admin`, '_blank');
    });
  }

  primaryDomainClicked(site: any): void {
    this.getSearchData(site.accountGroupName, site.partnerId).subscribe((res: any) => {
      const primaryDomain = res.sites[0].primaryDomain;
      window.open(`https://${primaryDomain}`, '_blank');
    });
  }

  formatNumber(number: string): number {
    // convert string to number
    return Number(number).toFixed(2) as unknown as number;
  }
  onFilterChanged(filters: GalaxyFilterInterface[]): void {
    this.dataSource.updateFilter(filters);
  }

  openSide(data) {
    this.sideData = data;

    // Fetch health data
    const healthData = { siteId: this.sideData.siteId, cursor: '', pageSize: 1 };
    this.monitorApiService.listHealthChecksVstore(healthData).subscribe((res: any) => {
      this.sideData.healthData = { created: res.list[0].created, duration: this.getDuration(res.list[0].created) };
    });

    // Fetch partner branding
    this.brandingService.getBranding(this.partnerId).subscribe((res: any) => {
      this.sideData.partnerName = res.name;
    });

    // Fetch primary domain
    this.getSearchData(this.sideData.accountGroupName, this.partnerId).subscribe((res: any) => {
      this.sideData.primaryDomain = res.sites[0].primaryDomain;
    });

    if (this.sideData.statusCode) {
      const errorData = jsonData.PAGES.UPTIME.HTTPERRORSTEPS[this.sideData.statusCode] || null;

      if (errorData) {
        this.sideData.errorDescription = errorData.DESCRIPTION || jsonData.PAGES.NOHTTPDATA.NO_DESCRIPTION;
        this.sideData.errorSteps = errorData.STEPS_TO_RESOLVE
          ? Object.values(errorData.STEPS_TO_RESOLVE)
          : [jsonData.PAGES.NOHTTPDATA.NO_STEPS];
      } else {
        this.sideData.errorDescription = jsonData.PAGES.NOERRORCODEDATA.NO_DATA_DESCRIPTION;
        this.sideData.errorSteps = [jsonData.PAGES.NOERRORCODEDATA.NO_DATA_STEPS];
      }
    }
  }

  getKeys(obj: Record<string, unknown>): string[] {
    return Object.keys(obj);
  }

  getSearchData(accountGroupName: string, partnerId: string) {
    return this.adminCenterService.search({
      searchTerm: accountGroupName,
      filters: {
        partnerId: partnerId,
      },
      pageSize: 1,
      from: 0,
    });
  }

  getDuration(created: any) {
    // current time
    const now = new Date().getTime();
    const createdDate = new Date(created).getTime();
    // time since message was sent in seconds
    const delta = (now - createdDate) / 1000;
    // format string
    if (delta < 10) {
      return 'Just now';
    } else if (delta < 60) {
      return Math.floor(delta) + ' seconds ago';
    } else if (delta < 3600) {
      return Math.floor(delta / 60) + ' minutes ago';
    } else if (delta < 86400) {
      return Math.floor(delta / 3600) + ' hours ago';
    } else {
      return Math.floor(delta / 86400) + ' days ago';
    }
  }
}
