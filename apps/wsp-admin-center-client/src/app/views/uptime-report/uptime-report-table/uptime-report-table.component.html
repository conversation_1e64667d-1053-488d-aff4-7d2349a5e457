<div class="table-info" appearance="outlined" class="sites-list-container">
  <div class="update-row">
    <div [class.active]="currentSelection === 'ALL'" (click)="setCurrent('ALL')" class="update-cell">
      <mat-card appearance="outlined">
        <mat-card-header>
          <mat-card-title class="all-websites">
            <span>
              {{ 'PAGES.UPTIME.ALL_WEBSITES' | translate }}
              <mat-icon class="globe-icon">public</mat-icon>
            </span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <h1>
            <ng-container *ngIf="totalSitesCount > 0; else loadingTemplate">
              {{ dataSource.totalSitesCount$ | async }}
            </ng-container>
          </h1>
        </mat-card-content>
      </mat-card>
    </div>

    <div [class.active]="currentSelection === 'UP'" (click)="setCurrent('UP')" class="update-cell">
      <mat-card appearance="outlined">
        <mat-card-header>
          <mat-card-title>
            <span>
              {{ 'PAGES.UPTIME.SITES_UP' | translate }}
              <mat-icon class="up-icon">arrow_circle_up</mat-icon>
            </span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <h1>
            <ng-container *ngIf="totalSitesCount > 0; else loadingTemplate">
              {{ dataSource.upSitesCount$ | async }}
            </ng-container>
          </h1>
        </mat-card-content>
      </mat-card>
    </div>

    <div [class.active]="currentSelection === 'DOWN'" (click)="setCurrent('DOWN')" class="update-cell">
      <mat-card appearance="outlined">
        <mat-card-header>
          <mat-card-title>
            <span>
              {{ 'PAGES.UPTIME.SITES_DOWN' | translate }}
              <mat-icon class="down-icon">arrow_circle_down</mat-icon>
            </span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <h1>
            <ng-container *ngIf="totalSitesCount > 0; else loadingTemplate">
              {{ dataSource.downSitesCount$ | async }}
            </ng-container>
          </h1>
        </mat-card-content>
      </mat-card>
    </div>

    <div [class.active]="currentSelection === 'DEACTIVATED'" (click)="setCurrent('DEACTIVATED')" class="update-cell">
      <mat-card appearance="outlined">
        <mat-card-header>
          <mat-card-title>
            <span>
              {{ 'PAGES.UPTIME.SITES_PAUSED' | translate }}
              <mat-icon class="deactivate-icon">pause_circle_outline</mat-icon>
            </span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <h1>
            <ng-container *ngIf="totalSitesCount > 0; else loadingTemplate">
              {{ dataSource.deactivateSitesCount$ | async }}
            </ng-container>
          </h1>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div>

<mat-drawer-container [hasBackdrop]="false">
  <mat-drawer role="region" #details [position]="'end'" [mode]="'over'" class="side">
    <div class="side-header">
      <strong>
        <a class="domain-link" href="https://{{ sideData?.primaryDomain }}" target="_blank">
          Visit Site <mat-icon>open_in_new</mat-icon>
        </a>
      </strong>
      <button class="right" mat-icon-button (click)="details.close()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <div class="side-content">
      <div class="status">
        <strong>Status:</strong>
        <ng-container *ngIf="sideData?.currentHealth === '200'">
          <glxy-badge [color]="'green'">{{ 'PAGES.UPTIME.UP' | translate }}</glxy-badge>
        </ng-container>

        <ng-container *ngIf="sideData?.currentHealth !== '200' && sideData?.currentHealth !== 'Deactivated'">
          <glxy-badge [color]="'red'">{{ 'PAGES.UPTIME.DOWN' | translate }}</glxy-badge>
        </ng-container>

        <ng-container *ngIf="sideData?.currentHealth === 'Deactivated'">
          <glxy-badge [color]="'grey'">{{ 'PAGES.UPTIME.PAUSED' | translate }}</glxy-badge>
        </ng-container>
        <ng-container>
          <mat-icon
            class="info-icon"
            aria-hidden="true"
            matTooltipPosition="right"
            matTooltipClass="info-tooltip"
            data-mat-icon-type="font"
            matTooltip="{{ 'PAGES.UPTIME.TOOLTIP' | translate }}"
          >
            info_outline
          </mat-icon>
        </ng-container>
      </div>

      <div class="dates" *ngIf="sideData?.currentHealth !== '200' && sideData?.currentHealth !== 'Deactivated'">
        <div class="started">
          <strong>Started:</strong>
          <div>
            {{ sideData?.healthData?.created | date: 'medium' }}
          </div>
        </div>
        <div class="duration">
          <strong>Duration:</strong>
          <div>
            {{ sideData?.healthData?.duration }}
          </div>
        </div>
      </div>

      <div class="root-cause" *ngIf="sideData?.currentHealth !== '200' && sideData?.currentHealth !== 'Deactivated'">
        <strong>Root Cause:</strong>
      </div>

      <div
        class="http-error-details"
        *ngIf="sideData?.currentHealth !== '200' && sideData?.currentHealth !== 'Deactivated'"
      >
        <div class="step-text">
          <span>{{ sideData?.statusCode }}</span> - <span>{{ sideData?.errorDescription }}</span>
        </div>

        <div class="steps-to-resolve">
          <div *ngIf="sideData?.errorSteps">
            <strong>Steps to Resolve:</strong>
            <ul>
              <li *ngFor="let key of getKeys(sideData.errorSteps)">
                {{ sideData.errorSteps[key] }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </mat-drawer>

  <mat-drawer-content>
    <ng-template #loadingTemplate>
      <ngx-skeleton-loader count="1" [theme]="{ height: '26px', width: '100px' }"></ngx-skeleton-loader>
    </ng-template>

    <glxy-table-container
      [columns]="columns"
      [dataSource]="dataSource"
      [pageSize]="dataSource.pageUptimeSize$ | async"
      [pageSizeOptions]="[10, 25, 50]"
      [fullWidth]="true"
      [showFooter]="false"
    >
      <glxy-table-content-header
        [showColumnArrange]="true"
        [showFilters]="true"
        [showFiltersOpen]="false"
        [showSearch]="true"
        [showSort]="false"
        [showExport]="false"
        [showActions]="true"
      >
        <div filters-area class="filters-section">
          <glxy-filter-chips [showAddFilter]="false" (filtersChanged)="onFilterChanged($event)"></glxy-filter-chips>
        </div>
      </glxy-table-content-header>
      <table id="site-list" mat-table>
        <ng-container matColumnDef="account">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.ACCOUNT' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site" (click)="openSide(site); details.open()">
            <div>
              {{ site.accountGroupName }}
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.UPTIME.TABLE.HEADERS.STATUS' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site" (click)="openSide(site); details.open()">
            <ng-container *ngIf="site.currentHealth; else loadingTemplate">
              <ng-container *ngIf="site.currentHealth === '200'">
                <glxy-badge [color]="'green'">{{ 'PAGES.UPTIME.UP' | translate }}</glxy-badge>
              </ng-container>

              <ng-container *ngIf="site.currentHealth !== '200' && site.currentHealth !== 'Deactivated'">
                <glxy-badge [color]="'red'">{{ 'PAGES.UPTIME.DOWN' | translate }}</glxy-badge>
              </ng-container>

              <ng-container *ngIf="site.currentHealth === 'Deactivated'">
                <glxy-badge [color]="'grey'">{{ 'PAGES.UPTIME.PAUSED' | translate }}</glxy-badge>
              </ng-container>

              <ng-container *ngIf="site.uptimeData?.errorMessage">
                &nbsp; | {{ site.uptimeData?.errorMessage | translate }}
              </ng-container>
            </ng-container>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="24hours">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.UPTIME.TABLE.HEADERS.24HOURS' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site" (click)="openSide(site); details.open()">
            <ng-container *ngIf="site.uptime1day; else loadingTemplate">
              {{ formatNumber(site.uptime1day) }} %
            </ng-container>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="7days">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.UPTIME.TABLE.HEADERS.7DAYS' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site" (click)="openSide(site); details.open()">
            <ng-container *ngIf="site.uptime7days; else loadingTemplate">
              {{ formatNumber(site.uptime7days) }} %
            </ng-container>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="30days">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.UPTIME.TABLE.HEADERS.30DAYS' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site" (click)="openSide(site); details.open()">
            <ng-container *ngIf="site.uptime30days; else loadingTemplate">
              {{ formatNumber(site.uptime30days) }} %
            </ng-container>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="links">
          <mat-header-cell *matHeaderCellDef> </mat-header-cell>
          <mat-cell class="actions-column-cell" *matCellDef="let site">
            <button mat-icon-button color="primary" [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
              <button mat-menu-item>
                <a data-action="navigate-wsp-dashboard" class="link" (click)="websiteProDashboardClicked(site)">
                  <img src="{{ logoImage }}" alt="" class="logo" />
                  &nbsp; WSP Dashboard
                </a>
              </button>
              <button mat-menu-item>
                <a data-action="navigate-wp-dashboard" class="link" (click)="wordPressDashboardClicked(site)">
                  <img src="{{ wordPressImage }}" alt="" class="logo" />
                  &nbsp; WordPress Dashboard
                </a>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </table>

      <!-- <mat-paginator
        #paginator
        [pageSize]="dataSource.pageUptimeSize$ | async"
        [pageIndex]="dataSource.pageUptimeIndex$ | async"
        (page)="dataSource.pageChanged($event)"
        [length]="dataSource.sitesUptimeCount$ | async"
        [pageSizeOptions]="[10, 25, 50, 100]"
      ></mat-paginator> -->
    </glxy-table-container>
  </mat-drawer-content>
</mat-drawer-container>

<ng-template #loadingTemplate>
  <ngx-skeleton-loader
    count="1"
    [theme]="{ 'border-radius': '12px', height: '24px', width: '100px' }"
  ></ngx-skeleton-loader>
</ng-template>
