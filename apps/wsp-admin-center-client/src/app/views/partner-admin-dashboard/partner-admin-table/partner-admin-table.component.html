<mat-card appearance="outlined" class="sites-list-container">
  <va-filter-container
    [filters]="filters"
    [savedFilters]="filterService.savedFilters"
    [searchable]="true"
    (searchTermChanged)="updateSearchTerm($event)"
    (savedFilterSelected)="applySavedFilter($event)"
    (saveCurrentFilter)="filterService.saveCurrentFilter($event)"
    (removeSavedFilter)="filterService.removeSavedFilter($event)"
  >
    <div class="table-info" content>
      <mat-table id="site-list" #table [dataSource]="dataSource" [trackBy]="trackById">
        <ng-container matColumnDef="account">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.ACCOUNT' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <div>
              {{ site.accountGroupName }}
              <ng-container *ngIf="getIsMultisite(site) === true">
                <glxy-badge [size]="'small'" [color]="'blue-solid'">
                  {{ 'PAGES.DASHBOARD.TABLE.MULTISITE_BADGE' | translate }}
                </glxy-badge>
              </ng-container>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="url">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.URL' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <ng-container *ngIf="!!site.primaryDomain">
              <div data-action="navigate-site-url" (click)="primaryDomainClicked(site)" class="cell-clickable">
                <a>{{ site.primaryDomain }}</a>
              </div>
            </ng-container>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="subsites_count">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.SUBSITES_COUNT' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <ng-container *ngIf="getIsMultisite(site); else noSubsitesBlock">
              <div>
                {{ getSubsitesCount(site) }}
              </div>
            </ng-container>
            <ng-template #noSubsitesBlock>
              <div class="tooltip">
                —
                <span class="tooltiptext">
                  {{ 'PAGES.DASHBOARD.TABLE.NOT_MULTISITE_TOOLTIP' | translate }}
                </span>
              </div>
            </ng-template>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="pluginUpdates">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.PLUGIN_UPDATES' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <div>
              <ng-container *ngIf="isPluginsNeedsUpdate(site); else pluginsNoUpdateBlock">
                <img src="{{ warningImage }}" alt="" />
              </ng-container>
              <ng-template #pluginsNoUpdateBlock>
                <img src="{{ checkImage }}" alt="" />
              </ng-template>
              <div data-action="update-wp-plugin" (click)="wordPressPluginClicked(site)" class="cell-clickable">
                <a>
                  {{ getPluginUpdates(site) }}
                </a>
              </div>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="wordPressVersion">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.WORDPRESS_VERSION' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <div>
              <ng-container *ngIf="site.currentCoreVersion !== undefined">
                <ng-container *ngIf="isCoreNeedsUpdate(site); else coreNoUpdateBlock">
                  <img src="{{ warningImage }}" alt="" />
                </ng-container>
                <ng-template #coreNoUpdateBlock>
                  <img src="{{ checkImage }}" alt="" />
                </ng-template>
              </ng-container>
              <ng-container *ngIf="getCoreVersion(site) === NO_AVAILABLE_VERSION; else coreVersionBlock">
                <div class="tooltip">
                  —
                  <span class="tooltiptext">
                    {{ 'PAGES.DASHBOARD.TABLE.NO_DATA_TOOLTIP' | translate }}
                  </span>
                </div>
              </ng-container>
              <ng-template #coreVersionBlock>
                <div data-action="update-wp-core" (click)="wordPressCoreVersionClicked(site)" class="cell-clickable">
                  <a>{{ getCoreVersion(site) }}</a>
                </div>
              </ng-template>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="themeVersion">
          <mat-header-cell class="data-column-cell" *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.THEME_VERSION' | translate }}
          </mat-header-cell>
          <mat-cell class="data-column-cell" *matCellDef="let site">
            <div>
              <ng-container *ngIf="site.currentTheme !== undefined && site.currentTheme.currentVersion !== undefined">
                <ng-container *ngIf="isThemeNeedsUpdate(site); else themeNoUpdateBlock">
                  <img src="{{ warningImage }}" alt="" />
                </ng-container>
                <ng-template #themeNoUpdateBlock>
                  <img src="{{ checkImage }}" alt="" />
                </ng-template>
              </ng-container>
              <ng-container *ngIf="getThemeVersion(site) === NO_AVAILABLE_VERSION; else themeVersionBlock">
                <div class="tooltip">
                  —
                  <span class="tooltiptext">
                    {{ 'PAGES.DASHBOARD.TABLE.NO_DATA_TOOLTIP' | translate }}
                  </span>
                </div>
              </ng-container>
              <ng-template #themeVersionBlock>
                <div data-action="update-wp-theme" (click)="wordPressThemeClicked(site)" class="cell-clickable">
                  <a>{{ getThemeVersion(site) }}</a>
                </div>
              </ng-template>
            </div>
          </mat-cell>
        </ng-container>

        <ng-container matColumnDef="links">
          <mat-header-cell *matHeaderCellDef>
            {{ 'PAGES.DASHBOARD.TABLE.HEADERS.LINKS' | translate }}
          </mat-header-cell>
          <mat-cell class="actions-column-cell" *matCellDef="let site">
            <button mat-icon-button color="primary" [matMenuTriggerFor]="menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
              <button mat-menu-item>
                <a data-action="navigate-wsp-dashboard" class="link" (click)="websiteProDashboardClicked(site)">
                  <img src="{{ getLogoImage(site) }}" alt="" class="logo" />
                  &nbsp; {{ 'PAGES.DASHBOARD.TABLE.WSP_DASHBOARD' | translate }}
                </a>
              </button>
              <button mat-menu-item>
                <a data-action="navigate-wp-dashboard" class="link" (click)="wordPressDashboardClicked(site)">
                  <img src="{{ wordPressImage }}" alt="" class="logo" />
                  &nbsp; {{ 'PAGES.DASHBOARD.TABLE.WP_DASHBOARD' | translate }}
                </a>
              </button>
            </mat-menu>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
      </mat-table>

      <mat-spinner
        *ngIf="dataSource.loading$ | async"
        class="total-table-spinner"
        color="accent"
        mode="indeterminate"
      ></mat-spinner>

      <mat-paginator
        #paginator
        [pageSize]="dataSource.pageSize$ | async"
        [pageIndex]="dataSource.pageIndex$ | async"
        (page)="dataSource.pageOptionsChanged($event)"
        [length]="dataSource.sitesCount$ | async"
        [pageSizeOptions]="[10, 25, 50, 100]"
      ></mat-paginator>
    </div>
  </va-filter-container>
</mat-card>
