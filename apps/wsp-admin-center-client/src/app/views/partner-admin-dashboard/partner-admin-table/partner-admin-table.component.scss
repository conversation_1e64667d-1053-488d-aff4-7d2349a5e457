@use 'design-tokens' as *;

.sites-list-container {
  min-height: min-content;
  min-width: 720px;
  margin: 0 auto;
  background: none;
  background-color: white;
  box-shadow:
    0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  padding: 0px;

  .mat-column-account {
    flex: 0 0 20%;
  }
  .mat-column-url {
    flex: 0 0 30%;
  }

  mat-header-cell {
    font-weight: bold;
    color: black;
    border-bottom-width: 0;
  }

  .data-column-cell {
    text-align: center;
    min-width: 5%;
  }

  .actions-column-cell {
    text-align: right;
    min-width: 100px;
  }

  .plugin-clickable {
    cursor: pointer;
    color: $primary-color;
  }

  .total-table-spinner {
    margin-top: 20px;
    margin-left: 40%;
  }

  .website-logo {
    float: left;
    padding: 0 5px;
    height: 24px;
  }

  .wordpress-logo {
    float: left;
    padding: 0 5px;
    height: 24px;
  }

  .cell-clickable {
    float: right;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .logo-clickable {
    float: left;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .wordpress-clickable {
    float: right;
    padding: 0 5px;
    cursor: pointer;
    color: $primary-color;
  }

  .table-info {
    overflow-x: auto;
    ::ng-deep mat-cell {
      div {
        text-align: left;
      }
    }
  }

  #site-list {
    text-align: left;
    flex-basis: auto;
  }

  .tooltip {
    display: inline-block;
  }

  .tooltip .tooltiptext {
    visibility: hidden;
    width: 250px;
    background-color: black;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 5px;

    /* Position the tooltip */
    position: absolute;
    z-index: 1;
  }

  .tooltip:hover .tooltiptext {
    visibility: visible;
  }
}

.link {
  line-height: 24px;
  display: flex;
}

.logo {
  height: 24px !important;
}
