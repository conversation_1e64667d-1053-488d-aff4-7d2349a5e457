import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { PartnerRouteParams } from '../../../core/routeparams';
import { MatTable } from '@angular/material/table';
import { WordPressSiteDataSource, FilterId } from '../../../core/wordpress-site/wordpress-site-data-source';
import { WordPressSite } from '../../../core/wsp_admin_center_sdk/_internal/objects';
import { WordpressSiteService } from '../../../core/wordpress-site/wordpress-site.service';
import { getImageSrc } from '../../../core/image-utilities';
import { Environment, EnvironmentService } from '@galaxy/core';
import { CheckboxFilterField, Filters, FilterSection, FilterService } from '@vendasta/uikit';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'app-partner-admin-table',
  templateUrl: './partner-admin-table.component.html',
  styleUrls: ['./partner-admin-table.component.scss'],
  standalone: false,
})
export class PartnerAdminTableComponent implements OnInit, OnDestroy {
  NO_THEME_VERSION: string;
  NO_CORE_VERSION: string;
  NO_AVAILABLE_VERSION: string;

  displayedColumns = ['account', 'url', 'pluginUpdates', 'wordPressVersion', 'themeVersion', 'links'];

  dataSource: WordPressSiteDataSource;
  partnerId: string;
  isCalledSingletonFilter = false;
  isCalledSingletonSearch = false;

  subscriptions: Subscription[] = [];
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatTable, { static: true }) table: MatTable<any>;

  checkImage = getImageSrc('images/check-circle-green.svg');
  warningImage = getImageSrc('images/warning-yellow.svg');

  logoImage = getImageSrc('website-pro.svg');
  wordPressImage = getImageSrc('wordpress-logo.svg');

  pluginUpdatesFilterField = new CheckboxFilterField({
    name: 'Plugin updates',
    id: FilterId.PluginUpdates,
    value: false,
  });

  coreUpdatesFilterField = new CheckboxFilterField({
    name: 'WordPress version updates',
    id: FilterId.CoreUpdates,
    appliedTextPrefix: 'Account',
    value: false,
  });

  themeUpdatesFilterField = new CheckboxFilterField({
    name: 'Theme version updates',
    id: FilterId.ThemeUpdates,
    value: false,
  });

  multisiteFilterField = new CheckboxFilterField({
    name: 'Multisites',
    id: FilterId.Multisite,
    value: false,
  });

  filters: Filters;

  constructor(
    private route: ActivatedRoute,
    private wordpressSiteService: WordpressSiteService,
    private environmentService: EnvironmentService,
    readonly filterService: FilterService,
    private readonly analyticsService: ProductAnalyticsService,
  ) {
    this.setPartnerId();
    this.filters = new Filters('Filters', [
      new FilterSection({
        title: 'Updates available',
        type: 'and',
        fields: [
          this.pluginUpdatesFilterField,
          this.coreUpdatesFilterField,
          this.themeUpdatesFilterField,
          this.multisiteFilterField,
        ],
      }),
    ]);
    this.filterService.setFilters(this.filters);
  }

  ngOnInit(): void {
    this.NO_THEME_VERSION = 'NO-THEME-VERSION'; // version is empty
    this.NO_CORE_VERSION = 'NO-CORE-VERSION';
    this.NO_AVAILABLE_VERSION = 'NO_AVAILABLE_VERSION';
    this.dataSource = new WordPressSiteDataSource(this.wordpressSiteService, this.partnerId);
    this.subscriptions.push(
      this.filterService.fieldValueChanges.subscribe((changedFilters) => {
        this.dataSource.updateFilter(changedFilters);
      }),
    );
  }

  setPartnerId(): void {
    this.partnerId = (this.route.snapshot.params as PartnerRouteParams).partnerId;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  trackById(index: number, wordPressSite: WordPressSite): string {
    return wordPressSite.siteId;
  }

  isPluginsNeedsUpdate(site: WordPressSite): boolean {
    return site.pluginUpdates !== undefined && site.pluginUpdates > 0;
  }

  getPluginUpdates(site: WordPressSite): number {
    return site.pluginUpdates === undefined ? 0 : site.pluginUpdates;
  }

  isThemeNeedsUpdate(site: WordPressSite): boolean {
    return site.currentTheme !== undefined && site.currentTheme.currentVersion !== site.currentTheme.latestVersion;
  }

  getThemeVersion(site: WordPressSite): string {
    // set the default version
    if (site.currentTheme === undefined) {
      return this.NO_AVAILABLE_VERSION;
    }
    return site.currentTheme.currentVersion === this.NO_THEME_VERSION ? '1.0' : site.currentTheme.currentVersion;
  }

  isCoreNeedsUpdate(site: WordPressSite): boolean {
    return site.currentCoreVersion !== site.latestCoreVersion;
  }

  getCoreVersion(site: WordPressSite): string {
    // set the default version
    if (site.currentCoreVersion === undefined) {
      return this.NO_AVAILABLE_VERSION;
    }
    return site.currentCoreVersion === this.NO_CORE_VERSION ? '1.0' : site.currentCoreVersion;
  }

  wordPressPluginClicked(site: WordPressSite): void {
    window.open(`https://${site.primaryDomain}/wp-admin/plugins.php`, '_blank');
  }

  wordPressCoreVersionClicked(site: WordPressSite): void {
    window.open(`https://${site.primaryDomain}/wp-admin/update-core.php`, '_blank');
  }

  wordPressThemeClicked(site: WordPressSite): void {
    window.open(`https://${site.primaryDomain}/wp-admin/themes.php`, '_blank');
  }

  websiteProDashboardClicked(site: WordPressSite): void {
    const environ = this.environmentService.getEnvironment();
    if (environ === Environment.PROD) {
      window.open(`https://www.websiteprodashboard.com/site/${site.siteId}/overview`, '_blank');
    } else {
      window.open(`https://websiteprodashboard-demo.com/site/${site.siteId}/overview`, '_blank');
    }
  }

  wordPressDashboardClicked(site: WordPressSite): void {
    window.open(`https://${site.primaryDomain}/wp-admin`, '_blank');
  }

  primaryDomainClicked(site: WordPressSite): void {
    window.open(`https://${site.primaryDomain}`, '_blank');
  }

  applySavedFilter(event: any): void {
    if (!this.isCalledSingletonFilter) {
      this.analyticsService.trackEvent('apply-saved-filter', 'admin-table', 'apply-filter');
      this.isCalledSingletonFilter = true;
    }
    this.filterService.applySavedFilter(event);
  }

  updateSearchTerm(event: any): void {
    if (!this.isCalledSingletonSearch) {
      this.analyticsService.trackEvent('update-search-term', 'admin-table', 'search-term');
      this.isCalledSingletonSearch = true;
    }
    this.dataSource.updateSearchTerm(event);
  }

  getIsMultisite(site: WordPressSite): boolean {
    return site.isMultisite === undefined ? false : site.isMultisite;
  }

  getSubsitesCount(site: WordPressSite): number {
    let subsitesCount = 0;
    if (site.subsiteInfo !== undefined) {
      subsitesCount = site.subsiteInfo.length - 1;
    }
    return subsitesCount;
  }

  getLogoImage(site: WordPressSite): string {
    const imageName =
      site.isMultisite !== undefined && site.isMultisite === true ? 'website-multisite.svg' : 'website-pro.svg';
    return getImageSrc(imageName);
  }
}
