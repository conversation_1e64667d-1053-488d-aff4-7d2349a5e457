import { HttpClient, HttpHandler } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyNavModule } from '@vendasta/galaxy/nav';
import { AppComponent } from './app.component';
import { AppConfigService } from './core/appconfig/app-config.service';
import { WspAdminCenterModule } from './core/wsp_admin_center_sdk/wsp-admin-center.module';
import { SelectedPartnerService } from './selected-partner.service';

describe('AppComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AppComponent],
      imports: [
        GalaxyNavModule,
        MatToolbarModule,
        RouterTestingModule,
        MatIconModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        WspAdminCenterModule,
      ],
      providers: [AppConfigService, HttpClient, HttpHandler, SelectedPartnerService],
    }).compileComponents();
    TestBed.inject(TranslateService).setDefaultLang = jest.fn();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have as title 'wsp-admin-center-client'`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.title).toEqual('wsp-admin-center-client');
  });

  it('should render galaxy nav header title', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('span').textContent).toContain('Website Pro Admin');
  });
});
