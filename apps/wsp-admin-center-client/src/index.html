<!doctype html>
<html lang="en">
  <head>
    <!-- vStaticInject:deployment -->
    <!-- vStaticInject:environment -->
    <!-- vStaticInject:appId -->
    <!-- vStaticInject:iam -->
    <script>
      // this iamSession should only be manually modified and placed here for local development. Typically, sessions
      // are stamped down by vStatic when it serves this client to the browser, but that is not happening on local
      // so this iamSession is used as a replacement for that. This should be commented out when deployed, and only left
      // uncommented while developing locally. See README on how to obtain a session
      // var iamSession = ''
    </script>

    <!-- Hotjar Tracking Code for Website Pro Admin -->
    <script>
      (function (h, o, t, j, a, r) {
        h.hj =
          h.hj ||
          function () {
            (h.hj.q = h.hj.q || []).push(arguments);
          };
        h._hjSettings = { hjid: 421555, hjsv: 6 };
        a = o.getElementsByTagName('head')[0];
        r = o.createElement('script');
        r.async = 1;
        r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
        a.appendChild(r);
      })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>

    <meta charset="utf-8" />
    <title>Website Pro Admin Center</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons&display=block" rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700,400italic,700italic"
      rel="stylesheet"
      type="text/css"
    />

    <link
      rel="icon"
      type="image/x-icon"
      href="https://vstatic-prod.apigateway.co/wsp-portal-client/assets/favicon.png"
    />
    <script src="https://cdn.apigateway.co/static/no-es6.js" nomodule defer></script>
  </head>
  <body>
    <app-root></app-root>
  </body>
</html>
