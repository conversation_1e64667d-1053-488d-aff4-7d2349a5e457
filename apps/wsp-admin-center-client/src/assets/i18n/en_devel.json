{"APP_NAME": "Website Pro Admin", "NAVIGATION": {"TABS": {"PRODUCT_SETTINGS": "Product settings", "DASHBOARD": "Dashboard", "UPDATE": "Update", "UPTIME": "Uptime"}, "FOOTER": {"POWERED_BY": "Powered by"}}, "ADVANCED_PAGE": {"SHOW_ADVANCED_FEATURES_CARD": {"TITLE": "Show advanced features", "DESCRIPTION": "Disable this setting to hide the \"Advanced\" tab from your client users. Users in your organization will still be able to see this tab when they access a client's Website product.", "TOGGLE_LABEL": "Show \"Advanced\" tab from your client users", "SHOW_ADVANCED_FEATURES_ON": "on", "SHOW_ADVANCED_FEATURES_OFF": "off"}, "SHOW_INCLUDED_TEMPLATES_CARD": {"TITLE": "Show included templates", "DESCRIPTION": "Disable this setting to hide the included Website templates from your clients and only show the custom templates you have created.", "TOGGLE_LABEL": "Show the included templates from your client users", "SHOW_INCLUDED_TEMPLATES_ON": "on", "SHOW_INCLUDED_TEMPLATES_OFF": "off"}}, "PAGES": {"PRODUCT_SETTINGS": {"TITLE": "Product settings"}, "DASHBOARD": {"TITLE": "Dashboard", "SUMMARY": {"TITLE": "Updates available", "PLUGINS": "Plugins", "WORDPRESS": "WordPress", "THEME": "Theme", "TOTAL": "Total"}, "TABLE": {"HEADERS": {"ACCOUNT": "Account", "URL": "URL", "SUBSITES_COUNT": "Subsites Count", "PLUGIN_UPDATES": "Plugin Updates", "WORDPRESS_VERSION": "WordPress Version", "THEME_VERSION": "Theme Version", "LINKS": "Links"}, "NO_DATA_TOOLTIP": "We couldn't grab this data. This is usually caused by your site's firewall, a security plugin, or a temporary loss of access.", "NOT_MULTISITE_TOOLTIP": "There are no subsites detected because this account is a single site account.", "MULTISITE_BADGE": "Multisite", "WSP_DASHBOARD": "WSP Dashboard", "WP_DASHBOARD": "WordPress Dashboard"}, "MS_REPORT_TABLE": {"TITLE": "Marketing Services Website Vulnerability Report"}}, "UPTIME": {"TITLE": "Uptime", "ALL_WEBSITES": "All Websites", "SITES_UP": "Sites Up", "SITES_DOWN": "Sites Down", "SITES_PAUSED": "Sites Paused", "UP": "Up", "DOWN": "Down", "PAUSED": "Paused", "TOOLTIP": "Uptime checks are performed every 30 minutes", "TABLE": {"HEADERS": {"STATUS": "Status", "24HOURS": "24 Hours", "7DAYS": "7 Days", "30DAYS": "30 Days"}}, "HTTPERRORSTEPS": {"400": {"ERROR_NAME": "Bad Request", "DESCRIPTION": "The server could not understand the request due to invalid syntax.", "STEPS_TO_RESOLVE": {"STEP1": "Check the URL for any typos or malformed syntax.", "STEP2": "Clear your browser cache and cookies.", "STEP3": "Disable any WordPress plugins that might interfere with requests.", "STEP4": "Ensure the NGINX configuration does not block specific requests.", "STEP5": "If using a CDN, ensure it is properly configured."}}, "401": {"ERROR_NAME": "Unauthorized", "DESCRIPTION": "Authentication is required and has failed or not been provided.", "STEPS_TO_RESOLVE": {"STEP1": "Ensure the correct username and password are being used.", "STEP2": "Verify the site's authentication headers or tokens are correctly configured.", "STEP3": "Disable or troubleshoot WordPress security plugins blocking access.", "STEP4": "Check the server logs for more detailed error messages.", "STEP5": "Ensure file permissions are set correctly (e.g., wp-admin should be accessible)."}}, "403": {"ERROR_NAME": "Forbidden", "DESCRIPTION": "You do not have permission to access the requested resource.", "STEPS_TO_RESOLVE": {"STEP1": "Check file and directory permissions (recommended: 755 for directories and 644 for files).", "STEP2": "Disable WordPress security plugins temporarily to test for conflicts.", "STEP3": "Verify the NGINX configuration for any restrictive rules.", "STEP4": "Ensure your IP or country is not blocked by server-level rules.", "STEP5": "Contact the hosting provider to check server-side configurations."}}, "404": {"ERROR_NAME": "Not Found", "DESCRIPTION": "The server cannot find the requested resource.", "STEPS_TO_RESOLVE": {"STEP1": "Ensure the URL is correct and points to an existing page.", "STEP2": "Check WordPress permalink settings under Settings > Permalinks and re-save them.", "STEP3": "Ensure NGINX is configured to pass requests correctly to the WordPress index.php file.", "STEP4": "Clear the site cache and browser cache.", "STEP5": "Restore missing files or content if they were accidentally deleted."}}, "500": {"ERROR_NAME": "Internal Server Error", "DESCRIPTION": "The server encountered an internal error and could not complete your request.", "STEPS_TO_RESOLVE": {"STEP1": "Check the server error logs for specific issues.", "STEP2": "Disable all WordPress plugins and switch to a default theme to test for conflicts.", "STEP3": "Verify the NGINX configuration for any incorrect directives.", "STEP4": "Ensure your hosting environment meets the WordPress requirements (e.g., PHP version)."}}, "502": {"ERROR_NAME": "Bad Gateway", "DESCRIPTION": "The server received an invalid response from the upstream server.", "STEPS_TO_RESOLVE": {"STEP1": "Restart your web server or contact your hosting provider to resolve upstream issues.", "STEP2": "Disable proxy services like Cloudflare temporarily to test direct server access.", "STEP3": "Check for server overload or high traffic and upgrade hosting if necessary.", "STEP4": "Ensure your WordPress site is not hitting PHP-FPM or FastCGI limits.", "STEP5": "Verify DNS settings to ensure the domain resolves to the correct server."}}, "503": {"ERROR_NAME": "Service Unavailable", "DESCRIPTION": "The server is temporarily unable to handle the request.", "STEPS_TO_RESOLVE": {"STEP1": "Check for ongoing maintenance mode in WordPress (remove .maintenance file if present).", "STEP2": "Disable all plugins to test for performance-related issues.", "STEP3": "Upgrade your hosting plan if the site exceeds server resource limits.", "STEP4": "Contact your hosting provider to investigate server overload.", "STEP5": "Ensure cron jobs are not causing excessive server load."}}, "504": {"ERROR_NAME": "Gateway Timeout", "DESCRIPTION": "The server took too long to respond to the request.", "STEPS_TO_RESOLVE": {"STEP1": "Increase the server timeout limits in php.ini or NGINX configuration.", "STEP2": "Optimize your WordPress database to improve query performance.", "STEP3": "Disable plugins causing high server load (e.g., poorly optimized caching plugins).", "STEP4": "Check DNS settings and ensure the server resolves correctly.", "STEP5": "Upgrade to a more powerful hosting plan if necessary."}}, "526": {"ERROR_NAME": "Invalid SSL Certificate", "DESCRIPTION": "The SSL certificate used is invalid.", "STEPS_TO_RESOLVE": {"STEP1": "Reinstall the SSL certificate through your hosting provider's dashboard.", "STEP2": "Check the certificate's expiration date and renew it if expired.", "STEP3": "Ensure the server is configured to use the correct SSL certificate.", "STEP4": "Use an SSL checker tool to validate the certificate's chain and configuration.", "STEP5": "Contact your hosting provider to regenerate the SSL certificate."}}}}, "NOHTTPDATA": {"NO_DESCRIPTION": "No description available.", "NO_STEPS": "No steps available."}, "NOERRORCODEDATA": {"NO_DATA_DESCRIPTION": "No description available for this error code.", "NO_DATA_STEPS": "No resolution steps available for this error code. Please contact the support."}, "HTTPERROR": {"404": "Not found", "403": "Forbidden", "500": "Internal Server Error", "502": "Bad Gateway", "301": "Moved Permanently", "429": "Too Many Requests", "521": "Web Server Is Down", "526": "Invalid SSL Certificate", "530": "Origin DNS Error", "525": "SSL Handshake Failed", "400": "Bad Request", "504": "Gateway Timeout", "410": "Gone", "436": "Blocked by Wordfence", "406": "Not Acceptable", "520": "Web Server Is Returning an Unknown Error", "401": "Unauthorized", "409": "Conflict", "302": "Temporarily Moved", "308": "Permanent Redirect", "303": "See Other", "523": "Origin Is Unreachable", "405": "Method Not Allowed", "307": "Temporary Redirect", "408": "Request Timeout", "508": "Loop Detected", "415": "Unsupported Media Type", "423": "Locked"}}, "APP_FAILURE": {"TITLE": "Something's gone wrong, but we're working on it.", "SITE_ERROR": "Try accessing this page again in a few minutes.", "DEFAULT": "We're terribly sorry, something went wrong."}, "APP_INSUFFICIENT_PERMISSION": {"TITLE": "You don't have permission to view this page.", "SITE_ERROR": "Sign in as a different user to continue.", "SIGN_IN": "Sign in"}}