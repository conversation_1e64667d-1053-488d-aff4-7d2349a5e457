# Website Pro Admin Center Client

## Getting Started

These instructions will get you a copy of the project up and running on your
local machine for development and testing purposes. See deployment for
notes on how to deploy the project on a live system.

## Deployment

Both environments, demo and prod, are managed through
[Mission Control](https://mission-control-prod.vendasta-internal.com/applications/wsp-admin-center-client).

## Authentication

### Set up Service Provider
Set up service provider through URL: https://admin-demo.vendasta-internal.com/integrations/wsp-admin-center, choose `Service Provider Type` to "Platform", also create OAuth2 configuration here. 

For production, the URL is https://admin.vendasta-internal.com/integrations/wsp-admin-center.  

### Auth Configuration
After creating service provier and OAuth2 configuration, we create a [auth-config.ts](https://github.com/vendasta/galaxy/blob/master/apps/wsp-admin-center-client/src/app/auth/auth-config.ts)

```
scopes: ['admin'],
serviceProviderConfigs: {
    [Environment.LOCAL]: {
      // sso serviceProviderId
      serviceProviderId: 'wsp-admin-center',
      // URL to redirect back into your app at.
      redirectUri: 'https://localhost:4200',
      // See 'Configuring silent refresh' for more information
      silentRefreshRedirectUri: 'https://localhost:4200/silent-refresh.html',
      // sso oauth2 client id
      clientId: 'c5a86f66-5ed1-4c11-82e3-157088e94831',
    },
    [Environment.DEMO]: {
      serviceProviderId: 'wsp-admin-center',
      redirectUri: 'https://wsp-admin-center-demo.apigateway.co',
      silentRefreshRedirectUri: 'https://wsp-admin-center-demo.apigateway.co/silent-refresh.html',
      clientId: 'c5a86f66-5ed1-4c11-82e3-157088e94831',
    },
    [Environment.PROD]: {
      serviceProviderId: 'wsp-admin-center',
      redirectUri: 'https://wsp-admin-center-prod.apigateway.co',
      silentRefreshRedirectUri: 'https://wsp-admin-center-prod.apigateway.co/silent-refresh.html',
      clientId: 'f6486e6d-740c-48b1-b46e-38923aba92b5',
    },
  },
```

`clientId` is from OAuth2 configurations, `redirectUri` is the URL to redirect back into your app after SSO, `silentRefreshRedirectUri` is used to silently refresh access token periodly. See the below for silent refresh. 

If the app gives access to only admin users, you may need to specify the scope to `admin` in the auth config, you'll also need to change the service API to have the scope set to admin as well. see the API details: https://github.com/vendasta/vendastaapis/blob/master/wsp_admin_center/v1/api.proto#L107

```
service AdminCenterService {

  // GetAppConfig returns the AppConfig associated with the partner ID present on the request message
  rpc GetAppConfig(GetAppConfigRequest) returns (GetAppConfigResponse) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // SetShowAdvancedFeatures set whether to show advanced feature for a partner
  rpc SetShowAdvancedFeatures(SetShowAdvancedFeaturesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

  // SetShowIncludedTemplates set whether to show Vendasta templates for a partner
  rpc SetShowIncludedTemplates(SetShowIncludedTemplatesRequest) returns (google.protobuf.Empty) {
    option (vendastatypes.access) = {
      scope: "admin"
    };
  };

}
``` 

### Auth Service
Use [Auth Service](https://github.com/vendasta/galaxy/blob/master/apps/wsp-admin-center-client/src/app/auth/auth.service.ts), pay attention that in `canActivate`, we need get `partner_id` from the route and call `this.partnerService.setPartnerId`. This is used to set custom query param of OAuthService, and pass the partner_id, or it will throw errors. 

```
private setQueryParams(partnerId: string): void {
  // Specify partner for the sso service
  this.oauthService.customQueryParams = {
    partner_id: partnerId,
  };
}
```

The general process is as follows:
1) User access `https://wsp-admin-center-prod.apigateway.co/partner/ABC/settings` for the first time
2) Redirect to SSO login and ask for grant access
3) Redirect to service provider
4) Return back to `redirectUrl`: https://wsp-admin-center-prod.apigateway.co
5) Go to OAuthService.canActivate, check if it has valid access token
6) Redirect to `nextUrl` which is `https://wsp-admin-center-prod.apigateway.co/partner/ABC/settings` when checking token is valid

### Use OAuthService as route guard 
In the [route.ts](https://github.com/vendasta/galaxy/blob/master/apps/wsp-admin-center-client/src/app/views/routes.ts), using OAuth2Service as route guard.
```
export const routes: Routes = [
  {
    path: '',
    canActivate: [OAuth2Service],
    component: HomeComponent,
  },
  {
    path: 'partner/:partnerId/settings',
    loadChildren: () => import('./settings/settings-page.module').then((m) => m.SettingsPageModule),
    resolve: { partnerId: SelectedPartnerService },
    canActivate: [OAuth2Service],
  },
  {
    path: 'partner/:partnerId/dashboard',
    loadChildren: () => import('./partner-admin-dashboard/partner-admin.module').then((m) => m.PartnerAdminModule),
    resolve: { partnerId: SelectedPartnerService },
    canActivate: [OAuth2Service],
  },
];
```

### Use silent-refresh.html
Add a [silent-refresh.html](https://github.com/vendasta/galaxy/blob/master/apps/wsp-admin-center-client/src/silent-refresh.html) in the source root directory. It is used to refresh the access token. Add `silent-refresh.html` into the [angular.json](https://github.com/vendasta/galaxy/blob/master/angular.json#L5251), so that this html is treated a static resource, and accessing this resource does not go to Angular route.

In the backend `wsp-admin-center`, create a handler that will periodly access this silent-refresh.html: https://github.com/vendasta/wsp-admin-center/blob/master/server/main.go#L268
```
mux.HandleFunc("/silent-refresh.html", c.GetAssetHandler(ctx, true))
```

### Change app.module.ts
In app.module.ts, use OAuth2Service as the Session service, so that the API requests have `Authorization` header which comes from OAuthService token.
```
  OAuth2Service,
  { provide: OAuthStorage, useFactory: storageFactory },
  { provide: SessionService, useExisting: OAuth2Service },
  { provide: OAuth2ServiceConfigToken, useValue: oauth2Config },
``` 


## Running Locally

Before navigating to local host, we need to inject the IAM token.

1. Navigate to `https://iam-demo.apigateway.co/personas` and copy your token to the clipboard.
2. Uncomment the `iamSession` line in [index.html](https://github.com/vendasta/galaxy/blob/29e8ad9e7c3a2055be96132f9a7d95740ba35eb9/apps/wsp-admin-center-client/src/index.html#L8) and paste your token in the quotes.

Then run `npm run start wsp-admin-center-client` for a dev server. By default, this runs off demo and we recomend using demo since some of the prod configs might be missing.

Open an **new Incognito window** and navigate to one of the following urls depending on what page you want to access:

- `https://localhost:4200/` 

## Code scaffolding

Run `ng generate component <component-name>` (or, simply, `ng g c <component-name>`) to
generate a new component. You can also use `ng g directive|pipe|service|class|guard|interface |enum|module`.

## Build

Run `npm run build:<env>` to build the project. The build artifacts will be stored in the
`dist/` directory.

## Running unit tests

See the Galaxy repository [README](https://github.com/vendasta/galaxy) for instructions on running tests.

## Running Linting

See the Galaxy repository [README](https://github.com/vendasta/galaxy) for instructions on linting.

