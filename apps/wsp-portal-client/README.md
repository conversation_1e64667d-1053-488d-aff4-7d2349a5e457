# Website Pro Portal Client

## Getting Started

These instructions will get you a copy of the project up and running on your
local machine for development and testing purposes. See deployment for
notes on how to deploy the project on a live system.

## Deployment

Both environments, demo and prod, are managed through
[Mission Control](https://mission-control-prod-regional.vendasta-internal.com/applications/wsp-portal-client).

## Running Locally

**Running locally only works with <PERSON><PERSON>, not Prod**

Before navigating to local host, we need to inject the IAM token.

1. Navigate to `https://iam-demo.apigateway.co/personas` and copy your token to the clipboard.
2. Uncomment the `iamSession` line in [index.html](./src/index.html#L8) and paste your token in the quotes.
4. Run `PROXY_ENV=demo npm run start wsp-portal-client` for a dev server. By default, this runs off demo and we recomend using demo since some of the prod configs might be missing. To choose a different proxyHost, edit proxy.conf.js
5. Open an **new Incognito window** and navigate to one of the following urls depending on what page you want to access:

- `https://localhost:4200/create-from-template/<agid>` if no site has been created
- `https://localhost:4200/site/<site-id>/overview` if there is a site

Example: https://localhost:4200/site/db3f60dae1e61eae9e743474f65a5652d745668c402f3774102df193ff900994/overview

## Code scaffolding

Run `ng generate component <component-name>` (or, simply, `ng g c <component-name>`) to
generate a new component. You can also use `ng g directive|pipe|service|class|guard|interface |enum|module`.

## Build

Run `npm run build:<env>` to build the project. The build artifacts will be stored in the
`dist/` directory.

## Running unit tests

See the Galaxy repository README for instructions on running tests.

## Running Linting

See the Galaxy repository README for instructions on linting.

## Prettier

Run `npx prettier --help` for help on running prettier on WSP. The command `npx prettier --write apps/wsp-portal-client` performs a prettier check on the repository and
cleans up fiels according to the `.prettierc` file at the top-level of the Galaxy repository.

Most IDEs have the ability to run prettier on save:

- JetBrains: preferences -> file watchers -> add `prettier`
- vscode: add `"editor.formatOnSave": true` to your settings.json
