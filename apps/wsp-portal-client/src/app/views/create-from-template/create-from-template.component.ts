import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatStepper } from '@angular/material/stepper';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { combineLatest, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { DEFAULT_DOMAIN_SUFFIX, MULTISITE_DOMAIN_SUFFIX, MULTISITE_EDITION_NAME } from '../../../globals';
import { AccountsService } from '../../accounts.service';
import { AccountService } from '../../core/account';
import { ApiErrorFactory } from '../../core/api-errors';
import { DefaultDomainUtility } from '../../core/domain-utility';
import { ProductSettingsService } from '../../core/product-settings';
import { SiteService } from '../../core/site';
import { AppBrandingService } from '../../core/white-label/app-branding.service';
import { FormFields } from '../create-progress/form-fields';
import { ConfirmationDialog } from '../dialog';
import { DomainMessagesService } from '../domain/domain-messages.service';
import { BaseComponent } from './base.component';
import { CreateFromTemplateService } from './create-from-template.service';

@Component({
  selector: 'app-create-from-template',
  templateUrl: './create-from-template.component.html',
  styleUrls: ['./create-from-template.component.scss'],
  standalone: false,
})
export class CreateFromTemplateComponent extends BaseComponent implements OnInit {
  loading$: Observable<boolean>;
  productName$: Observable<string>;
  formFields: FormFields;
  defaultDomainSuffix: string = DEFAULT_DOMAIN_SUFFIX;
  msDomainSuffix: string = MULTISITE_DOMAIN_SUFFIX;
  msEditionName: string = MULTISITE_EDITION_NAME;
  multisite = false;
  maxLength = 150;
  templateSelectionStep = true;
  maxSubDomainLength: number = DefaultDomainUtility.maxDefaultLabelLength;
  domainFormGroup: UntypedFormGroup;
  isPlaceholder = false;
  isStepCompleted = false;

  @ViewChild('stepper') private matStepper: MatStepper;

  constructor(
    protected alertService: SnackbarService,
    protected apiErrorFactory: ApiErrorFactory,
    protected route: ActivatedRoute,
    protected router: Router,
    protected siteService: SiteService,
    protected accountService: AccountService,
    protected createFromTemplateService: CreateFromTemplateService,
    protected productSettingsService: ProductSettingsService,
    private translate: TranslateService,
    private domainMessages: DomainMessagesService,
    private accountsService: AccountsService,
    private dialog: MatDialog,
    private appBrandingService: AppBrandingService,
    public readonly analyticsService: ProductAnalyticsService,
    private changeDetector: ChangeDetectorRef,
  ) {
    super(route, router, siteService, accountService, createFromTemplateService, productSettingsService);

    this.defaultDomainSuffix = DEFAULT_DOMAIN_SUFFIX;
    this.msDomainSuffix = MULTISITE_DOMAIN_SUFFIX;
    this.loading$ = this.createFromTemplateService.loading$();
    this.productName$ = combineLatest([
      this.appBrandingService.appBranding$.pipe(
        filter((appBranding) => appBranding !== null),
        map((appBranding) => (appBranding && appBranding.enabled && appBranding.name ? appBranding.name : 'Website')),
      ),
      this.accountsService.editionName$,
    ]).pipe(
      filter(([appName, edition]: [string, string]) => !!appName && !!edition),
      map(([appName, edition]: [string, string]) => appName + ' ' + edition),
    );
    this.createFromTemplateService.initFormFields();

    this.accountsService.editionName$.subscribe((editionName: string) => {
      if (editionName == this.msEditionName) {
        this.multisite = true;
      }
      if (this.multisite && !!this.domainFormGroup) {
        this.domainFormGroup.get('multisite')?.setValue(this.multisite);
      }
    });
    this.subscriptions.push(this.createFromTemplateService.form$$.subscribe((f) => (this.formFields = f)));
  }

  ngOnInit(): void {
    this.calcColumns(window.innerWidth);
    this.domainFormGroup = new UntypedFormGroup({
      siteTitle: new UntypedFormControl(this.formFields.siteTitle, [Validators.required]),
      siteTagline: new UntypedFormControl(this.formFields.siteTagline, [Validators.required]),
      defaultDomain: new UntypedFormControl(this.formFields.defaultDomain, [Validators.required]),
      multisite: new UntypedFormControl(this.multisite, [Validators.required]),
    });
  }

  openImportDialog(): void {
    this.dialog
      .open(ConfirmationDialog, {
        data: {
          content: 'PAGES.CREATE_FROM_TEMPLATES.IMPORT_DIALOG.TITLE',
          subtitle: 'PAGES.CREATE_FROM_TEMPLATES.IMPORT_DIALOG.CONTENT',
          action: 'PAGES.CREATE_FROM_TEMPLATES.IMPORT_DIALOG.ACTION',
          cancel: 'PAGES.CREATE_FROM_TEMPLATES.IMPORT_DIALOG.CANCEL',
        },
        disableClose: false,
        hasBackdrop: true,
      })
      .afterClosed()
      .pipe(filter((create) => !!create))
      .subscribe(() => {
        this.isPlaceholder = true;
        this.goToNextStep();
      });
  }

  onSubmit(): void {
    this.formFields.siteTitle = this.domainFormGroup.value['siteTitle'];
    this.formFields.siteTagline = this.domainFormGroup.value['siteTagline'];
    this.formFields.defaultDomain = this.domainFormGroup.value['defaultDomain'];
    this.formFields.multisite = this.domainFormGroup.value['multisite'];
    this.formFields.clean();
    this.subscriptions.push(
      this.createFromTemplateService.setFormFields(this.formFields, this.isPlaceholder).subscribe(
        (res) => {
          const url = `/create/${this.accountGroupId}/create/${res.siteId}/job/${res.jobId}/site`;
          this.createFromTemplateService.navigateByUrl(url);
          this.matStepper.next();
        },
        (err) => {
          this.createFromTemplateService.setCreateDisabled(false);
          this.createFromTemplateService.handleError(err);
        },
      ),
    );
  }

  stepperClicked(event: any): void {
    this.templateSelectionStep = event.selectedIndex === 0;
  }

  nextPage(event: boolean): void {
    if (event) {
      this.goToNextStep();
    }
  }

  goToNextStep(): void {
    this.isStepCompleted = true;
    this.changeDetector.detectChanges();
    this.matStepper.next();
  }

  calculateRemainingChars(currentChars: number): number {
    return this.maxLength - currentChars;
  }

  calculateRemainingSubdomainChars(currentChars: number): number {
    return this.maxSubDomainLength - currentChars;
  }
}
