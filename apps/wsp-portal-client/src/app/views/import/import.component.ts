import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { AccountsService } from '../../accounts.service';

@Component({
  selector: 'app-site-import',
  template: `
    <glxy-page>
      <glxy-page-toolbar>
        <glxy-page-title>{{ 'NAVIGATION.TABS.IMPORT' | translate }}</glxy-page-title>
        <glxy-page-actions>
          <a class="toolbar-help-button" mat-icon-button target="_blank" href="https://websitepro.zendesk.com/hc/en-us">
            <mat-icon>help_outline</mat-icon>
          </a>
        </glxy-page-actions>
      </glxy-page-toolbar>

      <glxy-page-wrapper>
        <div class="page page-normal">
          <div>
            <h2>
              {{ isMultisite ? ('PAGES.IMPORT_MULTISITE.TITLE' | translate) : ('PAGES.IMPORT.TITLE' | translate) }}
            </h2>
          </div>
          <mat-card appearance="outlined" class="card-container textalign-left">
            <mat-card-content class="card-regular">
              <mat-list>
                <ng-container *ngIf="!isMultisite">
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">1</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="
                        'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_1' | translate: { edition: editionName$ | async }
                      "
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">2</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_2' | translate"
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">3</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_3.STEP_3A' | translate"
                    ></span>
                    <span
                      matListItemLine
                      class="line-item tabbed"
                      [innerHTML]="'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_3.STEP_3B' | translate"
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">4</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_4.STEP_4A' | translate"
                    ></span>
                    <span
                      matListItemLine
                      class="line-item tabbed"
                      [innerHTML]="'PAGES.IMPORT.INSTRUCTIONS_MODAL.STEP_4.STEP_4B' | translate"
                    ></span>
                  </mat-list-item>
                </ng-container>
                <ng-container *ngIf="isMultisite">
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">1</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="
                        'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_1'
                          | translate: { edition: editionName$ | async }
                      "
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">2</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_2' | translate"
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">3</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_3.STEP_3A' | translate"
                    ></span>
                    <span
                      matListItemLine
                      class="line-item tabbed"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_3.STEP_3B' | translate"
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">4</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_4' | translate"
                    ></span>
                  </mat-list-item>
                  <mat-list-item class="list-item">
                    <span matListItemIcon class="list-index">5</span>
                    <span
                      matListItemLine
                      class="line-item"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_5.STEP_5A' | translate"
                    ></span>
                    <span
                      matListItemLine
                      class="line-item tabbed"
                      [innerHTML]="'PAGES.IMPORT_MULTISITE.INSTRUCTIONS_MODAL_MULTISITE.STEP_5.STEP_5B' | translate"
                    ></span>
                  </mat-list-item>
                </ng-container>
              </mat-list>
            </mat-card-content>
            <mat-card-actions class="action-buttons">
              <a
                mat-button
                (click)="navigateHelp()"
                [innerHTML]="'PAGES.IMPORT.BUTTONS.SEE_HOW_IT_WORKS' | translate"
              ></a>
            </mat-card-actions>
          </mat-card>
        </div>
      </glxy-page-wrapper>
    </glxy-page>
  `,
  styleUrls: ['./import.styles.scss'],
  standalone: false,
})
export class ImportComponent {
  editionName$: Observable<string>;
  isMultisite = false;

  constructor(
    private route: ActivatedRoute,
    private accountsService: AccountsService,
  ) {
    this.editionName$ = this.accountsService.editionName$;
    this.editionName$.subscribe((edition) => {
      this.isMultisite = edition === 'Pro Multisite';
    });
  }

  navigateHelp(): void {
    const url = 'https://www.youtube.com/watch?v=BpWxCeUWBOk';
    window.open(url, '_blank');
  }
}
