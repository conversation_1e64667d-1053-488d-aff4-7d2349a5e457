import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatStepper } from '@angular/material/stepper';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { DEFAULT_DOMAIN_SUFFIX, MULTISITE_DOMAIN_SUFFIX, MULTISITE_EDITION_NAME } from '../../../globals';
import { AccountsService } from '../../accounts.service';
import { AccountService } from '../../core/account';
import { ApiErrorFactory } from '../../core/api-errors';
import { DefaultDomainUtility } from '../../core/domain-utility';
import { ProductSettingsService } from '../../core/product-settings';
import { SiteService } from '../../core/site';
import { AppBrandingService } from '../../core/white-label/app-branding.service';
import { FormFields } from './migrate-progress/migrate-form-fields';
import { DomainMessagesService } from '../domain/domain-messages.service';
import { MigrateService } from './migrate.service';
import { AccountRouteParams } from '../../core/routeparams';
import { IAMService } from '@vendasta/iam';

@Component({
  selector: 'app-migrate',
  templateUrl: './migrate.component.html',
  styleUrls: ['./migrate.component.scss'],
  standalone: false,
})
export class MigrateComponent implements OnInit {
  loading$: Observable<boolean>;
  productName$: Observable<string>;
  formFields: FormFields;
  defaultDomainSuffix: string = DEFAULT_DOMAIN_SUFFIX;
  msDomainSuffix: string = MULTISITE_DOMAIN_SUFFIX;
  msEditionName: string = MULTISITE_EDITION_NAME;
  multisite = false;
  maxLength = 150;
  templateSelectionStep = true;
  maxSubDomainLength: number = DefaultDomainUtility.maxDefaultLabelLength;
  domainFormGroup: UntypedFormGroup;
  isPlaceholder = false;
  private loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  form$$: BehaviorSubject<FormFields> = new BehaviorSubject<FormFields>(undefined);
  private accountGroupId: string;
  protected subscriptions: Subscription[] = [];
  @ViewChild('stepper') private matStepper: MatStepper;
  private userId: string;

  constructor(
    protected alertService: SnackbarService,
    protected apiErrorFactory: ApiErrorFactory,
    protected route: ActivatedRoute,
    protected router: Router,
    protected siteService: SiteService,
    protected accountService: AccountService,
    protected migrateService: MigrateService,
    protected productSettingsService: ProductSettingsService,
    private translate: TranslateService,
    private domainMessages: DomainMessagesService,
    private accountsService: AccountsService,
    private dialog: MatDialog,
    private appBrandingService: AppBrandingService,
    public readonly analyticsService: ProductAnalyticsService,
    private changeDetector: ChangeDetectorRef,
    private iAmService: IAMService,
  ) {
    this.iAmService.getToken().subscribe((token) => {
      this.iAmService.getMultiUsers([{ token: token }]).subscribe((user) => {
        console.log(user);
        this.userId = user[0].userId;
      });
    });

    this.defaultDomainSuffix = DEFAULT_DOMAIN_SUFFIX;
    this.msDomainSuffix = MULTISITE_DOMAIN_SUFFIX;

    this.productName$ = combineLatest([
      this.appBrandingService.appBranding$.pipe(
        filter((appBranding) => appBranding !== null),
        map((appBranding) => (appBranding && appBranding.enabled && appBranding.name ? appBranding.name : 'Website')),
      ),
      this.accountsService.editionName$,
    ]).pipe(
      filter(([appName, edition]: [string, string]) => !!appName && !!edition),
      map(([appName, edition]: [string, string]) => appName + ' ' + edition),
    );
    this.accountGroupId = (this.route.snapshot.params as AccountRouteParams).accountGroupId;
    this.initFormFields();

    this.accountsService.editionName$.subscribe((editionName: string) => {
      if (editionName == this.msEditionName) {
        this.multisite = true;
      }
    });

    this.formFields = new FormFields();
  }

  ngOnInit(): void {
    this.domainFormGroup = new UntypedFormGroup({
      defaultDomain: new UntypedFormControl('', [Validators.required]),
    });
  }

  initFormFields(): void {
    this.loading$$.next(true);

    if (this.siteService.hasCachedCreateMessage()) {
      this.formFields.populateFromSiteMigrateMessage(this.siteService.cachedMigrateMessage);
      return;
    }
    this.accountService.fetch(this.accountGroupId);
    this.accountService.result().subscribe((account) => {
      this.formFields.defaultDomain = DefaultDomainUtility.convertToDomainLabel(account.companyName);

      this.domainFormGroup = new UntypedFormGroup({
        defaultDomain: new UntypedFormControl(this.formFields.defaultDomain, [Validators.required]),
      });
    });
    this.accountService.error().subscribe((err) => {
      if (err.statusInt === 404 || err.statusInt === 403) {
        this.navigateByUrl('/app-failure/We%20can%27t%20find%20your%20account.');
      }
    });
    this.form$$.next(this.formFields);
    this.loading$$.next(false);
  }

  onSubmit(): void {
    this.formFields.defaultDomain = this.domainFormGroup.value.defaultDomain;
    this.formFields.clean();
    this.subscriptions.push(
      this.migrateService.setFormFields(this.formFields, this.userId).subscribe(
        (res) => {
          const url = `/migrate/${this.accountGroupId}/migrate/${res.siteId}/job/${res.jobId}/site`;
          this.migrateService.navigateByUrl(url);
          this.matStepper.next();
        },
        (err) => {
          this.migrateService.setCreateDisabled(false);
          this.migrateService.handleError(err);
        },
      ),
    );
  }

  stepperClicked(event: any): void {
    this.templateSelectionStep = event.selectedIndex === 0;
  }

  nextPage(event: boolean): void {
    if (event) {
      this.goToNextStep();
    }
  }

  goToNextStep(): void {
    this.changeDetector.detectChanges();
    this.matStepper.next();
  }

  calculateRemainingChars(currentChars: number): number {
    return this.maxLength - currentChars;
  }

  calculateRemainingSubdomainChars(currentChars: number): number {
    return this.maxSubDomainLength - currentChars;
  }

  navigateByUrl(url: string): void {
    this.router.navigateByUrl(url);
  }
}
