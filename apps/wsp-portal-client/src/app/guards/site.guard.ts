import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable, of } from 'rxjs';
import { SiteRouteParams } from '../core/routeparams';
import { catchError, map } from 'rxjs/operators';
import { Site, SiteApiService } from '../core/site';

@Injectable()
export class SiteIdLoggedInCanActivateChild {
  constructor(private siteService: SiteApiService, private router: Router) {}

  public static buildLoginUrl(siteId: string, nextUrl: string): string {
    let loginUrl = `/vdc-session/transfer/site/${siteId}`;
    if (nextUrl) {
      loginUrl = loginUrl.concat(`?nextUrl=${encodeURIComponent(nextUrl)}`);
    }
    return loginUrl;
  }

  private static isTemplatePath(url: string): boolean {
    return url.indexOf('template') !== -1;
  }

  private static isSitePath(url: string): boolean {
    return url.indexOf('site') !== -1;
  }

  canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean | UrlTree> {
    const siteId = (route.params as SiteRouteParams).siteId;
    const nextUrlParam = 'nextUrl';
    let nextUrl = route.queryParams[nextUrlParam];
    if (nextUrl === '' || nextUrl === undefined || nextUrl === null) {
      nextUrl = state.url;
    }

    return this.siteService.getV2(siteId).pipe(
      map((site: Site) => {
        if (SiteIdLoggedInCanActivateChild.isTemplatePath(nextUrl)) {
          return site.template ? true : this.router.parseUrl(`/site/${siteId}/overview`);
        }
        if (SiteIdLoggedInCanActivateChild.isSitePath(nextUrl)) {
          return site.template ? this.router.parseUrl(`/template/${siteId}/overview`) : true;
        }
      }),
      catchError(() => {
        window.location.href = SiteIdLoggedInCanActivateChild.buildLoginUrl(siteId, nextUrl);
        return of(null);
      }),
    );
  }
}
