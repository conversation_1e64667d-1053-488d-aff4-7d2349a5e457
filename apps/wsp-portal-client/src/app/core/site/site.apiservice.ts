import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { CreateSiteResponseV2JSON, SiteCreateMessageV2, SiteCreateResponseV2 } from './apisite-create';
import { ApiSiteMigrateResponse, ApiSiteMigrateMessage, ApiMigrateSiteResponseJSON } from './apisite-migrate';
import { Job, JobResponseJSON } from '../job/job';
import { SiteCreateMessage } from './site-create';
import {
  ApiSiteMessage,
  ChangePrimaryDomainResponseJSON,
  GetSiteResponseV2JSON,
  LookupAllResponseJSON,
  LookupResponseJSON,
  SiteReplies,
  SiteReply,
} from './apisite';
import { Domains, DomainsResponseJSON } from './domain';
import {
  Site,
  SiteSettingFlagRequest,
  SiteSettingFlagResponse,
  SiteStatus,
  SiteStatusResponseJSON,
  SiteUpdate,
  SiteUpdateResponseJSON,
} from './site';

import { SiteMigrateMessage } from './site-migrate';

const SITE_URL_V1 = '/api/v1/site/';
const SITE_URL_V2 = '/api/v2/site/';
const ACCOUNT_URL_V2 = '/api/v2/account/';
const SITE_URL_V3 = '/api/v3/site/';
const SITE_LOOKUP_V2 = '/api/v2/lookup/site/';

@Injectable()
export class SiteApiService {
  private headers = new HttpHeaders().set('Content-Type', 'application/json');

  constructor(private http: HttpClient) {}

  lookup(accountId: string): Observable<Site> {
    const params = new HttpParams({
      fromObject: {
        account_id: accountId,
      },
    });

    return this.http.get<LookupResponseJSON>(SITE_LOOKUP_V2, { params: params }).pipe(
      map((res) => {
        const reply = SiteReply.fromApi(res.data.sites[0]);
        return new Site(
          reply.siteId,
          reply.uid,
          reply.primaryDomain,
          reply.alternateDomains,
          reply.title,
          reply.tagline,
          reply.accountId,
          reply.partnerId,
          reply.createdDatetime,
          reply.updatedDatetime,
          reply.stagingDomain,
          reply.template,
          reply.templateVersion,
        );
      }),
      catchError((err) => throwError(err)),
    );
  }

  changePrimaryDomain(siteId: string, domain: string): Observable<Site> {
    const url = `${SITE_URL_V1}${siteId}/primary-domain`;
    return this.http.post(url, JSON.stringify({ domain }), { headers: this.headers }).pipe(
      map((res: ChangePrimaryDomainResponseJSON) => ApiSiteMessage.toSite(res.data) as Site),
      catchError((err) => throwError(err)),
    );
  }

  getJobForSite(siteId: string, jobId: string): Observable<Job> {
    const url = `${SITE_URL_V1}${siteId}/job/${jobId}`;
    return this.http.get(url).pipe(
      map((res: JobResponseJSON) => Job.fromApi(res.data)),
      catchError((err) => throwError(err)),
    );
  }

  // V2 APIs

  createV2(site: SiteCreateMessage, userId?: string): Observable<SiteCreateResponseV2> {
    const createMessage = new SiteCreateMessageV2(
      site.accountGroupId,
      site.siteTitle,
      site.siteTagline,
      site.defaultDomain,
      userId,
      site.templateId,
      site.multisite,
    );
    return this.http
      .post(SITE_URL_V2, JSON.stringify(createMessage.toApi()), {
        headers: this.headers,
      })
      .pipe(
        map((res: CreateSiteResponseV2JSON) => SiteCreateResponseV2.fromApi(res.data)),
        catchError((err) => throwError(err)),
      );
  }

  apiMigrate(site: SiteMigrateMessage, userId?: string): Observable<ApiSiteMigrateResponse> {
    const createMessage = new ApiSiteMigrateMessage(site.accountId, site.defaultDomain, userId);
    return this.http
      .post(`${SITE_URL_V2}upgrade`, JSON.stringify(createMessage.toApi()), {
        headers: this.headers,
      })
      .pipe(
        map((res: ApiMigrateSiteResponseJSON) => ApiSiteMigrateResponse.fromApi(res.data)),
        catchError((err) => throwError(err)),
      );
  }

  getV2(siteId: string): Observable<Site> {
    return this.http.get(`${SITE_URL_V2}${siteId}`).pipe(
      map((res: GetSiteResponseV2JSON) => {
        const reply = SiteReply.fromApi(res.data);
        return new Site(
          reply.siteId,
          reply.uid,
          reply.primaryDomain,
          reply.alternateDomains,
          reply.title,
          reply.tagline,
          reply.accountId,
          reply.partnerId,
          reply.createdDatetime,
          reply.updatedDatetime,
          reply.stagingDomain,
          reply.template,
          reply.templateVersion,
        );
      }),
      catchError((err) => throwError(err)),
    );
  }

  lookupAll(partnerId: string, cursor: string = null): Observable<{ sites: Site[]; cursor: string }> {
    let params;
    if (cursor) {
      params = new HttpParams({
        fromObject: {
          partner_id: partnerId,
          cursor_string: cursor,
        },
      });
    } else {
      params = new HttpParams({
        fromObject: {
          partner_id: partnerId,
        },
      });
    }
    return this.http.get(SITE_LOOKUP_V2, { params }).pipe(
      map((res: LookupAllResponseJSON) => {
        const cursorString = res.data.cursor_string;
        const replies = SiteReplies.fromApi(res.data.sites);
        const sites: Site[] = [];
        for (const reply of replies) {
          sites.push(
            new Site(
              reply.siteId,
              reply.uid,
              reply.primaryDomain,
              reply.alternateDomains,
              reply.title,
              reply.tagline,
              reply.accountId,
              reply.partnerId,
              reply.createdDatetime,
              reply.updatedDatetime,
              reply.stagingDomain,
              reply.template,
              reply.templateVersion,
            ),
          );
        }
        return {
          sites: sites,
          cursor: cursorString,
        };
      }),
      catchError((err) => throwError(err)),
    );
  }

  getAssociatedDomainsV2(siteId: string): Observable<Domains> {
    const url = this.buildDomainRequestURL(siteId, 'list');
    return this.http.get(url).pipe(
      map((res: DomainsResponseJSON) => Domains.fromJSON(res.data)),
      catchError((err) => throwError(err)),
    );
  }

  addDomainV3(siteId: string, domains: string[]): Observable<boolean> {
    const url = this.buildDomainRequestV3URL(siteId, 'add');
    const data = JSON.stringify({ domains });
    const postConfig = { headers: this.headers };
    return this.http.post(url, data, postConfig).pipe(
      map(() => true),
      catchError((err) => throwError(err)),
    );
  }

  deleteDomainV2(siteId: string, domain: string): Observable<boolean> {
    const url = this.buildDomainRequestURL(siteId, 'delete');
    const body = JSON.stringify({ domain });
    const options = { headers: this.headers };
    return this.http.post(url, body, options).pipe(
      map(() => true),
      catchError((err) => throwError(err)),
    );
  }

  getStatus(siteId: string): Observable<SiteStatus> {
    const url = `${SITE_URL_V2}${siteId}/status/`;
    return this.http.get(url).pipe(
      map((res: SiteStatusResponseJSON) => SiteStatus.fromJSON(res.data)),
      catchError((err) => throwError(err)),
    );
  }

  getSiteUpdate(siteId: string, staging = false): Observable<SiteUpdate> {
    const url = `${SITE_URL_V2}${siteId}/get-site-update/?staging=${staging}`;
    const options = { headers: this.headers };
    return this.http.get(url, options).pipe(
      map((res: SiteUpdateResponseJSON) => {
        // The PHP version is overwritten to use a constant number "7.4.11" because the PHP version that
        // WordPress container uses (7.3.22) is different from the PHP version that
        // PHP-CGI container uses (7.4.11) to process PHP requests. We didn't upgrade the PHP version that
        // WordPress manager docker container uses because existing alpine packages use 7.3 and
        // installing external dependencies for PHP 7.4 would increase the size of the docker image.
        // Therefore we'd like to hardcode the PHP version for now.
        res.data.update.php_version = '7.4.11';
        return res.data.update;
      }),
      catchError((err) => throwError(err)),
    );
  }

  getSkipHTTPSUpgradeFlag(siteId: string): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/skip-upgrading-http/`;
    const options = { headers: this.headers };
    return this.http.get(url, options).pipe(
      map((res: SiteSettingFlagResponse) => res.data.enabled),
      catchError((err) => throwError(err)),
    );
  }

  setSkipHTTPSUpgradeFlag(siteId: string, skipUpgradeFlag: boolean): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/skip-upgrading-http/`;
    const body = JSON.stringify(new SiteSettingFlagRequest(skipUpgradeFlag));
    const options = { headers: this.headers };
    return this.http.post(url, body, options).pipe(
      map(() => true),
      catchError((err) => throwError(err)),
    );
  }

  getWPLoginExposedFlag(siteId: string): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/wp-login-exposed/`;
    const options = { headers: this.headers };
    return this.http.get(url, options).pipe(map((res: SiteSettingFlagResponse) => res.data.enabled));
  }

  setWPLoginExposedFlag(siteId: string, exposeWPLoginFlag: boolean): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/wp-login-exposed/`;
    const body = JSON.stringify(new SiteSettingFlagRequest(exposeWPLoginFlag));
    const options = { headers: this.headers };
    return this.http.post(url, body, options).pipe(map(() => true));
  }

  private buildDomainRequestURL(siteId: string, action: string): string {
    return `${SITE_URL_V2}${siteId}/domain/${action}/`;
  }

  private buildDomainRequestV3URL(siteId: string, action: string): string {
    return `${SITE_URL_V3}${siteId}/domain/${action}/`;
  }

  isEcommerceEnabled(accountId: string): Observable<boolean> {
    const url = `${ACCOUNT_URL_V2}${accountId}/is-ecommerce-enabled/`;
    const options = { headers: this.headers };
    return this.http.get(url, options).pipe(map((resp: SiteSettingFlagResponse) => resp.data?.enabled));
  }

  getGAState(siteId: string): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/google-analytics-state/`;
    const options = { headers: this.headers };
    return this.http.get(url, options).pipe(map((res: SiteSettingFlagResponse) => res.data.enabled));
  }

  setGAState(siteId: string, gaState: boolean): Observable<boolean> {
    const url = `${SITE_URL_V2}${siteId}/google-analytics-state/`;
    const body = JSON.stringify(new SiteSettingFlagRequest(gaState));
    const options = { headers: this.headers };
    return this.http.post(url, body, options).pipe(map(() => true));
  }
}
