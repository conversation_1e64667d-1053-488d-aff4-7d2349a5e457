import { Component, OnDestroy } from '@angular/core';
import { Event as e, NavigationEnd, Router } from '@angular/router';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Branding } from '@galaxy/marketplace-apps';
import { BrandingV2Interface, BrandingV2Service } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, combineLatest, merge, Observable, of, Subscription } from 'rxjs';
import { catchError, filter, map, switchMap, tap } from 'rxjs/operators';
import { AccountsService } from './accounts.service';
import { AppService } from './app.service';
import { Account, AccountService } from './core/account';
import { AppConfig, AppConfigService } from './core/app-config.service';
import { getCookie } from './core/cookie';
import { FeatureFlagService } from './core/feature-flag.service';
import { UpgradeCTADialogService } from './core/get-pro/pro-upgrade-cta.service';
import { getImageSrc } from './core/image-utilities';
import { GetAppConfigResponse, ProductSettingsService } from './core/product-settings';
import { Site, SiteService } from './core/site';
import { IsPartner, UserService } from './core/user.service';
import { AppBrandingService } from './core/white-label/app-branding.service';
import { SiteIdLoggedInCanActivateChild } from './guards';
import { MULTISITE_EDITION_NAME } from '../globals';

declare const deployment: string; // Stamped down by vStatic in the index.html

export interface PartnerConfig {
  logo?: string;
  name?: string;
}

export interface ProductNavItem {
  icon?: string;
  label: string;
  url: string;
  children?: ProductNavItem[];
  isIconSvg?: boolean;
  showUpgradeChip?: boolean;
}
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: false,
})
export class AppComponent implements OnDestroy {
  isExpress$: Observable<boolean>;
  productNameWithCurrentEdition$: Observable<string> = of('');
  upgradedText$: Observable<string>;
  isTemplate$: Observable<boolean>;
  logoUrl$: Observable<string>;
  isPartner$: Observable<boolean>;
  editionName$: Observable<string>;

  currentUrl: string;
  sitePath: boolean;
  templatePath: boolean;
  manageTemplatePath: boolean;
  siteCreatePath: boolean;
  exceptionPath: boolean;
  sidebarPrimaryActionIcon: string;
  isNavBarEnabled$: Observable<boolean>;
  private showAtlasFeatureFlagId = 'business_navigation';
  msEditionName: string = MULTISITE_EDITION_NAME;
  productLogoName = 'website-pro.svg';

  supportedLanguages = [
    { display: 'Čeština', locale: 'cs' },
    { display: 'Deutsch', locale: 'de' },
    { display: 'English', locale: 'en' },
    { display: 'Español', locale: 'es-419' },
    { display: 'Français', locale: 'fr-fr' },
    { display: 'Français canadien', locale: 'fr-ca' },
    { display: 'Nederlands', locale: 'nl' },
  ];

  navItems$$: BehaviorSubject<ProductNavItem[]> = new BehaviorSubject<ProductNavItem[]>(null);
  navItems$: Observable<ProductNavItem[]> = this.navItems$$.asObservable();

  sidebarPrimaryActionButton$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  sidebarPrimaryActionButton$: Observable<string> = this.sidebarPrimaryActionButton$$.asObservable();

  partnerConfig$$: BehaviorSubject<PartnerConfig> = new BehaviorSubject<PartnerConfig>(undefined);
  partnerConfig$: Observable<PartnerConfig> = this.partnerConfig$$.asObservable();

  config$: Observable<AppConfig>;
  appBranding$: Observable<Branding>;
  productSettings$: Observable<GetAppConfigResponse>;

  private navigation$ = new Observable<[e, IsPartner, BrandingV2Interface, GetAppConfigResponse]>(undefined);
  private siteId: string;
  private partnerId: string;
  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private env: EnvironmentService,
    private snowplowTracker: ProductAnalyticsService,
    private translate: TranslateService,
    private account: AccountService,
    private siteService: SiteService,
    private accountsService: AccountsService,
    private proCTAUpgrade: UpgradeCTADialogService,
    private brandingService: BrandingV2Service,
    private appConfigService: AppConfigService,
    private userService: UserService,
    private appBrandingService: AppBrandingService,
    private featureFlagService: FeatureFlagService,
    private productSettingsService: ProductSettingsService,
  ) {
    this.isPartner$ = this.userService.isPartner$.pipe(map((isPartner) => isPartner === IsPartner.Yes));

    this.isTemplate$ = this.siteService.get().pipe(map((site: Site) => site.template));
    this.config$ = this.appConfigService.config$;
    this.appBranding$ = this.appBrandingService.appBranding$;
    translate.setDefaultLang('en');
    // icon will be override in css .sidebar-primary-action-button
    this.sidebarPrimaryActionIcon = '';
    this.subscriptions.push(
      merge(this.account.result(), this.siteService.get().pipe(tap((site: Site) => (this.siteId = site.siteId))))
        .pipe(map((res: Account | Site) => res.partnerId))
        .subscribe((partnerId: string) => {
          this.partnerId = partnerId;
        }),
    );
    this.productSettings$ = merge(
      this.account.result(),
      this.siteService.get().pipe(tap((site: Site) => (this.siteId = site.siteId))),
    ).pipe(
      map((res: Account | Site) => res.partnerId),
      switchMap((partnerId: string) => this.productSettingsService.getAppConfig(partnerId)),
      catchError(() => {
        return of(
          GetAppConfigResponse.fromProto({
            config: {
              showAdvancedFeatures: true,
              showIncludedTemplates: true,
            },
          }),
        );
      }),
    );

    const productName$ = this.appBranding$.pipe(
      filter((appBranding) => appBranding !== null),
      map((appBranding) => (appBranding && appBranding.enabled && appBranding.name ? appBranding.name : 'Website')),
    );
    const iconUrl$ = this.appBranding$.pipe(
      filter((appBranding) => appBranding !== null),
      map((appBranding) =>
        appBranding && appBranding.enabled && appBranding.iconUrl
          ? appBranding.iconUrl
          : getImageSrc(this.productLogoName),
      ),
    );
    this.editionName$ = this.accountsService.editionName$;
    this.productNameWithCurrentEdition$ = combineLatest([productName$, this.accountsService.editionName$]).pipe(
      filter(([productName, edition]: [string, string]) => !!productName && !!edition),
      map(([productName, edition]: [string, string]) => {
        if (edition == this.msEditionName) {
          this.productLogoName = 'website-multisite.svg';
        }
        return productName + ' ' + edition;
      }),
      tap((name) => (document.title = name)),
    );
    this.upgradedText$ = productName$.pipe(
      map((productName) => this.translate.instant('UPGRADE_CTA.GET_PRO_INFO') + ' ' + productName + ' Pro'),
    );
    this.logoUrl$ = iconUrl$.pipe(
      map((iconUrl) => {
        return iconUrl;
      }),
    );
    this.isExpress$ = this.accountsService.isExpress$;
    const branding$ = this.config$.pipe(
      switchMap((config: AppConfig) => {
        if (!config) {
          return of({});
        }
        const partner_id = config && config.partner_id ? config.partner_id : getCookie('partner_id');
        const market_id = config && config.market_id ? config.market_id : '';
        return this.brandingService.getBranding(partner_id, market_id);
      }),
      catchError(() => of({})),
    );
    this.navigation$ = combineLatest([
      this.router.events.pipe(filter((url: e) => url instanceof NavigationEnd)),
      this.userService.isPartner$,
      branding$,
      this.productSettings$,
    ]);

    // Prevents capturing events on the local environment
    if (typeof deployment !== 'undefined') {
      this.snowplowTracker.initialize({
        environment: this.env.getEnvironment(),
        projectUUID: '16713a44-c7f7-46f4-8274-d0dc21ce1340',
        postHogID: '6_zD-DiknPJylKXABk-TgWgiNZgC7kX7QrcLtwKLcpc',
        projectName: 'wsp-portal-client',
        partner: {
          pid: getCookie('partner_id'),
        },
        businessID: getCookie('account_id'),
      });
    }

    this.subscriptions.push(
      this.navigation$.subscribe(([url, isPartner, branding, appConfig]) => {
        const urlSplit = (url as NavigationEnd).urlAfterRedirects.split('/');
        this.setPaths(urlSplit);
        this.setPrimaryAction();
        this.setLiveTabs(isPartner, url as NavigationEnd, appConfig);
        setTimeout(() => {
          this.isExpress$.subscribe((isExpress) => {
            if (!isExpress) {
              this.setZendeskWidget(isPartner);
            }
          });
        }, 2000);
        this.checkIsPartner(isPartner);
        this.setPartnerSettings(branding);
        this.setShowAtlasNavbar();
      }),
    );
  }

  /**
   * Adds the Zendesk widget to the DOM based on whether the current user has a persona type of Partner
   * @param isPartner: an IsPartner object representing whether the current user is a Partner
   */
  setZendeskWidget(isPartner: IsPartner): void {
    if (isPartner === IsPartner.Yes) {
      // the following is the script tag that contains the styling of the zendesk widget
      const widgetStyleScript = document.createElement('script');
      widgetStyleScript.type = 'text/javascript';
      widgetStyleScript.async = true;
      widgetStyleScript.innerHTML = `
  window.zESettings = {
    webWidget: {
      launcher: {
        chatLabel: {
          '*': 'Live chat'
        }
      },
      color: {
        theme: '#1e88e5',
        launcher: '#1e88e5',
        launcherText: '#FFFFFF',
        header: '#1e88e5',
        buttonText: '#FFFFFF',
      },
      chat: {
        departments: {
          enabled: [],
        },
      },
    },
  };`;
      const head = document.getElementsByTagName('head')[0];
      head.appendChild(widgetStyleScript);

      // the following is the actual zendesk script, which is equivalent to adding
      // <script id="ze-snippet"
      //         src="https://static.zdassets.com/ekr/snippet.js?key=c59ee39b-e0e2-493d-b444-c076eb97953f"></script>
      // to the DOM
      const zendeskScript = document.createElement('script');
      zendeskScript.type = 'text/javascript';
      zendeskScript.async = true;
      zendeskScript.id = 'ze-snippet';
      zendeskScript.src = 'https://static.zdassets.com/ekr/snippet.js?key=c59ee39b-e0e2-493d-b444-c076eb97953f';
      head.appendChild(zendeskScript);
    }
  }
  setPartnerSettings(branding: BrandingV2Interface): void {
    this.partnerConfig$$.next({ logo: branding.logoUrl, name: branding.name });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  setPrimaryAction(): void {
    if (this.sitePath || this.templatePath) {
      this.sidebarPrimaryActionButton$$.next(
        this.translate.instant('PAGES.OVERVIEW.MANAGE_SITE_MODAL.WORDPRESS_DASHBOARD'),
      );
    }
  }

  setPaths(urlSplit: string[]): void {
    this.sitePath = AppService.isSitePath(urlSplit);
    this.siteCreatePath = AppService.siteCreatePath(urlSplit);
    this.templatePath = AppService.templatePath(urlSplit);
    this.manageTemplatePath = AppService.manageTemplatePath(urlSplit);
    this.exceptionPath = AppService.exceptionPath(urlSplit);
  }

  setLiveTabs(partner: IsPartner, url: NavigationEnd, appConfig: GetAppConfigResponse): void {
    const showAdvancedFeatures = appConfig && appConfig.config && !!appConfig.config.showAdvancedFeatures;
    this.currentUrl = url.urlAfterRedirects;
    if (this.exceptionPath) {
      this.navItems$$.next([]);
    } else if (this.siteCreatePath) {
      this.navItems$$.next(this.siteCreateTabs(partner));
    } else if (this.sitePath || this.templatePath || this.manageTemplatePath) {
      this.subscriptions.push(
        combineLatest([this.siteService.get(), this.accountsService.isExpress$])
          .pipe(
            filter(([site, express]: [Site, boolean]) => !!site && express !== undefined),
            map(([site, express]: [Site, boolean]) => {
              this.partnerId = site.partnerId;
              this.navItems$$.next(
                site.template ? this.templateTabs(partner) : this.siteTabs(partner, express, showAdvancedFeatures),
              );
            }),
          )
          .subscribe(),
      );
    } else {
      this.navItems$$.next([]);
    }
  }

  checkIsPartner(partner: IsPartner): void {
    if (partner === IsPartner.Unauthorized && !!this.siteId) {
      const nextUrl = window.location.href;
      window.location.href = SiteIdLoggedInCanActivateChild.buildLoginUrl(this.siteId, nextUrl);
    }
  }

  siteCreateTabs(partner: IsPartner): ProductNavItem[] {
    return partner === IsPartner.Yes
      ? [
          {
            label: 'NAVIGATION.TABS.MANAGE_TEMPLATES',
            url: `/${this.partnerId}/templates/manage`,
            icon: 'devices',
            showUpgradeChip: false,
          },
        ]
      : [];
  }

  templateTabs(partner: IsPartner): ProductNavItem[] {
    const tabs = [
      {
        label: 'NAVIGATION.TABS.OVERVIEW',
        url: `/template/${this.siteId}/overview`,
        icon: 'view_compact',
        showUpgradeChip: false,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.BACKUPS',
        url: `/template/${this.siteId}/backup/management`,
        icon: 'backup',
        showUpgradeChip: false,
        children: null,
      },
    ];
    if (partner === IsPartner.Yes) {
      tabs.push({
        label: 'NAVIGATION.TABS.ADVANCED_TOOLS',
        url: `template/${this.siteId}/advanced`,
        icon: 'build',
        showUpgradeChip: false,
        children: null,
      });
    }
    return tabs;
  }

  siteTabs(partner: IsPartner, express: boolean, showAdvancedFeatures: boolean): ProductNavItem[] {
    this.editionName$ = this.accountsService.editionName$;
    let hideEmailsTab = false;

    this.editionName$.subscribe((editionName) => {
      if (editionName === this.msEditionName) {
        hideEmailsTab = true;
      }
    });

    const tabs = [
      {
        label: 'NAVIGATION.TABS.OVERVIEW',
        url: `/site/${this.siteId}/overview`,
        icon: 'view_compact',
        showUpgradeChip: false,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.ANALYTICS',
        url: `/site/${this.siteId}/analytics`,
        icon: 'trending_up',
        showUpgradeChip: false,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.DOMAINS',
        url: `/site/${this.siteId}/domain/management`,
        icon: 'domain_verification',
        showUpgradeChip: express,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.IMPORT',
        url: `/site/${this.siteId}/import`,
        icon: 'import_export',
        showUpgradeChip: false,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.BACKUPS',
        url: `/site/${this.siteId}/backup/management`,
        icon: 'backup',
        showUpgradeChip: false,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.STAGING',
        url: `/site/${this.siteId}/staging/management`,
        icon: 'layers',
        showUpgradeChip: express,
        children: null,
      },
      {
        label: 'NAVIGATION.TABS.EMAILS',
        url: `/site/${this.siteId}/emails`,
        icon: 'email',
        showUpgradeChip: false,
        children: null,
      },
    ].filter((tab) => {
      // Exclude the "Emails" tab if hideEmailsTab is true
      if (hideEmailsTab && tab.label === 'NAVIGATION.TABS.EMAILS') {
        return false;
      }
      return true;
    });
    if (partner === IsPartner.Yes) {
      tabs.push(
        {
          label: 'NAVIGATION.TABS.MANAGE_TEMPLATES',
          url: `site/${this.siteId}/templates/${this.partnerId}/manage`,
          icon: 'devices',
          showUpgradeChip: false,
          children: null,
        },
        {
          label: 'NAVIGATION.TABS.ADVANCED_TOOLS',
          url: `site/${this.siteId}/advanced`,
          icon: 'build',
          showUpgradeChip: false,
          children: null,
        },
      );
    } else if (showAdvancedFeatures) {
      tabs.push({
        label: 'NAVIGATION.TABS.ADVANCED_TOOLS',
        url: `site/${this.siteId}/advanced`,
        icon: 'build',
        showUpgradeChip: false,
        children: null,
      });
    }
    return tabs;
  }

  /**
   * Tracks that a user has clicked on the Pro upgrade in Website
   */
  upgradeClicked(): void {
    this.proCTAUpgrade
      .openUpgradeModel()
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.proCTAUpgrade.businessCenterUpgrade();
        }
      });
  }

  /**
   * Tracks whether the user has clicked on the WordPress dashboard button and redirects the user accordingly
   */
  wordPressDashboardClicked(): void {
    window.open(`/redirect/wp-admin/${this.siteId}/`, '_blank');
  }

  adminDashboardClicked(): void {
    window.open(
      `https://wsp-admin-center-${this.getEnv()}.apigateway.co/partner/${this.partnerId}/dashboard`,
      '_blank',
    );
  }

  getEnv(): string {
    if (this.env.getEnvironment() == Environment.PROD) {
      return 'prod';
    } else {
      return 'demo';
    }
  }
  /*
   *  Hide or Show the atlas navbar based on partner settings
   */
  setShowAtlasNavbar(): void {
    if (this.partnerId) {
      this.isNavBarEnabled$ = this.featureFlagService
        .isFeatureEnabled(this.partnerId, this.showAtlasFeatureFlagId, '')
        .pipe((r) => {
          return r;
        });
    }
  }
}
