export const environment: string = globalThis.environment || 'prod';
export const deployment: string = globalThis.deployment || null;
export const devServer: boolean = globalThis.devServer || false;
export const partnerId: string = globalThis.partnerId?.toUpperCase() || null;
export const prod = environment === 'prod' || environment === 'production';
export const DEFAULT_DOMAIN_SUFFIX = prod ? '.websitepro.hosting' : '.websitepro-demo.com';
export const CNAME_SUFFIX = prod ? '.websiteprohosting.com' : '.websitepro-demo.com';
export const SFTP_DOMAIN = `sftp${DEFAULT_DOMAIN_SUFFIX}`;
export const REQUIRED_CNAME = `host${CNAME_SUFFIX}`;
export const WSP_SITE_EXTERNAL_IP = prod ? '*************' : '**************';
export const PLACEHOLDER_TEMPLATE_ID = prod
  ? '90bbae4990aa146bdb74cf1bd1ec37ede99c5d07d1ebbf993dc6c1bd1ef5ffbc'
  : '00cc8e41f74504a2c483726ccb5894855d74eee34408273ea89988b0b27d1558';
export const VDC_APP_ID = prod ? 'MP-ee4ea04e553a4b1780caf7aad7be07cd' : 'MP-9cc9f21f0a234a46ad78087fc09f16bc';
export const MULTISITE_DOMAIN_SUFFIX = prod ? '.wp-premium-hosting.com' : '.wp-premium-hosting-demo.com';
export const MULTISITE_EDITION_NAME = 'Pro Multisite';
export const WSP_MS_SITE_EXTERNAL_IP = prod ? '**************' : '**************';
export const MS_CNAME = `host${MULTISITE_DOMAIN_SUFFIX}`;
export const MS_SFTP_DOMAIN = `sftp${MULTISITE_DOMAIN_SUFFIX}`;
