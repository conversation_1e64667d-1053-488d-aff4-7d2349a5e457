import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AuthInterceptor } from '@galaxy/core';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyI18NModule } from '@vendasta/galaxy/i18n';
import { GalaxyNavModule } from '@vendasta/galaxy/nav';
import { GalaxyDefaultProviderOverrides } from '@vendasta/galaxy/provider-default-overrides';
import { AppComponent } from './app.component';
import { AppRoutingModule } from './app.routing';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { PartnerIdService } from './partner-id.service';
import { PartnerServiceInterfaceToken } from '@galaxy/partner';
import { PartnerService } from '../../../shell-automations-client/src/app/partner.service';
import { CachedRouteReuseStrategy } from './app.routing.strategy';
import { RouteReuseStrategy } from '@angular/router';
import { CrmCoreModule } from '@galaxy/crm/static';
import { CrmInjectionToken } from './crm/providers';

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    GalaxyI18NModule,
    TranslateModule,
    LexiconModule.forRoot(),
    AppRoutingModule,
    GalaxyNavModule,
    MatIconModule,
    MatButtonModule,
    MatSnackBarModule,
    MatDialogModule,
    PageNotFoundComponent,
    CrmCoreModule.forRoot(CrmInjectionToken),
  ],
  providers: [
    ...GalaxyDefaultProviderOverrides,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: 'PARTNER_ID',
      useFactory: (partnerIdService: PartnerIdService) => {
        return partnerIdService.partnerId$;
      },
      deps: [PartnerIdService],
    },
    {
      provide: PartnerServiceInterfaceToken,
      useClass: PartnerService,
    },
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: RouteReuseStrategy,
      useClass: CachedRouteReuseStrategy,
    },
  ],
})
export class AppModule {}
