import { inject, InjectionToken } from '@angular/core';
import { Router } from '@angular/router';
import {
  CrmFulfillmentProjectsAssociationCardComponent,
  ExportActionsService,
  ExportSelectAllRowAction,
} from '@galaxy/crm/dynamic';
import {
  CompanyAssociationPanelComponent,
  CompanyService,
  CompanyToContactAssociationsCardComponent,
  CompanyToOpportunityAssociationsPanelComponent,
  ContactAssociationPanelComponent,
  ContactCampaignsCardComponent,
  CrmDependencies,
  CrmObjectDisplayService,
  CrmPipelineService,
  CRMRowObject,
  CustomObjectToCompanyAssociationsPanelComponent,
  CustomObjectToContactAssociationsPanelComponent,
  CustomObjectToOpportunityAssociationsPanelComponent,
  FormDefaultInputValue,
  OpportunityAssociationPanelComponent,
  ParentChildCompanyAssociationsCardComponent,
  PlatformExtensionFieldIds,
  ProfileCard,
  StandardIds,
  SystemFieldIds,
} from '@galaxy/crm/static';
import { combineLatest, firstValueFrom, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { PartnerIdService } from '../partner-id.service';
import { UserIdService } from '../user-id.service';
import { CRMUserService } from '../user.service';
import { AutomationsComponent } from './mock-company-panel/automations/automations.component';
import { PartnerInfoComponent } from './mock-company-panel/partner-info/partner-info.component';
import { OpportunityService } from './services/opportunity.service';
import { SalespersonFilterChipComponent, SalespersonFilterInputComponent } from '@galaxy/crm/integrations/salesperson';
import { CrmObjectInterface, GetMultiCrmObjectResponse } from '@vendasta/crm';
import {
  CrmUserColumnCellComponent,
  CrmUserFilterChipComponent,
  CrmUserFilterInputComponent,
  CrmUserInputComponent,
} from '@galaxy/crm/components/user';
import { CRMAccessService } from '../access.service';
import { addMonths } from 'date-fns';
import { CacheFactory, CacheInjectionOptions } from '@galaxy/crm/components/cache';
import {
  CrmPipelineColumnCellComponent,
  CrmPipelineFilterChipComponent,
  CrmPipelineFilterInputComponent,
  CrmStageColumnCellComponent,
} from '@galaxy/crm/components/pipeline';
import { FeatureFlagService } from '@galaxy/partner';

interface FulfillmentActionsDependencies {
  partnerIdService: PartnerIdService;
  companyService: CompanyService;
  router: Router;
}

function buildCompanyProfileCards$(partnerId$: Observable<string>): Observable<ProfileCard[]> {
  return partnerId$.pipe(
    map((partnerId) => {
      const profileCards = [
        { component: CompanyToContactAssociationsCardComponent },
        { component: CompanyToOpportunityAssociationsPanelComponent },
        { component: ParentChildCompanyAssociationsCardComponent },
        { component: CrmFulfillmentProjectsAssociationCardComponent },
        { component: AutomationsComponent },
      ] as ProfileCard[];
      if (partnerId === 'VMF') {
        profileCards.unshift({ component: PartnerInfoComponent });
      }
      return profileCards;
    }),
  );
}

function buildContactProfileCards$(): Observable<ProfileCard[]> {
  return of([
    { component: CompanyAssociationPanelComponent },
    { component: OpportunityAssociationPanelComponent },
    { component: ContactCampaignsCardComponent },
  ] as ProfileCard[]);
}

function buildOpportunityProfileCards$(): Observable<ProfileCard[]> {
  return of([
    { component: ContactAssociationPanelComponent },
    { component: CompanyAssociationPanelComponent },
  ] as ProfileCard[]);
}

function buildCustomObjectProfileCards$(): Observable<ProfileCard[]> {
  return of([
    { component: CustomObjectToContactAssociationsPanelComponent },
    { component: CustomObjectToCompanyAssociationsPanelComponent },
    { component: CustomObjectToOpportunityAssociationsPanelComponent },
  ] as ProfileCard[]);
}

function navigateToFulfillmentProjects(dep: FulfillmentActionsDependencies): (row: CRMRowObject) => void {
  return (row: CRMRowObject): void => {
    const pid$ = dep.partnerIdService.partnerId$;
    const crmObject$ = pid$.pipe(
      switchMap((pid) => dep.companyService.getMultiObject({ namespace: pid, crmObjectIds: [row.objectId] })),
      map((companies) => companies?.crmObjects?.[0]),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const agid$ = crmObject$.pipe(
      map(
        (crmObject) =>
          crmObject?.fields?.find((field) => field.fieldId === PlatformExtensionFieldIds.AccountGroupID)?.stringValue ||
          '',
      ),
    );
    const name$ = crmObject$.pipe(
      map(
        (crmObject) => crmObject?.fields?.find((field) => field.fieldId === StandardIds.CompanyName)?.stringValue || '',
      ),
    );
    const companyInfo = firstValueFrom(combineLatest([agid$, name$]));
    companyInfo.then(([accountGroupId, name]) => {
      const url = 'task-manager/reporting';
      const queryParams = { 'project-report': JSON.stringify({ account: { accountGroupId, name } }) };
      dep.router.navigate([url], { queryParams });
    });
  };
}

function getAccountId() {
  return (crmObject: Observable<GetMultiCrmObjectResponse>) =>
    crmObject.pipe(
      map((response: GetMultiCrmObjectResponse) => {
        let field;
        if (response.crmObjects[0].fields) {
          field = response.crmObjects[0].fields.find(
            (field) => field.fieldId === PlatformExtensionFieldIds.AccountGroupID,
          );
        }
        return field?.stringValue;
      }),
    );
}

export const CrmInjectionToken = new InjectionToken<CrmDependencies>('dependencies given to crm library', {
  factory: () => {
    const crmUserServiceCacheOptions: CacheInjectionOptions<CRMUserService> = {
      methods: [
        {
          method: 'getMultiUsers',
          strategy: { maxAge: 600000, autoCleanup: true },
          multiKey: { paramIndex: 0 },
        },
      ],
    };
    const userService = CacheFactory.inject(CRMUserService, crmUserServiceCacheOptions);

    const crmPipelineServiceCacheOptions: CacheInjectionOptions<CrmPipelineService> = {
      methods: [
        {
          method: 'getMultiPipeline$',
          strategy: { maxAge: 600000, autoCleanup: true },
          multiKey: { paramIndex: 0 },
        },
        {
          method: 'getPipeline$',
          strategy: { maxAge: 600000, autoCleanup: true },
        },
      ],
    };
    const crmPipelineService = CacheFactory.inject(CrmPipelineService, crmPipelineServiceCacheOptions);

    const partnerIdService = inject(PartnerIdService);
    const userIdService = inject(UserIdService);
    const opportunityService = inject(OpportunityService);
    const accessService = inject(CRMAccessService);
    const router = inject(Router);
    const companyService = inject(CompanyService);
    const exportActionsService = inject(ExportActionsService);
    const crmObjectDisplayService = inject(CrmObjectDisplayService);
    const featureFlagService = inject(FeatureFlagService);

    return {
      appID: 'shell-client',
      routePrefix$: of('/crm'),
      namespace$: partnerIdService.partnerId$,
      parentNamespace$: of(''),
      viewProfileFlag$: of(true),
      hasCrmAssociationModalFeatureFlag$: of(true),
      customObjectFeatureFlag: 'pcc_crm_custom_objects',
      hasTimelineActivitiesFeatureFlag$: partnerIdService.partnerId$.pipe(
        switchMap((namespace) => {
          return featureFlagService.batchGetStatus(namespace, '', ['crm_timeline_activities']);
        }),
        map((featureFlagStatus) => featureFlagStatus['crm_timeline_activities'] ?? false),
        shareReplay({ refCount: true, bufferSize: 1 }),
      ),
      currentUserId$: userIdService.userId$,
      contact: {
        profileCards$: buildContactProfileCards$(),
        formDefaultOnCreateInputValues$: of([
          { fieldId: StandardIds.ContactSourceName, value: 'CRM UI' },
          { fieldId: StandardIds.ContactRecordSourceDrill1, value: window.location.href },
        ] as FormDefaultInputValue[]),
        createAssociationFields: {
          objectTypes: ['Opportunity', 'Company'],
          required: false,
        },
        selectAllTableActions$: partnerIdService.partnerId$.pipe(
          map((partnerId) => {
            return [ExportSelectAllRowAction(partnerId, exportActionsService, 'Contact', 'ACTIONS.CRM.EXPORT.LABEL')];
          }),
        ),
      },
      company: {
        profileCards$: buildCompanyProfileCards$(partnerIdService.partnerId$),
        createAssociationFields: {
          objectTypes: ['Opportunity', 'Contact'],
          required: false,
        },
        formCustomInputs: [],
        filterInputOverrides$: of([
          {
            title: 'Salesperson',
            fieldId: StandardIds.CompanyPrimarySalespersonID,
            filterInput: SalespersonFilterInputComponent,
            filterChip: SalespersonFilterChipComponent,
          },
        ]),
        formDefaultOnCreateInputValues$: partnerIdService.partnerId$.pipe(
          map(() => {
            const defaults = [
              { fieldId: StandardIds.CompanySourceName, value: 'CRM UI' },
              { fieldId: StandardIds.CompanyRecordSourceDrill1, value: router.url },
              { fieldId: StandardIds.CompanyOriginalSource, value: 'CRM UI' },
              { fieldId: StandardIds.CompanyOriginalSourceDrill1, value: router.url },
            ];
            return defaults;
          }),
        ),
        selectAllTableActions$: partnerIdService.partnerId$.pipe(
          map((partnerId) => {
            return [ExportSelectAllRowAction(partnerId, exportActionsService, 'Company', 'ACTIONS.CRM.EXPORT.LABEL')];
          }),
        ),
        syncIds$: of([
          StandardIds.CompanyName,
          StandardIds.CompanyPhoneNumber,
          StandardIds.CompanyPrimaryAddressLine1,
          StandardIds.CompanyPrimaryAddressLine2,
          StandardIds.CompanyPrimaryAddressCity,
          StandardIds.CompanyPrimaryAddressState,
          StandardIds.CompanyPrimaryAddressPostalCode,
          StandardIds.CompanyPrimaryAddressCountry,
          StandardIds.CompanyTags,
          StandardIds.CompanyPrimarySalespersonID,
          StandardIds.CompanyAdditionalSalespersonIDs,
          StandardIds.CompanyLinkedInURL,
          StandardIds.CompanyFacebookURL,
          StandardIds.CompanyXURL,
          StandardIds.CompanyInstagramURL,
          StandardIds.CompanyPinterestURL,
          StandardIds.CompanyCategoryIDs,
        ]),
        accountId$: getAccountId(),
      },
      opportunity: {
        formCustomInputs: [
          {
            fieldId: SystemFieldIds.OpportunityOwnerID,
            component: CrmUserInputComponent,
          },
        ],
        tableCustomCells: [
          {
            fieldIds: [SystemFieldIds.OpportunityOwnerID],
            columnDefinition: {
              id: SystemFieldIds.OpportunityOwnerID,
            },
            customCellComponent: CrmUserColumnCellComponent,
          },
          {
            fieldIds: [StandardIds.OpportunityPipelineID],
            columnDefinition: {
              id: StandardIds.OpportunityPipelineID,
            },
            customCellComponent: CrmPipelineColumnCellComponent,
          },
          {
            fieldIds: [StandardIds.OpportunityCalculatedStageID, StandardIds.OpportunityPipelineID],
            columnDefinition: {
              id: StandardIds.OpportunityCalculatedStageID,
            },
            customCellComponent: CrmStageColumnCellComponent,
          },
        ],
        filterInputOverrides$: of([
          {
            title: 'Owner',
            fieldId: SystemFieldIds.OpportunityOwnerID,
            filterInput: CrmUserFilterInputComponent,
            filterChip: CrmUserFilterChipComponent,
          },
          {
            title: 'Pipeline',
            fieldId: StandardIds.OpportunityPipelineID,
            filterInput: CrmPipelineFilterInputComponent,
            filterChip: CrmPipelineFilterChipComponent,
          },
        ]),
        profileCards$: buildOpportunityProfileCards$(),
        createAssociationFields: {
          objectTypes: ['Contact', 'Company'],
          required: true,
        },
        formDefaultOnCreateInputValues$: userIdService.userId$.pipe(
          map((userId) => [
            {
              fieldId: StandardIds.OpportunityAmount,
              value: {
                currencyCode: 'CAD',
                amount: 0,
              },
            },
            {
              fieldId: SystemFieldIds.OpportunityOwnerID,
              value: userId,
            },
            {
              fieldId: StandardIds.OpportunityExpectedCloseDate,
              value: addMonths(new Date(), 1),
            },
            {
              fieldId: StandardIds.OpportunityStatus,
              value: 'Open',
            },
            {
              fieldId: StandardIds.OpportunitySourceName,
              value: 'CRM UI',
            },
          ]),
        ),
      },
      customObject: {
        baseColumnIds: [StandardIds.CustomObjectName],
        profileCards$: buildCustomObjectProfileCards$(),
        getDisplayName: (crmObject: CrmObjectInterface) => {
          return crmObjectDisplayService.customObjectDisplayName(crmObject);
        },
        createAssociationFields: {
          objectTypes: ['Contact', 'Company', 'Opportunity', 'CustomObject'],
          required: false,
        },
      },
      accessBusinessApp: false,
      showBackToUsersButton: false,
      showBackToAccountsButton: false,
      services: {
        userService: userService,
        opportunityService: opportunityService,
        accessService: accessService,
        pipelineService: crmPipelineService,
      },
      customActions: {
        navigateToFulfillmentProjects: navigateToFulfillmentProjects({
          partnerIdService,
          companyService,
          router,
        }),
      },
      hasInboxAccess$: of(true),
      canShowInboxForContact$: of(false), // if true also requires implementing openContactConversation
      hasAccessToFeature$: (_: string) => {
        return of(true);
      },
    } as CrmDependencies;
  },
});
