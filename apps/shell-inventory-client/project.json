{"name": "shell-inventory-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/shell-inventory-client/src", "tags": ["scope:client"], "targets": {"build": {"executor": "@nx/angular:browser-esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/shell-inventory-client", "index": "apps/shell-inventory-client/src/index.html", "main": "apps/shell-inventory-client/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/shell-inventory-client/tsconfig.app.json", "assets": ["apps/shell-inventory-client/src/favicon.ico", "apps/shell-inventory-client/src/assets"], "styles": ["apps/shell-inventory-client/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["libs/galaxy/styles/", "libs/uikit/src/lib/"]}, "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "scripts": []}, "configurations": {"devServer": {"indexHtmlTransformer": "index-html-transform.ts"}, "production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"options": {"buildTarget": "shell-inventory-client:build:devServer", "proxyConfig": "apps/shell-inventory-client/proxy.conf.js", "port": 4200, "publicHost": "https://localhost:4200"}, "configurations": {"fast": {"hmr": true}}, "defaultConfiguration": "production"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "shell-inventory-client:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/shell-inventory-client/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "shell-inventory-client:build"}}}}