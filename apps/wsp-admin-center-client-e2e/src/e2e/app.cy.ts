import { getGreeting } from '../support/app.po';

describe('wsp-admin-center-client', () => {
  beforeEach(() => cy.visit('/'));

  it('should display welcome message', () => {
    // Custom command example, see `../support/commands.ts` file
    cy.login('<EMAIL>', 'myPassword');

    // Function helper example, see `../support/app.po.ts` file
    getGreeting().contains('Website Pro Admin');
  });
});
