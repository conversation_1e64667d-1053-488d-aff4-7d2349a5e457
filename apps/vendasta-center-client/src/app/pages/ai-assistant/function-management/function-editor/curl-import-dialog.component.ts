import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { HttpMethod, ParameterType } from './function-editor.component';
import { parseArgsStringToArgv } from 'string-argv';

export interface CurlImportObject {
  name: string;
  type?: ParameterType;
  properties?: CurlImportObject[];
  item?: CurlImportArrayItem;
}

export interface CurlImportArrayItem {
  type?: ParameterType;
  properties?: CurlImportObject[];
  item?: CurlImportArrayItem;
}

export interface CurlImportResult {
  methodType: HttpMethod;
  url: string;
  headers: { key: string; value: string }[];
  bodyParams: CurlImportObject[];
  queryParams: { name: string }[];
}

@Component({
  selector: 'app-curl-import-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    GalaxyFormFieldModule,
    GalaxySnackbarModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './curl-import-dialog.component.html',
  styleUrls: ['./curl-import-dialog.component.scss'],
})
export class CurlImportDialogComponent {
  private readonly dialogRef = inject(MatDialogRef<CurlImportDialogComponent>);
  private readonly snackbarService = inject(SnackbarService);

  curlCommand = new FormControl('');

  close(): void {
    this.dialogRef.close();
  }

  import(): void {
    const curlString = this.curlCommand.value;
    if (!curlString) {
      this.snackbarService.openErrorSnack('Please enter a cURL command');
      return;
    }

    try {
      const result = this.parseCurlCommand(curlString);
      this.dialogRef.close(result);
    } catch (error) {
      console.error('Error parsing cURL command:', error);
      this.snackbarService.openErrorSnack('Failed to parse cURL command');
    }
  }

  parseCurlCommand(curlString: string): CurlImportResult {
    const result: CurlImportResult = {
      methodType: HttpMethod.POST,
      url: '',
      headers: [],
      bodyParams: [],
      queryParams: [],
    };

    try {
      if (!curlString.startsWith('curl ')) {
        throw new Error('Invalid cURL command. Please enter a valid cURL command.');
      }

      const args = parseArgsStringToArgv(curlString);
      const startIndex = args[0].toLowerCase() === 'curl' ? 1 : 0;
      let url = '';

      // Process arguments
      for (let i = startIndex; i < args.length; i++) {
        const arg = args[i];

        if ((arg === '-X' || arg === '--request') && i + 1 < args.length) {
          const method = args[++i];
          if (Object.values(HttpMethod).includes(method as HttpMethod)) {
            result.methodType = method as HttpMethod;
          }
        } else if ((arg === '-H' || arg === '--header') && i + 1 < args.length) {
          const headerStr = args[++i];
          const separatorIndex = headerStr.indexOf(':');

          if (separatorIndex > 0) {
            const key = headerStr.substring(0, separatorIndex).trim();
            const value = headerStr.substring(separatorIndex + 1).trim();
            result.headers.push({ key, value });
          }
        }

        // Request body
        else if ((arg === '-d' || arg === '--data') && i + 1 < args.length) {
          const dataString = args[++i];
          this.processDataString(dataString, result);
        }

        // URL (usually the last argument that's not a flag)
        else if (!arg.startsWith('-') && url === '') {
          url = arg;
        }
      }

      if (url) {
        try {
          const urlObj = new URL(url);
          result.url = urlObj.origin + urlObj.pathname;

          // Extract query parameters
          urlObj.searchParams.forEach((_, name) => {
            result.queryParams.push({ name });
          });
        } catch (e) {
          // If URL parsing fails, just use the raw string
          result.url = url;
        }
      }
    } catch (error) {
      console.error('Error parsing curl command:', error);
      throw new Error('Failed to parse cURL command');
    }

    return result;
  }

  private processDataString(dataString: string, result: CurlImportResult): void {
    try {
      const bodyData = JSON.parse(dataString);
      console.log('bodyData', bodyData);

      if (typeof bodyData === 'object' && bodyData !== null && !Array.isArray(bodyData)) {
        Object.entries(bodyData).forEach(([name, value]) => {
          let paramType = ParameterType.STRING;
          let properties: CurlImportObject[] = [];
          let item: CurlImportArrayItem = {};

          // Determine parameter type based on value
          if (typeof value === 'number') {
            paramType = Number.isInteger(value) ? ParameterType.INTEGER : ParameterType.NUMBER;
          } else if (typeof value === 'boolean') {
            paramType = ParameterType.BOOLEAN;
          } else if (Array.isArray(value)) {
            paramType = ParameterType.ARRAY;

            // Process the first item of the array to determine item type
            if (value.length > 0) {
              const firstItem = value[0];
              if (typeof firstItem === 'object' && firstItem !== null && !Array.isArray(firstItem)) {
                // Process object properties in array
                item = {
                  type: ParameterType.OBJECT,
                  properties: this.processObjectProperties(firstItem),
                };
              } else if (Array.isArray(firstItem)) {
                item = { type: ParameterType.ARRAY };
              } else if (typeof firstItem === 'number') {
                item = {
                  type: Number.isInteger(firstItem) ? ParameterType.INTEGER : ParameterType.NUMBER,
                };
              } else if (typeof firstItem === 'boolean') {
                item = { type: ParameterType.BOOLEAN };
              } else {
                item = { type: ParameterType.STRING };
              }
            }
          } else if (typeof value === 'object' && value !== null) {
            paramType = ParameterType.OBJECT;
            // Process nested object properties
            properties = this.processObjectProperties(value);
          }

          result.bodyParams.push({
            name,
            type: paramType,
            properties,
            item: item,
          });
        });
      }
    } catch (e) {
      throw new Error('Non-JSON data detected');
    }
  }

  private processObjectProperties(obj: any): CurlImportObject[] {
    return Object.entries(obj).map(([propName, propValue]) => {
      let paramType = ParameterType.STRING;
      let properties: CurlImportObject[] = [];
      let item: CurlImportArrayItem = {};

      if (typeof propValue === 'number') {
        paramType = Number.isInteger(propValue) ? ParameterType.INTEGER : ParameterType.NUMBER;
      } else if (typeof propValue === 'boolean') {
        paramType = ParameterType.BOOLEAN;
      } else if (Array.isArray(propValue)) {
        paramType = ParameterType.ARRAY;

        // Process the first item of the array to determine item type
        if (propValue.length > 0) {
          const firstItem = propValue[0];
          if (typeof firstItem === 'object' && firstItem !== null && !Array.isArray(firstItem)) {
            item = {
              type: ParameterType.OBJECT,
              properties: this.processObjectProperties(firstItem),
            };
          } else if (Array.isArray(firstItem)) {
            item = { type: ParameterType.ARRAY, item: this.getArrayItemType(firstItem) };
          } else if (typeof firstItem === 'number') {
            item = {
              type: Number.isInteger(firstItem) ? ParameterType.INTEGER : ParameterType.NUMBER,
            };
          } else if (typeof firstItem === 'boolean') {
            item = { type: ParameterType.BOOLEAN };
          } else {
            item = { type: ParameterType.STRING };
          }
        }
      } else if (typeof propValue === 'object' && propValue !== null) {
        paramType = ParameterType.OBJECT;
        properties = this.processObjectProperties(propValue);
      }

      return {
        name: propName,
        type: paramType,
        properties,
        item,
      };
    });
  }

  // Helper to recursively determine array item type for nested arrays
  private getArrayItemType(arr: any[]): CurlImportArrayItem {
    if (arr.length === 0) return { type: ParameterType.STRING };
    const firstItem = arr[0];
    if (typeof firstItem === 'object' && firstItem !== null && !Array.isArray(firstItem)) {
      return {
        type: ParameterType.OBJECT,
        properties: this.processObjectProperties(firstItem),
      };
    } else if (Array.isArray(firstItem)) {
      return { type: ParameterType.ARRAY, item: this.getArrayItemType(firstItem) };
    } else if (typeof firstItem === 'number') {
      return {
        type: Number.isInteger(firstItem) ? ParameterType.INTEGER : ParameterType.NUMBER,
      };
    } else if (typeof firstItem === 'boolean') {
      return { type: ParameterType.BOOLEAN };
    } else {
      return { type: ParameterType.STRING };
    }
  }
}
