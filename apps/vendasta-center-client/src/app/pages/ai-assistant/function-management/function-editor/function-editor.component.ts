import { ChangeDetectorRef, Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef, MatDialog } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';

import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

import {
  FunctionInterface,
  Namespace,
  FunctionParameterInterface,
  FunctionHeaderInterface,
  FunctionParameterParameterLocation,
} from '@vendasta/ai-assistants';
import { FunctionManagementService } from '../function-management.service';
import { ParameterEditorComponent } from './parameter-editor/parameter-editor.component';
import { NamespaceUtils } from '../../shared/utils';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { map, shareReplay, take } from 'rxjs/operators';
import { ConsentAdminApiService } from '@vendasta/sso';
import { Clipboard } from '@angular/cdk/clipboard';
import { CurlImportDialogComponent } from './curl-import-dialog.component';
import { CurlImportResult } from './curl-import-dialog.component';

export enum NamespaceType {
  SYSTEM = 'SYSTEM',
  GLOBAL = 'GLOBAL',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
  PARTNER = 'PARTNER',
}

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  INTEGER = 'integer',
  OBJECT = 'object',
  ARRAY = 'array',
}

interface FormParameterInterface extends FunctionParameterInterface {
  autoFill?: boolean;
}

interface HeaderInterface {
  key: string;
  value: string;
}

@Component({
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatDialogModule,
    MatInputModule,
    MatListModule,
    MatTabsModule,
    MatSelectModule,
    MatIconModule,
    MatCheckboxModule,
    MatRadioModule,
    GalaxyLoadingSpinnerModule,
    GalaxySnackbarModule,
    GalaxyFormFieldModule,
    GalaxyBadgeModule,
    GalaxyTooltipModule,
    ParameterEditorComponent,
    TranslateModule,
  ],
  providers: [FunctionManagementService],
  templateUrl: './function-editor.component.html',
  styleUrls: ['./function-editor.component.scss'],
})
export class FunctionEditorComponent implements OnInit {
  private readonly data = inject(MAT_DIALOG_DATA);
  private readonly dialogRef = inject(MatDialogRef<FunctionEditorComponent>);
  private readonly dialog = inject(MatDialog);
  private readonly clipboard = inject(Clipboard);
  private readonly functionManagementService = inject(FunctionManagementService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly cdr = inject(ChangeDetectorRef);
  protected readonly NamespaceUtils = NamespaceUtils;

  isLoading = signal<boolean>(false);
  isEditMode = signal<boolean>(false);
  func = signal<FunctionInterface | null>(null);
  form = signal<FormGroup | null>(null);

  namespaceOptions = [
    { value: NamespaceType.GLOBAL, label: 'Global' },
    { value: NamespaceType.SYSTEM, label: 'System' },
    { value: NamespaceType.ACCOUNT_GROUP, label: 'Account group' },
  ];

  httpMethodOptions = [
    { value: HttpMethod.GET, label: 'GET' },
    { value: HttpMethod.POST, label: 'POST' },
    { value: HttpMethod.PUT, label: 'PUT' },
    { value: HttpMethod.DELETE, label: 'DELETE' },
    { value: HttpMethod.PATCH, label: 'PATCH' },
  ];

  parameterTypeOptions = [
    { value: ParameterType.STRING, label: 'String' },
    { value: ParameterType.NUMBER, label: 'Number' },
    { value: ParameterType.BOOLEAN, label: 'Boolean' },
    { value: ParameterType.INTEGER, label: 'Integer' },
    { value: ParameterType.OBJECT, label: 'Object' },
    { value: ParameterType.ARRAY, label: 'Array' },
  ];

  parameterLocationOptions = [
    { value: FunctionParameterParameterLocation.LOCATION_BODY, label: 'Body' },
    { value: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM, label: 'Query Parameter' },
  ];

  scopeOptions$: Observable<string[]> = this.scopes.listScopes({ pageSize: 1000 }).pipe(
    take(1),
    map((resp) => resp?.scopes?.map((s) => s.id)),
    shareReplay(),
  );

  constructor(private scopes: ConsentAdminApiService) {}

  ngOnInit() {
    this.form.set(this.initForm());
    this.form().valueChanges.subscribe((_) => {
      this.cdr.detectChanges();
    });

    if (this.data?.functionId && this.data?.namespace) {
      this.isEditMode.set(true);
      this.loadFunction(this.data.functionId, this.data.namespace);
    }

    // Add conditional validation for Group Path and Partner ID based on namespace type
    this.form()
      .get('namespaceType')
      ?.valueChanges.subscribe((namespaceType) => {
        const accountGroupIdControl = this.form().get('accountGroupId');

        // Clear all validators first
        accountGroupIdControl?.clearValidators();

        // Set validators based on namespace type
        if (namespaceType === NamespaceType.ACCOUNT_GROUP) {
          accountGroupIdControl?.setValidators([Validators.required]);
        }

        // Update validity
        accountGroupIdControl?.updateValueAndValidity();
      });

    // Subscribe to generatesAnswer changes
    this.form()
      .get('generatesAnswer')
      ?.valueChanges.subscribe((generates) => {
        const methodTypeControl = this.form().get('methodType');
        const urlControl = this.form().get('url');

        if (generates) {
          methodTypeControl?.disable();
          urlControl?.disable();
          // Set default values when disabled
          methodTypeControl?.setValue(HttpMethod.GET);
          urlControl?.setValue('');

          // Check if Output parameter exists, if not add it
          const hasOutputParam = this.parametersFormArray.controls.some(
            (control) => control.get('name')?.value === 'Output',
          );
          if (!hasOutputParam) {
            const outputParam = this.createParameterFormGroup({
              name: 'Output',
              type: ParameterType.STRING,
              description:
                'The output considering all provided instructions. Replace any straight double quotation marks here (") with curly double quotation marks(" or ").',
            });
            this.parametersFormArray.insert(0, outputParam);
          }

          // Clear headers when generatesAnswer is true
          while (this.headersFormArray.length) {
            this.headersFormArray.removeAt(0);
          }
        } else {
          methodTypeControl?.enable();
          urlControl?.enable();
        }
      });
  }

  private initForm(): FormGroup {
    const form = new FormGroup({
      id: new FormControl('', [Validators.required]),
      description: new FormControl(''),
      namespaceType: new FormControl(NamespaceType.GLOBAL, [Validators.required]),
      groupPath: new FormControl(''),
      partnerId: new FormControl(''),
      accountGroupId: new FormControl(''),
      methodType: new FormControl(HttpMethod.GET, [Validators.required]),
      url: new FormControl('', [Validators.required]),
      parameters: new FormArray([]),
      headers: new FormArray([]),
      generatesAnswer: new FormControl(false),
      usePlatformManagedAuth: new FormControl(false),
      platformManagedAuthRequiredScopes: new FormControl([]),
    });

    // Add initial empty parameter when creating a new function
    if (!this.isEditMode()) {
      (form.get('parameters') as FormArray).push(this.createParameterFormGroup());
    }

    // Add conditional validation for method and URL
    const updateValidators = (generates: boolean) => {
      const methodControl = form.get('methodType');
      const urlControl = form.get('url');

      if (generates) {
        methodControl?.clearValidators();
        urlControl?.clearValidators();
      } else {
        methodControl?.setValidators([Validators.required]);
        urlControl?.setValidators([Validators.required]);
      }

      methodControl?.updateValueAndValidity();
      urlControl?.updateValueAndValidity();
    };

    // Set initial validators
    updateValidators(form.get('generatesAnswer')?.value);

    // Update validators when generatesAnswer changes
    form.get('generatesAnswer')?.valueChanges.subscribe(updateValidators);

    const updatePlatformManagedAuthRequiredScopesValidators = (usePlatformAuth: boolean) => {
      if (usePlatformAuth) {
        form.get('platformManagedAuthRequiredScopes').setValidators([Validators.required]);
      } else {
        form.get('platformManagedAuthRequiredScopes').clearValidators();
      }
    };

    // Set initial validators for scopes
    updatePlatformManagedAuthRequiredScopesValidators(form.get('usePlatformManagedAuth').value);

    // Update validators when usePlatformManagedAuth changes
    form.get('usePlatformManagedAuth').valueChanges.subscribe(updatePlatformManagedAuthRequiredScopesValidators);

    return form;
  }

  private createParameterFormGroup(parameter?: FunctionParameterInterface): FormGroup {
    const hasValue = parameter?.value && parameter.value.length > 0;
    const locationFormControl = new FormControl(
      parameter?.location || FunctionParameterParameterLocation.LOCATION_BODY,
      [Validators.required],
    );
    return new FormGroup({
      name: new FormControl(parameter?.name || '', [Validators.required]),
      type: new FormControl(parameter?.type || ParameterType.STRING, [Validators.required]),
      location: locationFormControl,
      description: new FormControl(parameter?.description || ''),
      value: new FormControl({ value: parameter?.value || '', disabled: !hasValue }),
      properties: new FormControl<FunctionParameterInterface[] | null>(parameter?.properties || null),
      items: new FormControl<FunctionParameterInterface | null>(parameter?.items || null),
      autoFill: new FormControl(hasValue),
    });
  }

  private createHeaderFormGroup(header?: HeaderInterface): FormGroup {
    return new FormGroup({
      key: new FormControl(header?.key || '', [Validators.required]),
      value: new FormControl(header?.value || '', [Validators.required]),
    });
  }

  get parametersFormArray(): FormArray {
    return this.form().get('parameters') as FormArray;
  }

  get headersFormArray(): FormArray {
    return this.form().get('headers') as FormArray;
  }

  get usePlatformManagedAuthFormControl(): FormControl {
    return this.form().get('usePlatformManagedAuth') as FormControl;
  }

  async loadFunction(functionId: string, namespace: Namespace) {
    this.isLoading.set(true);
    try {
      const func = await this.functionManagementService.getFunction(functionId, namespace);
      this.func.set(func);

      // Clear existing parameters
      while (this.parametersFormArray.length) {
        this.parametersFormArray.removeAt(0);
      }

      // Clear existing headers
      while (this.headersFormArray.length) {
        this.headersFormArray.removeAt(0);
      }
      // Add parameters from function
      func.functionParameters
        ?.sort((a, b) => a.name.localeCompare(b.name))
        .forEach((param) => {
          this.parametersFormArray.push(this.createParameterFormGroup(param));
        });

      // Add headers from function if they exist
      if (func.headers && Array.isArray(func.headers)) {
        func.headers.forEach((header) => {
          if (header.key && header.value) {
            this.headersFormArray.push(this.createHeaderFormGroup({ key: header.key, value: header.value }));
          }
        });
      }

      let accountGroupId = '';

      if (func.namespace.accountGroupNamespace) {
        const accountGroupNamespace = func.namespace.accountGroupNamespace;
        accountGroupId = accountGroupNamespace.accountGroupId || '';
      }

      // Determine namespace type from function namespace
      let namespaceType = NamespaceType.GLOBAL;
      if (func.namespace.systemNamespace) {
        namespaceType = NamespaceType.SYSTEM;
      } else if (func.namespace.accountGroupNamespace) {
        namespaceType = NamespaceType.ACCOUNT_GROUP;
      }

      this.form().patchValue({
        id: func.id,
        description: func.description,
        namespaceType: namespaceType,
        accountGroupId: accountGroupId,
        methodType: func.methodType || HttpMethod.GET,
        url: func.url,
        generatesAnswer: func.generatesAnswer,
        usePlatformManagedAuth: !!func.authStrategy?.platformManaged,
        platformManagedAuthRequiredScopes: func.authStrategy?.platformManaged?.requiredScopes,
      });
    } catch (error) {
      console.error('Error loading function:', error);
      this.snackbarService.openErrorSnack('Failed to load function');
    } finally {
      this.isLoading.set(false);
    }
  }

  addParameter(): void {
    this.parametersFormArray.push(this.createParameterFormGroup());
    this.form()?.markAsDirty();
  }

  addHeader(): void {
    this.headersFormArray.push(this.createHeaderFormGroup());
    this.form()?.markAsDirty();
  }

  removeParameter(index: number): void {
    this.parametersFormArray.removeAt(index);
    this.form()?.markAsDirty();
  }

  removeHeader(index: number): void {
    this.headersFormArray.removeAt(index);
    this.form()?.markAsDirty();
  }

  close(): void {
    this.dialogRef.close();
  }

  private cleanParameter(param: FormParameterInterface): FunctionParameterInterface {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { autoFill, ...cleanParam } = param;

    if (cleanParam.properties) {
      cleanParam.properties = cleanParam.properties.map((p: FormParameterInterface) => this.cleanParameter(p));
    }
    if (cleanParam.items) {
      cleanParam.items = this.cleanParameter(cleanParam.items);
    }

    return cleanParam;
  }

  async submit(): Promise<void> {
    // Always enable all form controls temporarily for validation
    const disabledControls: { control: any; path: string[] }[] = [];

    // Find and temporarily enable all disabled controls
    const findAndEnableDisabledControls = (group: FormGroup, path: string[] = []) => {
      Object.keys(group.controls).forEach((key) => {
        const control = group.get(key);
        const currentPath = [...path, key];

        if (control instanceof FormGroup) {
          findAndEnableDisabledControls(control, currentPath);
        } else if (control instanceof FormArray) {
          control.controls.forEach((ctrl, index) => {
            if (ctrl instanceof FormGroup) {
              findAndEnableDisabledControls(ctrl, [...currentPath, index.toString()]);
            }
          });
        } else if (control?.disabled) {
          disabledControls.push({ control, path: currentPath });
          control.enable();
        }
      });
    };

    // Enable all disabled controls temporarily for validation
    findAndEnableDisabledControls(this.form());

    // Check if form is valid now that all controls are enabled
    const isValid = this.form().valid;

    // Re-disable controls
    disabledControls.forEach((item) => {
      item.control.disable();
    });

    if (!isValid) {
      this.snackbarService.openErrorSnack('Please fix validation errors in the form before saving.');
      return;
    }

    // Prompt user if they're trying to save without changes
    if (!this.isEditMode() && this.parametersFormArray.length === 0) {
      // For new functions, require at least one parameter
      this.snackbarService.openErrorSnack('Please add at least one parameter before saving.');
      return;
    }

    this.isLoading.set(true);
    try {
      // Get values including from disabled controls
      const formValue = this.form().getRawValue();

      // Clean up parameters before sending to API
      const cleanParameters = formValue.parameters.map((param) => this.cleanParameter(param));

      let namespace: Namespace;

      switch (formValue.namespaceType) {
        case NamespaceType.GLOBAL:
          namespace = new Namespace({ globalNamespace: {} });
          break;
        case NamespaceType.SYSTEM:
          namespace = new Namespace({ systemNamespace: {} });
          break;
        case NamespaceType.ACCOUNT_GROUP:
          namespace = new Namespace({ accountGroupNamespace: { accountGroupId: formValue.accountGroupId } });
          break;
        case NamespaceType.PARTNER:
          namespace = new Namespace({ partnerNamespace: { partnerId: formValue.partnerId } });
          break;
        default:
          this.snackbarService.openErrorSnack('Invalid namespace');
          return;
      }

      const func: FunctionInterface = {
        id: formValue.id,
        description: formValue.description,
        namespace: namespace,
        methodType: formValue.methodType,
        url: formValue.url,
        functionParameters: cleanParameters,
        generatesAnswer: formValue.generatesAnswer,
        authStrategy: {
          unspecified: {},
        },
      };

      if (formValue.usePlatformManagedAuth) {
        const requiredScopes = (this.form().get('platformManagedAuthRequiredScopes') as FormArray).value;
        func.authStrategy = {
          platformManaged: {
            requiredScopes: requiredScopes || [],
          },
        };
      }

      // Add headers if they exist and the function doesn't generate an answer
      if (!formValue.generatesAnswer && formValue.headers && formValue.headers.length > 0) {
        // Create headers array
        const headersArray: FunctionHeaderInterface[] = [];
        formValue.headers.forEach((header) => {
          if (header.key && header.value) {
            headersArray.push({
              key: header.key,
              value: header.value,
            });
          }
        });

        if (headersArray.length > 0) {
          func.headers = headersArray;
        } else {
          // If no valid headers, set to undefined
          func.headers = undefined;
        }
      } else {
        // Ensure headers are null/undefined when generatesAnswer is true
        func.headers = undefined;
      }

      await this.functionManagementService.upsertFunction(func);
      this.snackbarService.openSuccessSnack('Function saved successfully');
      this.form()?.markAsPristine();
    } catch (error) {
      console.error('Error saving function:', error);
      this.snackbarService.openErrorSnack('Failed to save function');
    } finally {
      this.isLoading.set(false);
    }
  }

  copyCurlCommand(): void {
    if (!this.func()) return;

    let curlCommand = `curl -X ${this.func().methodType} `;

    if (this.headersFormArray && this.headersFormArray.length > 0) {
      Object.entries(this.headersFormArray.value).forEach(([_, v]) => {
        curlCommand += `-H '${v['key']}: ${v['value']}' `;
      });
    }

    const requestBodyArgs = {};
    const urlQueryArgs = {};

    const getDefaultValue = (param: any): any => {
      switch (param.type) {
        case ParameterType.INTEGER:
          return 0;
        case ParameterType.NUMBER:
          return 0;
        case ParameterType.BOOLEAN:
          return false;
        case ParameterType.ARRAY:
          if (param.items) {
            // For arrays, create an array with a single default value for the item type
            return [getDefaultValue(param.items)];
          }
          return [];
        case ParameterType.OBJECT:
          if (param.properties && Array.isArray(param.properties)) {
            const obj: any = {};
            param.properties.forEach((prop: any) => {
              obj[prop.name] = getDefaultValue(prop);
            });
            return obj;
          }
          return {};
        case ParameterType.STRING:
        default:
          return '';
      }
    };

    if (this.parametersFormArray && this.parametersFormArray.length > 0) {
      this.parametersFormArray.value.forEach((param) => {
        if (param.location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
          urlQueryArgs[param.name] = '';
        } else {
          if (param.name !== 'Output') {
            requestBodyArgs[param.name] = getDefaultValue(param);
          }
        }
      });
    }

    if (Object.keys(requestBodyArgs).length > 0) {
      curlCommand += `-d '${JSON.stringify(requestBodyArgs)}' `;
    }

    let url = this.func().url;
    if (this.parametersFormArray && this.parametersFormArray.length > 0) {
      url += `?${new URLSearchParams(urlQueryArgs).toString()}`;
    }

    curlCommand += `'${url}'`;

    this.clipboard.copy(curlCommand);
  }

  importFromCurl(): void {
    const dialogRef = this.dialog.open(CurlImportDialogComponent, {
      width: '600px',
      disableClose: false,
      autoFocus: false,
    });

    dialogRef.afterClosed().subscribe((result: CurlImportResult) => {
      if (result) {
        this.applyImportedCurl(result);
      }
    });
  }

  private applyImportedCurl(result: CurlImportResult): void {
    // Update method type
    this.form().patchValue({
      methodType: result.methodType,
      url: result.url,
    });

    // Clear existing parameters
    while (this.parametersFormArray.length) {
      this.parametersFormArray.removeAt(0);
    }

    // Add Output parameter
    const outputParam = this.createParameterFormGroup({
      name: 'Output',
      type: ParameterType.STRING,
      description:
        'The output considering all provided instructions. Replace any straight double quotation marks here (") with curly double quotation marks(" or ").',
      location: FunctionParameterParameterLocation.LOCATION_BODY,
    });
    this.parametersFormArray.push(outputParam);

    // Clear existing headers
    while (this.headersFormArray.length) {
      this.headersFormArray.removeAt(0);
    }

    // Add headers
    if (result.headers.length > 0) {
      result.headers.forEach((header) => {
        this.headersFormArray.push(this.createHeaderFormGroup(header));
      });
    }

    // Add body parameters
    if (result.bodyParams.length > 0) {
      result.bodyParams.forEach((param) => {
        // Create parameter definition with correct type handling for nested structures
        const parameterDefinition: FunctionParameterInterface = {
          name: param.name,
          type: param.type || ParameterType.STRING,
          description: 'Describe the parameter and how the AI should use it',
          location: FunctionParameterParameterLocation.LOCATION_BODY,
        };

        // Add properties for object types
        if (param.properties && param.type === ParameterType.OBJECT) {
          parameterDefinition.properties = param.properties.map((prop) => this.convertNestedProperty(prop));
        }

        // Add items for array types
        if (param.item && param.type === ParameterType.ARRAY) {
          parameterDefinition.items = this.convertNestedProperty(param.item);
        }

        const paramFormGroup = this.createParameterFormGroup(parameterDefinition);
        this.parametersFormArray.push(paramFormGroup);
      });
    }

    // Add query parameters
    if (result.queryParams.length > 0) {
      result.queryParams.forEach((param) => {
        const paramFormGroup = this.createParameterFormGroup({
          name: param.name,
          type: ParameterType.STRING, // Default to string
          description: 'Describe the parameter and how the AI should use it',
          location: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM,
        });
        this.parametersFormArray.push(paramFormGroup);
      });
    }

    // Show success message
    this.snackbarService.openSuccessSnack('cURL command imported successfully');
  }

  private convertNestedProperty(prop: any): FunctionParameterInterface {
    const result: FunctionParameterInterface = {
      name: prop.name || '',
      type: prop.type || ParameterType.STRING,
      description: 'Describe the parameter and how the AI should use it',
      location: FunctionParameterParameterLocation.LOCATION_BODY,
    };

    // Handle nested properties for objects
    if (prop.properties && prop.type === ParameterType.OBJECT) {
      result.properties = prop.properties.map((p) => this.convertNestedProperty(p));
    }

    // Handle items for arrays
    if (prop.items && prop.type === ParameterType.ARRAY) {
      result.items = this.convertNestedProperty(prop.items);
    }

    return result;
  }
}
