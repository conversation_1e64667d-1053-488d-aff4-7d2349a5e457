export type InternalListWithHeadings = {
  heading: string;
  items: InternalSiteOrTool[];
};

type InternalSiteOrTool = {
  name: string;
  description?: string;
  image?: string;
  matIcon?: string;
  iconBackgroundColor?: string;
  links?: { name: string; url: string }[];
  urlDemo?: string;
};

//////////////

const centersList: InternalSiteOrTool[] = [
  {
    name: 'Partner Center',
    description: 'The main portal for managing your Vendasta account',
    links: [
      { name: 'Prod', url: 'https://partners.vendasta.com/' },
      { name: 'Demo', url: 'https://partner-central-demo.appspot.com/' },
    ],
    matIcon: 'business',
    iconBackgroundColor: '#F5A623',
  },
  {
    name: 'Sales and Success Center',
    description: 'CRM for managing sales, oppertunities, and customers',
    links: [
      { name: 'Prod', url: 'https://abc.snapshotreport.biz/' },
      { name: 'Demo', url: 'https://abc-dot-vbc-demo.appspot.com/' },
      { name: 'VMF Prod', url: 'https://vmf.snapshotreport.biz/' },
      { name: 'VMF Demo', url: 'https://vmf-dot-salestool-demo.appspot.com/' },
    ],
    image: '/assets/tools/sales-center.png',
  },
  {
    name: 'Business App',
    description: 'The main portal for your customers to manage their business',
    links: [
      { name: 'Prod', url: 'https://login.custom-abc.com/account/location/AG-BXVDT6WV/dashboard' },
      { name: 'Demo', url: 'https://abc-dot-vbc-demo.appspot.com/' },
    ],
    image: '/assets/tools/business-app.png',
  },
  {
    name: 'Vendor Center',
    description: 'Manage marketplace products and services',
    links: [
      { name: 'Prod', url: 'https://vendors.vendasta.com/' },
      { name: 'Demo', url: 'https://vendors-demo.vendasta.com/' },
    ],
    image: '/assets/tools/marketplace.png',
  },
  {
    name: 'Task Manager',
    description: 'Manage tasks and workflows',
    links: [
      { name: 'Prod', url: 'https://task-manager.biz/' },
      { name: 'Demo', url: 'https://arm-demo.appspot.com/' },
    ],
    image: '/assets/tools/task-manager.png',
  },
  {
    name: 'Billing Center',
    description: 'Manage billing and payments',
    links: [
      { name: 'Prod', url: 'https://billing-prod.apigateway.co/' },
      { name: 'Demo', url: 'https://billing-demo.apigateway.co/' },
    ],
    matIcon: 'receipt_long',
    iconBackgroundColor: '#43A047',
  },
  {
    name: 'Superadmin',
    description: 'Manage partner accounts and users',
    links: [
      { name: 'Prod', url: 'https://partners.vendasta.com/superadmin' },
      { name: 'Demo', url: 'https://partner-central-demo.appspot.com/superadmin' },
    ],
    matIcon: 'admin_panel_settings',
    iconBackgroundColor: '#F5A623',
  },
  {
    name: 'Vendasta Center (this site)',
    description: 'Manage internal setings',
    links: [
      { name: 'Prod', url: 'https://admin.vendasta-internal.com/' },
      { name: 'Demo', url: 'https://admin-demo.vendasta-internal.com/' },
    ],
    matIcon: 'settings',
  },
  {
    name: 'Developer Center',
    description: 'Serves the public documentation for partner and vendor developers',
    links: [
      { name: 'Prod', url: 'https://developers.vendasta.com/' },
      { name: 'Demo', url: 'https://developer-center-demo.vendasta-internal.com/developers/' },
    ],
    matIcon: 'api',
  },
];

//

const appsList: InternalSiteOrTool[] = [
  {
    name: 'Reputation Management',
    description: 'Manage your business reputation',
    links: [
      { name: 'Prod', url: 'https://abc.steprep.com/account/AG-BXVDT6WV/app/overview' },
      { name: 'Demo', url: 'https://abc.steprep-demo.com/account/AG-F6P8WWQL/app/overview' },
    ],
  },
  {
    name: 'Social Marketing',
    description: 'Manage your business social media',
    links: [
      { name: 'Prod', url: 'https://abc.socialsmbs.com/account/AG-BXVDT6WV/overview' },
      { name: 'Demo', url: 'https://abc-dot-socmktg-demo.appspot.com/account/AG-F6P8WWQL/overview' },
    ],
  },
  {
    name: 'Listing Builder',
    description: 'Manage your business listings',
    links: [
      { name: 'Prod', url: 'https://abc.pdqs.mobi/edit/account/AG-BXVDT6WV/app/overview' },
      { name: 'Demo', url: 'http://abc.microsite-demo.appspot.com/edit/account/AG-F6P8WWQL/app/overview' },
    ],
  },
  {
    name: 'Website Pro',
    description: 'Manage your business website',
    links: [
      {
        name: 'Prod',
        url: 'https://www.websiteprodashboard.com/site/0d640873b107cc9e308f7f7b2edc101d572b40929ab4176cf025ef37ed107ddb/overview',
      },
      {
        name: 'Demo',
        url: 'https://websiteprodashboard-demo.com/site/77540f29eb8106044cb7b9b4e06c9217c106128793c5669c5dbfa679462827d2/overview',
      },
    ],
  },
  {
    name: 'Customer Voice',
    description: 'Request reviews from your customers',
    links: [
      { name: 'Prod', url: 'https://customervoice.biz/cv/entry/AG-BXVDT6WV/' },
      { name: 'Demo', url: 'https://steprep-demo-hrd.appspot.com/cv/entry/AG-F6P8WWQL/' },
    ],
  },
  {
    name: 'Advertising Intelligence',
    description: 'Manage your business advertising',
    links: [
      { name: 'Prod', url: 'https://www.advertisingintelligence.io/partner/ABC/business/AG-BXVDT6WV/overview' },
      {
        name: 'Demo',
        url: 'https://advertising-accounts-demo.apigateway.co/partner/ABC/business/AG-F6P8WWQL/overview',
      },
    ],
  },
];

//

const buildList: InternalSiteOrTool[] = [
  {
    name: 'Mission Control',
    description: 'Manage deployments and releases. View and deploy builds',
    links: [
      { name: 'Prod', url: 'https://mission-control-prod.vendasta-internal.com/' },
      { name: 'Demo', url: 'https://mission-control-demo.vendasta-internal.com/' },
    ],
    image: '/assets/tools/mission-control.png',
  },
  {
    name: 'ArgoCD',
    description: 'GitOps continuous delivery tool for Kubernetes',
    links: [{ name: 'View site', url: 'https://argocd.vendasta-internal.com/' }],
    image: '/assets/tools/argocd.png',
  },
  {
    name: 'Google Cloud Build',
    description: 'CI/CD tool for building and testing software',
    links: [{ name: 'View site', url: 'https://console.cloud.google.com/cloud-build/builds?project=repcore-prod' }],
    image: '/assets/tools/cloudbuild.png',
  },
];

//

const trackingAndAnalyticsList: InternalSiteOrTool[] = [
  {
    name: 'Posthog',
    description: 'User Events and Analytics',
    links: [{ name: 'View site', url: 'https://pa.apigateway.co/' }],
    image: '/assets/tools/posthog.png',
  },
  {
    name: 'Google Analytics',
    description: 'Pageview tracking and funnels',
    links: [
      {
        name: 'View site',
        url: 'https://analytics.google.com/analytics/web/?hl=en#/report/visitors-mobile-overview/a5217948w75600469p78102284/_u.dateOption=last30days/',
      },
    ],
    image: '/assets/tools/google-analytics.png',
  },
];

//

const databaseList: InternalSiteOrTool[] = [
  {
    name: 'vStore',
    description: 'OLTP Database - For storing all of your µservice data',
    links: [
      { name: 'Prod', url: 'https://vstore-prod.vendasta-internal.com/' },
      { name: 'Demo', url: 'https://vstore-demo.vendasta-internal.com/' },
    ],
    image: '/assets/tools/vstore.png',
  },
  {
    name: 'Google BigQuery',
    description: 'OLAP Database - For querying Multilocation metrics',
    links: [{ name: 'View site', url: 'https://console.cloud.google.com/bigquery?project=vendasta-prod' }],
    image: '/assets/tools/google-bigquery.png',
  },
  {
    name: 'Account Finder',
    description: 'Quickly search accounts and generate tokens',
    links: [{ name: 'Prod', url: 'http://account-finder.vendasta-internal.com/' }],
    matIcon: 'search',
    iconBackgroundColor: '#F5A623',
  },
  {
    name: 'Tesseract Query',
    description: 'OLAP Database - For querying Multilocation metrics',
    links: [{ name: 'Prod', url: 'https://mission-control-prod.vendasta-internal.com/tesseract/query' }],
    image: '/assets/tools/tesseract.png',
  },
];

//

const authList: InternalSiteOrTool[] = [
  {
    name: 'IAM - Identity and Access Management',
    description: 'Identity and Access Management for Vendasta Users, Partners, Vendors and Directories',
    links: [
      { name: 'Prod', url: 'https://iam-prod.apigateway.co/' },
      { name: 'Demo', url: 'https://iam-demo.apigateway.co/' },
    ],
    image: '/assets/tools/iam.png',
  },
];

//

const loggingList: InternalSiteOrTool[] = [
  {
    name: 'vLogs',
    description: 'BigQuery Log Viewer',
    links: [
      { name: 'Prod', url: 'https://vlogs-dot-vmonitor-prod.appspot.com/' },
      { name: 'Demo', url: 'https://vlogs-dot-vmonitor-demo.appspot.com/' },
    ],
    image: '/assets/tools/google-bigquery.png',
  },
  {
    name: 'Github Team Dashboard',
    description: 'View all Github PRs per team over time',
    links: [{ name: 'Prod', url: 'https://vendasta-team-dashboard.appspot.com/' }],
    matIcon: 'query_stats',
    iconBackgroundColor: '#F5A623',
  },
  {
    name: 'SRE Reporting',
    description: 'Depricated? SRED Reporting site',
    links: [
      { name: 'Prod', url: 'https://sre-reporting-prod.apigateway.co/' },
      { name: 'Demo', url: 'https://sre-reporting-demo.apigateway.co/' },
    ],
    image: '/assets/tools/sre-reporting.png',
  },
  {
    name: 'Cortex',
    description:
      'Service catalog and associated tooling to manage microservice ecosystems. Workspace name is "vendasta"',
    links: [{ name: 'Prod', url: 'https://app.getcortexapp.com/admin/home' }],
    image: '/assets/tools/cortex.png',
  },
];

//

const frontendList: InternalSiteOrTool[] = [
  {
    name: 'Galaxy Observatory',
    description: 'Galaxy Observatory',
    links: [
      { name: 'Prod', url: 'https://galaxy.vendasta.com' },
      { name: 'Demo', url: 'https://galaxy-demo.vendasta-internal.com' },
    ],
    image: '/assets/tools/galaxy.png',
  },
  {
    name: 'Galaxy Sandbox',
    description: 'Galaxy Sandbox',
    links: [
      { name: 'Prod', url: 'https://galaxy-sandbox.vendasta-internal.com' },
      { name: 'Demo', url: 'https://galaxy-sandbox-demo.vendasta-internal.com' },
    ],
    matIcon: 'construction',
    iconBackgroundColor: '#1c2951',
  },
  {
    name: 'Angular Material',
    description: 'Angular Material Docs',
    links: [{ name: 'Docs', url: 'https://material.angular.io/' }],
    image: '/assets/tools/angular.png',
  },
];

//

const translationList: InternalSiteOrTool[] = [
  {
    name: 'Weblate',
    description:
      'Web-based translation tool with tight version control integration. Builds the translations for our centers and products',
    links: [
      { name: 'Prod', url: 'https://weblate.apigateway.co/' },
      { name: 'Demo', url: 'https://weblate-demo.apigateway.co/' },
    ],
    image: '/assets/tools/weblate.png',
  },
];

//

const miscList: InternalSiteOrTool[] = [
  {
    name: 'External R&D Blog',
    description: 'Articles about processes and tech at Vendasta published on Medium',
    links: [{ name: 'View site', url: 'https://medium.com/vendasta' }],
    image: '/assets/tools/medium-blog.png',
  },
  {
    name: 'Internal R&D Blog',
    description: 'Articles about processes and tech at Vendasta published on Medium',
    links: [{ name: 'View site', url: 'https://vendasta.jira.com/wiki/spaces/BLOG/overview' }],
    matIcon: 'article',
    iconBackgroundColor: '#1c2951',
  },
  {
    name: 'Conquer Local',
    description: 'Articles, forums, and courses for the Conquer Local Academy',
    links: [
      { name: 'Prod', url: 'https://www.conquerlocal.com/' },
      { name: 'Demo', url: 'https://conquer-local-academy.websitepro-staging.com/' },
    ],
    image: '/assets/tools/conquer-local.png',
  },
  {
    name: 'Vendasta Video Search',
    description:
      'View and search the auto-generated text of all internal Vendasta youtube videos, including Tech Demos',
    links: [{ name: 'Prod', url: 'https://youtube-search-167120.appspot.com/' }],
    matIcon: 'video_library',
    iconBackgroundColor: '#1c2951',
  },
  {
    name: 'Vendasta Office Library',
    description: 'Browse and borrow books from the Vendasta Library',
    links: [{ name: 'Prod', url: 'https://vendasta-office-library.appspot.com/' }],
    matIcon: 'menu_book',
    iconBackgroundColor: '#1c2951',
  },
  {
    name: 'Marketing Email Builder',
    description:
      'Drag and drop builder for marketing and transactional emails. Includes a library of prebuild <a href="https://email-builder.vendasta-internal.com/general-marketing-email/component-examples/" target="_blank" rel="noopener">marketing</a> and <a href="https://email-builder.vendasta-internal.com/transactional-emails/transactional-emails-design-pattern-library/" target="_blank" rel="noopener">transactional components</a>. Talk to @jkesler or @rmorris for access',
    links: [{ name: 'Prod', url: 'https://email-builder.vendasta-internal.com/' }],
    matIcon: 'email',
    iconBackgroundColor: '#1c2951',
  },
];

//////////////

export const internalSitesAndTools: InternalListWithHeadings[] = [
  {
    heading: 'Vendasta Centers',
    items: [...centersList],
  },
  {
    heading: 'Building and Deploying',
    items: [...buildList],
  },
  {
    heading: 'Database',
    items: [...databaseList],
  },
  {
    heading: 'Permissions and Authentication',
    items: [...authList],
  },
  {
    heading: 'Logging and Monitoring',
    items: [...loggingList],
  },
  {
    heading: 'Frontend',
    items: [...frontendList],
  },
  {
    heading: 'Tracking and Analyitics',
    items: [...trackingAndAnalyticsList],
  },
  {
    heading: 'Translation and Internationalization (i18n)',
    items: [...translationList],
  },
  {
    heading: 'Misc',
    items: [...miscList],
  },
  {
    heading: 'Vendasta Apps',
    items: [...appsList],
  },
];
