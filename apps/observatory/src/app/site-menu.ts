export enum Status {
  ToDo = 'todo',
  Done = 'done',
  Partial = 'partial',
  NotApplicable = 'not applicable',
}

export interface LinkItem {
  name: string;
  url?: string;
  selector?: string;
  codeDone?: Status;
  designDone?: Status;
  guideDone?: Status;
  deprecated?: boolean;
}

export interface SiteMenuInterface {
  groupName: string;
  links: LinkItem[];
}

export const siteMenu: SiteMenuInterface[] = [
  {
    groupName: 'Shared Styles',
    links: [
      {
        name: 'Base styles',
        url: '/shared-styles/base-styles',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Layout',
        url: '/shared-styles/layout',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Media queries',
        url: '/shared-styles/media-queries',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Theming',
        url: '/shared-styles/theming',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Typography presets',
        url: '/shared-styles/typography',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Hide sizes',
        url: '/shared-styles/hide-sizes',
        selector: 'glxy-hide-sizes',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
    ],
  },
  {
    groupName: 'Design tokens',
    links: [
      {
        name: 'Using design tokens',
        url: '/design-tokens/using-design-tokens',
        codeDone: Status.NotApplicable,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Border radius',
        url: '/design-tokens/border-radius',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Breakpoints',
        url: '/design-tokens/breakpoints',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Color',
        url: '/design-tokens/color',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Font sizes',
        url: '/design-tokens/font-sizes',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Spacing',
        url: '/design-tokens/spacing',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
    ],
  },
  {
    groupName: 'Tools',
    links: [
      {
        name: 'I18N',
        url: '/i18n',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
    ],
  },
  {
    groupName: 'Components',
    links: [
      {
        name: 'Alert',
        url: '/alert',
        selector: 'glxy-alert',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Avatar',
        url: '/avatar',
        selector: 'glxy-avatar',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Badge',
        url: '/badge',
        selector: 'glxy-badge',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Button',
        url: '/button',
        selector: 'mat-button',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Button group',
        url: '/button-group',
        selector: 'glxy-button-group',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Button toggle group',
        url: '/button-toggle-group',
        selector: 'mat-button-toggle-group',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Button loading indicator',
        url: '/button-loading-indicator',
        selector: 'glxy-button-loading-indicator',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.ToDo,
      },
      {
        name: 'Card',
        url: '/card',
        selector: 'mat-card',
        codeDone: Status.NotApplicable,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Chat',
        url: '/chat',
        selector: 'glxy-chat-*',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Chat Composer (Beta)',
        url: '/chat-composer',
        selector: 'glxy-chat-composer',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Chip',
        url: '/chip',
        selector: 'mat-chip',
        codeDone: Status.NotApplicable,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Comment',
        url: '/comment',
        selector: 'glxy-comment',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Date range presets',
        url: '/date-range-presets',
        selector: 'glxy-date-range-presets',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Email Viewer',
        url: '/email-viewer',
        selector: 'glxy-email-viewer',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Empty state',
        url: '/empty-state',
        selector: 'glxy-empty-state',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Expansion panel',
        url: '/expansion-panel',
        selector: 'mat-expansion-panel',
        codeDone: Status.NotApplicable,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Filter',
        url: '/filter',
        selector: 'glxy-filter',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.ToDo,
      },

      {
        name: 'Frequency',
        url: '/frequency',
        selector: 'glxy-frequency',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Icon',
        url: '/icon',
        selector: 'mat-icon',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Loading Spinner',
        url: '/loading-spinner',
        selector: 'glxy-loading-spinner',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.ToDo,
      },
      {
        name: 'Menu',
        url: '/menu',
        selector: 'mat-menu',
        codeDone: Status.NotApplicable,
        designDone: Status.Partial,
        guideDone: Status.ToDo,
      },
      {
        name: 'Modal (dialog)',
        url: '/modal',
        selector: 'mat-dialog-[...]',
        codeDone: Status.NotApplicable,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Page',
        url: '/page',
        selector: 'glxy-page',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Popover',
        url: '/popover',
        selector: 'glxy-popover',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Progress bar',
        url: '/progress-bar',
        selector: 'glxy-progress-bar',
        codeDone: Status.Done,
        designDone: Status.ToDo,
        guideDone: Status.Done,
      },
      {
        name: 'Side panel (drawer)',
        url: '/side-panel',
        selector: 'mat-drawer',
        codeDone: Status.NotApplicable,
        designDone: Status.ToDo,
        guideDone: Status.Done,
      },
      {
        name: 'Slide toggle',
        url: '/slide-toggle',
        selector: 'mat-slide-toggle',
        codeDone: Status.NotApplicable,
        designDone: Status.ToDo,
        guideDone: Status.Partial,
      },
      {
        name: 'Snackbar service',
        url: '/snackbar',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Statistic',
        url: '/statistic',
        selector: 'glxy-statistic',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Stepper',
        url: '/stepper',
        selector: 'mat-stepper',
        codeDone: Status.NotApplicable,
        designDone: Status.ToDo,
        guideDone: Status.Done,
      },
      {
        name: 'Sticky footer',
        url: '/sticky-footer',
        selector: 'glxy-sticky-footer',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Tabs',
        url: '/tabs',
        selector: 'mat-tab',
        codeDone: Status.NotApplicable,
        designDone: Status.Partial,
        guideDone: Status.ToDo,
      },
      {
        name: 'Three Panel',
        url: '/three-panel',
        selector: 'glxy-three-panel',
        codeDone: Status.Partial,
        designDone: Status.Partial,
        guideDone: Status.Partial,
      },
      {
        name: 'Timezone selector',
        url: '/timezone-selector',
        selector: 'glxy-timezone-selector',
        codeDone: Status.Done,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Tooltip',
        url: '/tooltip',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Transcript',
        url: '/transcript',
        selector: 'glxy-transcript',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
    ],
  },
  {
    groupName: 'Default Pages',
    links: [
      {
        name: 'Page Not Found - 404',
        url: '/page-not-found-404',
        selector: 'glxy-page-not-found-404',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'No Access - Unauthorized',
        url: '/page-no-access-unauthorized',
        selector: 'glxy-page-no-access-unauthorized',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
    ],
  },
  {
    groupName: 'Table (Beta)',
    links: [
      {
        name: 'Table',
        url: '/table',
        selector: 'glxy-table-container',
        codeDone: Status.Done,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Paginated API',
        url: '/table/paginated-api',
      },
      {
        name: 'Datasource',
        url: '/table/datasource',
      },
      {
        name: 'Selection',
        url: '/table/selection',
      },
    ],
  },
  {
    groupName: 'Form fields and inputs',
    links: [
      {
        name: 'Using form fields and inputs',
        url: '/input/using-inputs',
        selector: 'glxy-using-inputs',
      },
      {
        name: 'Form field',
        url: '/form-field',
        selector: 'glxy-form-field',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Currency field',
        url: '/input/currency-field',
        selector: 'glxy-currency-field',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Password input',
        url: '/input/password',
        selector: 'glxy-password-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Search select input',
        url: '/input/search-select',
        selector: 'glxy-search-select-input',
        codeDone: Status.Partial,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Rich text editor (Beta)',
        url: '/rich-text-editor',
        selector: 'glxy-rich-text-editor',
        codeDone: Status.Done,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Tags',
        url: '/tags',
        selector: 'glxy-tags-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
    ],
  },
  {
    groupName: 'Navigation',
    links: [
      {
        name: 'Nav',
        url: '/nav',
        selector: 'glxy-nav',
        codeDone: Status.Done,
        designDone: Status.Partial,
        guideDone: Status.Done,
      },
      {
        name: 'In-page nav (Beta)',
        url: '/page-nav',
        selector: 'glxy-in-page-nav',
        codeDone: Status.Partial,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Nav layout',
        url: '/nav-layout',
        selector: '[multiple]',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Nav item',
        url: '/nav-layout/nav-item',
        selector: 'glxy-nav-item',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Nav item list',
        url: '/nav-layout/nav-item-list',
        selector: 'glxy-nav-item-list',
        codeDone: Status.Done,
        designDone: Status.Partial,
        guideDone: Status.Done,
      },
    ],
  },
  {
    groupName: 'Uploader',
    links: [
      {
        name: 'Uploader',
        url: '/uploader',
        selector: 'glxy-uploader',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Uploader file list',
        url: '/uploader/file-list',
        selector: 'glxy-uploader-list',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Uploader hidden drag/drop',
        url: '/uploader/file-drag-drop',
        selector: 'glxy-file-drag-drop',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Uploader interfaces',
        url: '/uploader/interfaces',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Image uploader',
        url: '/uploader/image',
        selector: 'glxy-image-uploader',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Image editor',
        url: '/uploader/image-editor',
        selector: 'glxy-image-editor',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'Image tile',
        url: '/uploader/image-tile',
        selector: 'glxy-image-upload-tile',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
      {
        name: 'Image list',
        url: '/uploader/image-list',
        selector: 'glxy-image-list',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Partial,
      },
    ],
  },
  {
    groupName: 'Pipes',
    links: [
      {
        name: 'Address pipe',
        url: '/address-pipe',
        selector: 'glxyAddress',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Async Status Pipe',
        url: '/async-status-pipe',
        selector: 'glxyAsyncStatus',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Currency pipe',
        url: '/currency-pipe',
        selector: 'glxyCurrency',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Date pipe',
        url: '/date-pipe',
        selector: 'glxyDate',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'EscapeHtml Pipe',
        url: '/escape-html-pipe',
        selector: 'glxyEscapeHtml',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'iTrustThisHtml Pipe',
        url: '/i-trust-this-html-pipe',
        selector: 'iTrustThisHtml',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'Ordinal Pipe',
        url: '/ordinal-pipe',
        selector: 'glxyOrdinal',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'TruncateUrlPipe',
        url: '/truncate-url-pipe',
        selector: 'glxy-truncate-url-pipe',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.ToDo,
      },
      {
        name: 'Markdown Pipe',
        url: '/markdown-pipe',
        selector: 'glxy-markdown-pipe',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
    ],
  },
  {
    groupName: 'Galaxy internal',
    links: [
      {
        name: 'Create new component',
        url: '/documentation/new-component',
      },
      {
        name: 'Create new doc page',
        url: '/documentation/new-doc-page',
      },
      {
        name: 'Code viewer',
        url: '/documentation/code-viewer',
        selector: 'obs-code-viewer',
      },
    ],
  },
  {
    groupName: 'Experimental',
    links: [
      {
        name: 'Infinite scroll trigger',
        url: '/experimental/infinite-scroll-trigger',
        selector: 'glxy-infinite-scroll-trigger',
        codeDone: Status.Done,
        designDone: Status.NotApplicable,
        guideDone: Status.Done,
      },
      {
        name: 'AI icon',
        url: '/ai-icon',
        selector: 'GalaxyAiIconService',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
      {
        name: 'AI Text Button',
        url: '/ai-text-button',
        selector: 'glxy-ai-text-button',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Confirmation Modal',
        url: '/confirmation-modal',
        selector: 'OpenConfirmationModalService',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Contact Info Card',
        url: '/contact-info-card',
        selector: 'glxy-contact-info-card',
        codeDone: Status.ToDo,
        designDone: Status.ToDo,
        guideDone: Status.ToDo,
      },
      {
        name: 'Container queries',
        url: '/experimental/container-query',
        selector: 'EXP__glxyContainerObserver',
      },
      {
        name: 'Galaxy resize',
        url: '/experimental/galaxy-resize',
        selector: 'glxy-resize',
      },
      {
        name: 'Galaxy side drawer',
        url: '/experimental/side-drawer',
        selector: 'glxy-side-drawer',
      },
      {
        name: 'Galaxy list',
        url: '/experimental/galaxy-list',
        selector: 'glxy-list',
        codeDone: Status.ToDo,
        designDone: Status.Partial,
        guideDone: Status.ToDo,
      },
      {
        name: 'Code Sample',
        url: '/code-sample',
        selector: 'glxy-code-sample',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
      },
    ],
  },
  {
    groupName: 'Deprecated - Stop use',
    links: [
      {
        name: 'Checkbox input',
        url: '/input/checkbox',
        selector: 'glxy-checkbox',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Currency input',
        url: '/input/currency',
        selector: 'glxy-currency-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Datepicker',
        url: '/datepicker',
        selector: 'glxy-datepicker',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.ToDo,
      },
      {
        name: 'Dense inputs',
        url: '/input/dense-inputs',
        selector: 'glxy-dense-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Email input',
        url: '/input/email',
        selector: 'glxy-email-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Galaxy wrap',
        url: '/experimental/galaxy-wrap',
        selector: 'EXP__glxy-wrap',
        deprecated: true,
      },
      {
        name: 'Phone input',
        url: '/input/phone',
        selector: 'glxy-phone-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Select input',
        url: '/input/select',
        selector: 'glxy-select-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
      {
        name: 'Text input',
        url: '/input/text',
        selector: 'glxy-input',
        codeDone: Status.Done,
        designDone: Status.Done,
        guideDone: Status.Done,
        deprecated: true,
      },
    ],
  },
];
