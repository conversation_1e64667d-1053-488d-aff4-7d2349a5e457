import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { SideDrawerDocComponent } from './closeable-side-drawer/side-drawer-doc.component';
import { ContainerQueryComponent } from './container-query/container-query.component';
import { GalaxyWrapDocComponent } from './galaxy-wrap/galaxy-wrap-doc.component';
import { ResizeComponent } from './resize/resize.component';

export const routes: Routes = [
  {
    path: 'container-query',
    component: ContainerQueryComponent,
  },
  {
    path: 'galaxy-wrap',
    component: GalaxyWrapDocComponent,
  },
  {
    path: 'galaxy-resize',
    component: ResizeComponent,
  },
  {
    path: 'side-drawer',
    component: SideDrawerDocComponent,
  },
  {
    path: 'galaxy-list',
    loadComponent: () => import('../list/list.component'),
  },
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'container-query',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ExperimentalRoutingModule {}
