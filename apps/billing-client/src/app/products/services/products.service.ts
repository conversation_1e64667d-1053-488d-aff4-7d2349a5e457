import { Injectable } from '@angular/core';

import { Observable, ReplaySubject } from 'rxjs';

import { Product, ProductType } from '../product';

@Injectable()
export class ProductsService {
  private products$$: ReplaySubject<Product[]> = new ReplaySubject<Product[]>(1);
  private total$$: ReplaySubject<number> = new ReplaySubject<number>(1);

  get products$(): Observable<Product[]> {
    return this.products$$.asObservable();
  }

  get total$(): Observable<number> {
    return this.total$$.asObservable();
  }

  listProducts(types: ProductType[], cursor = '', pageSize = 10): void {
    const start: number = this.decodeCursor(cursor);
    const end: number = start + pageSize;
    const filteredProducts: Product[] = this.stubbedProducts.filter((p) => types.includes(p.type));
    this.products$$.next(filteredProducts.slice(start, end));
    this.total$$.next(filteredProducts.length);
  }

  private decodeCursor(cursor: string): number {
    return Number(window.atob(cursor));
  }

  private get stubbedProducts(): Product[] {
    const initialList: Product[] = [
      {
        created: new Date(),
        updated: new Date(),
        sku: 'RM',
        name: 'Reputation Management',
        type: ProductType.VENDASTA,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'SM',
        name: 'Social Marketing',
        type: ProductType.VENDASTA,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'LD',
        name: 'Listing Distribution',
        type: ProductType.VENDASTA,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'WSP',
        name: 'Website Pro',
        type: ProductType.PRODUCT,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'LSP',
        name: 'Listing Sync Pro',
        type: ProductType.PRODUCT,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'MS',
        name: 'Listing Builder',
        type: ProductType.PRODUCT,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'professional_subscription',
        name: 'Professional Subscription',
        type: ProductType.SUBSCRIPTION,
      },
      {
        created: new Date(),
        updated: new Date(),
        sku: 'listing_bundle',
        name: 'Listing Bundle',
        type: ProductType.BUNDLE,
      },
    ];
    let finalList: Product[] = [];
    let reverse = true;
    for (let n = 0; n < 10; n += 1) {
      finalList = finalList.concat(reverse ? initialList.reverse() : initialList);
      reverse = !reverse;
    }
    return finalList;
  }
}
