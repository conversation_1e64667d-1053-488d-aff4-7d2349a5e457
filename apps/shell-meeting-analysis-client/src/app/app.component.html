<atlas-navbar [dropdownItems]="[]" [hideBottomBorder]="false">
  <div class="atlas-navbar-space">
    <atlas-item>
      <button mat-icon-button (click)="nav.toggle()">
        <mat-icon>menu</mat-icon>
      </button>
    </atlas-item>
    <div class="atlas-spacer"></div>
    <div class="atlas-actions">
      <atlas-item interactable icon="event" [matTooltip]="'SCHEDULE_BOT.JOIN_MEETING' | translate">
        <atlas-menu id="atlas-navbar__meetings-join">
          <meeting-analysis-schedule-bot></meeting-analysis-schedule-bot>
        </atlas-menu>
      </atlas-item>
    </div>
  </div>
</atlas-navbar>

<glxy-nav #nav [fixedTopGap]="40" appName="shell-meeting-client" class="glxy-nav--light-theme">
  <glxy-nav-panel style="width: 276px">
    <glxy-nav-header>
      <a routerLink="/">
        <div class="sidenav-header">
          <mat-icon inline style="font-size: 54px; opacity: 0.75">offline_bolt</mat-icon>
          <div class="sidenav-header-text">meeting-analysis</div>
        </div>
      </a>
    </glxy-nav-header>

    <glxy-nav-item route="/calls">Calls</glxy-nav-item>

    <glxy-nav-footer class="footer-actions">
      <button type="button" mat-stroked-button (click)="changeNamespace()">
        Change Namespace ({{ namespaceService.namespace$ | async }})
      </button>
      <button type="button" mat-stroked-button (click)="changeEnv()">Change Env ({{ currentEnv }})</button>
      <button type="button" mat-stroked-button (click)="changeLocalSession()">Change Session Token</button>
      <div>
        <a href="https://iam-demo.apigateway.co/" target="_blank">IAM Demo</a>
        |
        <a href="https://iam-prod.apigateway.co/" target="_blank">IAM Prod</a>
      </div>
    </glxy-nav-footer>
  </glxy-nav-panel>

  <router-outlet></router-outlet>
</glxy-nav>
