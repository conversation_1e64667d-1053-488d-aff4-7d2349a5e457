import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { inject, InjectionToken, NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { AiKnowledgeConfig, AiKnowledgeModule } from '@galaxy/ai-knowledge';
import { AuthInterceptor, EnvironmentService, SessionService } from '@galaxy/core';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyI18NModule } from '@vendasta/galaxy/i18n';
import { GalaxyNavModule } from '@vendasta/galaxy/nav';
import { GalaxyDefaultProviderOverrides } from '@vendasta/galaxy/provider-default-overrides';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AppComponent } from './app.component';
import { appRoutes } from './app.routes';
import { NamespaceService } from './namespace.service';
import { MatInputModule } from '@angular/material/input';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { of } from 'rxjs';
import { ApplicationControlsComponent } from './application-controls/application-controls.component';
import { PartnerServiceInterfaceToken } from '@galaxy/partner';
import { PartnerService } from './partner.service';

export const SHELL_AI_KNOWLEDGE = new InjectionToken<AiKnowledgeConfig>('[shell-ai-knowledge] config for reports', {
  providedIn: 'root',
  factory: () => {
    const environmentService = inject(EnvironmentService);
    return {
      partnerId$: inject(NamespaceService).partnerId$,
      marketId$: inject(NamespaceService).marketId$,
      accountGroupId$: inject(NamespaceService).accountGroupId$,
      manageKnowledgeUrl$: of('/knowledge'),
      businessProfileUrl$: of('/business-profile'),
      webChatWidgetEditRoute$: of(['', { outlets: { inbox: 'inbox/settings' } }]),
      showBusinessProfileSource$: of(true),

      getEnvironment: () => environmentService.getEnvironment(),
      sessionId$: inject(SessionService).getSessionId(),
    };
  },
});

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    GalaxyI18NModule,
    TranslateModule,
    LexiconModule.forRoot(),
    GalaxySnackbarModule,
    RouterModule.forRoot(appRoutes, {
      scrollPositionRestoration: 'top',
    }),
    AiKnowledgeModule.forRoot({
      config: SHELL_AI_KNOWLEDGE,
    }),
    GalaxyNavModule,
    GalaxyFormFieldModule,
    GalaxyAlertModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    CommonModule,
    ApplicationControlsComponent,
  ],
  providers: [
    ...GalaxyDefaultProviderOverrides,
    SnackbarService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: PartnerServiceInterfaceToken, // This is required by the AccountGroupService for some reason?
      useClass: PartnerService,
    },
  ],
})
export class AppModule {}
