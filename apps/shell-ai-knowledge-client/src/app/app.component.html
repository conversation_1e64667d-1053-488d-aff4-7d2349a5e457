<div class="top-nav-bar">
  <button mat-icon-button (click)="nav.toggle()">
    <mat-icon>menu</mat-icon>
  </button>
</div>

<glxy-nav #nav [fixedTopGap]="40" appName="knowledge" class="glxy-nav--light-theme">
  <glxy-nav-panel style="width: 276px">
    <glxy-nav-header>
      <div class="sidenav-header">
        <mat-icon inline style="font-size: 54px; opacity: 0.75">insights</mat-icon>
      </div>
    </glxy-nav-header>

    <glxy-nav-item [route]="'/knowledge'" [icon]="'bar_chart'" [activeExactRoute]="true"> AI Knowledge </glxy-nav-item>
    <glxy-nav-item [route]="'/application-controls'" [icon]="'assignment'" [activeExactRoute]="true">
      Application Controls
    </glxy-nav-item>

    <glxy-nav-footer>
      <ng-container *ngIf="showNoUserInfoAlert">
        <glxy-alert
          type="warning"
          [showClose]="true"
          [size]="'small'"
          [stackedView]="true"
          (close)="showNoUserInfoAlert = false"
          style="margin-bottom: 16px"
        >
          Add a Session Token to view AI Knowledge settings.
        </glxy-alert>
      </ng-container>

      <glxy-form-field size="small" bottomSpacing="small">
        <glxy-label>Partner ID</glxy-label>
        <input
          #partnerIdInput
          type="text"
          placeholder="ABC"
          matInput
          autocapitalize="characters"
          [value]="namespaceService.partnerId$ | async"
          (focus)="partnerIdInput.select()"
          (blur)="setPartnerId(partnerIdInput.value)"
          (keydown.enter)="partnerIdInput.blur(); reloadWindow()"
        />
      </glxy-form-field>

      <glxy-form-field size="small" bottomSpacing="small">
        <glxy-label>Market ID</glxy-label>
        <input
          #marketIdInput
          type="text"
          placeholder="default"
          matInput
          [value]="namespaceService.marketId$ | async"
          (focus)="marketIdInput.select()"
          (blur)="setMarketId(marketIdInput.value)"
          (keydown.enter)="marketIdInput.blur(); reloadWindow()"
        />
      </glxy-form-field>

      <glxy-form-field size="small" bottomSpacing="small">
        <glxy-label>Account Group ID</glxy-label>
        <input
          #accountGroupIdInput
          type="text"
          placeholder="AG-123"
          matInput
          autocapitalize="characters"
          [value]="namespaceService.accountGroupId$ | async"
          (focus)="accountGroupIdInput.select()"
          (blur)="setAccountGroupId(accountGroupIdInput.value)"
          (keydown.enter)="accountGroupIdInput.blur(); reloadWindow()"
        />
      </glxy-form-field>

      <glxy-form-field size="small" bottomSpacing="small">
        <glxy-label>Session Token</glxy-label>
        <textarea
          #sessionInput
          matInput
          placeholder=""
          [value]="getLocalSession()"
          (focus)="sessionInput.select()"
          (blur)="setLocalSession(sessionInput.value)"
          (keydown.enter)="sessionInput.blur(); reloadWindow()"
        ></textarea>
        <glxy-hint>Hit return key to reload with updated settings</glxy-hint>
      </glxy-form-field>
      <div style="text-align: center">
        <a href="https://iam-demo.apigateway.co/" target="_blank">IAM Demo</a>
        |
        <a href="https://iam-prod.apigateway.co/" target="_blank">IAM Prod</a>
      </div>
    </glxy-nav-footer>
  </glxy-nav-panel>

  <router-outlet></router-outlet>
</glxy-nav>
