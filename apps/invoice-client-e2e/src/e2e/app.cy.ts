describe('e2e using a unpaid Demo Invoice', () => {
  before(() => {
    cy.visit('invoice/SHAY/7608ca20-c6e4-4e57-b122-03157854207a');
  });

  it('test the header loads correctly', () => {
    //If we change the name of the partner, this will break.
    cy.contains('Invoice from Cloud Fire');
    cy.contains('Billed to Far away Caz');

    cy.contains('$82.59 due June 12, 2020');
    cy.contains('Invoice #000095');
  });

  // TODO: skipping because form action should not be called in e2e tests
  it.skip('tests that invoice has the payment form ', () => {
    cy.get('billing-payment-form > form > button').contains('Pay invoice').click();
    cy.get('.card-errors', { timeout: 500 }).should('be.visible');
    cy.contains(' Your card number is incomplete.');
  });

  it('tests that invoice shows its line items correctly', () => {
    cy.get('thead > tr > .description').contains('Description');
    cy.get('thead > tr > .quantity').contains('Quantity');
    cy.get('thead > tr > :nth-child(3)').contains('Price');
    cy.get('thead > tr > :nth-child(4)').contains('Tax');
    cy.get('thead > tr > :nth-child(5)').contains('Subtotal');

    // Bundle 1
    cy.get('tbody > .bundle-header > .bundle-name').contains('Great Bundle For Great Customers');
    cy.get('tbody > tr:nth-child(2) > .description').contains('Reputation Management Pro');
    cy.get('tbody > tr:nth-child(2) > .quantity').contains('1');
    cy.get('tbody > tr:nth-child(2) > :nth-child(3)').contains('$18.00');
    cy.get('tbody > tr:nth-child(2) > :nth-child(4)').contains('5%');
    cy.get('tbody > tr:nth-child(2) > :nth-child(5)').contains('$18.00');

    // Bundle 2
    cy.get('tbody > .bundle-header > .bundle-name').contains('An Awesome Bundle');
    //Item1
    cy.get('tbody > :nth-child(2) > .description').contains('Listing Builder');
    cy.get('tbody > :nth-child(2) > .quantity').contains('1');
    cy.get('tbody > :nth-child(2) > :nth-child(3)').contains('$0.00');
    cy.get('tbody > :nth-child(2) > :nth-child(5)').contains('$0.00');

    //Item 2
    cy.get('tbody > :nth-child(3) > .description').contains('Advertising Intelligence');
    cy.get('tbody > :nth-child(3) > .quantity').contains('1');
    cy.get('tbody > :nth-child(3) > :nth-child(3)').contains('$15.00');
    cy.get('tbody > :nth-child(3) > :nth-child(4)').contains('6%');
    cy.get('tbody > :nth-child(3) > :nth-child(5)').contains('$15.00');

    // Bundle 3
    cy.get('tbody > .bundle-header > .bundle-name').contains('The First Bundle');
    //Item 1
    cy.get('tbody > :nth-child(2) > .description').contains('Reputation Management Pro');
    cy.get('tbody > :nth-child(2) > .quantity').contains('1');
    cy.get('tbody > :nth-child(2) > :nth-child(3)').contains('$18.00');
    cy.get('tbody > :nth-child(2) > :nth-child(5)').contains('$18.00');

    //Item 2
    cy.get('tbody > :nth-child(3) > .description').contains('Social Marketing Pro');
    cy.get('tbody > :nth-child(3) > .quantity').contains('1');
    cy.get('tbody > :nth-child(3) > :nth-child(3)').contains('$22.00');
    cy.get('tbody > :nth-child(3) > :nth-child(5)').contains('$22.00');

    //Item without Bundle
    cy.get('tr').contains('Advertising Intelligence');
    cy.get('tr > .quantity').contains('1');
    cy.get('tr > :nth-child(3)').contains('$15.00');
    cy.get('tr > :nth-child(5)').contains('$15.00');
  });

  it('shows the totals correctly', () => {
    cy.get('tfoot > tr').contains('Subtotal');
    cy.get('tfoot > tr').contains('Total');
    cy.get('tfoot > tr').contains('Great Bundle For Great Customers Discount');
    cy.get('tfoot > tr').contains('An Awesome Bundle Discount');
    cy.get('tfoot > tr').contains('The First Bundle Discount');
    cy.get('tfoot > tr').contains('$88.00');
    cy.get('tfoot > tr').contains('-$0.90');
    cy.get('tfoot > tr').contains('-$0.90');
    cy.get('tfoot > tr').contains('-$5.30');
    cy.get('tfoot > tr').contains('$0.85');
    cy.get('tfoot > tr').contains('$0.84');
    cy.get('tfoot > tr').contains('$82.59');
  });
});

describe('e2e using a paid Demo Invoice', () => {
  it('tests that payment form shows the invoice is payed and lets the user download it', () => {
    cy.visit('/invoice/SHAY/9c45e008-f558-40a7-b407-8a27f7a0ad02');
    cy.get('.paid').contains('INVOICE PAID');
    cy.get('.status').contains('$82.59 on June 2, 2020');
    cy.get('.mat-menu-trigger').contains('Download as PDF').click();
    cy.get('.mat-menu-content > :nth-child(1)').contains('Download receipt');
    cy.get('.mat-menu-content > :nth-child(2)').contains('Download invoice');
  });
});

describe('e2e using an unpayable Demo Invoice', () => {
  it('tests that payment form shows the invoice due and does not display payment', () => {
    cy.visit('/invoice/EDKV/19250674-dd12-4bad-bde8-4ef54671099d');

    //If we change the name of the partner, this will break.
    cy.contains("Invoice from Mike's Guitars & Invoicing");
    cy.contains('Billed to Michaels');

    cy.contains('$15.00 due June 12, 2020');
    cy.contains('Invoice #000002');
  });

  it('tests that invoice shows its line items correctly', () => {
    cy.contains('Description')
      .parent('tr')
      .within(() => {
        cy.get('th').eq(1).contains('Quantity');
        cy.get('th').eq(2).contains('Price');
        cy.get('th').eq(3).contains('Tax');
        cy.get('th').eq(4).contains('Subtotal');
      });

    cy.contains('Advertising Intelligence')
      .parent('tr')
      .within(() => {
        cy.get('td').eq(0).contains('Advertising Intelligence');
        cy.get('td').eq(1).contains('1');
        cy.get('td').eq(2).contains('$15.00');
        cy.get('td').eq(4).contains('$15.00');
      });
  });

  it('shows the totals correctly', () => {
    cy.get('tfoot > tr').contains('Subtotal');
    cy.get('tfoot > tr').contains('Total');
    cy.get('tfoot > tr').contains('$15.00');
  });
});

describe('e2e using a Demo Invoice containing a bundle with hidden items', () => {
  before(() => {
    cy.visit('/invoice/T41P/82e61f7e-4736-45c6-b20f-fedac56bd790');
  });

  it('test the header loads correctly', () => {
    //If we change the name of the partner, this will break.
    cy.contains('Invoice from Splinter Demo');
    cy.contains("Billed to Evan's Complex");

    cy.contains('$279.95 on November 16, 2020');
    cy.contains('Invoice #000134');
  });

  it('tests that invoice shows its line items correctly', () => {
    cy.get('thead > tr > .description').contains('Description');
    cy.get('thead > tr > .quantity').contains('Quantity');
    cy.get('thead > tr > :nth-child(3)').contains('Price');
    cy.get('thead > tr > :nth-child(4)').contains('Tax');
    cy.get('thead > tr > :nth-child(5)').contains('Subtotal');

    // Bundle 1
    cy.get('tbody > tr > .bundle-name').contains('Package with hidden details');
    cy.get('tbody > tr > .quantity').contains('1');
    cy.get('tbody > tr > :nth-child(5)').contains('$115.00');

    // Bundle 2
    cy.get('tbody > .bundle-header > .bundle-name').contains('Package without hidden details');
    //Item1
    cy.get('tbody > tr:nth-child(2) > .description').contains('Website Pro');
    cy.get('tbody > tr:nth-child(2) > .quantity').contains('2');
    cy.get('tbody > tr:nth-child(2) > :nth-child(3)').contains('$50.00');
    cy.get('tbody > tr:nth-child(2) > :nth-child(4)').contains('7% + 5%');
    cy.get('tbody > tr:nth-child(2) > :nth-child(5)').contains('$100.00');

    //Item 2
    cy.get('tbody > tr:nth-child(3) > .description').contains('GoDaddy Domain');
    cy.get('tbody > tr:nth-child(3) > .quantity').contains('1');
    cy.get('tbody > tr:nth-child(3) > :nth-child(3)').contains('$15.00');
    cy.get('tbody > tr:nth-child(3) > :nth-child(4)').contains('6%');
    cy.get('tbody > tr:nth-child(3) > :nth-child(5)').contains('$15.00');
  });

  it('shows the totals correctly', () => {
    cy.get('tfoot > tr').contains('Subtotal');
    cy.get('tfoot > tr').contains('Total');
    cy.get('tfoot > tr').contains('$255.00');
    cy.get('tfoot > tr').contains('$6.90');
    cy.get('tfoot > tr').contains('$10.00');
    cy.get('tfoot > tr').contains('$8.05');
    cy.get('tfoot > tr').contains('Package with hidden details Discount').should('not.exist');
    cy.get('tfoot > tr').contains('Package without hidden details Discount').should('not.exist');
    cy.get('tfoot > tr').contains('-$0.00').should('not.exist');
    cy.get('tfoot > tr').contains('$279.95');
  });
});

describe('e2e using a Demo invoice containing bundles with and without discounts', () => {
  before(() => {
    cy.visit('invoice/T41P/d489e586-e4c5-4e6b-9d44-253a1dc2ccc2');
  });
  it('test the header loads correctly', () => {
    // If we change the name of the partner or account, this will break.
    cy.contains('Invoice from Splinter Demo');
    cy.contains("Billed to Nick's Barber");
    cy.contains('Invoice #000243');
    cy.contains('INVOICE PAID');
    cy.contains('$243.20 on April 12, 2021');
    cy.contains('Download as PDF');
  });

  it('tests the invoice shows its line items correctly', () => {
    // Tests the invoice header is displayed correctly.
    cy.get('thead > tr > .description').contains('Description');
    cy.get('thead > tr > .quantity').contains('Quantity');
    cy.get('thead > tr > :nth-child(3)').contains('Price');
    cy.get('thead > tr > :nth-child(4)').contains('Tax');
    cy.get('thead > tr > :nth-child(5)').contains('Subtotal');

    // Tests the first bundle is displayed correctly.
    cy.get('.bundle-grouping > .bundle-header > .bundle-name').contains('Upgrade Test Package 1');

    cy.get('tbody')
      .contains('Customer Voice Pro')
      .parent('tr')
      .within(() => {
        cy.get('.description').contains('Customer Voice Pro');
        cy.get('.quantity').contains('3');
        cy.get('td').eq(2).contains('$55.00');
        cy.get('td').eq(4).contains('$165.00');
      });

    // Tests the second bundle is displayed correctly.
    cy.get('.bundle-grouping > .bundle-header > .bundle-name').contains('Upgrade Test Package 2');

    cy.get('tbody')
      .contains('Social Marketing Pro')
      .parent('tr')
      .within(() => {
        cy.get('.description').contains('Social Marketing Pro');
        cy.get('.quantity').contains('5');
        cy.get('td').eq(2).contains('$30.00');
        cy.get('td').eq(4).contains('$150.00');
      });

    // Tests the third bundle is displayed correctly
    cy.get('tbody')
      .contains('Upgrade Test Package 3')
      .parent('tr')
      .within(() => {
        cy.get('.quantity').contains('1');
        cy.get('td').eq(2).contains('$110.00');
        cy.get('td').eq(4).contains('$110.00');
      });
  });

  it('test the invoice shows its totals and discounts correctly', () => {
    cy.get('tfoot > tr > td').contains('Subtotal');
    cy.get('tfoot > tr > td').contains('$425.00');
    cy.get('tfoot > tr > td').contains('Upgrade Test Package 1 Discount');
    cy.get('tfoot > tr > td').contains('-$151.80');
    cy.get('tfoot > tr > td').contains('Upgrade Test Package 2 Discount');
    cy.get('tfoot > tr > td').contains('-$30.00');
    cy.get('tfoot > tr > td').contains('Upgrade Test Package 3 Discount').should('not.exist');
    cy.get('tfoot > tr > td').contains('-$0.00').should('not.exist');
    cy.get('tfoot > tr > td').contains('Total');
    cy.get('tfoot > tr > td').contains('$243.20');
  });
});
