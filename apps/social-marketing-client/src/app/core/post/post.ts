import moment from 'moment';
import { v4 as uuidv4 } from 'uuid';
import { DEFAULT_PROFILE_IMAGE, GMB_ACTION_TYPES } from '../../composer/constants';
import { Draft, ServiceData } from './draft';
import { PostType, PostingStatus } from '@vendasta/social-posts';
import { GmbCtaOptions, GmbEventOptions } from '../../composer/models/gmb-options';
import { MediaEntry, MetaData } from '@vendasta/composer';
import * as uuid from 'uuid';
import { Media } from '../../composer/interfaces';
import { MediaEntryInterface } from '@vendasta/composer/public_api';
import { YoutubePostCustomization } from '../../composer/models/youtube-options';
import { TiktokPostCustomization } from '../../composer/models/tiktok-option.model';
import { BlogPostCustomization } from '../../composer/models/blog-post-option.model';
import { WorkflowType } from '../../composer/post';

export const GMB_LABEL_ACTION_MAP = {
  LEARN_MORE: 'POSTS.GMB_CTA.LEARN_MORE',
  BOOK: 'POSTS.GMB_CTA.BOOK',
  ORDER: 'POSTS.GMB_CTA.ORDER',
  SHOP: 'POSTS.GMB_CTA.SHOP',
  SIGN_UP: 'POSTS.GMB_CTA.SIGN_UP',
  GET_OFFER: 'POSTS.GMB_CTA.GET_OFFER',
  CALL: 'POSTS.GMB_CTA.CALL',
};

export enum AIInstruction {
  BOTH = 'BOTH',
  SOCIAL_INSTRUCTION = 'SOCIAL_INSTRUCTION',
  BLOG_INSTRUCTION = 'BLOG_INSTRUCTION',
}

export enum SocialServiceName {
  TWITTER = 'TW_USER',
  INSTAGRAM = 'IG_USER',
  GOOGLE_MY_BUSINESS = 'GMB_LOCATION',
  GMB = 'GMB',
  FACEBOOK = 'FB_PAGE',
  LINKEDIN = 'LI_USER',
  LINKEDIN_COMPANY = 'LI_COMPANY',
  PINTEREST = 'pinterest',
  CURATED_CONTENT = 'CURATED_CONTENT',
  YOUTUBE_CHANNEL = 'YT_CHANNEL',
  TIKTOK_ACCOUNT = 'TK_ACCOUNT',
  WORDPRESS_BLOG = 'WP_BLOG',
}

export enum SocialServiceType {
  TWITTER = 'twitter',
  INSTAGRAM = 'instagram',
  GOOGLE_MY_BUSINESS = 'googlemybusiness',
  FACEBOOK = 'facebook',
  LINKEDIN = 'linkedin',
  PINTEREST = 'pinterest',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  WORDPRESS = 'wordpress',
}

export enum SocialServiceTypeCommentCharacterLimit {
  TWITTER = 280,
  INSTAGRAM = 2200,
  GOOGLE_MY_BUSINESS = 1500,
  FACEBOOK = 8000,
  LINKEDIN = 2000,
  PINTEREST = -1,
}

export enum PostErrorCategory {
  DEFAULT = 'default',
  PERMANENT_FAILURE = 'permanent_failure',
  IMAGE_SIZE_INVALID = 'image_size_invalid',
  POST_LENGTH_INVALID = 'post_length_invalid',
  AUTH_INVALID = 'auth_invalid',
  INSUFFICIENT_PERMISSIONS = 'insufficient_permissions',
}

export enum PostTag {
  SCHEDULED = 'scheduled',
  PENDING = 'pending',
  ROOT = 'root',
  POSTED = 'posted',
  ACTION_REQUIRED = 'action-required',
  REPLIED = 'replied',
  REPLIED_AS_MENTION = 'replied-as-mention',
  DISMISSED = 'dismissed',
}

export interface InstagramReplyResponse {
  replies: Reply[];
  cursor: string;
  hasMore: boolean;
}

export class Comment {
  commentText = '';
  createdDateTime: Date;
  profileImageUrl = '';
  hasReplies = false;
  commentId = '';
  commenterName = '';

  constructor(data: CommentData) {
    Object.assign(this, data);
    if (data.createdDateTime) {
      this.createdDateTime = new Date(data.createdDateTime);
    }
  }

  get postedSinceNow(): string {
    return moment(this.createdDateTime).fromNow();
  }
}

export type Reply = Comment;

export const regex = /<\/?([a-z][a-z0-9]*)[^>]*?(\/?)>/gi;

export interface CommentsResponse {
  comments: Comment[];
  cursor: string;
  hasMore: boolean;
}

export class SocialPost {
  constructor(postData: any) {
    Object.assign(this, postData);

    if (postData.comments) {
      for (let i = 0; i < postData.comments.length; i++) {
        this.comments[i] = new Comment(postData.comments[i]);
      }
    }
    this.scheduledDateTime = new Date(postData.scheduledDateTime);
    this.lastUpdateDateTime = new Date(postData.lastUpdateDateTime);
    const postCreatedMoment = moment(this.postCreatedDateTime || postData.posted || null);
    const scheduledMoment = this.scheduledDateTime ? moment(this.scheduledDateTime) : null;
    const lastUpdateMoment = this.lastUpdateDateTime ? moment(this.lastUpdateDateTime) : null;
    this.readablePostedDate = postCreatedMoment.format('MMM DD, YYYY');
    this.readablePostedTime = postCreatedMoment.format('h:mm a');
    this.readablePostedDateTime = postCreatedMoment.format('MMM DD, YYYY [at] h:mm a');
    this.readableScheduledDate = scheduledMoment.format('MMM DD, YYYY');
    this.readableScheduledTime = scheduledMoment.format('h:mm a');
    this.readableScheduledDateTime = scheduledMoment.format('MMM DD, YYYY [at] h:mm a');
    this.readableLastUpdateDateTime = lastUpdateMoment.format('MMM DD, YYYY [at] h:mm a');
    this.isScheduled = new Date(this.scheduledDateTime) > new Date();
    this.service = postData.service || SocialPost.getServiceNameFromServiceId(postData.ssid);
    this.serviceType = this.getServiceType();
    //Below validation to avoid displaying invalid date time in calendar day click dialog box.
    if (this.readablePostedDateTime === 'Invalid date' && !this.isScheduled) {
      this.readablePostedDateTime = moment(this.scheduledDateTime).format('MMM DD, YYYY [at] h:mm a');
      this.readablePostedDate = moment(this.scheduledDateTime).format('MMM DD, YYYY');
      this.readablePostedTime = moment(this.scheduledDateTime).format('h:mm a');
    }

    if (this.event) {
      const eventStartMoment = moment(this.event.startDateTime);
      const eventEndMoment = moment(this.event.endDateTime);
      this.gmbEventStartDate = eventStartMoment.format('MMM Do, YYYY h:mm a');
      this.gmbEventEndDate = eventEndMoment.format('MMM Do, YYYY h:mm a');
      this.gmbEventStartDateShort = eventStartMoment.format('MMM D');
      this.gmbEventEndDateShort = eventEndMoment.format('MMM D');
    }

    if (this.callToAction) {
      if (this.callToAction.type in GMB_LABEL_ACTION_MAP) {
        this.gmbCallToActionLabel = GMB_LABEL_ACTION_MAP[this.callToAction.type];
      } else {
        this.gmbCallToActionLabel = this.callToAction.type;
      }
    }
    this.isGif = !!(
      this.service === SocialServiceName.FACEBOOK &&
      this.videoUrl &&
      this.videoUrl.indexOf('video') === -1
    );

    this.displayImages = SocialPost.setDisplayImages(postData);
    this.serviceIcon = serviceIconPath(this.service);
    this.profileImageUrl = postData.profileImageUrl || DEFAULT_PROFILE_IMAGE;
    this.title = postData.title;

    switch (this.service) {
      case SocialServiceName.FACEBOOK:
        this.serviceName = 'Facebook';
        break;
      case SocialServiceName.TWITTER:
        this.serviceName = 'Twitter';
        break;
      case SocialServiceName.GOOGLE_MY_BUSINESS:
      case SocialServiceName.GMB:
        this.serviceName = 'Google My Business';
        break;
      case SocialServiceName.INSTAGRAM:
        this.serviceName = 'Instagram';
        break;
      case SocialServiceName.LINKEDIN:
        this.serviceName = 'LinkedIn';
        break;
      case SocialServiceName.LINKEDIN_COMPANY:
        this.serviceName = 'LinkedIn Page';
        break;
      case SocialServiceName.CURATED_CONTENT:
        this.serviceName = 'Custom Feed';
        break;
      case SocialServiceName.WORDPRESS_BLOG:
        this.serviceName = 'Wordpress Blog';
        break;
      case SocialServiceName.YOUTUBE_CHANNEL:
        this.serviceName = 'YouTube';
        break;
      case SocialServiceName.TIKTOK_ACCOUNT:
        this.serviceName = 'TikTok';
        break;
      default:
        this.serviceName = '';
    }

    if (this.errorCategory) {
      if (this.errorCategory === PostErrorCategory.PERMANENT_FAILURE) {
        this.errorMessage = 'POSTS.ERROR_MESSAGE.PERMANENT_FAILURE';
      } else if (this.errorCategory === PostErrorCategory.DEFAULT) {
        this.errorMessage = 'POSTS.ERROR_MESSAGE.DEFAULT_ERROR_CATEGORY';
      } else if (this.errorCategory === PostErrorCategory.INSUFFICIENT_PERMISSIONS) {
        this.errorMessage = 'POSTS.ERROR_MESSAGE.INSUFFICIENT_PERMISSIONS';
      } else if (!this.errorCategory) {
        this.errorMessage = null;
      } else {
        this.errorMessage = 'POSTS.ERROR_MESSAGE.DEFAULT';
      }

      if (this.errorCategory === PostErrorCategory.IMAGE_SIZE_INVALID) {
        this.callToActionText = 'POSTS.CALL_TO_ACTION.IMAGE_SIZE_INVALID';
      } else if (this.errorCategory === PostErrorCategory.POST_LENGTH_INVALID) {
        this.callToActionText = 'POSTS.CALL_TO_ACTION.POST_LENGTH_INVALID';
      } else if (this.errorCategory === PostErrorCategory.AUTH_INVALID) {
        this.callToActionText = 'POSTS.CALL_TO_ACTION.AUTH_INVALID';
      } else if (this.errorCategory === PostErrorCategory.PERMANENT_FAILURE) {
        this.callToActionText = 'POSTS.CALL_TO_ACTION.PERMANENT_FAILURE';
      } else {
        this.callToActionText = 'POSTS.CALL_TO_ACTION.RETRY';
      }
    } else if (this.errors) {
      this.errorMessage = this.errors;
      this.callToActionText = 'POSTS.CALL_TO_ACTION.RETRY';
    }

    this.status = postData.status;
    this.youtubeCustomization = postData.youtubeCustomization;
    this.tiktokCustomization = postData.tiktokCustomization;
    this.blogPostCustomization = postData.blogPostCustomization;
    this.parentId = postData.parentId;
  }

  get timeSincePosted(): string {
    return moment(this.postCreatedDateTime).fromNow();
  }

  get errorHeader(): string | null {
    if (this.isError) {
      return SocialPost.headers[this.errors] || '';
    }
    return null;
  }

  get userFriendlyErrors(): string[] {
    if (this.isError) {
      const errorsArray = SocialPost.error_details[this.errors] || [this.errors];
      return errorsArray.map((error) => this.removeHTMLTags(error));
    }
    return [];
  }

  // remove the html tags in the error itself so that it could be displayed in standard form
  private removeHTMLTags(input: string): string {
    if (!input) {
      return '';
    }
    const cleanText = input.replace(regex, '');
    return this.cleanText(cleanText);
  }

  // clean the message by removing trailing spaces, multiple new line characters and multiple spaces
  private cleanText(text: string): string {
    text = text.trim();
    text = text.replace(/\s+/g, ' ');
    return text;
  }

  get containsImage(): boolean {
    return this.mediaEntries?.length === 1 || !!this.imageUrls || !!this.imageUrl || !!this.linkPreviewImageUrl;
  }

  static readonly headers = {
    'Avoid duplicate posts, check your Facebook page to confirm that this post was published before reposting':
      'This post may not have been published',
    "The business category you've selected isn't eligible to create or update local posts.":
      'Creating/Updating a local post is not authorized for this location',
    "Google My Business couldn't find what you're looking for.":
      "Google My Business couldn't find what you're looking for.",
    'There was an issue when trying to publish this post. Retry to finish posting.': 'We couldn’t publish this post',
    'Error: Media upload has failed with error code 2207026': 'Failed to upload video',
  };

  static readonly error_details = {
    "The business category you've selected isn't eligible to create or update local posts.": [
      'This error occurs if one of the following:',
      '1. Your location is not longer verified and must be re-verified',
      '2. You are connected to a location that is considered part of a chain (location has 10 or more listings for the business or brand)',
    ],
    "Google My Business couldn't find what you're looking for.": [
      "This error occurs when you don't have access to the Google Business Profile (invalid access token or user access has been revoked etc). " +
        'Please make sure you have claimed Google Business Profile. ' +
        'Then re-authenticate by login to your Google Business Profile, and try reconnecting your account again',
    ],
    'Error: Media upload has failed with error code 2207026': [
      "There was a problem uploading your video to Instagram. Ensure that your file follows Instagram’s <a href='https://help.instagram.com/****************' target='_blank'>video specifications</a> and try re-uploading the file before publishing.",
      'Instagram Error Code 2207026',
    ],
  };
  name: string;
  username: string;
  postId: string;
  draftId: string;
  itemId: string; // a unique-id regardless of post or draft to differentiate items with same draftId
  brandId: string;
  multilocationPostId: string;
  title: string;
  postText: string;
  postCreatedDateTime: Date;
  lastUpdateDateTime: Date;
  scheduledDateTime: Date;
  postedByOwner: boolean;
  mediaEntries: MediaEntryInterface[];
  metaData: MetaData[];

  permalink: string;

  displayImages?: string[];
  imageUrl: string;
  imageUrls: string[];
  linkPreviewImageUrl?: string;

  videoUrl: string;
  imagePath?: string;
  imageSize?: string;
  profileImageUrl: string;
  profileUrl: string;
  ssid: string;
  handle: string;

  service: SocialServiceName;
  serviceName?: string;
  serviceIcon?: string;
  serviceType: string;
  postType: PostType;
  customerPostType: string;
  postContextText: string;
  replyToPostId: string;
  tags: PostTag[];
  workFlowType?: WorkflowType;

  comments: Comment[];
  moreCommentsCursor: string;
  errors: any;
  isError: boolean;
  errorCategory: PostErrorCategory;
  callToActionText?: string;
  errorMessage?: string;

  callToAction: {
    url: string;
    type: string;
  };
  event: {
    endDateTime: string;
    startDateTime: string;
    title: string;
  };

  status: PostingStatus;

  readablePostedDate = '';
  readablePostedTime = '';
  readablePostedDateTime = '';
  readableScheduledDate = '';
  readableScheduledTime = '';
  readableScheduledDateTime = '';
  readableLastUpdateDateTime = '';
  gmbEventStartDate = '';
  gmbEventEndDate = '';
  gmbEventStartDateShort = '';
  gmbEventEndDateShort = '';
  gmbCallToActionLabel = '';

  showDelete = false;
  isScheduled = false;
  isGif = false;
  isDraft = false;
  isHidden = false;

  readMoreExpanded = false;
  youtubeCustomization: YoutubePostCustomization;
  tiktokCustomization: TiktokPostCustomization;
  blogPostCustomization: BlogPostCustomization;
  parentId: string;

  static setDisplayImages(post: any): string[] {
    if (!post) {
      return null;
    }

    if (post.isGif) {
      return [post.videoUrl];
    } else if (!!post.imageUrls && post.imageUrls.length > 0) {
      let imagesToDisplay = [];
      post.imageUrls.forEach((imageUrl) => {
        if (imageUrl) {
          imagesToDisplay.push(imageUrl);
        }
      });
      imagesToDisplay = post.imageUrl && !imagesToDisplay.length ? [post.imageUrl] : imagesToDisplay;
      return imagesToDisplay;
    } else if (post.imageUrl) {
      return [post.imageUrl];
    } else {
      return post.linkPreviewImageUrl ? [post.linkPreviewImageUrl] : null;
    }
  }

  static fromSubmitPostResponse(response: any): SocialPost {
    const keyMap = {
      blogPostCustomization: response?.blogPostCustomization,
      imageUrl: response.imageUrl,
      imageUrls: response.imageUrls,
      videoUrl: response.videoUrl,
      postText: response.postText,
      postCreatedDateTime: response.postCreatedDateTime ? new Date(response.postCreatedDateTime) : null,
      scheduledDateTime: new Date(response.scheduledDateTime),
      postId: response.socialPostId,
      ssid: response.ssid,
      service: this.getServiceNameFromServiceId(response.ssid),
      username: response.username,
      linkPreviewImageUrl: response.linkPreviewImageUrl,
      brandId: response.brandId,
      mediaEntries: response.mediaEntries,
      title: response.title,
    };
    if (
      keyMap.service === SocialServiceName.TWITTER ||
      keyMap.service === SocialServiceName.INSTAGRAM ||
      keyMap.service === SocialServiceName.TIKTOK_ACCOUNT
    ) {
      keyMap['handle'] = `@${response.username}`;
    }

    if (keyMap.service === SocialServiceName.YOUTUBE_CHANNEL) {
      keyMap['handle'] = `${response.username}`;
    }
    if (keyMap.service === SocialServiceName.FACEBOOK) {
      keyMap['name'] = response.username;
    } else {
      keyMap['name'] = response.name;
    }

    if (keyMap.service === SocialServiceName.GMB || keyMap.service === SocialServiceName.GOOGLE_MY_BUSINESS) {
      if (response.gmbPostCustomization) {
        if (response.gmbPostCustomization.ctaType) {
          keyMap['callToAction'] = {
            url: response.gmbPostCustomization.linkUrl,
            type: response.gmbPostCustomization.ctaType,
          };
        }
        if (response.gmbPostCustomization.title) {
          keyMap['event'] = {
            title: response.gmbPostCustomization.title,
            startDateTime: response.gmbPostCustomization.eventStart,
            endDateTime: response.gmbPostCustomization.eventEnd,
          };
        }
      }
    }
    return new SocialPost(keyMap);
  }

  static fromFetchPostResponse(response: any): SocialPost {
    const keyMap = {
      blogPostCustomization: response?.blogPostCustomization,
      imageUrl: response.imageUrl,
      imageUrls: response.imageUrls,
      videoUrl: response.videoUrl,
      postText: response.postText,
      postCreatedDateTime: response.status === 'COMPLETED' ? response.postCreatedDateTime : null,
      scheduledDateTime: new Date(response.scheduledDateTime),
      postId: response.socialPostId,
      ssid: response.ssid,
      service: this.getServiceNameFromServiceId(response.ssid),
      username: response.username,
      linkPreviewImageUrl: response.linkPreviewImageUrl,
      brandId: response.brandId,
      title: response.title,
      postType: response.postType,
    };

    if (
      keyMap.service === SocialServiceName.TWITTER ||
      keyMap.service === SocialServiceName.INSTAGRAM ||
      keyMap.service === SocialServiceName.TIKTOK_ACCOUNT
    ) {
      keyMap['handle'] = `@${response.username}`;
    }
    if (keyMap.service === SocialServiceName.YOUTUBE_CHANNEL) {
      keyMap['handle'] = `${response.username}`;
    }
    if (keyMap.service === SocialServiceName.FACEBOOK) {
      keyMap['name'] = response.username;
    } else {
      keyMap['name'] = response.name;
    }

    if (response.callToAction) {
      keyMap['callToAction'] = {
        url: response.callToAction.googleMyBusinessCallToActionUrl,
        type: response.callToAction.googleMyBusinessCallToActionType,
      };
    }
    if (response.event) {
      keyMap['event'] = {
        startDateTime: response.event.googleMyBusinessEventStartDateTime,
        endDateTime: response.event.googleMyBusinessEventEndDateTime,
        title: response.event.googleMyBusinessEventTitle,
      };
    }
    return new SocialPost(keyMap);
  }

  static fromSocialPostsSDK(rawPost: any): SocialPost {
    function extractValidMedias(mediaEntries) {
      return mediaEntries ? mediaEntries.filter((media) => !!media.mediaType && !!media.mediaUrl) : [];
    }

    const keyMap = {
      title: rawPost.title,
      postText: rawPost.postText,
      imageUrl: rawPost.imageUrl || null,
      imageUrls: rawPost.imageUrls || null,
      videoUrl: rawPost.videoUrl || null,
      postCreatedDateTime: rawPost.posted > new Date(1970, 0) ? rawPost.posted : null,
      scheduledDateTime: rawPost.scheduled || null,
      postId: rawPost.socialPostId,
      ssid: rawPost.socialServiceId,
      service: SocialPost.getServiceNameFromServiceId(rawPost.socialServiceId),
      username: rawPost.username,
      profileUrl: rawPost.profileUrl,
      profileImageUrl: rawPost.profileImageUrl,
      permalink: rawPost.permalink ? rawPost.permalink : null,
      isError: rawPost.isError || false,
      errors: rawPost.error ? rawPost.error.reason : null,
      errorCategory: rawPost.error ? rawPost.error.category : null,
      linkPreviewImageUrl: rawPost.linkPreviewImageUrl ? rawPost.linkPreviewImageUrl : null,
      brandId: rawPost.brandId ? rawPost.brandId : null,
      multilocationPostId: rawPost.multilocationPostId ? rawPost.multilocationPostId : null,
      mediaEntries: rawPost?.mediaEntries ? extractValidMedias(rawPost.mediaEntries) : [],
      metaData: rawPost?.metaData ? rawPost.metaData : [],
      postType: rawPost?.postType ? rawPost.postType : PostType.POST_TYPE_INVALID,
      customerPostType: rawPost?.customerPostType ? rawPost.customerPostType : 'POST_TYPE_INVALID',
      youtubeCustomization: YoutubePostCustomization.fromSdkSocialPost(rawPost),
      tiktokCustomization: TiktokPostCustomization.fromSdkSocialPost(rawPost),
      parentId: rawPost?.parentId,
      blogPostCustomization: BlogPostCustomization.fromSdkSocialPost(rawPost),
    };
    if (
      keyMap.service === SocialServiceName.TWITTER ||
      keyMap.service === SocialServiceName.INSTAGRAM ||
      keyMap.service === SocialServiceName.TIKTOK_ACCOUNT
    ) {
      keyMap['handle'] = `@${rawPost.username}`;
    }
    if (keyMap.service === SocialServiceName.YOUTUBE_CHANNEL) {
      keyMap['handle'] = `${rawPost.username}`;
    }
    if (keyMap.service === SocialServiceName.FACEBOOK) {
      keyMap['name'] = rawPost.username;
    } else {
      keyMap['name'] = rawPost.name;
    }
    if (keyMap.service === SocialServiceName.GOOGLE_MY_BUSINESS) {
      if (rawPost.callToAction) {
        rawPost.callToAction.type = GMB_ACTION_TYPES[rawPost.callToAction.actionType];
        if (!rawPost.callToAction.type && !!rawPost.callToAction.url) {
          // Fill in Learn More CTA, as it's indexed as CTA 0 and gets lost during the API call.
          rawPost.callToAction.type = GMB_ACTION_TYPES[0];
        }

        keyMap['callToAction'] = rawPost.callToAction;
      }
      if (rawPost.event && rawPost.event.startDatetime >= new Date(1970, 0)) {
        rawPost.event.startDateTime = rawPost.event.startDatetime;
        rawPost.event.endDateTime = rawPost.event.endDatetime;
        keyMap['event'] = rawPost.event;
      }
    }
    keyMap['status'] = rawPost.status;
    return new SocialPost(keyMap);
  }

  static fromDraft(draft: Draft, itemId?: string, serviceData?: ServiceData): SocialPost {
    const keyMap = {
      postText: draft.post_text,
      imageUrl: null,
      imageUrls: SocialPost.getDraftImageUrls(draft),
      videoUrl: SocialPost.getDraftVideoUrl(draft),
      postCreatedDateTime: draft.created,
      scheduledDateTime: draft.post_date_time,
      lastUpdateDateTime: draft.updated,
      postId: '',
      draftId: draft.draft_id,
      itemId: itemId ?? uuidv4(),
      ssid: serviceData?.serviceId,
      service: serviceData?.serviceShortName,
      username: serviceData?.userName,
      profileUrl: serviceData?.profileUrl,
      profileImageUrl: serviceData?.profileImageUrl,
      permalink: null,
      isError: false,
      errors: null,
      errorCategory: null,
      linkPreviewImageUrl: draft.previews,
      brandId: null,
      multilocationPostId: null,
      isScheduled: SocialPost.isScheduled(draft.post_date_time, new Date(draft.created)),
      mediaEntries: SocialPost.getMediaEntriesFromDraft(draft.media),
      metaData: draft?.metaData || [],
      isDraft: true,
      title: draft?.youtubeCustomization?.title || '',
      youtubeCustomization: draft?.youtubeCustomization,
      tiktokCustomization: draft?.tiktokCustomization,
      blogPostCustomization: draft?.blogPostCustomization,
      postType: draft?.postType || PostType.POST_TYPE_INVALID,
      isHidden: draft?.isHidden || false,
    };
    if (
      keyMap.service === SocialServiceName.TWITTER ||
      keyMap.service === SocialServiceName.INSTAGRAM ||
      keyMap.service === SocialServiceName.TIKTOK_ACCOUNT
    ) {
      keyMap['handle'] = `@${serviceData.userName}`;
    }
    if (keyMap.service === SocialServiceName.YOUTUBE_CHANNEL) {
      keyMap['handle'] = `${serviceData.userName}`;
    }
    if (keyMap.service === SocialServiceName.FACEBOOK) {
      keyMap['name'] = serviceData.userName;
    }
    if (keyMap.service === SocialServiceName.GMB || keyMap.service === SocialServiceName.GOOGLE_MY_BUSINESS) {
      if (draft.gmb_post_customization) {
        if (draft.gmb_post_customization?.makeEvent) {
          const {
            title: eventTitle,
            startDate: eventStart,
            endDate: eventEnd,
          } = draft.gmb_post_customization?.eventOptions || <GmbEventOptions>{};

          keyMap['event'] = {
            title: eventTitle,
            startDateTime: eventStart,
            endDateTime: eventEnd,
          };
        }
        if (draft.gmb_post_customization?.addCta) {
          const { ctaUrl, action: ctaAction } = draft.gmb_post_customization?.ctaOptions || <GmbCtaOptions>{};
          keyMap['callToAction'] = {
            url: ctaUrl,
            type: !!ctaUrl && !ctaAction ? GMB_ACTION_TYPES[0] : ctaAction,
          };
        }
      }
    }
    return new SocialPost(keyMap);
  }

  static getDraftImageUrls(draft: any): string[] {
    if (draft.media && draft.media.length) {
      return draft.media.filter((m) => !!m.image_url).map((m) => m.image_url);
    }
    return [];
  }

  static getDraftVideoUrl(draft: any): string {
    if (draft.media && draft.media.length > 0) {
      const video = draft.media.find((m) => !!m.video_url);
      return video ? video.video_url : '';
    }
    return '';
  }

  static isScheduled(postDate: Date, createdDate: Date): boolean {
    // 2 = we are assuming the scheduled time should be at least 2 minutes after the created time
    const minutesApart = moment(postDate).diff(createdDate, 'm');
    return minutesApart > 2;
  }

  static getServiceNameFromServiceId(serviceId: string): SocialServiceName {
    if (serviceId) {
      switch (serviceId.split('-')[0]) {
        case 'FBP':
          return SocialServiceName.FACEBOOK;
        case 'LIU':
          return SocialServiceName.LINKEDIN;
        case 'LIC':
          return SocialServiceName.LINKEDIN_COMPANY;
        case 'TWU':
          return SocialServiceName.TWITTER;
        case 'IGU':
          return SocialServiceName.INSTAGRAM;
        case 'CCA':
          return SocialServiceName.CURATED_CONTENT;
        case 'YTC':
          return SocialServiceName.YOUTUBE_CHANNEL;
        case 'TKT':
          return SocialServiceName.TIKTOK_ACCOUNT;
        case 'WPW':
          return SocialServiceName.WORDPRESS_BLOG;
        default:
          return SocialServiceName.GOOGLE_MY_BUSINESS;
      }
    }
  }

  static getMediaEntriesFromDraft(media: Media[]): MediaEntry[] {
    return media
      ? media.map((m) => {
          let mediaType;
          if (m.video_url?.length > 0) {
            mediaType = m.video_url.indexOf('video') === -1 ? 'GIF' : 'VIDEO';
          } else {
            mediaType = 'IMAGE';
          }
          return new MediaEntry({
            mediaEntryId: uuid.v4(),
            mediaType: mediaType,
            mediaUrl: m.video_url || m.image_url,
          });
        })
      : [];
  }

  getServiceType(): string {
    switch (this.service) {
      case SocialServiceName.FACEBOOK:
        return SocialServiceType.FACEBOOK;
      case SocialServiceName.TWITTER:
        return SocialServiceType.TWITTER;
      case SocialServiceName.GOOGLE_MY_BUSINESS:
      case SocialServiceName.GMB:
        return SocialServiceType.GOOGLE_MY_BUSINESS;
      case SocialServiceName.INSTAGRAM:
        return SocialServiceType.INSTAGRAM;
      case SocialServiceName.LINKEDIN:
        return SocialServiceType.LINKEDIN;
      case SocialServiceName.TIKTOK_ACCOUNT:
        return SocialServiceType.TIKTOK;
      case SocialServiceName.WORDPRESS_BLOG:
        return SocialServiceType.WORDPRESS;
      default:
        return '';
    }
  }

  viewExternalService(): void {
    window.open(this.permalink, '_blank');
  }

  getImageUrls(): string[] {
    if (this.isGif) {
      return [this.videoUrl];
    } else if (!!this.imageUrls && this.imageUrls.length > 0) {
      const imageUrls = this.imageUrls.filter(Boolean);
      return imageUrls.length > 0 ? imageUrls : null;
    } else if (this.imageUrl) {
      return [this.imageUrl];
    } else {
      return this.linkPreviewImageUrl ? [this.linkPreviewImageUrl] : null;
    }
  }
}

export function serviceIconPath(service: string): string {
  // This is to make vstatic work. When vstatic supports assets, remove this
  const prefix = 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/social-icons/';
  switch (service) {
    case SocialServiceName.FACEBOOK:
      return `${prefix}facebook.png`;
    case SocialServiceName.TWITTER:
      return `${prefix}X.png`;
    case SocialServiceName.GOOGLE_MY_BUSINESS:
    case SocialServiceName.GMB:
      return `${prefix}google_my_business.png`;
    case SocialServiceName.LINKEDIN:
    case SocialServiceName.LINKEDIN_COMPANY:
      return `${prefix}linkedin.png`;
    case SocialServiceName.INSTAGRAM:
      return `${prefix}instagram.png`;
    case SocialServiceName.PINTEREST:
      return `${prefix}pinterest.png`;
    case 'REELS':
      return `${prefix}instagram-reel.png`;
    case SocialServiceName.CURATED_CONTENT:
      return `${prefix}build_circle.svg`;
    case SocialServiceName.YOUTUBE_CHANNEL:
      return `${prefix}youtube_round.png`;
    case SocialServiceName.TIKTOK_ACCOUNT:
      return `${prefix}tiktok_round.png`;
    case SocialServiceName.WORDPRESS_BLOG:
      return `${prefix}wordpress-logo.svg`;
    default:
      return '';
  }
}

export function serviceTypeDisplay(serviceType: string): string {
  switch (serviceType) {
    case SocialServiceName.FACEBOOK:
    case SocialServiceName.GOOGLE_MY_BUSINESS:
    case SocialServiceName.GMB:
    case SocialServiceName.LINKEDIN_COMPANY:
    case SocialServiceName.WORDPRESS_BLOG:
      return 'Page';
    case SocialServiceName.TWITTER:
    case SocialServiceName.LINKEDIN:
    case SocialServiceName.INSTAGRAM:
    case SocialServiceName.TIKTOK_ACCOUNT:
      return 'Account';
    case SocialServiceName.YOUTUBE_CHANNEL:
      return 'Channel';
    default:
      return '';
  }
}

export function serviceCommentCharacterLimit(serviceType: string): number {
  switch (serviceType) {
    case SocialServiceType.FACEBOOK:
      return SocialServiceTypeCommentCharacterLimit.FACEBOOK;
    case SocialServiceType.GOOGLE_MY_BUSINESS:
      return SocialServiceTypeCommentCharacterLimit.GOOGLE_MY_BUSINESS;
    case SocialServiceType.LINKEDIN:
      return SocialServiceTypeCommentCharacterLimit.LINKEDIN;
    case SocialServiceType.TWITTER:
      return SocialServiceTypeCommentCharacterLimit.TWITTER;
    case SocialServiceType.INSTAGRAM:
      return SocialServiceTypeCommentCharacterLimit.INSTAGRAM;
    default:
      return -1;
  }
}

export function serviceTypeFullName(serviceType: string): string {
  switch (serviceType) {
    case SocialServiceName.FACEBOOK:
      return 'FaceBook';
    case SocialServiceName.GOOGLE_MY_BUSINESS:
      return 'Google My Business Location';
    case SocialServiceName.GMB:
      return 'Google My Business';
    case SocialServiceName.LINKEDIN_COMPANY:
      return 'LinkedIn Page';
    case SocialServiceName.LINKEDIN:
      return 'LinkedIn';
    case SocialServiceName.TWITTER:
      return 'Twitter';
    case SocialServiceName.INSTAGRAM:
      return 'Instagram';
    case SocialServiceName.YOUTUBE_CHANNEL:
      return 'YouTube';
    case SocialServiceName.TIKTOK_ACCOUNT:
      return 'TikTok';
    case SocialServiceName.WORDPRESS_BLOG:
      return 'WordPress';
    default:
      return '';
  }
}

export interface CommentData {
  commentId: string;
  commentText: string;
  commenterName: string;
  createdDateTime: Date;
  hasReplies?: boolean;
  profileImageUrl?: string;
}

export interface PostMediaEntry {
  mediaEntryId?: string;
  mediaUrl?: string;
  mediaType?: string;
  containerId?: string;
  metaData?: PostMetaData[];
}

export interface PostMetaData {
  propertyName?: string;
  propertyValue?: string;
}
