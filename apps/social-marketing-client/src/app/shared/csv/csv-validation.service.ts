import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ValidationError } from './csv-model';

@Injectable({ providedIn: 'root' })
export class ValidationService {
  private errorsSubject = new BehaviorSubject<ValidationError[]>([]);
  errors$: Observable<ValidationError[]> = this.errorsSubject.asObservable();

  addError(error: ValidationError) {
    const currentErrors = this.errorsSubject.getValue();
    this.errorsSubject.next([...currentErrors, error]);
  }

  getErrors(): ValidationError[] {
    return this.errorsSubject.getValue();
  }

  hasErrors(): boolean {
    return this.errorsSubject.getValue().length > 0;
  }

  clearErrors() {
    this.errorsSubject.next([]);
  }
}
