@use 'design-tokens' as *;
:host {
  display: block;

  .error-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px 0 10px;
  }

  .error-table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      border: 1px solid #e0e0e0;
      padding: 12px;
      vertical-align: top;
      text-align: left;
    }
    .row-data {
      white-space: nowrap;
      word-break: keep-all;
    }

    thead {
      background-color: $primary-background-color;
      font-weight: bold;
    }

    ul {
      padding-left: 20px;
      margin: 0;
    }

    li {
      margin-bottom: 4px;
    }
  }
}

.title-settings {
  font-weight: 300;
}
