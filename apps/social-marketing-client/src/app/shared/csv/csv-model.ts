import { SSIDPostType, Location } from '@vendasta/social-posts';
import { MLSocialNetworkType } from '../dynamic-posts/dynamic-posts-models';

export interface ParsedPost {
  postText: string;
  Medias?: Media[];
  scheduleDate: Date;
  isIGStory?: boolean;
  gmbCustomization?: gmbCustomization;
  postTypes?: SSIDPostType[];
}

export interface PreviewUploadedPosts {
  posts: ParsedPost[];
  isML: boolean;
  selectedMLNetworks?: MLSocialNetworkType[];
  locations?: Location[];
}

export interface Media {
  mediaType: MediaType;
  mediaUrl: string;
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  GIF = 'GIF',
}

export interface Event {
  title: string;
  start: Date;
  end: Date;
}

export interface Action {
  type: string;
  linkUrl: string;
}

export interface gmbCustomization {
  event: Event;
  action: Action;
}

export interface ValidationError {
  fileName: string;
  lineNumber: number;
  message: string[];
}

export const ALLOWED_GMB_ACTIONS = ['LEARN_MORE', 'BOOK', 'ORDER', 'SHOP', 'SIGN_UP', 'GET_OFFER', 'CALL'];
