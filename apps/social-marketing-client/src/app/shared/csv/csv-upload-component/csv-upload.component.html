<div class="csv-container">
  <div class="csv-uploader">
    <glxy-uploader
      #glxyUploader
      [accept]="'text/csv'"
      [files]="files"
      [autoUpload]="false"
      [buttonText]="[submitText | translate]"
      (fileUploadErrored)="handleFileUploadError($event)"
      (filesChanged)="onUpload($event)"
      [maxFiles]="1"
      [maxFileSize]="1024 * 1024 * 10"
      [hintText]="'BULK_UPLOAD.CSV_UPLOAD.HINT' | translate"
      [descriptionText]="['BULK_UPLOAD.CSV_UPLOAD.DESC' | translate]"
    />
    <glxy-uploader-list [files]="(glxyUploader.filesChanged | async) || []" (fileDeleted)="removeFile($event)" />
  </div>
  <div class="csv-error-container" *ngIf="validationErrors.length > 0">
    <glxy-alert
      type="error"
      [showAction]="true"
      [border]="false"
      actionTitle="{{ 'BULK_UPLOAD.CSV_UPLOAD.VIEW_ALL' | translate }}"
      (actionClick)="showErrors()"
    >
      {{ 'BULK_UPLOAD.CSV_UPLOAD.WARNING' | translate }}
    </glxy-alert>
  </div>
</div>
