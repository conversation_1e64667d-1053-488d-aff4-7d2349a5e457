import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { parse } from 'date-fns';
import { NgxCsvParser, NgxCSVParserError } from 'ngx-csv-parser';
import { AcceptedImageMimeTypes, DEFAULT_MAX_BULK_UPLOAD_POSTS } from '../../composer/constants';
import { ALLOWED_GMB_ACTIONS, Media, MediaType, ParsedPost } from './csv-model';
import { CsvParserStore } from './csv-parser.store';
import { ValidationService } from './csv-validation.service';

@Injectable({ providedIn: 'root' })
export class CsvParserService {
  constructor(
    private validationService: ValidationService,
    private store: CsvParserStore,
    private translateService: TranslateService,
    private ngxCsvParser: NgxCsvParser,
  ) {}

  async parseCSVFile(file: File): Promise<boolean> {
    return new Promise((resolve) => {
      let hasError = false;

      this.ngxCsvParser
        .parse(file, { header: true, delimiter: ',' })
        .pipe()
        .subscribe({
          next: async (result: any[]) => {
            const rows = result;
            const errors: string[] = [];

            const requiredHeaders = [
              'Content',
              'Images',
              'Videos',
              'Schedule Date and Time',
              'GBP Action Name',
              'Instagram Story',
              'GBP Action Url',
              'GBP Event Title',
              'GBP Event Start Date and Time',
              'GBP Event End Date and Time',
            ];

            // Validate headers
            const headers = Object.keys(rows[0] || {});
            requiredHeaders.forEach((header) => {
              if (!headers.includes(header)) {
                errors.push(`Missing required header: ${header}`);
              }
            });

            if (rows.length > DEFAULT_MAX_BULK_UPLOAD_POSTS) {
              errors.push(`Number of posts in the uploaded csv is more than ${DEFAULT_MAX_BULK_UPLOAD_POSTS}`);
            }

            if (errors.length > 0) {
              hasError = true;
              this.validationService.addError({
                fileName: file.name,
                lineNumber: 1,
                message: errors,
              });
              resolve(hasError);
              return;
            }

            const parsedPosts: ParsedPost[] = [];

            for (let i = 0; i < rows.length; i++) {
              const row = rows[i];
              const rowErrors: string[] = [];

              const postText = row['Content']?.trim();
              const imageUrls = this.extractUrls(row['Images']);
              const videoUrls = this.extractUrls(row['Videos']);
              const scheduleDate = this.parseCustomDate(row['Schedule Date and Time']);
              const isIGStory = row['Instagram Story']?.trim().toUpperCase() === 'YES';

              const mediaUrls: Media[] = [];
              if (imageUrls && imageUrls.length > 0) {
                const validImages = await this.validateAndAddImages(imageUrls, rowErrors);
                mediaUrls.push(...validImages);
              }
              if (videoUrls && videoUrls.length > 0) {
                if (videoUrls.length > 2) {
                  rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.TOO_MANY_VIDEO_URLS'));
                }
                mediaUrls.push(...videoUrls.map((url) => ({ mediaType: MediaType.VIDEO, mediaUrl: url })));
              }

              let gmbCustomization: any = null;

              const type = row['GBP Action Name']?.trim();
              const linkUrl = row['GBP Action Url']?.trim();
              const action = type === 'CALL' ? { type } : type && linkUrl ? { type, linkUrl } : undefined;

              const event =
                row['GBP Event Title']?.trim() &&
                row['GBP Event Start Date and Time']?.trim() &&
                row['GBP Event End Date and Time']?.trim()
                  ? {
                      title: row['GBP Event Title']?.trim(),
                      start: this.parseCustomDate(row['GBP Event Start Date and Time']),
                      end: this.parseCustomDate(row['GBP Event End Date and Time']),
                    }
                  : undefined;

              if (action || event) {
                gmbCustomization = { action, event };
              }

              if (!postText) rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.MISSING_POST_CONTENT'));
              if (mediaUrls.length > 10)
                rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.TOO_MANY_URLS'));
              if (isNaN(scheduleDate?.getTime()))
                rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_SCHEDULE_DATE'));

              if (gmbCustomization) {
                if (gmbCustomization.action) {
                  const actionType = String(gmbCustomization.action.type);
                  const validActionTypes = ALLOWED_GMB_ACTIONS;
                  if (!validActionTypes.includes(actionType)) {
                    rowErrors.push(
                      this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_GMB_TYPE', {
                        type: actionType,
                        validTypes: validActionTypes,
                      }),
                    );
                  }

                  if (gmbCustomization && videoUrls.length > 0) {
                    rowErrors.push(
                      this.translateService.instant('BULK_UPLOAD.ERROR.GMB_CUSTOMIZATION_DISALLOWS_VIDEOS'),
                    );
                  }

                  // Make linkUrl optional for 'CALL' action type
                  if (actionType !== 'CALL' && !gmbCustomization.action.linkUrl) {
                    rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.MISSING_ACTION_URL'));
                  }
                }
                if (gmbCustomization.event) {
                  if (!gmbCustomization.event.title) {
                    rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.MISSING_GMB_TITLE'));
                  }
                  if (isNaN(gmbCustomization.event.start?.getTime())) {
                    rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_GMB_START_DATE'));
                  }
                  if (isNaN(gmbCustomization.event.end?.getTime())) {
                    rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_GMB_END_DATE'));
                  }
                  if (gmbCustomization.event.start >= gmbCustomization.event.end) {
                    rowErrors.push(this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_GMB_START_END_TIME'));
                  }
                }
              }

              if (rowErrors.length > 0) {
                hasError = true;
                this.validationService.addError({
                  fileName: file.name,
                  lineNumber: i + 2, // +2 because headers are on line 1
                  message: rowErrors,
                });
              } else {
                parsedPosts.push({ postText, Medias: mediaUrls, scheduleDate, gmbCustomization, isIGStory });
              }
            }

            this.store.addPosts(parsedPosts);
            resolve(hasError);
          },
          error: (error: NgxCSVParserError) => {
            hasError = true;
            this.validationService.addError({
              fileName: file.name,
              lineNumber: 1,
              message: [error.message],
            });
            resolve(hasError);
          },
        });
    });
  }

  extractUrls(raw: string): string[] {
    if (!raw) return [];
    return raw
      .split(';')
      .map((url) => url.trim())
      .filter((url) => url.startsWith('http'));
  }

  async validateAndAddImages(
    imageUrls: string[],
    errors: string[],
  ): Promise<{ mediaType: MediaType; mediaUrl: string }[]> {
    const batchSize = 10;

    const results: { mediaType: MediaType; mediaUrl: string }[] = [];

    for (let i = 0; i < imageUrls.length; i += batchSize) {
      const batch = imageUrls.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(async (url) => {
          try {
            const response = await fetch(url);
            if (!response.ok) {
              errors.push(this.translateService.instant('BULK_UPLOAD.ERROR.FAILED_TO_LOAD'));
              return null;
            }

            const blob = await response.blob();
            if (AcceptedImageMimeTypes.includes(blob.type)) {
              return { mediaType: MediaType.IMAGE, mediaUrl: url };
            } else {
              errors.push(this.translateService.instant('BULK_UPLOAD.ERROR.FAILED_TO_LOAD'));
              return null;
            }
          } catch (err) {
            errors.push(this.translateService.instant('BULK_UPLOAD.ERROR.INVALID_IMAGE'));
            return null;
          }
        }),
      );

      results.push(...(batchResults.filter(Boolean) as { mediaType: MediaType; mediaUrl: string }[]));
    }
    return results;
  }

  parseCustomDate(dateStr: string): Date | null {
    try {
      return parse(dateStr, 'yyyy-MM-dd HH:mm', new Date());
    } catch {
      return null;
    }
  }
}
