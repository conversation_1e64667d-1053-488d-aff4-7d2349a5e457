import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-delete-post-dialog',
  templateUrl: './delete-post-dialog.component.html',
  standalone: false,
})
export class DeletePostDialogComponent implements OnInit {
  deleteTitle$: Observable<string>;
  deleteMessage$: Observable<string>;
  deleteConfirm$: Observable<string>;
  deleteCancel$: Observable<string>;

  constructor(
    public dialogRef: MatDialogRef<DeletePostDialogComponent>,
    private translateService: TranslateService,
    @Inject(MAT_DIALOG_DATA)
    private data: { socialServiceType: string; type: 'post' | 'draft' | 'csv' | 'lastpost' | 'template' },
  ) {}

  ngOnInit(): void {
    if (this.data.type === 'post') {
      this.deleteTitle$ = this.translateService.stream('POSTS.DELETE_POST');
      this.deleteMessage$ = this.translateService.stream('POSTS.DELETE_POST_MESSAGE', {
        socialServiceType: this.data.socialServiceType,
      });
      this.deleteConfirm$ = this.translateService.stream('POSTS.YES_DELETE');
      this.deleteCancel$ = this.translateService.stream('POSTS.NO_KEEP');
    } else if (this.data.type === 'draft') {
      this.deleteTitle$ = this.translateService.stream('POSTS.DELETE_DRAFT');
      this.deleteMessage$ = this.translateService.stream('POSTS.DELETE_DRAFT_MESSAGE', {
        socialServiceType: this.data.socialServiceType,
      });
      this.deleteConfirm$ = this.translateService.stream('POSTS.YES_DELETE_DRAFT');
      this.deleteCancel$ = this.translateService.stream('POSTS.NO_KEEP_DRAFT');
    } else if (this.data.type === 'csv') {
      this.deleteTitle$ = this.translateService.stream('POSTS.DELETE_POST');
      this.deleteMessage$ = this.translateService.stream('POSTS.DELETE_POST_MESSAGE');
      this.deleteConfirm$ = this.translateService.stream('POSTS.YES_DELETE');
      this.deleteCancel$ = this.translateService.stream('POSTS.NO_KEEP');
    } else if (this.data.type === 'lastpost') {
      this.deleteTitle$ = this.translateService.stream('POSTS.DELETE_POST');
      this.deleteMessage$ = this.translateService.stream('POSTS.DELETE_CSV_POST');
      this.deleteConfirm$ = this.translateService.stream('POSTS.YES_DELETE');
      this.deleteCancel$ = this.translateService.stream('POSTS.NO_KEEP');
    } else {
      this.deleteTitle$ = this.translateService.stream('POSTS.DELETE_TEMPLATE');
      this.deleteMessage$ = this.translateService.stream('POSTS.DELETE_TEMPLATE_MESSAGE');
      this.deleteConfirm$ = this.translateService.stream('POSTS.YES_DELETE_TEMPLATE');
      this.deleteCancel$ = this.translateService.stream('POSTS.NO_KEEP_TEMPLATE');
    }
  }

  confirm(): void {
    this.dialogRef.close(true);
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
