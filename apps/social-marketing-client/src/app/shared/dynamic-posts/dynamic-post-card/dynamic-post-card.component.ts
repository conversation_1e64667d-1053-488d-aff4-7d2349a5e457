import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NEW_POST_ID } from '../../../composer/constants';
import { ComposerSettings, FileType } from '../../../composer/interfaces';
import { mediaentriesToUploadedMedia } from '../../../composer/shared-methods';
import { ComposerSettingsService } from '../../../core/composer-settings/composer-settings.service';
import { Customization } from '../../../core/post/customization';
import { SocialConnection, PostScheduleType } from '../../../pages/posts/ai-bundle-posts/model';
import { ConfigService } from '../../../core';
import { PostInterface } from '@vendasta/composer';
import { CommonUtils } from '../../../composer/shared/utils/common-utils';
import { MultilocationPost } from '@vendasta/social-posts';
import { Overlay } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ComposerComponent } from '../../../composer/composer.component';
import { filter, mapTo, take, tap } from 'rxjs';
import { GmbOptions } from '../../../composer/models/gmb-options';
import { MatDialog } from '@angular/material/dialog';
import { DeletePostDialogComponent } from '../../post/delete-post-dialog/delete-post-dialog.component';
import { PostableService } from '../../social-service/social-service.service';
import { DynamicMedia, MAX_CONTENT_LENGTH, MediaType, MLSocialNetworkType, PostContent } from '../dynamic-posts-models';
import { DynamicPostsService } from '../dynamic-posts.service';
import { GalaxyDateAdapter, MY_DATE_FORMATS } from '@vendasta/galaxy/datepicker/src/date-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { getLocale } from '@vendasta/galaxy/utility/locale';

@Component({
  selector: 'app-dynamic-post-card',
  standalone: false,
  providers: [
    { provide: DateAdapter, useClass: GalaxyDateAdapter },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
    { provide: MAT_DATE_LOCALE, useValue: getLocale() },
  ],
  templateUrl: './dynamic-post-card.component.html',
  styleUrl: './dynamic-post-card.component.scss',
})
export class DynamicPostCardComponent implements OnInit {
  @Input() postContent: PostContent[];
  @Input() socialNetworks: SocialConnection[];
  @Output() postEdited = new EventEmitter();
  defaultProfileImageUrl: string;
  fbIcon: string;
  gmbIcon: string;
  igIcon: string;
  networkType = MLSocialNetworkType;
  isInvalidTime = false;
  isExpanded = false;
  selectedIndex = 0;
  editedPostIndex = 0;
  isMLPost = false;
  value: Date;
  selectedOption: any;
  postStatusOptions: { key: string; value: string }[] = Object.keys(PostScheduleType).map(
    (
      key,
    ): {
      key: string;
      value: string;
    } => ({
      key,
      value: PostScheduleType[key],
    }),
  );

  constructor(
    public optimizedPostsService: DynamicPostsService,
    private config: ConfigService,
    private composerSettingsService: ComposerSettingsService,
    private overlayService: Overlay,
    private dialog: MatDialog,
  ) {}

  ngOnInit() {
    if (!this.config?.config()?.is_digital_agent) {
      this.postStatusOptions = this.postStatusOptions.filter((ele) => ele.value !== PostScheduleType.HIDDEN_DRAFT);
    }
    this.isMLPost = this.postContent?.some((m) => m?.isML);

    if (!this.isMLPost) {
      this.postContent = this.optimizedPostsService.mapNetworksWithPosts(this.socialNetworks, this.postContent);
    }

    if (this.isMLPost) {
      this.postStatusOptions = this.postStatusOptions.filter((ele) => ele.value !== PostScheduleType.DRAFT);
      this.selectedOption = this.postStatusOptions.find((v) => v).value;
    }

    this.defaultProfileImageUrl = CommonUtils.getImageSrc('social-icons/default.png');
    this.fbIcon = CommonUtils.getImageSrc('social-icons/facebook.png');
    this.gmbIcon = CommonUtils.getImageSrc('social-icons/google_my_business.png');
    this.igIcon = CommonUtils.getImageSrc('social-icons/instagram.png');
  }

  deletePost(postIndex: number) {
    this.postContent.splice(postIndex, 1);
  }

  showDeleteConfirmationDialog(postIndex: number): void {
    const isLastPost = this.postContent.length === 1;
    this.dialog
      .open(DeletePostDialogComponent, {
        data: {
          type: isLastPost ? 'lastpost' : 'csv',
        },
        width: '700px',
        autoFocus: false,
      })
      .afterClosed()
      .subscribe((confirmed: boolean) => {
        if (confirmed) {
          this.deletePost(postIndex);
        }
      });
  }

  onTabChange(index: number) {
    this.selectedIndex = index;
  }

  handleContentEdited = (posts: PostInterface[]) => {
    const postIndex = this.isMLPost ? this.editedPostIndex : this.selectedIndex;
    this.postContent[postIndex] = {
      ...this.postContent[postIndex],
      postText: posts[0]?.postText,
      Medias: posts[0]?.mediaEntries.map(
        (media) =>
          ({
            mediaType: media.mediaType,
            mediaUrl: media.mediaUrl,
          }) as DynamicMedia,
      ),
      postType: posts[0]?.postType,
      scheduleDate: posts[0]?.scheduled,
      gmbCustomization: posts[0]?.gmbPostCustomization
        ? {
            event: {
              title: posts[0]?.gmbPostCustomization.title || '',
              start: posts[0]?.gmbPostCustomization.eventStart || null,
              end: posts[0]?.gmbPostCustomization.eventEnd || null,
            },
            action: {
              type: posts[0]?.gmbPostCustomization.ctaType || '',
              linkUrl: posts[0]?.gmbPostCustomization.linkUrl || '',
            },
          }
        : undefined,
    };
    this.postEdited.emit({
      post: this.postContent[postIndex],
      index: postIndex,
    });
  };

  now(): Date {
    return new Date();
  }

  editPosts(post: PostContent, postIndex: number) {
    const postId = NEW_POST_ID;
    const brandId = this.config.brandId;
    this.editedPostIndex = postIndex;
    let composerSettings: ComposerSettings;
    if (brandId) {
      const multilocationPost = new MultilocationPost();
      multilocationPost.brandId = brandId;
      multilocationPost.originalText = post?.postText;
      multilocationPost.originalScheduledDate = post?.scheduleDate;
      multilocationPost.originalMedia = post?.Medias.map((media) => media.mediaUrl);
      const media = [];
      if (multilocationPost.originalMedia && multilocationPost.originalMedia.length) {
        post?.Medias.map((m) => {
          if (m?.mediaType === MediaType.IMAGE) {
            media.push({ url: m?.mediaUrl, fileType: FileType.IMAGE });
          } else if (m?.mediaType === MediaType.VIDEO) {
            media.push({ url: m?.mediaUrl, fileType: FileType.VIDEO });
          } else if (m?.mediaType === MediaType.GIF) {
            media.push({ url: m?.mediaUrl, fileType: FileType.GIF });
          }
        });
      }
      const gmbOptions = new GmbOptions();
      if (post?.gmbCustomization) {
        if (
          post?.gmbCustomization?.event?.title &&
          post?.gmbCustomization?.event?.start &&
          post?.gmbCustomization?.event?.end
        ) {
          gmbOptions.makeEvent = true;
          gmbOptions.eventOptions = {
            title: post.gmbCustomization.event.title,
            startDate: post.gmbCustomization.event.start,
            endDate: post.gmbCustomization.event.end,
          };
        }
        if (post.gmbCustomization?.action?.type || post.gmbCustomization?.action?.linkUrl) {
          gmbOptions.addCta = true;
          gmbOptions.ctaOptions = {
            action: post.gmbCustomization.action.type,
            ctaUrl: post.gmbCustomization.action.linkUrl,
          };
        }
      }

      composerSettings = {
        isEditing: true,
        activeLocations: post.locations,
        groupedCustomization: new Customization({
          postText: multilocationPost.originalText,
          scheduledDate: multilocationPost.originalScheduledDate,
          uploadedMediaObjects: media,
          gmbOptions: gmbOptions,
        }),
        multilocationId: 'FAKE-ML-ID', // Giving a fake multilocationId to avoid error
        isCSVUploadedPost: true,
        composerCallback: this.handleContentEdited,
        brandId: brandId,
        postType: post.postType,
      } as ComposerSettings;
    } else {
      const services = new Map<PostableService, string>();
      const bundleServices = Array.isArray(post.socialConnection) ? post.socialConnection : [post.socialConnection];
      bundleServices.forEach((service) => {
        services.set(service as PostableService, postId);
      });

      composerSettings = {
        composerCallback: this.handleContentEdited,
      };
      const createCustomization = (post: PostContent, services: Map<PostableService, string>) => {
        const { postText: postText, Medias } = post;
        const mediaEntries = Medias || [];
        const uploadedMedia = mediaentriesToUploadedMedia(mediaEntries);
        return new Customization({
          postText,
          mediaEntries,
          uploadedMediaObjects: uploadedMedia,
          services,
        });
      };

      const customization = createCustomization(post, services);
      composerSettings = {
        ...composerSettings,
        groupedCustomization: customization,
      };
    }

    brandId ? this.showComposer(composerSettings) : this.composerSettingsService.showComposer(composerSettings);
  }

  public showComposer(setting?: ComposerSettings): void {
    const overlayRef = this.overlayService.create();
    const portal = new ComponentPortal(ComposerComponent);
    const compRef = overlayRef.attach(portal);
    if (this.config.brandId) {
      compRef.instance.brandId = this.config.brandId;
      compRef.instance.showClose = true;
      compRef.instance.setAgidOnStore();
      this.config.getAgidFromBrand(this.config.brandId);
    }

    const ssids = [];
    if (setting && setting.groupedCustomization) {
      for (const service of Array.from(setting.groupedCustomization.services$$.getValue().keys())) {
        ssids.push(service.ssid);
      }
    }

    compRef.instance.initialize(ssids);
    compRef.instance.showComposer(setting);

    compRef.instance.visibleEvent.pipe(
      filter((isComposerVisible) => !isComposerVisible),
      mapTo(true),
      tap(() => {
        overlayRef.detach();
      }),
      take(1),
    );
  }

  validateDateTime(index?: any): void {
    const now = new Date();
    const effectiveIndex = index ?? this.selectedIndex;
    let scheduledDate = this.postContent[effectiveIndex]?.scheduleDate;

    if (scheduledDate && new Date(scheduledDate) < now) {
      this.isInvalidTime = true;
      scheduledDate = now; // Reset to current time if needed
    } else {
      this.isInvalidTime = false;
    }

    this.postEdited.emit({
      post: this.postContent[effectiveIndex],
      index: effectiveIndex,
    });
  }

  protected readonly MAX_CONTENT_LENGTH = MAX_CONTENT_LENGTH;
}
