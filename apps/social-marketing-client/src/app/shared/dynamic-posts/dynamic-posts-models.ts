import { PostType } from '@vendasta/composer';
import { PostScheduleType, SocialConnection } from '../../pages/posts/ai-bundle-posts/model';
import { SocialService } from '../../composer/post';
import { Location } from '@vendasta/social-posts';

export const MAX_CONTENT_LENGTH = 200;
export interface PostContent {
  socialConnection?: SocialConnection;
  socialNetwork?: SocialService;
  mlSocialNetworkType?: MLSocialNetworkType[];
  postText: string;
  postType?: PostType;
  Medias?: DynamicMedia[];
  postMode?: PostScheduleType;
  scheduleDate: Date;
  isEditing?: boolean;
  isML?: boolean;
  locations?: Location[];
  gmbCustomization?: gmbCustomization;
}

export interface Posts {
  title?: '';
  postContent: PostContent[];
}

export interface DynamicMedia {
  mediaUrl: string;
  mediaType: MediaType;
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  GIF = 'GIF',
}

export enum MLSocialNetworkType {
  FACEBOOK = 'FBP',
  GMB = 'account',
  INSTAGRAM = 'IGU',
}

export interface gmbCustomization {
  event: Event;
  action: Action;
}

export interface Event {
  title: string;
  start: Date;
  end: Date;
}

export interface Action {
  type: string;
  linkUrl: string;
}
