import { Component, Input, OnInit } from '@angular/core';

import { SocialServiceName } from '../../../../core/post/post';

import { BehaviorSubject, Observable, of, Subscription } from 'rxjs';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { SMFeaturesService } from '../../../../core/features.service';
import { ConfigService } from '../../../../core';
import { RepcoreApiService } from '../../../repcore-api/repcore-api.service';

import { GroupedPostsService } from '../../../post/grouped-posts.service';
import { ViewPostsComponent } from '../../../post/posts-view/view-posts.component';
import { CardType } from '../../../post';
import { Draft } from '../../../../core/post/draft';
import { GMB_SSID_REGEX } from '../../../../composer/constants';
import { isBlog } from '../../../utils/common';

@Component({
  selector: 'app-post-card-content',
  standalone: false,
  templateUrl: './post-card-content.component.html',
  styleUrl: './post-card-content.component.scss',
})
export class PostCardContentComponent implements OnInit {
  @Input() workFlowTypeReadable;
  @Input() workflowType;
  private _post: any;
  @Input() set post(value: any) {
    if (value?.postText) {
      value.postText = (value.postText || '')
        .split('\n')
        .map((line) => line.replace(/\[(\/?[\w-]+)(\s+[^\]]*)?\]/g, '')) // Remove shortcodes but keep surrounding text
        .join('');
    }
    this._post = value;
  }
  get post(): any {
    return this._post;
  }
  @Input() cardType: CardType;
  spid: string;
  pid: string;
  subscriptions: Subscription[] = [];
  serviceName = SocialServiceName;
  isMobile$: Observable<boolean> = of(false);
  readMoreMaxLength = 500;
  isBlog = false;

  constructor(
    protected dialog: MatDialog,
    public featuresService: SMFeaturesService,
    private config: ConfigService,
    private repcoreService: RepcoreApiService,
    private groupedPostsService: GroupedPostsService,
  ) {}

  ngOnInit() {
    this.isBlog = isBlog(this.post);
    this.subscriptions.push(
      this.config.config$.subscribe((config) => {
        this.spid = config.account.spid;
        this.pid = config.partner_id;
      }),
    );
  }

  handleViewMore() {
    const data = {
      postList: this.post,
      isBlog: this.isBlog,
      cardType: this.cardType,
    };
    this.dialog
      .open(ViewPostsComponent, {
        data: data,
        autoFocus: false,
        width: '624px',
        height: 'auto',
        panelClass: 'sm-chatbot-dialog',
        disableClose: true,
        enterAnimationDuration: 0,
      } as MatDialogConfig)
      .afterClosed()
      .subscribe();
  }

  parsePostText(postText: string, service: string, postId: string, ssid: string): Observable<string> {
    const hash_exp = /(^|\s)#(\w+)/g;
    const user_exp = /(^|\s)@(\w+)/g;
    postText = postText || '';
    if (service === this.serviceName.INSTAGRAM) {
      postText = postText.replace(user_exp, '$1<a href="http://www.instagram.com/$2" target="_blank">@$2</a>');
      postText = postText.replace(
        hash_exp,
        '$1<a href="https://instagram.com/explore/tags/$2?" target="_blank">#$2</a>',
      );
    } else if (service === SocialServiceName.TWITTER) {
      postText = postText.replace(user_exp, '$1<a href="http://www.twitter.com/$2" target="_blank">@$2</a>');
      postText = postText.replace(
        hash_exp,
        '$1<a href="https://twitter.com/hashtag/$2?src=hash" target="_blank">#$2</a>',
      );
    } else if (service === this.serviceName.LINKEDIN) {
      postText = postText.replace(
        hash_exp,
        '$1<a href="https://www.linkedin.com/feed/hashtag/$2?" target="_blank">#$2</a>',
      );
    } else if (service === SocialServiceName.FACEBOOK) {
      postText = postText.replace(hash_exp, '$1<a href="https://www.facebook.com/hashtag/$2" target="_blank">#$2</a>');
      return this.parseFbPostText(postId, postText, ssid);
    }
    return of(postText);
  }

  parseFbPostText(postId: string, postText: string, ssid: string): Observable<string> {
    if (this.groupedPostsService.fbPostTextMap.get(postId)) {
      return this.groupedPostsService.fbPostTextMap.get(postId);
    } else {
      this.groupedPostsService.fbPostTextMap.set(postId, new BehaviorSubject<string>(postText));
      const mentionExp = /(@\[)(\w+)(\])/g;
      let fbIds = postText ? postText.match(mentionExp) : [];

      if (fbIds && fbIds.length > 0) {
        fbIds = fbIds.map((el) => el.replace(mentionExp, '$2'));
        this.subscriptions.push(
          this.repcoreService
            .getFacebookMentionNames(fbIds, this.config.accountGroupId, this.spid, ssid, this.pid)
            .subscribe((result) => {
              const pageEntries = Object.entries(result);
              pageEntries.forEach((entry) => {
                const entryKey = entry[0];
                const entryVal: any = entry[1];
                const link = `<a href='${entryVal.link}' target='_blank'>@${entryVal.name}</a>`;
                postText = postText.replace(new RegExp('@\\[(' + entryKey + ')\\]', 'g'), link);
                this.groupedPostsService.fbPostTextMap.get(postId).next(postText);
              });
            }),
        );
      }
      return this.groupedPostsService.fbPostTextMap.get(postId).asObservable();
    }
  }

  sanitizeHtmlContent(htmlContent: string): string {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    return tempDiv.innerText || tempDiv.textContent || '';
  }

  postTextToDisplay(post, postText: string): string {
    if (postText.length <= this.readMoreMaxLength || post.readMoreExpanded) {
      return postText;
    }

    if (this.sanitizeHtmlContent(postText).length <= this.readMoreMaxLength || post.readMoreExpanded) {
      return postText;
    }
    if (post.service == this.serviceName.WORDPRESS_BLOG) {
      return this.blogPostTextToDisplay(post, postText);
    }

    if (postText.length > this.readMoreMaxLength) {
      // case when the postText has link in it
      const linkStartIndex = postText.indexOf('<a');
      const linkEndIndex = postText.indexOf('</a>') + 4;

      // when link start index in substring but end index is not
      if (linkStartIndex !== -1 && linkEndIndex > this.readMoreMaxLength && linkStartIndex < this.readMoreMaxLength) {
        return postText.substring(0, linkStartIndex) + '...';
      }

      return postText.substring(0, this.readMoreMaxLength) + '...';
    } else {
      return postText;
    }
  }

  blogPostTextToDisplay(post, postText: string): string {
    let charCount = 0,
      truncateIndex = 0,
      insideTag = false;
    const shortcodeRegex = /\[et_pb_[^\]]+\]/g;

    for (let i = 0; i < postText.length && charCount < this.readMoreMaxLength; i++) {
      if (postText[i] === '<' && postText[i + 1] !== '/') insideTag = true;
      else if (postText[i] === '>' && insideTag) {
        insideTag = false;
        continue;
      }

      const shortcodeMatch = postText.slice(i).match(shortcodeRegex);
      if (shortcodeMatch && postText.slice(i).startsWith(shortcodeMatch[0])) {
        i += shortcodeMatch[0].length - 1;
        continue;
      }

      if (!insideTag && postText[i] !== '>') charCount++;
      truncateIndex = i + 1;
    }

    const linkStart = postText.indexOf('<a', 0);
    if (linkStart !== -1 && linkStart < truncateIndex) {
      const linkEnd = postText.indexOf('</a>', 0);
      if (linkEnd === -1 || linkEnd + 4 > truncateIndex) {
        truncateIndex = linkStart;
      }
    }
    return postText.substring(0, truncateIndex) + '...';
  }

  getTitle(post): string {
    if (post?.blogPostCustomization?.title) {
      return post?.blogPostCustomization?.title.trim();
    } else if (post?.title) {
      return post.title.trim();
    }
  }
  shouldShowDraftGMBCtaOptions(draft: Draft): boolean {
    return (
      draft.draft_id &&
      draft.services.length === 1 &&
      draft.services[0].match(GMB_SSID_REGEX) &&
      draft.gmb_post_customization &&
      draft.gmb_post_customization.addCta
    );
  }
}
