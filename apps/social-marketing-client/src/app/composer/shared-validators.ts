import { FileType, MediaLimits, UploadedFile } from './interfaces';
import { SocialService } from './post';
import { DynamicContentEnums } from './components/dynamic-content/dynamic-content';
import { DynamicContentPlaceholders } from './interface';
import { PostType } from '@vendasta/social-posts';
import { AbstractControl, ValidatorFn } from '@angular/forms';

export function validateImageTypes(mediaObjects: UploadedFile[], serviceTypes?): boolean {
  const types = new Set();
  mediaObjects.reduce((fileTypes, media) => {
    fileTypes.add(media.fileType);
    return fileTypes;
  }, types);

  const allowedServices = serviceTypes?.some(
    (type) => type === SocialService.INSTAGRAM || type === SocialService.CURATED_CONTENT,
  );
  const isMultiTypes = types.size > 1;
  const multiNetworks = serviceTypes?.length > 1;

  return isMultiTypes && (!allowedServices || (allowedServices && multiNetworks));
}

export function validateMultipleGifs(mediaObjects: UploadedFile[]): boolean {
  return mediaObjects.filter((media) => media.fileType === FileType.GIF).length > 1;
}

export function validateMultipleVideos(mediaObjects: UploadedFile[], serviceTypes?, postType?: PostType): boolean {
  const isMultiVideos = mediaObjects.filter((media) => media.fileType === FileType.VIDEO).length > 1;
  const allowedServices = serviceTypes?.some(
    (type) => type === SocialService.INSTAGRAM || type === SocialService.CURATED_CONTENT,
  );
  const isMultiNetworks = serviceTypes?.length > 1;
  const storiesSelected =
    serviceTypes?.some((type) => type === SocialService.INSTAGRAM) && postType === PostType.POST_TYPE_STORIES;

  return isMultiVideos && (!allowedServices || (isMultiNetworks && allowedServices)) && !storiesSelected;
}

export function validateImagesUsedPreviously(mediaObjects): boolean {
  if (mediaObjects && mediaObjects.length > 3) {
    return false;
  }
  return mediaObjects.filter((media) => media.fileType === FileType.IMAGE && media.lastPostedDate).length > 0;
}

export function hasInvalidMedia(
  mediaObjects: UploadedFile[],
  serviceTypes: SocialService[],
  mediaType: FileType,
): boolean {
  const isValidMediaType = (media: UploadedFile) =>
    mediaType === FileType.VIDEO ? media.fileType === FileType.VIDEO : media.fileType !== FileType.VIDEO;

  return mediaObjects
    .filter(isValidMediaType)
    .some((media) => serviceTypes?.some((service) => Boolean(media.errors?.[service]?.length)));
}

//TODO Improve the logic here
export function firstInvalidMessage(mediaObjects, serviceTypes, mediaType: FileType): MediaLimits {
  const medias =
    mediaType === FileType.VIDEO
      ? mediaObjects.filter((media) => media.fileType === FileType.VIDEO)
      : mediaObjects.filter((media) => media.fileType !== FileType.VIDEO);
  let message: MediaLimits = null;
  for (const media of medias) {
    for (const service of serviceTypes) {
      if (media.errors) {
        const limits = media.errors[service];
        if (limits.length) {
          message = limits[0];
          break;
        }
      }
    }
  }
  return message;
}

export function dynamicContentInfoDoesNotExist(postText: string, brandAgids: Map<string, unknown>): boolean {
  const placeholders = Object.keys(DynamicContentEnums);
  const chosenDynamicContent = [] as string[];
  placeholders.forEach((placeHolderEnum) => {
    if (postText.includes(DynamicContentEnums[placeHolderEnum])) {
      chosenDynamicContent.push(DynamicContentEnums[placeHolderEnum]);
    }
  });

  if (chosenDynamicContent.length > 0) {
    if (brandAgids) {
      for (const agid of Object.keys(brandAgids)) {
        const info = brandAgids[agid];
        if (info['agid_info']) {
          const dynamicContent = info['agid_info'] as DynamicContentPlaceholders;
          // Check that all placeholders have info to fill them
          for (const placeholder of chosenDynamicContent) {
            if (placeholderInfoInvalid(dynamicContent, placeholder)) {
              return true;
            }
          }
        } else {
          return true;
        }
      }
    } else {
      return true;
    }
  }
  return false;
}

export function customTopicValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const value: string = control.value;
    const hasAlphabets = /[a-zA-Z]/.test(value);
    const hasOnlySpecialChars = /^[!@#$%^&*(),.?":{}|<>]+$/.test(value);
    const hasEmptySpaces = value?.trim().length === 0;
    if (value?.length > 2500) {
      return { lengthExceed: true };
    }
    if (!hasAlphabets || hasOnlySpecialChars || hasEmptySpaces) {
      return { invalidTopic: true };
    }
    return null;
  };
}

export function inputTextValidator(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    const value: string = control.value || '';

    const hasOnlySpaces = value.trim().length === 0;

    if (hasOnlySpaces) {
      return { invalidInput: true };
    }
    return null;
  };
}

function placeholderInfoInvalid(agidInfo: DynamicContentPlaceholders, placeholder: string): boolean {
  switch (placeholder) {
    case DynamicContentEnums.BUSINESS_CITY:
      if (!agidInfo.city) {
        return true;
      }
      break;
    case DynamicContentEnums.BUSINESS_NAME:
      if (!agidInfo.business_name) {
        return true;
      }
      break;
    case DynamicContentEnums.PHONE_NUMBER:
      if (!agidInfo.work_number) {
        return true;
      }
      break;
    default:
      return true;
  }
  return false;
}
