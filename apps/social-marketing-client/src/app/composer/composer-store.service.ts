import { Injectable, signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  BehaviorSubject,
  combineLatest,
  EMPTY,
  EMPTY as empty,
  forkJoin,
  from,
  fromEvent,
  iif,
  merge,
  Observable,
  of,
  Subject,
  Subscription,
} from 'rxjs';
import {
  catchError,
  debounceTime,
  distinct,
  distinctUntilChanged,
  filter,
  finalize,
  first,
  map,
  mergeMap,
  shareReplay,
  skipWhile,
  startWith,
  switchMap,
  take,
  takeLast,
  tap,
  throttleTime,
  withLatestFrom,
} from 'rxjs/operators';
import * as twitterText from 'twitter-text';

import { toSignal } from '@angular/core/rxjs-interop';
import { MatSnackBarConfig } from '@angular/material/snack-bar';
import { EmojiData } from '@ctrl/ngx-emoji-mart/ngx-emoji';
import { TranslateService } from '@ngx-translate/core';
import {
  MediaEntryInterface,
  PostInterface,
  ResultInterface,
  SchedulePostsResponse,
  SchedulePostsResponseInterface,
  SearchHashtagResponse,
  UpdatePostsResponseInterface,
} from '@vendasta/composer';
import { Location, MessageLength, PostStatusV2, PostType, TemplateType } from '@vendasta/social-posts';
import { AIButtonMenuItem, UpdateOption } from '../composer/components/ai-menu/ai-menu-settings';
import { ConfigService, SMConfig } from '../core';
import { SMFeaturesService } from '../core/features.service';
import { Customization } from '../core/post/customization';
import { Draft } from '../core/post/draft';
import { FeedItem } from '../core/post/interface';
import { SocialPost } from '../core/post/post';
import { SocialPostFeedStoreService } from '../core/post/post-feed.service';
import { PostTemplate } from '../core/post/post-template';
import { SocialDraftsAPIService } from '../core/post/social-drafts.api.service';
import { TemplateStoreService } from '../pages/posts/template-posts/template-store.service';
import { RpcSubject } from '../rpc-subject';
import { ConciergeTaskService } from '../shared/concierge-task/concierge.task.service';
import { SnackBarService } from '../shared/snack-bar/snack-bar.service';
import { SocialProfile, SocialService as SocialServiceInfo } from '../shared/social-profile/interface';
import { SocialProfileService } from '../shared/social-profile/social-profile.service';
import { PostableService, SocialServiceService } from '../shared/social-service/social-service.service';
import { MediaLibraryService } from './components/media-library/media-library.service';
import { ValidateImage } from './components/media-validators/validate-image';
import { ValidateVideo } from './components/media-validators/validate-video';
import { PixabayLibraryService } from './components/curated-images/pixabay/pixabay.service';
import { UnsplashLibraryService } from './components/curated-images/unsplash/unsplash.service';
import { ReviewImageService } from './components/review-image/review-image.service';
import { VideoUploadService } from './components/video-upload/video-upload.service';
import { ShortenLinkResponse, SMComposerApiService } from './composer-api.service';
import * as CONSTANTS from './constants';
import { DEFAULT_MAX_SIZE_AI_CONTENT, GMB_LABEL_ACTION_MAP, HASHTAG_REGEX, MENTIONS_REGEX } from './constants';
import { PublishPostEventService } from './core/events/publish-post.service';
import {
  ComposerSettings,
  Coupon,
  CustomizationSetup,
  FileType,
  InterestingContentItem,
  LinkHistory,
  Media,
  MediaLimits,
  RequiredContent,
  ReviewDetails,
  SiteMetaContent,
  SubmitResults,
  TaskManagerPost,
  typeServiceMlSelected,
  UploadedFile,
  VFormSection,
} from './interfaces';
import { defaultGmbOptionsValidity, GmbOptions, GmbOptionsValidity } from './models/gmb-options';
import UploadedImage from './models/uploaded-image';
import { UploadedVideo } from './models/uploaded-video';
import { defaultYoutubeCustomization, YoutubePostCustomization } from './models/youtube-options';
import {
  LONG_VIDEO_WORKFLOW_NETWORKS,
  serviceTypeFullName,
  SocialService,
  STORY_WORKFLOW_NETWORKS,
  WorkflowType,
} from './post';
import PreviewLinkHistory from './preview-link-history';
import { PostMode, PostModeEnum, PostModeType } from './components/post-type/config';
import { defaultTikTokCustomization, TiktokPostCustomization } from './models/tiktok-option.model';

declare let window: any;

@Injectable()
export class ComposerStoreService {
  rootCustomization: Customization = new Customization({});
  customizations$$: BehaviorSubject<Customization[]> = new BehaviorSubject<Customization[]>([]);
  initialValue: Map<string, string>;
  mediaUploading$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  urlMatches$: Observable<string[]> = new Observable<string[]>();

  availablePostableServices$: Observable<PostableService[]> = new Observable<PostableService[]>();

  requirePreviewLink$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  previewLinkLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  previewLinkHistory$$: BehaviorSubject<LinkHistory> = new BehaviorSubject<LinkHistory>(null);
  customHint$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  coupons$$: BehaviorSubject<Coupon[]> = new BehaviorSubject<Coupon[]>([]);

  templateTitle$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  tweetLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  tweetValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  hashtagCount$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  igHashtagCountValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  postLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  instagramValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  linkedInValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  gmbValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  tiktokValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  startSavingDefaultServices$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  defaultServiceLoading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  setUpButtonClickIg$$: Subject<boolean> = new Subject<boolean>();

  scheduledPostCount$$: BehaviorSubject<number> = new BehaviorSubject<number>(null);
  readonly SCHEDULED_POST_LIMIT = 20;

  subscriptions: Subscription[] = [];

  interestingContentFieldSections$$: BehaviorSubject<VFormSection[]> = new BehaviorSubject<VFormSection[]>([]);
  interestingContent: InterestingContentItem[] = null;
  interestingContentCursor$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  interestingContentLoading = false;

  gmbOptionsValidity$$: BehaviorSubject<GmbOptionsValidity> = new BehaviorSubject<GmbOptionsValidity>(
    defaultGmbOptionsValidity(),
  );
  draftId$$: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  templateId$$: BehaviorSubject<string> = new BehaviorSubject<string>(null);
  staticTemplateId$$: BehaviorSubject<string> = new BehaviorSubject<string>('-1');
  requiredContent: RequiredContent[];

  isLoading = false;
  isEditing$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  private postUpdated$$: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  failedServices$$: BehaviorSubject<PostableService[]> = new BehaviorSubject<PostableService[]>([]);
  failedServiceReason: string[] = [];

  workFlowType$$: BehaviorSubject<WorkflowType> = new BehaviorSubject<WorkflowType>(WorkflowType.POST_WORKFLOW);

  connectAccountUrl: string;
  selectedTypes$$: BehaviorSubject<SocialService[]> = new BehaviorSubject(null);
  isCustomizing$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  selectedCustomization$$ = new BehaviorSubject<number>(-1);
  editedTemplate$$ = new BehaviorSubject<boolean>(false);
  isBundleAIPost$$ = new BehaviorSubject<boolean>(false);
  isCSVUploadedPost$$ = new BehaviorSubject<boolean>(false);
  brandAgids$$: BehaviorSubject<Map<string, unknown>> = new BehaviorSubject<Map<string, unknown>>(null);
  activeLocations: Location[] = [];
  brandIdForEditing = null;
  multilocationId = null;
  interestingContentExcludedSources = new Set<string>(['twitterSearches']);
  isProBrand$ = this.brandAgids$$.pipe(
    filter(Boolean),
    map((agidMap) => Array.from(agidMap.values()).map((info: any) => info.agid_info)),
    map((agidsInfo) => agidsInfo.every((businessInfo: any) => businessInfo.isPro)),
    startWith(false),
  );
  networkPostType: PostType = PostType.POST_TYPE_INVALID;
  triggeredFrom: string;

  //TODO: Improve the type of typeConnectionMlSelected$$ to be by number of pages selected
  typeConnectionMlSelected$$: BehaviorSubject<typeServiceMlSelected> = new BehaviorSubject<typeServiceMlSelected>({
    facebook: false,
    gmb: false,
    instagram: false,
    linkedin: false,
  });

  usedPrompt$$: BehaviorSubject<string> = new BehaviorSubject<string>(this.getTextPrompt('${topic}', 'short'));
  composerCallback: (post: PostInterface[], mode: 'single' | 'customize') => void;

  get usedPrompt$(): Observable<string> {
    return this.usedPrompt$$.asObservable().pipe(shareReplay(1));
  }

  get typeConnectionMlSelected$(): Observable<typeServiceMlSelected> {
    return this.typeConnectionMlSelected$$.asObservable();
  }

  private selectedTabSource$$ = new BehaviorSubject<string>('');
  selectedTab$ = this.selectedTabSource$$.asObservable();

  updateTab(tab: string) {
    this.selectedTabSource$$.next(tab);
  }

  readonly postModeSelected = signal<PostModeEnum>(PostModeEnum.SCHEDULED);
  private postModeSelected$$ = new BehaviorSubject<PostModeEnum>(PostModeEnum.SCHEDULED);

  updatePostModeSelected(value: PostModeEnum) {
    this.postModeSelected.set(value);
    this.postModeSelected$$.next(value);
  }

  get postModeSelected$(): Observable<PostModeEnum> {
    return this.postModeSelected$$.asObservable();
  }

  handleDateChangeWithPostMode() {
    if (this.postModeSelected() === PostModeEnum.POST_NOW) {
      this.rootCustomization.scheduledDate$$.next(null);
    }
  }

  getPostModeSelectorConfig(): PostMode[] {
    const brandId = this.configService.brandId;
    const config = this.configService.config();
    const isUWM = config?.partner_id === 'USFS';

    if (brandId || isUWM) {
      return this.postModeType.NO_DRAFT_POST_MODE;
    }

    if (!config?.is_digital_agent) {
      return this.postModeType.NO_HIDDEN_DRAFT_MODE;
    }

    return this.postModeType.HIDDEN_POST_MODE;
  }

  /******************************************************************************************************
   typeServiceMlSelected to SocialService[] ,
   - if we have facebook and gmb selected then we will return [SocialService.FACEBOOK, SocialService.GMB]
   - if we have facebook selected then we will return [SocialService.FACEBOOK]
   - if we have gmb selected then we will return [SocialService.GMB]
   ******************************************************************************************************/

  get mlServiceTypes$(): Observable<SocialService[]> {
    return this.typeConnectionMlSelected$$.pipe(
      map((typeConnectionMlSelected) => {
        const serviceEntries = Object.entries(typeConnectionMlSelected);
        const mlServiceTypes = serviceEntries
          .filter(([, value]) => value)
          .map(([key]) => SocialService[key.toUpperCase()]);

        return mlServiceTypes;
      }),
    );
  }

  fbMentionNameToIdMap: Map<string, string> = new Map<string, string>();
  urlSsidMap: Map<string, string[]> = new Map<string, string[]>();
  placeHolder$: Observable<string>;

  isReels = false;
  isVideo = false;

  @RpcSubject<boolean>('submitting') submitting$$ = new BehaviorSubject<boolean>(false);
  @RpcSubject<SubmitResults>('submitResults') submitResults$$ = new Subject<SubmitResults>();

  constructor(
    private composerService: SMComposerApiService,
    private snackBarService: SnackBarService,
    private dialog: MatDialog,
    private mediaLibraryService: MediaLibraryService,
    private pixabayService: PixabayLibraryService,
    private unsplashService: UnsplashLibraryService,
    private conciergeTaskService: ConciergeTaskService,
    private translateService: TranslateService,
    private publishPostService: PublishPostEventService,
    private configService: ConfigService,
    private videoService: VideoUploadService,
    private socialProfileService: SocialProfileService,
    private templateStoreService: TemplateStoreService,
    private reviewImageService: ReviewImageService,
    private socialServiceService: SocialServiceService,
    private socialPostFeedService: SocialPostFeedStoreService,
    private smFeaturesService: SMFeaturesService,
    private postModeType: PostModeType,
  ) {
    this.connectAccountUrl = '/account/' + this.configService.accountGroupId + '/settings';
    this.rootCustomization.parentTaskId$ = conciergeTaskService.parentTaskId$.pipe(startWith(''));
    this.isCustomizing$
      .pipe(
        withLatestFrom(this.rootCustomization.services$),
        switchMap(([, services]) => this.splitCustomizationsFromRoot(services)),
      )
      .subscribe((customizations) => {
        this.customizations$$.next(customizations);
      });

    // These chain of observables are used to reflect the changes from root customization to customization[]
    // TODO: Refactor this to be reactive and not use a subscription in the store
    this.rootCustomization.postText$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([postText, customizations]) => {
        customizations.forEach((c) => c.updatePostText(postText));
      });
    this.rootCustomization.scheduledDate$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([scheduledDate, customizations]) => {
        customizations.forEach((c) => c.updateScheduledDate(scheduledDate));
      });
    combineLatest([this.rootCustomization.linkUrl$, this.customizations$])
      .pipe(
        filter(([linkUrl]) => !!linkUrl),
        tap(([linkUrl, customizations]: [string, Customization[]]) =>
          customizations.forEach((c) => c.updateLinkUrl(linkUrl)),
        ),
        mergeMap(([linkUrl, customizations]) => {
          const output = customizations.map((c) => this.updateMetaContent(c, linkUrl));
          return combineLatest(output);
        }),
      )
      .subscribe();
    this.rootCustomization.siteMetaContent$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([siteMetaContent, customizations]) => {
        customizations.forEach((c) => c.updateSiteMetaContent(siteMetaContent));
      });
    this.rootCustomization.siteMetaContentLoading$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([siteMetaContentLoading, customizations]) => {
        customizations.forEach((c) => c.updateSiteMetaContentLoading(siteMetaContentLoading));
      });
    this.rootCustomization.uploadedMediaObjects$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([uploadedMediaObjects, customizations]) => {
        const objects = uploadedMediaObjects.map((a) => a);
        customizations.forEach((c) => c.updateUploadedMediaObjects(objects));
      });
    this.rootCustomization.mediaEntries$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([mediaEntries, customizations]) => {
        const objects = mediaEntries.map((a) => a);
        customizations.forEach((c) => c.mediaEntries$$.next([...objects]));
      });

    this.rootCustomization.instagramOptions$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([instagramOptions, customizations]) => {
        customizations.forEach((c) => c.updateInstagramOptions(instagramOptions));
      });

    this.rootCustomization.youtubePostCustomization$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([youtubePostCustomization, customizations]) => {
        customizations.forEach((c) => c.updateYoutubePostCustomization(youtubePostCustomization));
      });

    this.rootCustomization.tiktokPostCustomization$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([tiktokPostCustomization, customizations]) => {
        customizations.forEach((c) => c.updateTikTokPostCustomization(tiktokPostCustomization));
      });

    this.rootCustomization.postType$
      .pipe(withLatestFrom(this.customizations$))
      .subscribe(([postType, customizations]) => {
        customizations.forEach((c) => c.updatePostType(postType));
      });

    this.rootCustomization.services$
      .pipe(
        distinct(),
        switchMap((services) => this.splitCustomizationsFromRoot(services)),
        withLatestFrom(this.customizations$, this.isCustomizing$),
      )
      .subscribe(
        ([allCustomizations, oldCustomizations, isCustomizing]: [Customization[], Customization[], boolean]) => {
          const oldMap = new Map<string, Customization>();
          const newMap = new Map<string, Customization>();
          const allSsids = [];

          oldCustomizations.map((customization) => {
            const keys = Array.from(customization.services$$.getValue().keys());
            oldMap.set(keys[0].ssid, customization);
          });
          allCustomizations.map((customization) => {
            const keys = Array.from(customization.services$$.getValue().keys());
            allSsids.push(keys[0].ssid);
            newMap.set(keys[0].ssid, customization);
          });

          const newCustomizations: Customization[] = [];
          for (const currSsid of allSsids) {
            if (oldMap.has(currSsid)) {
              const existingCustomization = oldMap.get(currSsid);
              if (!isCustomizing) {
                const newMedia = newMap.get(currSsid).uploadedMediaObjects$$.getValue();
                existingCustomization.updateUploadedMediaObjects(newMedia);
              }
              newCustomizations.push(existingCustomization);
            } else {
              newCustomizations.push(newMap.get(currSsid));
            }
          }

          this.customizations$$.next(newCustomizations);
        },
      );

    this.rootCustomization.selectedServices$.pipe(withLatestFrom(this.workFlowType$)).subscribe(([_, workFlowType]) => {
      if (workFlowType === WorkflowType.STORY_WORKFLOW) {
        this.rootCustomization.postType$$.next(PostType.POST_TYPE_STORIES);
      } else if (workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW) {
        this.rootCustomization.postType$$.next(PostType.POST_TYPE_VIDEO);
      }
    });
    this.customHint$();
  }

  get postUpdated$(): Observable<any> {
    return this.postUpdated$$.asObservable();
  }

  get previewLinkHistory$(): Observable<LinkHistory> {
    return this.previewLinkHistory$$.asObservable();
  }

  get previewLinkLoading$(): Observable<boolean> {
    return this.previewLinkLoading$$.asObservable();
  }

  get requirePreviewLink$(): Observable<boolean> {
    return this.requirePreviewLink$$.asObservable();
  }

  customHint$(): Observable<any> {
    this.placeHolder$ = this.customHint$$.pipe(
      map((hint) => hint || this.translateService.instant('COMPOSER.AI_MENU.BUTTON.PLACEHOLDER')),
    ) as Observable<string>;

    this.configService.proFlag$.pipe(take(1)).subscribe((proFlag) => {
      if (proFlag === false) {
        this.configService.noUpgradePath$.pipe(take(1)).subscribe((noUpgradePath) => {
          if (noUpgradePath) {
            this.placeHolder$ = this.customHint$$.pipe(
              map((hint) => hint || this.translateService.instant('COMPOSER.AI_MENU.BUTTON.PLACEHOLDER_STD')),
            ) as Observable<string>;
          }
        });
      }
    });

    return this.placeHolder$;
  }

  get tweetLength$(): Observable<number> {
    return this.tweetLength$$.asObservable();
  }

  get tweetValid$(): Observable<boolean> {
    return this.tweetValid$$.asObservable();
  }

  get hashtagCount$(): Observable<number> {
    return this.hashtagCount$$.asObservable();
  }

  get igHashtagCountValid$(): Observable<boolean> {
    return this.igHashtagCountValid$$.asObservable();
  }

  get postLength$(): Observable<number> {
    return this.postLength$$.asObservable();
  }

  get instagramValid$(): Observable<boolean> {
    return this.instagramValid$$.asObservable();
  }

  get linkedInValid$(): Observable<boolean> {
    return this.linkedInValid$$.asObservable();
  }

  get gmbValid$(): Observable<boolean> {
    return this.gmbValid$$.asObservable();
  }

  get tiktokValid$(): Observable<boolean> {
    return this.tiktokValid$$.asObservable();
  }

  get templateTitle$(): Observable<string> {
    return this.templateTitle$$.asObservable();
  }

  get gmbOptions$(): Observable<GmbOptions> {
    return this.rootCustomization.gmbOptions$;
  }

  get isCustomizing$(): Observable<boolean> {
    return this.isCustomizing$$.asObservable();
  }

  get workFlowType$(): Observable<WorkflowType> {
    return this.workFlowType$$.asObservable();
  }

  get selectedCustomization$(): Observable<number> {
    return this.selectedCustomization$$.asObservable();
  }

  get brandAgids$(): Observable<Map<string, unknown>> {
    return this.brandAgids$$.asObservable();
  }

  changesMade$(): Observable<boolean> {
    return this.customizations$.pipe(
      map((customizations: Customization[]) => customizations || []),
      switchMap((customizations: Customization[]) => {
        if (customizations.length) {
          return combineLatest(customizations.map((c) => c.hasChanges$));
        }
        return of([]);
      }),
      map((hasChanges) => {
        if (!hasChanges || hasChanges.length === 0) {
          return false;
        }
        return hasChanges.reduce((othersHaveChanges, thisHasChanges) => othersHaveChanges || thisHasChanges, false);
      }),
      withLatestFrom(this.rootCustomization.hasChanges$),
      map(([customizationWork, rootWork]) => customizationWork || rootWork),
    );
  }

  startCustomizingByAccount(): void {
    this.isCustomizing$$.next(true);
  }

  stopCustomizingByAccount(): void {
    // commenting this for now because on discard customization we cant be sure
    // if newly customized or already exists
    // this.clearMediaStorage();
    this.isCustomizing$$.next(false);
  }

  splitCustomizationsFromRoot(services: Map<PostableService, string>): Observable<Customization[]> {
    const customizations$: Observable<Customization>[] = [];
    services.forEach((postId, service) => {
      customizations$.push(
        combineLatest([
          this.rootCustomization.postText$,
          this.rootCustomization.scheduledDate$,
          this.rootCustomization.uploadedMediaObjects$,
          this.rootCustomization.mediaEntries$,
          this.rootCustomization.gmbOptions$,
          this.rootCustomization.instagramOptions$,
          this.conciergeTaskService.parentTaskId$,
          this.rootCustomization.linkUrl$,
          this.rootCustomization.siteMetaContent$,
          this.rootCustomization.siteMetaContentLoading$,
          this.rootCustomization.selectedMetaThumbnails$,
          this.rootCustomization.reviewDetails$,
          this.rootCustomization.youtubePostCustomization$,
          this.customizations$,
        ]).pipe(
          take(1),
          map(
            ([
              postText,
              scheduledDate,
              uploadedMedia,
              mediaEntries,
              gmbOptions,
              igOptions,
              parentTaskId,
              previewLink,
              siteMetaContent,
              siteMetaContentLoading,
              thumbnails,
              reviewDetails,
              youtubePostCustomization,
              customizations,
            ]: [
              string,
              Date,
              UploadedFile[],
              MediaEntryInterface[],
              GmbOptions,
              string,
              string,
              string,
              SiteMetaContent,
              boolean,
              Map<string, number>,
              ReviewDetails,
              YoutubePostCustomization,
              Customization[],
            ]) => {
              const copiedMediaObjects = uploadedMedia.map((a) => a);
              const copiedMediaEntries = mediaEntries.map((a) => a);
              const svc = new Map<PostableService, string>();
              svc.set(service, postId);
              if (!siteMetaContent && customizations?.length > 0) {
                const cust = customizations.filter((c) => !!c.services$$.getValue().get(service));
                siteMetaContent = cust?.length > 0 ? cust[0].siteMetaContent$$.getValue() : siteMetaContent;
              }
              const customization = new Customization({
                services: svc,
                postText: postText,
                scheduledDate: scheduledDate,
                uploadedMediaObjects: copiedMediaObjects,
                mediaEntries: copiedMediaEntries,
                gmbOptions: gmbOptions,
                taskId: this.rootCustomization.taskId,
                reviewDetails: reviewDetails,
                linkUrl: previewLink,
                parentTaskId: parentTaskId,
                shortCodes: this.rootCustomization.shortCodes,
                siteMetaContent: siteMetaContent,
                siteMetaContentLoading: siteMetaContentLoading,
                hasGif: this.rootCustomization.hasGif,
                instagramOptions: igOptions,
                youtubePostCustomization: youtubePostCustomization,
              });
              customization.selectedMetaThumbnails.set(service.ssid, thumbnails.get(service.ssid));
              return customization;
            },
          ),
        ),
      );
    });
    return combineLatest(customizations$);
  }

  get customizations$(): Observable<Customization[]> {
    return this.customizations$$.asObservable();
  }

  get mediaUploading$(): Observable<number> {
    return this.mediaUploading$$.asObservable();
  }

  get draftId$(): Observable<string> {
    return this.draftId$$.asObservable();
  }

  get templateId$(): Observable<string> {
    return this.templateId$$.asObservable();
  }

  get isEditing$(): Observable<boolean> {
    return this.isEditing$$.asObservable();
  }

  get uploadedMedia$(): Observable<UploadedFile[]> {
    return combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$]).pipe(
      take(1),
      switchMap(([isCustomizing, selectedCustomization, customizations]) => {
        const customization =
          isCustomizing && selectedCustomization > -1 ? customizations[selectedCustomization] : this.rootCustomization;
        return customization.uploadedMediaObjects$;
      }),
    );
  }

  get succeededServices$(): Observable<PostableService[]> {
    return combineLatest([this.selectedPostableServices$, this.failedServices$]).pipe(
      map(([selected, failed]) => {
        return selected.filter((service: PostableService) => failed.indexOf(service) < 0);
      }),
    );
  }

  get selectedPostableServices$(): Observable<PostableService[]> {
    return this.rootCustomization.selectedServices$;
  }

  get failedServices$(): Observable<PostableService[]> {
    return this.failedServices$$.asObservable();
  }

  get defaultServiceLoading$(): Observable<boolean> {
    return this.defaultServiceLoading$$.asObservable();
  }

  get coupons$(): Observable<Coupon[]> {
    return this.coupons$$.asObservable();
  }

  get scheduledPostCount$(): Observable<number> {
    return this.scheduledPostCount$$.asObservable();
  }

  get submitting$(): Observable<boolean> {
    return this.submitting$$.asObservable();
  }

  updateAiBundlePost(isBundleAIPost: boolean): void {
    this.isBundleAIPost$$.next(isBundleAIPost);
  }

  updateCSVUploadedPost(isCSVUploadedPost: boolean): void {
    this.isCSVUploadedPost$$.next(isCSVUploadedPost);
  }

  initializeAgid(agid: string) {
    this.composerService.accountGroupId = agid;
    this.mediaLibraryService.loadImages();
  }

  getAgidFromConfig() {
    //Initialize agid with multilocation
    this.configService.agidBrand$.pipe(take(1)).subscribe((agid) => {
      this.initializeAgid(agid);
    });
  }

  setSocialProfile(services: Map<string, any>) {
    //triggered from brandservice once all the accounts were fetched  and set a default ssid to be used fetching meta-content
    //if there is a profile  then replace the default agid to match with the default ssid obtained
    const Profiles = Array.from(services.keys())
      .map((agid_key) => {
        return { agid: agid_key, service: services.get(agid_key)?.facebook_info };
      })
      .filter((profile) => !!profile?.service?.ssid && !profile?.service?.tokenIsBroken);

    if (Profiles.length) {
      this.composerService.defaultFBPage = Profiles[0]?.service?.ssid;
      this.composerService.accountGroupId = Profiles[0]?.agid;
    }
  }

  initializeStore(agid?: string, ssids?: string[]): void {
    if (agid) {
      this.initializeAgid(agid);
    }

    if (!this.configService.brandId) {
      this.socialServiceService.loadServices();
      this.availablePostableServices$ = combineLatest([
        this.socialServiceService.availablePostableServices$,
        this.configService.embedded$.pipe(startWith(false)),
      ]).pipe(
        map(([aps, isEmbedded]) => {
          if (!isEmbedded || ssids?.length === 0) return aps;
          //Let filter based on if we have ssids and can find any match
          return aps.filter((ps) => !!ssids?.find((ssid) => ssid === ps.ssid));
        }),
      );

      if (!ssids || ssids.length === 0) {
        this.subscriptions.push(this.getInitialServices$().subscribe());
      }
    }

    this.subscriptions.push(
      this.rootCustomization.postText$
        .pipe(
          withLatestFrom(this.staticTemplateId$),
          throttleTime(200),
          debounceTime(200),
          distinctUntilChanged(),
          filter(([, id]) => id !== '-1'),
          tap(() => {
            this.editedTemplate$$.next(true);
          }),
        )
        .subscribe(),
    );

    this.subscriptions.push(
      combineLatest([this.rootCustomization.postText$, this.selectedPostableServices$])
        .pipe(throttleTime(200), debounceTime(200))
        .subscribe(([postText, selectedServices]: [string, PostableService[]]) => {
          if (this.containsServiceType(selectedServices, SocialService.TWITTER)) {
            this.parseTweet(postText);
          }
          if (this.containsServiceType(selectedServices, SocialService.INSTAGRAM)) {
            this.parseHashtags(postText);
          }
          if (
            this.containsServiceType(selectedServices, SocialService.INSTAGRAM) ||
            this.containsServiceType(selectedServices, SocialService.LINKEDIN_COMPANY) ||
            this.containsServiceType(selectedServices, SocialService.LINKEDIN) ||
            this.containsServiceType(selectedServices, SocialService.GMB)
          ) {
            this.parsePostText(postText);
          }
        }),
    );

    //Listening the postText$ to extract the urls
    this.urlMatches$ = this.rootCustomization.postText$.pipe(
      debounceTime(1000),
      map((text) => this.extractUrls(text)),
      tap((urls) => {
        if (!urls?.length) this.clearPreviewLink();
      }),
      distinctUntilChanged(),
    );

    //Listening the urlMatches$ to extract the preview link
    this.subscriptions.push(
      combineLatest([this.urlMatches$, this.requirePreviewLink$])
        .pipe(
          tap(([urlMatches, requirePreviewLink]: [string[], boolean]) => {
            if (urlMatches.length > 0 && !requirePreviewLink) {
              this.rootCustomization.linkUrl$$.next(urlMatches[0]);
            }
          }),
        )
        .subscribe(),
    );

    this.subscriptions.push(
      this.rootCustomization.linkUrl$
        .pipe(
          filter((link) => !!link),
          distinctUntilChanged(),
          tap(() => this.previewLinkHistory$$.next(null)),
          switchMap((url: string) => this.composerService.fetchPreviewLinkHistory(url)),
          filter((response: { data: Record<string, unknown> }) => !!response.data['urlLastPostedDate']),
          tap((response) => {
            const lastPostedDate = this.setDateToLocalTime(new Date(response.data['urlLastPostedDate'] as Date));
            this.previewLinkHistory$$.next(
              new PreviewLinkHistory({
                lastPostedDate: lastPostedDate,
                isDraft: response.data['isDraft'] as boolean,
              }),
            );
          }),
        )
        .subscribe(),
    );

    this.setTaskIdFromUrl(window.location.href);

    this.subscriptions.push(
      this.configService.proFlag$.subscribe((proFlag) => {
        if (!proFlag) {
          this.fetchScheduledPostCount();
        }
      }),
    );

    this.subscriptions.push(
      this.selectedTypes$$.pipe(skipWhile((type) => !type)).subscribe((types) => {
        this.selectPostableServicesOfTypes(types);
      }),
    );

    this.subscriptions.push(
      this.rootCustomization.reviewDetails$
        .pipe(
          filter((reviewDetails) => !!reviewDetails),
          withLatestFrom(this.configService.config$),
          switchMap(([reviewDetails, smConfig]) => {
            return this.reviewImageService.draw(
              reviewDetails.content,
              reviewDetails.stars,
              reviewDetails.domain,
              reviewDetails.sourceId,
              smConfig.account.name,
            );
          }),
          switchMap((reviewCanvas) => {
            const url = reviewCanvas.toDataURL('image/jpeg');
            const file: File = new File([this.dataURItoBlob(url)], 'auto-created-review-image.jpg', {
              type: 'image/jpeg',
            });
            return this.uploadMedia({ files: [file] }, 'review-image');
          }),
        )
        .subscribe(),
    );
  }

  dataURItoBlob(dataURI): any {
    // convert base64/URLEncoded data component to raw binary data held in a string
    let byteString;
    if (dataURI.split(',')[0].indexOf('base64') >= 0) {
      byteString = atob(dataURI.split(',')[1]);
    } else {
      byteString = unescape(dataURI.split(',')[1]);
    }
    // separate out the mime component
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    // write the bytes of the string to a typed array
    const ia = new Uint8Array(byteString.length);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    return new Blob([ia], { type: mimeString });
  }

  updateMetaContent(customization: Customization, url: string): Observable<any> {
    customization.siteMetaContentLoading$$.next(true);
    return customization.selectedServices$.pipe(
      filter((services) => {
        let updatedServices = services;
        if (updatedServices) {
          const mapSsids = this.urlSsidMap.get(url);
          if (mapSsids) {
            updatedServices = updatedServices.filter((service) => {
              if (!mapSsids.includes(service.ssid)) {
                mapSsids.push(service.ssid);
                return service;
              }
            });
          }
          this.urlSsidMap.set(
            url,
            services.map((item) => item.ssid),
          );
        }
        return updatedServices && updatedServices.length > 0;
      }),
      take(1),
      switchMap((services: PostableService[]) => this.composerService.updateMetaContent(url, services)),
      catchError((err) => {
        customization.siteMetaContent$$.next(null);
        if (err.status === 404) {
          this.snackBarService.errorSnack(this.translateService.instant('PREVIEWS.URL_NOT_FOUND'));
        } else if (err.status === 403 || err.status === 400) {
          this.snackBarService.errorSnack(this.translateService.instant('PREVIEWS.NO_PREVIEW_IMAGE'));
        } else {
          this.snackBarService.errorSnack(this.translateService.instant('PREVIEWS.UNKOWN_ERROR'));
        }
        customization.siteMetaContentLoading$$.next(false);
        return empty;
      }),
      tap((response) => {
        customization.siteMetaContent$$.next(response.message.meta_content.__ALL__);
        customization.resetThumbnails();
        customization.siteMetaContentLoading$$.next(false);
      }),
    );
  }

  getAllInvalidServiceNames(): string[] {
    const serviceNames = [];
    const services = Array.from(this.rootCustomization.services$$.getValue().keys());
    services.forEach((service) => {
      if (service.socialTokenBroken) {
        serviceNames.push(serviceTypeFullName(service.serviceType));
      }
    });
    return serviceNames;
  }

  // ES2016 doesn't support negative lookbehinds, so we have to exclude email addresses from regex matches manually
  extractUrl(text: string): string {
    let url = text.match(CONSTANTS.URL_REGEX);
    let match = url ? url[0] : null;
    while (match && text.indexOf(match) > 0 && text[text.indexOf(match) - 1] === '@') {
      text = text.split(match, 2)[1];
      url = text.match(CONSTANTS.URL_REGEX);
      match = url ? url[0] : null;
    }
    return match;
  }

  // ES2016 doesn't support negative lookbehinds, so we have to exclude email addresses from regex matches manually
  extractUrls(text: string): string[] {
    const matches: string[] = [];
    let url;
    while ((url = text.match(CONSTANTS.URL_REGEX)) !== null) {
      const match = url ? url[0] : null;
      const index = text.indexOf(match);
      if (index === 0 || (index > 0 && text[index - 1] !== '@')) {
        matches.push(match);
      }
      text = text.slice(index + match.length);
    }
    return matches;
  }

  updateCustomHint(customHint: string): void {
    this.customHint$$.next(customHint);
  }

  clearPreviewLink(): void {
    this.rootCustomization.linkUrl$$.next('');
    this.previewLinkHistory$$.next(null);
    this.rootCustomization.siteMetaContentLoading$$.next(false);
    this.rootCustomization.siteMetaContent$$.next(null);
  }

  shortenPreviewLink(previewLink: string): void {
    this.previewLinkLoading$$.next(true);
    this.subscriptions.push(
      this.composerService
        .shortenLink(previewLink)
        .pipe(
          finalize(() => this.previewLinkLoading$$.next(false)),
          withLatestFrom(this.isCustomizing$, this.selectedCustomization$, this.customizations$),
        )
        .subscribe(([result, isCustomizing, selectedCustomization, customizations]) => {
          const customization =
            isCustomizing && selectedCustomization > -1
              ? customizations[selectedCustomization]
              : this.rootCustomization;
          const postText = customization.postText$$.getValue();
          if (postText.length === 0 || postText.endsWith(' ')) {
            this.updatePostText(customization.postText$$.getValue() + result.data.shortUrl);
          } else {
            this.updatePostText(customization.postText$$.getValue() + ' ' + result.data.shortUrl);
          }
          this.updateLinkUrl(result.data.shortUrl);
        }),
    );
  }

  addDynamicContentPlaceholder(placeholder: string): void {
    this.subscriptions.push(
      combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$])
        .pipe(take(1))
        .subscribe(([isCustomizing, selectedCustomization, customizations]) => {
          const customization =
            isCustomizing && selectedCustomization > -1
              ? customizations[selectedCustomization]
              : this.rootCustomization;
          this.updatePostText(customization.postText$$.getValue() + ' ' + placeholder);
        }),
    );
  }

  setTaskIdFromUrl(url: string): void {
    const urlParts = url.split('/');
    const qp = urlParts[urlParts.length - 1].split('?');
    if (qp.length < 2) {
      this.rootCustomization.taskId = undefined;
    } else {
      const taskParams = qp[1].split('parent-task-id=');
      if (taskParams.length < 2) {
        this.rootCustomization.taskId = undefined;
      } else {
        this.rootCustomization.taskId = taskParams[1].split('&')[0];
      }
    }
  }

  applySettings(composerSettings?: ComposerSettings): void {
    this.clearPostSettings();

    this.draftId$$.next(composerSettings.draftId || null);
    this.templateId$$.next(composerSettings.templateId || null);
    this.rootCustomization.scheduledDate$$.next(composerSettings.date || null);
    this.requiredContent = composerSettings.requiredContent || null;
    this.updateAiBundlePost(Boolean(composerSettings?.isBundleAIPost));
    this.updateCSVUploadedPost(Boolean(composerSettings?.isCSVUploadedPost));
    this.composerCallback = composerSettings?.composerCallback;
    this.setPostModeSelected(composerSettings);

    if (composerSettings.workFlowType === WorkflowType.STORY_WORKFLOW) {
      this.workFlowType$$.next(WorkflowType.STORY_WORKFLOW);
    } else if (composerSettings.workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW) {
      this.workFlowType$$.next(WorkflowType.LONG_VIDEO_WORKFLOW);
    }
    if (composerSettings.hasGif) {
      this.rootCustomization.hasGif = true;
    }

    if (composerSettings.templateId && composerSettings.templateTitle) {
      this.templateTitle$$.next(composerSettings.templateTitle);
    }

    if (composerSettings.content) {
      this.updatePostText(composerSettings.content);
    }

    if (composerSettings.composerHint) {
      this.customHint$$.next(composerSettings.composerHint);
    }

    if (composerSettings.composerLink) {
      this.updatePostText(composerSettings.composerLink);
      this.requirePreviewLink$$.next(true);
      this.rootCustomization.linkUrl$$.next(composerSettings.composerLink);
    }

    if (composerSettings.isEditing) {
      this.isEditing$$.next(composerSettings.isEditing);
    }

    if (composerSettings.activeLocations) {
      this.activeLocations = composerSettings.activeLocations;
    }

    if (composerSettings.brandId) {
      this.brandIdForEditing = composerSettings.brandId;
    }

    if (composerSettings.multilocationId) {
      this.multilocationId = composerSettings.multilocationId;
    }

    if (composerSettings.groupedCustomization) {
      this.applyGroupedPostSettings(composerSettings.groupedCustomization);
    }

    if (composerSettings?.customizationSetup) {
      this.applyCustomizationSetup(composerSettings.customizationSetup);
    }

    if (composerSettings?.triggeredFrom) {
      this.triggeredFrom = composerSettings.triggeredFrom;
    }

    if (composerSettings?.postType) {
      this.rootCustomization.postType$$.next(composerSettings.postType);
    }
  }

  // Set the post mode selected based on the composer settings , the default is POST_NOW
  setPostModeSelected(composerSettings?: ComposerSettings) {
    const scheduledDate =
      composerSettings?.groupedCustomization?.scheduledDate$$?.getValue() || composerSettings?.date || null;

    if (composerSettings.draftId) {
      composerSettings?.isHiddenDraft
        ? this.updatePostModeSelected(PostModeEnum.HIDDEN_DRAFT)
        : this.updatePostModeSelected(PostModeEnum.DRAFT);
    }
    if (!composerSettings?.draftId && scheduledDate) {
      this.updatePostModeSelected(PostModeEnum.SCHEDULED);
    }
  }

  // Apply a groupedCustomization to the root
  applyGroupedPostSettings(groupedCustomization: Customization): void {
    this.clearPostableServices();
    if (!this.initialValue) {
      this.initialValue = new Map<string, string>();
      const initialMap = groupedCustomization.services$$.getValue();
      initialMap.forEach((v, k) => {
        this.initialValue.set(k.ssid, v);
      });
    }
    this.selectServices([], groupedCustomization.services$$.getValue());
    this.updatePostText(groupedCustomization.postText$$.getValue());
    const scheduledDate = groupedCustomization.scheduledDate$$.getValue();
    this.rootCustomization.scheduledDate$$.next(scheduledDate ? scheduledDate : null);
    this.rootCustomization.uploadedMediaObjects$$.next(groupedCustomization.uploadedMediaObjects$$.getValue());
    this.rootCustomization.mediaEntries$$.next(groupedCustomization?.mediaEntries$$?.getValue());
    this.rootCustomization.reviewDetails$$.next(groupedCustomization.reviewDetails$$.getValue());
    const gmbOptions: GmbOptions = groupedCustomization.gmbOptions$$.getValue() || new GmbOptions();
    if (gmbOptions && gmbOptions.addCta && gmbOptions.ctaOptions?.action) {
      gmbOptions.ctaOptions.label = this.translateService.instant(GMB_LABEL_ACTION_MAP[gmbOptions.ctaOptions.action]);
    }
    this.rootCustomization.updateGmbOptions(gmbOptions);
    this.rootCustomization.updateInstagramOptions(groupedCustomization.instagramOptions$$.getValue());
    this.rootCustomization.linkUrl$$.next(groupedCustomization.linkUrl$$.getValue());
    this.rootCustomization.siteMetaContent$$.next(groupedCustomization.siteMetaContent$$.getValue());
    this.rootCustomization.youtubePostCustomization$$.next(groupedCustomization.youtubePostCustomization$$.getValue());
    this.rootCustomization.postType$$.next(groupedCustomization?.postType$$?.getValue() || PostType.POST_TYPE_INVALID);
    this.rootCustomization.tiktokPostCustomization$$.next(groupedCustomization.tiktokPostCustomization$$.getValue());
    this.stopCustomizingByAccount();
  }

  clearPostSettings(): void {
    this.templateTitle$$.next('');
    this.rootCustomization.clear();
    this.customizations$$.next([]);
    this.isCustomizing$$.next(false);
    this.workFlowType$$.next(WorkflowType.POST_WORKFLOW);
  }

  resetForTemplate(): void {
    this.staticTemplateId$$.next('-1');
    this.editedTemplate$$.next(false);
    this.rootCustomization.updateUploadedMediaObjects([]);
    this.customizations$$.getValue().forEach((c) => {
      c.updateUploadedMediaObjects([]);
    });
  }

  clearMediaStorage(): void {
    this.customizations$
      .pipe(
        take(1),
        mergeMap((customizations) => from(customizations)),
        mergeMap((customization) => customization.uploadedMediaObjects$.pipe(take(1))),
        mergeMap((uploadedMediaObjects) => from(uploadedMediaObjects)),
        filter((media) => !!media?.path),
        mergeMap((media) => this.composerService.deleteUploadedImage(media.path)),
      )
      .subscribe();
  }

  resetStore(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.staticTemplateId$$.next('-1');
    this.editedTemplate$$.next(false);

    this.draftId$$.next(null);
    this.templateId$$.next(null);
    this.isEditing$$.next(false);
    this.templateTitle$$.next('');

    this.requiredContent = null;
    this.requirePreviewLink$$.next(false);
    this.previewLinkHistory$$.next(null);
    this.customHint$$.next('');

    this.tweetLength$$.next(0);
    this.tweetValid$$.next(false);

    this.postLength$$.next(0);
    this.linkedInValid$$.next(false);
    this.instagramValid$$.next(false);
    this.gmbValid$$.next(false);
    this.tiktokValid$$.next(false);

    this.failedServices$$.next([]);
    this.defaultServiceLoading$$.next(false);

    this.isCustomizing$$.next(false);
    this.rootCustomization.clear();
    this.initialValue = null;
    this.customizations$$.next([]);

    this.interestingContent = [];
    this.interestingContentCursor$$.next(0);
    this.interestingContentFieldSections$$.next([]);
    this.fbMentionNameToIdMap = new Map<string, string>();
    this.updateYoutubeOptions(defaultYoutubeCustomization());
    this.updateTikTokOptions(defaultTikTokCustomization());
    this.isVideo = false;
    this.initializeStore(this.composerService.accountGroupId, []);
    this.updateAiBundlePost(false);
    this.updatePostModeSelected(PostModeEnum.SCHEDULED);
    this.triggeredFrom = '';
  }

  selectServices(services?: PostableService[], existingMap?: Map<PostableService, string>): void {
    if (this.configService.brandId) {
      const selected = new Map<PostableService, string>();
      services.forEach((s) => selected.set(s, CONSTANTS.NEW_POST_ID));
      this.rootCustomization.selectServices(selected);
    } else {
      this.subscriptions.push(
        this.availablePostableServices$
          .pipe(
            filter((aps) => aps.length > 0),
            take(1),
          )
          .subscribe((aps) => {
            const selected = new Map<PostableService, string>();
            if (existingMap) {
              existingMap.forEach((v: string, k: PostableService) => {
                const service = aps.find((a) => a.ssid === k.ssid);
                if (service && !!v) {
                  selected.set(service, v);
                }
              });
            } else {
              services.forEach((s) => selected.set(s, CONSTANTS.NEW_POST_ID));
            }
            this.rootCustomization.selectServices(selected);
          }),
      );
    }
  }

  deselectPostableService(services: PostableService[]): void {
    this.rootCustomization.deselectServices(services);
  }

  selectAllPostableServices(): void {
    this.subscriptions.push(
      this.availablePostableServices$.pipe(take(1)).subscribe((services) => {
        const validServices = services.filter(
          (service) => !service.socialTokenBroken && service.serviceType !== SocialService.WORDPRESS_BLOG,
        );

        const servicesMap = new Map<PostableService, string>();
        validServices.forEach((service) => {
          servicesMap.set(service, CONSTANTS.NEW_POST_ID);
        });
        this.filterServiceMap(servicesMap, this.workFlowType$$.getValue());
        this.rootCustomization.selectServices(servicesMap);
      }),
    );
  }

  private filterServiceMap(servicesMap: Map<PostableService, string>, workFlowType: WorkflowType) {
    if (workFlowType === WorkflowType.STORY_WORKFLOW) {
      servicesMap.forEach((value, key) => {
        if (!STORY_WORKFLOW_NETWORKS.includes(key.serviceType)) {
          servicesMap.delete(key);
        }
      });
    } else if (workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW) {
      servicesMap.forEach((value, key) => {
        if (!LONG_VIDEO_WORKFLOW_NETWORKS.includes(key.serviceType)) {
          servicesMap.delete(key);
        }
      });
    }
    // Add LongVideo Workflow filter here
  }

  selectPostableServicesOfTypes(types: SocialService[]): void {
    this.subscriptions.push(
      this.availablePostableServices$
        .pipe(
          take(1),
          map((services) => services.filter((svc) => types.indexOf(svc.serviceType) >= 0)),
        )
        .subscribe((servicesOfType) => {
          const servicesMap = new Map<PostableService, string>();
          servicesOfType.forEach((s) => {
            servicesMap.set(s, CONSTANTS.NEW_POST_ID);
          });
          this.rootCustomization.selectServices(servicesMap);
        }),
    );
  }

  setSelectedServiceTypes(types: SocialService[]): void {
    this.selectedTypes$$.next(types);
  }

  clearPostableServices(): void {
    this.customizations$$.next([]);
    this.rootCustomization.clearServices();
  }

  saveDefaultServices(): Observable<any> {
    return this.startSavingDefaultServices$.pipe(
      filter((flag) => flag === true),
      withLatestFrom(this.selectedPostableServices$),
      map(([, selectedServices]: [boolean, PostableService[]]) => selectedServices.map((service) => service.ssid)),
      switchMap((ssidList: string[]) => this.composerService.saveDefaultServices(ssidList)),
      catchError(() => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.SAVE_DEFAULT_SERVICES_ERROR'));
        return empty;
      }),
      tap(() => {
        this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.SAVE_DEFAULT_SERVICES_SUCCEED'));
        this.startSavingDefaultServices$$.next(false);
      }),
    );
  }

  getInitialServices$(): Observable<any> {
    this.defaultServiceLoading$$.next(true);
    return combineLatest([
      this.availablePostableServices$,
      this.composerService.getDefaultServices(),
      this.selectedTypes$$,
      this.workFlowType$$,
    ]).pipe(
      skipWhile(([availableServices]) => !availableServices),
      finalize(() => this.defaultServiceLoading$$.next(false)),
      map(([availableServices, defaultServices, selectedTypes, workFlowType]) => [
        availableServices,
        defaultServices.data,
        selectedTypes,
        workFlowType,
      ]),
      catchError((err) => {
        // TODO: Remove this 404 catcher for no default services gscoular 20181107
        if (err.status !== 404) {
          const config = new MatSnackBarConfig();
          config.duration = 10000;
          config.viewContainerRef = null;
          config.horizontalPosition = 'left';
          this.snackBarService.errorSnack(
            this.translateService.instant('SNACKBAR.GET_DEFAULT_SERVICES_ERROR'),
            undefined,
            config,
          );
        }
        // Don't select any services if we error getting defaults.
        return empty;
      }),
      take(1),
      tap(
        ([availablePostableServices, defaultServices, selectedTypes, workFlowType]: [
          PostableService[],
          string[],
          SocialService[],
          WorkflowType,
        ]) => {
          if (selectedTypes) {
            this.selectPostableServicesOfTypes(selectedTypes);
          } else if (!defaultServices) {
            this.selectAllPostableServices();
          } else {
            const aps = availablePostableServices.filter((service) => defaultServices.indexOf(service.ssid) !== -1);
            const servicesMap = new Map<PostableService, string>();
            aps.forEach((s) => {
              servicesMap.set(s, CONSTANTS.NEW_POST_ID);
            });
            if (workFlowType === WorkflowType.STORY_WORKFLOW) {
              servicesMap.forEach((value, key) => {
                if (key.serviceType != SocialService.INSTAGRAM) {
                  servicesMap.delete(key);
                }
              });
            } else if (workFlowType === WorkflowType.LONG_VIDEO_WORKFLOW) {
              servicesMap.forEach((value, key) => {
                if (key.serviceType != SocialService.YOUTUBE_CHANNEL) {
                  servicesMap.delete(key);
                }
              });
            }
            this.rootCustomization.selectServices(servicesMap);
          }
        },
      ),
    );
  }

  containsServiceType(services: PostableService[], type: SocialService): boolean {
    return (
      services.filter((svc: PostableService) => {
        if (svc.serviceType === SocialService.LINKEDIN) {
          return SocialService.LINKEDIN_COMPANY === type;
        }
        return svc.serviceType === type;
      }).length > 0
    );
  }

  selectedServicesOfType(type: SocialService): Observable<PostableService[]> {
    return combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$]).pipe(
      mergeMap(([isCustomizing, selectedCustomization, customizations]) =>
        iif(
          () => isCustomizing && selectedCustomization > -1,
          customizations[selectedCustomization]?.selectedServices$,
          this.rootCustomization?.selectedServices$,
        ),
      ),
      map((svcs) => {
        return svcs.filter((svc) => svc.serviceType === type);
      }),
    );
  }

  serviceSelected$(serviceType: SocialService): Observable<boolean> {
    return this.selectedServicesOfType(serviceType).pipe(map((services) => services.length > 0));
  }

  get facebookSelected$(): Observable<boolean> {
    return this.serviceSelected$(SocialService.FACEBOOK);
  }

  get twitterSelected$(): Observable<boolean> {
    return this.serviceSelected$(SocialService.TWITTER);
  }

  get instagramSelected$(): Observable<boolean> {
    return this.serviceSelected$(SocialService.INSTAGRAM);
  }

  get gmbSelected$(): Observable<boolean> {
    const isGmbSelectedInSingleLocation = this.serviceSelected$(SocialService.GMB);
    const isGmbSelectedInMultilocation = this.typeConnectionMlSelected$.pipe(map((selection) => selection.gmb));

    return combineLatest([isGmbSelectedInSingleLocation, isGmbSelectedInMultilocation]).pipe(
      map(([single, multi]) => single || multi),
    );
  }

  get linkedinSelected$(): Observable<boolean> {
    return combineLatest([
      this.serviceSelected$(SocialService.LINKEDIN_COMPANY),
      this.serviceSelected$(SocialService.LINKEDIN),
    ]).pipe(
      map(([linkedinCompanySelected, linkedinUserSelected]) => {
        return linkedinCompanySelected || linkedinUserSelected;
      }),
    );
  }

  get youtubeSelected$(): Observable<boolean> {
    return this.serviceSelected$(SocialService.YOUTUBE_CHANNEL);
  }

  get tiktokSelected$(): Observable<boolean> {
    return this.serviceSelected$(SocialService.TIKTOK_ACCOUNT);
  }

  get staticTemplateId$(): Observable<string> {
    return this.staticTemplateId$$.asObservable();
  }

  updateStaticTemplateId(id: string): void {
    this.staticTemplateId$$.next(id);
  }

  get unEditedTemplate$(): Observable<boolean> {
    return combineLatest([this.staticTemplateId$, this.editedTemplate$$.asObservable()]).pipe(
      map(([templateId, isEdited]: [string, boolean]) => templateId !== '-1' && !isEdited),
    );
  }

  updatePostText(text: string): void {
    this.subscriptions.push(
      combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$])
        .pipe(take(1))
        .subscribe(([isCustomizing, selectedCustomization, customizations]) => {
          const customization =
            isCustomizing && selectedCustomization > -1
              ? customizations[selectedCustomization]
              : this.rootCustomization;
          customization.updatePostText(text);
        }),
    );
  }

  updateLinkUrl(url: string): void {
    this.subscriptions.push(
      combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$])
        .pipe(
          take(1),
          switchMap(([isCustomizing, selectedCustomization, customizations]) => {
            const cmz =
              isCustomizing && selectedCustomization > -1
                ? customizations[selectedCustomization]
                : this.rootCustomization;
            cmz.updateLinkUrl(url);
            return this.updateMetaContent(cmz, url);
          }),
        )
        .subscribe(),
    );
  }

  updateTemplateTitle(title: string): void {
    this.templateTitle$$.next(title);
  }

  getServiceById(ssid: string): PostableService {
    const services = Array.from(this.rootCustomization.services$$.getValue().keys());
    for (const service of services) {
      if (service.ssid === ssid) {
        return service;
      }
    }
  }

  changePublishTime(event: { value: boolean }): void {
    const now = new Date();
    this.setScheduledDate(event.value ? new Date(now.setMinutes(now.getMinutes() + 5)) : null);
  }

  setScheduledDate(date: Date): void {
    this.rootCustomization.updateScheduledDate(date);
  }

  setScheduledTime(time: Date): void {
    this.rootCustomization.updateScheduledTime(time);
  }

  parseTweet(postText?: string): void {
    postText = postText || this.rootCustomization.postText$$.getValue();
    const parsedTweet = twitterText.parseTweet(postText);
    this.tweetLength$$.next(parsedTweet.weightedLength);
    this.tweetValid$$.next(parsedTweet.valid);
  }

  parseHashtags(postText?: string): void {
    postText = postText || this.rootCustomization.postText$$.getValue();
    const hashtags = postText.match(HASHTAG_REGEX);
    const hashtagCount = hashtags ? hashtags.length : 0;
    this.hashtagCount$$.next(hashtagCount);
    const valid = hashtagCount === 0 || hashtagCount <= CONSTANTS.SERVICE_MAX_HASHTAGS.IG_USER;
    this.igHashtagCountValid$$.next(valid);
  }

  parsePostText(postText?: string): void {
    const postTextParsed = postText || this.rootCustomization.postText$$.getValue();
    this.postLength$$.next(postTextParsed.length);
    const liTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.LINKEDIN_CHARS_LIMIT && !!postTextParsed?.length;
    const gmbTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.GMB_CHARS_LIMIT && !!postTextParsed?.length;
    const igTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.INSTAGRAM_CHARS_LIMIT && !!postTextParsed?.length;
    const tiktokTextValid =
      postTextParsed.length <= CONSTANTS.SERVICE_MAX_CHAR_COUNT.TIKTOK_CHAR_LIMIT && !!postTextParsed?.length;

    this.linkedInValid$$.next(liTextValid);
    this.gmbValid$$.next(gmbTextValid);
    this.instagramValid$$.next(igTextValid);
    this.tiktokValid$$.next(tiktokTextValid);
  }

  uploadMedia(element: any, uploadFolder?: string): Observable<UploadedFile[]> {
    if (!element.files) {
      return;
    }

    return combineLatest([this.gmbSelected$, this.instagramSelected$]).pipe(
      take(1),
      switchMap(([gmbSelected, instagramSelected]) => {
        const uploads: Observable<UploadedFile>[] = [];
        for (let i = 0; i < element.files.length; i++) {
          const file: File = element.files[i];
          if (!file) {
            this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.IMAGE_DETECT_ERROR'));
            return;
          }
          if (gmbSelected && file.type === 'image/gif') {
            const matBarConfig = new MatSnackBarConfig();
            matBarConfig.duration = 5000;
            this.snackBarService.errorSnack(
              this.translateService.instant('SNACKBAR.IMAGE_GIF_ERROR'),
              null,
              matBarConfig,
            );
            return;
          }
          if (CONSTANTS.AcceptedMediaMimeTypes.indexOf(file.type) < 0) {
            this.snackBarService.errorSnack(
              this.translateService.instant('SNACKBAR.IMAGE_UNSUPPORTED_ERROR', { fileType: file.type }),
            );
            return;
          }
          if (CONSTANTS.AcceptedImageMimeTypes.indexOf(file.type) >= 0) {
            uploads.push(this.uploadImage(element, uploadFolder, i, -1, instagramSelected));
          } else {
            uploads.push(this.uploadVideo(element, i, instagramSelected));
          }
        }
        return combineLatest(uploads);
      }),
    );
  }

  swapMedias(currIndex: number, newIndex: number) {
    return this.currSelectedCustomization$.pipe(
      map((customization: Customization) => {
        const objects = customization.uploadedMediaObjects$$.getValue();
        const mediaEntries = customization.mediaEntries$$.getValue();

        [objects[currIndex], objects[newIndex]] = [objects[newIndex], objects[currIndex]];
        [mediaEntries[currIndex], mediaEntries[newIndex]] = [mediaEntries[newIndex], mediaEntries[currIndex]];

        customization.updateUploadedMediaObjects([...objects]);
        customization.mediaEntries$$.next([...mediaEntries]);
      }),
    );
  }

  uploadImage(
    element: any,
    uploadFolder?: string,
    index?: number,
    replace = -1,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    instagramSelected = false,
  ): Observable<UploadedFile> {
    index = index || 0;
    const file: File = element.files[index];

    return this.setImageAsSrc(file).pipe(
      catchError(() => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.UNKNOWN_ERROR'));
        return empty;
      }),
      finalize(() => {
        this.mediaUploading$$.next(0);
      }),
      switchMap((uploadedImage: UploadedImage) => {
        this.mediaUploading$$.next(3);
        return combineLatest([this.composerService.uploadImage(file, uploadFolder), of(uploadedImage)]);
      }),
      catchError(() => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.FILE_TOO_LARGE_FOR_UPLOAD'));
        return empty;
      }),
      withLatestFrom(this.currSelectedCustomization$),
      take(1),
      map(([[response, uploadedImage], customization]: [[any, UploadedImage], Customization]) => {
        this.mediaUploading$$.next(0);
        if (!response) {
          return;
        }
        const objects = customization.uploadedMediaObjects$$.getValue();

        let lastPostedDate: Date = null;
        if (response.data[0].imageLastPostedDate) {
          lastPostedDate = this.setDateToLocalTime(new Date(response.data[0].imageLastPostedDate));
        }

        uploadedImage.url = response.data[0].fileurl;
        uploadedImage.fileType = file.type === 'image/gif' ? FileType.GIF : FileType.IMAGE;
        uploadedImage.path = response.data[0].filepath;
        uploadedImage.lastPostedDate = lastPostedDate;
        uploadedImage.isDraft = response.data[0].isDraft;

        if (replace === -1) {
          customization.updateUploadedMediaObjects([...objects, uploadedImage]);
          customization.updateMediaEntries(objects, uploadedImage);
        } else {
          objects[replace] = uploadedImage;
          customization.updateUploadedMediaObjects(objects);
          customization.updateMediaEntry(replace, uploadedImage);
        }
        return uploadedImage;
      }),
    );
  }

  setImageAsSrc(file: File | UploadedFile): Observable<UploadedImage> {
    const img = new Image();
    this.setElementSrc(img, file);
    return fromEvent(img, 'load').pipe(
      map(() => {
        const uploadedImage = new UploadedImage({
          name: file.name,
          size: file.size,
          naturalWidth: img.naturalWidth,
          naturalHeight: img.naturalHeight,
        });
        uploadedImage.errors = ValidateImage.validateForAllServices(uploadedImage, this.networkPostType);
        return uploadedImage;
      }),
    );
  }

  validateInstagramImage() {
    const validatedUplodedMedia = this.rootCustomization.uploadedMediaObjects$$.getValue().map((uploadedFile) => {
      const uploadedImage = new UploadedImage(uploadedFile);
      uploadedFile.errors = ValidateImage.validateForAllServices(uploadedImage, this.networkPostType);
      return uploadedFile;
    });
    this.rootCustomization.uploadedMediaObjects$$.next(validatedUplodedMedia);
  }

  get currSelectedCustomization$(): Observable<Customization> {
    return combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$]).pipe(
      take(1),
      map(([isCustomizing, selectedCustomization, customizations]) => {
        return isCustomizing && selectedCustomization > -1
          ? customizations[selectedCustomization]
          : this.rootCustomization;
      }),
    );
  }

  uploadVideo(element: any, index?: number, instagramSelected = false): Observable<UploadedFile> {
    index = index || 0;
    const file: File = element.files[index];

    return this.setVideoAsSrc(file).pipe(
      catchError(() => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.UNKNOWN_ERROR'));
        return empty;
      }),
      finalize(() => {
        this.mediaUploading$$.next(0);
      }),
      switchMap((result: UploadedVideo | MediaLimits) => {
        if ((result as MediaLimits).message) {
          this.snackBarService.errorSnack(
            this.translateService.instant((result as MediaLimits).message, (result as MediaLimits).values),
          );
          return;
        } else {
          this.mediaUploading$$.next(3);
          return combineLatest([this.videoService.uploadVideo(file, this.configService.brandId), of(result)]);
        }
      }),
      withLatestFrom(this.currSelectedCustomization$),
      take(1),
      map(([[videoUrl, video], customization]: [[string, UploadedVideo], Customization]) => {
        this.mediaUploading$$.next(0);
        if (!videoUrl) {
          this.snackBarService.errorSnack(this.translateService.instant('COMPOSER.UPLOAD_MEDIA.UPLOAD_VIDEO_AGAIN'));
          return;
        }

        //check if the format video can impact the carousel process or vice versa
        video.url = instagramSelected ? this.formatVideoInstagram(videoUrl) : videoUrl;
        video.fileType = FileType.VIDEO;

        customization.updateMediaEntries(customization.uploadedMediaObjects$$.getValue(), video);
        customization.updateUploadedMediaObjects([...customization.uploadedMediaObjects$$.getValue(), video]);
        return video;
      }),
    );
  }

  setVideoAsSrc(file: File | UploadedFile): Observable<UploadedVideo | MediaLimits> {
    const video: HTMLVideoElement = document.createElement('video') as HTMLVideoElement;
    this.setElementSrc(video, file);
    return merge(this.videoSuccess(file, video), this.videoError(video));
  }

  videoError(video: HTMLVideoElement): Observable<MediaLimits> {
    return fromEvent(video, 'error').pipe(
      map((event: any) => {
        let errors: MediaLimits = null;
        try {
          const err = event.path[0].error;
          if (err.message.indexOf('DEMUXER_ERROR_NO_SUPPORTED_STREAMS') >= 0) {
            errors = { source: '', message: this.translateService.instant('SNACKBAR.VIDEO_LOADING_FORMAT_ERROR') };
          }
        } catch (error) {
          errors = { source: '', message: this.translateService.instant('SNACKBAR.VIDEO_LOADING_ERROR') };
        }
        return errors;
      }),
    );
  }

  videoSuccess(file: File | UploadedFile, video: HTMLVideoElement): Observable<UploadedVideo> {
    return fromEvent(video, 'loadedmetadata').pipe(
      map(() => {
        const uploadedVideo = new UploadedVideo({
          name: file.name,
          size: file.size,
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          duration: video.duration,
        });
        uploadedVideo.errors = ValidateVideo.validateForAllServices(uploadedVideo);
        return uploadedVideo;
      }),
    );
  }

  setElementSrc(element: HTMLImageElement | HTMLVideoElement, file: File | UploadedFile): void {
    if (file instanceof File) {
      element.src = window.URL.createObjectURL(file);
    } else {
      element.src = (file as UploadedFile).url;
    }
  }

  replaceUploadedImage(newFile: File, replaceAt: number): void {
    this.subscriptions.push(
      this.uploadImage({ files: [newFile] }, '', 0, replaceAt)
        .pipe(take(1))
        .subscribe(),
    );
  }

  deleteMedia(): void {
    this.rootCustomization.uploadedMediaObjects$$.next([]);
    this.subscriptions.push(
      this.customizations$.pipe(take(1)).subscribe((customizations) => {
        customizations.forEach((customization) => {
          customization.updateUploadedMediaObjects([]);
        });
      }),
    );
  }

  //mediaEntry handles carousel stuff and uploadedMedia the general media .
  deleteUploadedImage(index: number): void {
    this.subscriptions.push(
      this.currSelectedCustomization$
        .pipe(
          switchMap((customization) =>
            combineLatest([of(customization), customization.uploadedMediaObjects$, customization.mediaEntries$]),
          ),
          take(1),
          map(
            ([customization, uploadedMedia, mediaEntries]: [Customization, UploadedFile[], MediaEntryInterface[]]) => {
              const uploadedImage = uploadedMedia[index];
              const mediaEntry = mediaEntries[index];
              if (uploadedImage) {
                uploadedMedia.splice(index, 1);
              }
              if (mediaEntry) {
                mediaEntries.splice(index, 1);
              }
              customization.updateUploadedMediaObjects(uploadedMedia);
              customization.mediaEntries$$.next([...mediaEntries]);
              return uploadedImage && uploadedImage.path ? uploadedImage.path : '';
            },
          ),
          filter((imagePath) => imagePath !== ''),
          switchMap((imagePath) => {
            // Excluding customizing mode from asset delete for now
            if (this.isCustomizing$$.getValue() === false) {
              this.composerService.deleteUploadedImage(imagePath).subscribe();
            }
            return of(null);
          }),
        )
        .subscribe(),
    );
  }

  deleteUploadedVideo(): void {
    this.mediaUploading$$.next(0);
    this.subscriptions.push(
      this.currSelectedCustomization$
        .pipe(
          switchMap((customization) => {
            return combineLatest([of(customization), customization.uploadedMediaObjects$, customization.mediaEntries$]);
          }),
          take(1),
          tap(
            ([customization, uploadedObjects, mediaEntries]: [
              Customization,
              UploadedFile[],
              MediaEntryInterface[],
            ]) => {
              const index = uploadedObjects.findIndex((object) => object.fileType === FileType.VIDEO);
              const mediaEntry = mediaEntries[index];
              if (index >= 0) {
                uploadedObjects.splice(index, 1);
              }
              if (mediaEntry) {
                mediaEntries.splice(index, 1);
              }
              this.isVideo = false;
              customization.mediaEntries$$.next([...mediaEntries]);
              customization.updateUploadedMediaObjects(uploadedObjects);
            },
          ),
        )
        .subscribe(),
    );
  }

  cycleMetaThumbnail(ssid: string, direction: 'next' | 'previous'): void {
    const targetCustomization = this.customizations$$.getValue().find((customization) => {
      const services: PostableService[] = Array.from(customization.services$$.getValue().keys());
      return !!services.find((service) => service.ssid === ssid);
    });
    if (!targetCustomization) {
      return;
    }
    const currentIndex = targetCustomization.selectedMetaThumbnails.get(ssid);
    const len = targetCustomization.siteMetaContent$$.getValue().images.length;
    let newIndex = currentIndex;
    if (currentIndex < len - 1 && direction === 'next') {
      newIndex += 1;
    } else if (currentIndex === len - 1 && direction === 'next') {
      newIndex = 0;
    } else if (currentIndex > 0 && direction === 'previous') {
      newIndex -= 1;
    } else if (currentIndex === 0 && direction === 'previous') {
      newIndex = len - 1;
    }
    targetCustomization.selectThumbnail(ssid, newIndex);
  }

  clearMetaThumbnail(ssid: string): void {
    this.subscriptions.push(
      this.customizations$.pipe(take(1)).subscribe((customizations: Customization[]) => {
        customizations.forEach((c) => c.clearMetaThumbnail(ssid));
      }),
    );
  }

  suggestMessage(
    prompt: string,
    postLength: MessageLength,
    templateType: TemplateType,
    customInstructions?: string,
    useCustomInstructions = true,
  ): Observable<string> {
    const metadata = {
      sm_context: !this.configService.brandId ? 'single-location' : 'multi-location',
      ...(useCustomInstructions && customInstructions && { custom_instructions: customInstructions }),
    };
    return this.composerService
      .suggestMessage(prompt, this.configService.accountGroupId, postLength, templateType, metadata)
      .pipe(tap(() => this.updateUsedPrompt(prompt)));
  }

  shortenLink(link: string, description?: string): Observable<boolean> {
    if (!link || link.length < 1) {
      return;
    }
    if (!description) {
      description = '';
    }
    this.previewLinkLoading$$.next(true);
    return this.composerService.shortenLink(link, description).pipe(
      finalize(() => this.previewLinkLoading$$.next(false)),
      withLatestFrom(this.isCustomizing$, this.selectedCustomization$, this.customizations$),
      map(([result, isCustomizing, selectedCustomization, customizations]) => {
        const customization =
          isCustomizing && selectedCustomization > -1 ? customizations[selectedCustomization] : this.rootCustomization;
        this.updatePostText(customization.postText$$.getValue() + ' ' + result.data.shortUrl);
        this.updateLinkUrl(result.data.shortUrl);
        return true;
      }),
    );
  }

  shortenLinks(): void {
    this.previewLinkLoading$$.next(true);
    this.subscriptions.push(
      this.urlMatches$
        .pipe(
          take(1),
          finalize(() => this.previewLinkLoading$$.next(false)),
          switchMap((matches: string[]) => {
            const shortenLinkRequests = matches.map((match) => this.composerService.shortenLink(match));
            return forkJoin(shortenLinkRequests);
          }),
          withLatestFrom(this.rootCustomization.postText$, this.urlMatches$),
          take(1),
          tap(([shortenResults, postText, matches]: [ShortenLinkResponse[], string, string[]]) => {
            matches.forEach((match, i) => {
              postText = postText.replace(match, shortenResults[i].data.shortUrl);
            });
            this.rootCustomization.updatePostText(postText);
          }),
        )
        .subscribe(),
    );
  }

  insertCoupon(coupon: Coupon): void {
    if (!coupon) {
      return;
    }
    this.subscriptions.push(
      this.shortenLink(coupon.url).subscribe((success: boolean) => {
        if (!success) {
          this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.INSERT_COUPON_ERROR'));
        }
      }),
    );
  }

  insertEmoji(emoji: EmojiData, idx: number): void {
    this.subscriptions.push(
      combineLatest([this.isCustomizing$, this.selectedCustomization$, this.customizations$])
        .pipe(take(1))
        .subscribe(([isCustomizing, selectedCustomization, customizations]) => {
          const customization =
            isCustomizing && selectedCustomization > -1
              ? customizations[selectedCustomization]
              : this.rootCustomization;
          let postText = customization.postText$$.getValue();
          if (typeof idx !== 'undefined' && idx > -1) {
            postText = postText.slice(0, idx) + emoji.native + postText.slice(idx, postText.length);
          } else {
            postText += emoji.native;
          }
          customization.updatePostText(postText);
        }),
    );
  }

  fetchInterestingContentSections(): void {
    this.interestingContentLoading = true;
    this.subscriptions.push(
      this.interestingContentFieldSections$$.asObservable().subscribe((sections) => {
        if (sections.length > 0) {
          this.interestingContent = [];
          this.fetchInterestingContent();
        }
      }),
    );
    this.composerService.fetchInterestingContentFields().subscribe((response) => {
      const sections: VFormSection[] = [];
      const data = response.data;
      data.sections.forEach((section) => {
        if (!this.interestingContentExcludedSources.has(section.name)) {
          sections.push({ label: section.label, name: section.name, options: [] });
        }
      });
      data.formdata.forEach((formdata) => {
        if (!this.interestingContentExcludedSources.has(formdata.name)) {
          const section = sections.filter((sec) => sec.name === formdata.name)[0];
          section.options = formdata.options;
        }
      });
      this.interestingContentFieldSections$$.next(sections);
    });
  }

  fetchInterestingContent(
    selectedRssCategories?: string[],
    selectedTwitterSearches?: string[],
    ignoreCursor = true,
    reload?: boolean,
  ): void {
    this.interestingContentLoading = true;
    if (reload) {
      this.interestingContent = [];
      this.interestingContentCursor$$.next(0);
    }

    this.subscriptions.push(
      this.interestingContentCursor$$
        .asObservable()
        .pipe(
          take(1),
          switchMap((cursor) => {
            return ignoreCursor
              ? this.composerService.fetchInterestingContent(selectedRssCategories, selectedTwitterSearches)
              : this.composerService.fetchInterestingContent(selectedRssCategories, selectedTwitterSearches, cursor);
          }),
        )
        .subscribe((response) => {
          if (!ignoreCursor) {
            this.interestingContentCursor$$.next(response.data.cursor);
          }
          this.interestingContent = this.interestingContent.concat(response.data.feed_data);
          this.interestingContentLoading = false;
        }),
    );
  }

  updateInstagramOptions(shoppableLink: string): void {
    combineLatest([
      this.isCustomizing$,
      this.customizations$,
      this.customizations$.pipe(
        switchMap((customizations) => combineLatest(customizations.map((c) => c.serviceTypes$))),
      ),
    ])
      .pipe(take(1))
      .subscribe(([isCustomizing, customizations, serviceTypes]) => {
        shoppableLink = this.getUpdatedLink(shoppableLink);
        if (isCustomizing) {
          customizations.forEach((customization, i) => {
            if (serviceTypes[i].indexOf(SocialService.INSTAGRAM) >= 0) {
              customization.updateInstagramOptions(shoppableLink);
            }
          });
        } else {
          this.rootCustomization.updateInstagramOptions(shoppableLink);
        }
      });
  }

  getUpdatedLink(shoppableLink: string): string {
    const httpsPrefix = 'https://';
    const httpPrefix = 'http://';
    if (!!shoppableLink && !shoppableLink.startsWith(httpsPrefix) && !shoppableLink.startsWith(httpPrefix)) {
      shoppableLink = `${httpsPrefix}${shoppableLink}`;
    }
    return shoppableLink;
  }

  updateIsReels() {
    this.isReels = !this.isReels;
  }

  handleMediaForReels() {
    return this.currSelectedCustomization$.pipe(
      takeLast(1),
      tap((customization: Customization) => {
        const media = customization.uploadedMediaObjects$$.getValue();
        //Check if the media is just one ,  otherwise it is a carousel and the filetype is video
        if (media.length === 1 && media[0].fileType === 'video') {
          customization.uploadedMediaObjects$$.next([...this.formatMedia(media[0], this.isReels)]);
        }
      }),
    );
  }

  // the first check when edit or draft
  initCheckReels() {
    return combineLatest([this.currSelectedCustomization$, this.instagramSelected$]).pipe(
      filter(([, instagram]: [Customization, boolean]) => !!instagram),
      map(([customization]: [Customization, boolean]) => customization),
      map((customization: Customization) => customization?.uploadedMediaObjects$$?.getValue()),
      take(1),
      tap((media) => this.checkReels(media)),
    );
  }

  checkReels(media: UploadedFile[]) {
    const singleMedia = media?.length === 1;
    this.isVideo = media[0]?.fileType?.toLowerCase() === 'video';
    this.isReels = singleMedia ? !!media[0]?.url?.includes('&media_type=reels') : true;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  formatMedia(media: UploadedFile, isReels: boolean): UploadedFile[] {
    const mediaFormated = media;
    const mediaRaw: UploadedFile[] = [];
    const rawUrl = mediaFormated.url.split('&media_type')[0];
    mediaFormated.url = this.formatVideoInstagram(rawUrl);
    mediaRaw.push(mediaFormated);
    return mediaRaw;
  }

  formatVideoInstagram(videoUrl: string) {
    this.isVideo = true;
    return videoUrl + (this.isReels ? '&media_type=reels' : '&media_type=video');
  }

  updateGmbOptions(gmbOptions: GmbOptions): void {
    combineLatest([
      this.isCustomizing$,
      this.customizations$,
      this.customizations$.pipe(
        switchMap((customizations) => combineLatest(customizations.map((c) => c.serviceTypes$))),
      ),
    ])
      .pipe(take(1))
      .subscribe(([isCustomizing, customizations, serviceTypes]) => {
        if (isCustomizing) {
          customizations.forEach((customization, i) => {
            if (
              serviceTypes[i].indexOf(SocialService.GMB) >= 0 ||
              serviceTypes[i].indexOf(SocialService.GOOGLE_MY_BUSINESS) >= 0
            ) {
              customization.updateGmbOptions(gmbOptions);
            }
          });
        } else {
          this.rootCustomization.updateGmbOptions(gmbOptions);
        }
      });
  }

  updateGmbValidity(validity: GmbOptionsValidity): void {
    this.gmbOptionsValidity$$.next(validity);
  }

  handlePostV2Error(scheduleResponse: any, postOrchestrationProxyEnabled: any): void {
    const failedServices: PostableService[] = [];
    const submitResults: SubmitResults = { failures: [] };
    scheduleResponse.socialPosts.forEach((post) => {
      const failedService = this.getServiceById(post.socialPost.socialServiceId);
      failedServices.push(failedService);
      this.failedServiceReason.push(post.statusMessage);
      submitResults.failures.push({ serviceId: post.socialPost.socialServiceId, reason: post.statusMessage });
    });
    this.failedServices$$.next(failedServices);
    this.submitResults$$.next(submitResults);
    // We don't want to create drafts, because we don't want to close composer when we face these exceptions
    if (postOrchestrationProxyEnabled) {
      throw new Error('Post failures: ' + this.failedServiceReason.join(', '));
    }
  }

  editPosts(
    agid?: string,
    couldBeSingleLocationMLpost?: boolean,
    existingPostIds?: string[],
    draftId?: string,
  ): Observable<any> {
    this.submitting$$.next(true);
    return this.customizations$.pipe(
      take(1),
      withLatestFrom(this.conciergeTaskService.parentTaskId$, this.configService.partnerId$),
      switchMap(([customizations, parentTaskId, partnerId]) => {
        this.addMentionIdToPostText(customizations);
        return this.composerService.editPosts(
          customizations,
          agid,
          existingPostIds,
          draftId,
          this.initialValue,
          parentTaskId,
          partnerId,
          this.getWorkFlowType(),
          this.rootCustomization.postType$$.getValue(),
        );
      }),
      finalize(() => {
        this.submitting$$.next(false);
        this.failedServiceReason = [];
      }),
      catchError((err) => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.POST_SUBMITTED_ERROR'));
        throw err;
      }),
      withLatestFrom(this.smFeaturesService.postOrchestrationProxy$),
      tap(([editPostResponse, postOrchestrationProxyEnabled]) => {
        const [updateResponse, scheduleResponse] = editPostResponse;
        const failedServices = this.extractFailedServices(updateResponse, scheduleResponse);
        if (!!failedServices && failedServices.length > 0) {
          this.failedServices$$.next(failedServices);
        } else if (scheduleResponse.socialPosts?.some((s) => s.status === PostStatusV2.POST_STATUS_ERROR)) {
          this.handlePostV2Error(scheduleResponse, postOrchestrationProxyEnabled);
        } else {
          const editedLocations = [];
          const updatedPosts = [];
          // SHOW SUCCESS
          this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.POST_SCHEDULED'));
          // UPDATE FEED
          const publishedPostResults = [];
          const accountGroupId = this.configService.accountGroupId;
          if (updateResponse.results) {
            updateResponse.results.forEach((result) => {
              if (result.socialPostId) {
                updatedPosts.push({ postId: result.socialPostId });
              }
              if (result.post) {
                editedLocations.push({
                  socialServiceIds: [result.post.socialServiceId],
                  accountGroupId: accountGroupId,
                });
                publishedPostResults.push(this.buildPublishedPost(result, couldBeSingleLocationMLpost));
              }
            });
          }
          if (scheduleResponse.socialPosts) {
            scheduleResponse.socialPosts.forEach((result) => {
              if (result.socialPostId) {
                updatedPosts.push({ postId: result.socialPostId });
              }
              if (result.post) {
                editedLocations.push({
                  socialServiceIds: [result.post.socialServiceId],
                  accountGroupId: accountGroupId,
                });
                publishedPostResults.push(this.buildPublishedPost(result, couldBeSingleLocationMLpost));
              }
              if (draftId) {
                this.socialPostFeedService.deleteDraft({ data: { draftId: draftId } } as FeedItem, true);
              }
            });
          }
          this.publishPostService.publishPost({ data: { results: publishedPostResults } });
          // HANDLE CALENDAR
          const deletedPostIds = this.getDeletedIds(existingPostIds, updatedPosts);
          this.postUpdated$$.next({ posts: updatedPosts, deletedIds: deletedPostIds });
          // MULTILOCATION post is being edited in single location
          if (couldBeSingleLocationMLpost && !!this.brandIdForEditing) {
            const multilocationId = updateResponse.results[0].post.internalPostId.split('-')[1];
            this.composerService
              .removeFromMultilocationPost(0, this.brandIdForEditing, multilocationId, editedLocations)
              .subscribe();
          }

          this.socialProfileService.socialProfile$
            .pipe(
              take(1),
              map((socialProfile) => {
                return publishedPostResults.map((r) => {
                  const username = this.getServiceUsername(r.ssid, socialProfile);
                  return {
                    ssid: r.ssid,
                    username: username,
                    postText: r.postText,
                    imageUrl: r.imageUrls ? r.imageUrls[0] : '',
                    imageUrls: r.imageUrls,
                    scheduledDateTime: r.scheduled,
                    linkUrl: r.linkUrl,
                    socialPostId: r.socialPostId,
                  } as TaskManagerPost;
                });
              }),
              withLatestFrom(this.configService.partnerId$),
              switchMap(([sideEffectData, partnerId]: [TaskManagerPost[], string]) => {
                const result1 = this.composerService.handleSocialPostsSideEffects(sideEffectData, false);
                const result2 = this.composerService.saveHashtags(
                  this.parseHashtagsStr(sideEffectData[0].postText),
                  partnerId,
                );
                return combineLatest([result1, result2]);
              }),
            )
            .subscribe();
        }
      }),
    );
  }

  getDeletedIds(existingPostIds: string[], updatedPosts: any[]): string[] {
    return (
      existingPostIds?.filter((id) => {
        for (const up of updatedPosts) {
          if (up['postId'] === id) {
            return false;
          }
        }
        return true;
      }) || []
    );
  }

  buildPublishedPost(result: ResultInterface, couldBeSingleLocationMLpost: boolean): any {
    const publishedPost = {
      imageUrl: null,
      imageUrls: result.post.imageUrls,
      videoUrl: result.post.videoUrl,
      postText: result.post.postText,
      scheduledDateTime: result.post.scheduled,
      socialPostId: result.post.internalPostId,
      ssid: result.post.socialServiceId,
      username: '',
      linkPreviewImageUrl: result.post.link ? result.post.link.picture : '',
      gmbPostCustomization: result.post.gmbPostCustomization,
      brandId: null,
      mediaEntries: result.post.mediaEntries,
    };
    if (couldBeSingleLocationMLpost && !!this.brandIdForEditing) {
      publishedPost.brandId = this.brandIdForEditing;
    }
    return publishedPost;
  }

  extractFailedServices(
    updateResponse: UpdatePostsResponseInterface,
    scheduleResponse: SchedulePostsResponseInterface,
  ): PostableService[] {
    const failedServices: PostableService[] = [];
    if (updateResponse.exceptions && updateResponse.exceptions.length > 0) {
      updateResponse.exceptions.forEach((exception) => {
        failedServices.push(this.getServiceById(exception.socialServiceId));
        this.failedServiceReason.push(exception.message);
      });
    }
    if (scheduleResponse.exceptions && scheduleResponse.exceptions.length > 0) {
      scheduleResponse.exceptions.forEach((exception) => {
        failedServices.push(this.getServiceById(exception.socialServiceId));
        this.failedServiceReason.push(exception.message);
      });
    }
    return failedServices;
  }

  isDictEmpty(obj): boolean {
    return Object.keys(obj).length === 0;
  }

  editPostToBrand(): Observable<any> {
    this.submitting$$.next(true);

    return this.composerService
      .editPostsBrand(this.configService.brandId, this.multilocationId, this.rootCustomization)
      .pipe(
        finalize(() => {
          this.submitting$$.next(false);
          top.postMessage('Edit Submitted', '*');
        }),
      );
  }

  submitPostToBrand(): Observable<any> {
    this.submitting$$.next(true);

    return combineLatest([this.brandAgids$, this.customizations$, this.typeConnectionMlSelected$$]).pipe(
      first(),
      switchMap(
        ([brandAgids, customizations, typeConectionMlSelected]: [
          Map<string, unknown>,
          Customization[],
          typeServiceMlSelected,
        ]) => {
          const multiLocationSSIDs = this.getMultiLocationSSIDs(brandAgids, typeConectionMlSelected);
          return this.composerService.submitPostsBrand(
            this.configService.brandId,
            this.rootCustomization,
            multiLocationSSIDs,
            customizations,
          );
        },
      ),
      finalize(() => {
        this.submitting$$.next(false);
        if (this.postModeSelected$$.getValue() == PostModeEnum.SCHEDULED) {
          top.postMessage('Post Scheduled', '*');
        } else {
          top.postMessage('Post Submitted', '*');
        }
      }),
    );
  }

  getMultiLocationSSIDs(brandAgids: Map<string, any>, typeConectionMlSelected: typeServiceMlSelected) {
    return Array.from(brandAgids.entries())
      .filter(([brandAgid, service]) => {
        return !!brandAgid && service.facebook_info && service.agid_info;
      })
      .map(([agid, service]) => {
        const fbServices = typeConectionMlSelected.facebook ? service.facebook_info.ssid : null;
        const gmbServices = typeConectionMlSelected.gmb ? service.gmb_info.ssid : null;
        const igServices = typeConectionMlSelected.instagram ? service.ig_info.ssid : null;
        const liServices = typeConectionMlSelected.linkedin ? service.linkedin_info.ssid : null;

        return {
          socialServiceIds: [fbServices, gmbServices, igServices, liServices].filter(Boolean),
          accountGroupId: agid,
        };
      });
  }

  toggleMLServiceGroup(serviceType: SocialService) {
    const isFB = serviceType === SocialService.FACEBOOK;
    const isGMB = serviceType === SocialService.GMB;
    const isIG = serviceType === SocialService.INSTAGRAM;
    const isLI = serviceType === SocialService.LINKEDIN || serviceType === SocialService.LINKEDIN_COMPANY;
    const prevState = this.typeConnectionMlSelected$$.getValue();

    this.typeConnectionMlSelected$$.next({
      facebook: isFB ? !prevState.facebook : prevState.facebook,
      gmb: isGMB ? !prevState.gmb : prevState.gmb,
      instagram: isIG ? !prevState.instagram : prevState.instagram,
      linkedin: isLI ? !prevState.linkedin : prevState.linkedin,
    });
  }

  updatePostType(postType: PostType) {
    this.networkPostType = postType;
    return this.currSelectedCustomization$.pipe(tap((customization) => customization.updatePostType(postType)));
  }

  get postType() {
    return toSignal(this.currSelectedCustomization$.pipe(switchMap((customization) => customization.postType$$)), {
      initialValue: PostType.POST_TYPE_INVALID,
    });
  }

  get IGOptionPostType() {
    return toSignal(
      this.currSelectedCustomization$.pipe(
        switchMap((customization) => customization.postType$$),
        map((postType) =>
          postType === PostType.POST_TYPE_STORIES ? PostType.POST_TYPE_STORIES : PostType.POST_TYPE_INVALID,
        ),
      ),
      {
        initialValue: PostType.POST_TYPE_INVALID,
      },
    );
  }

  get isBundleAIPost$(): Observable<boolean> {
    return this.isBundleAIPost$$.asObservable();
  }

  get isCSVUploadedPost$(): Observable<boolean> {
    return this.isCSVUploadedPost$$.asObservable();
  }

  submitAiBundle(): Observable<any> {
    this.submitting$$.next(true);
    return this.customizations$.pipe(
      first(),
      withLatestFrom(this.conciergeTaskService.parentTaskId$, this.configService.partnerId$, this.isCustomizing$),
      tap(([customizations, parentTaskId, partnerId, isCustomizing]: [Customization[], string, string, boolean]) => {
        this.addMentionIdToPostText(customizations);
        const result = this.composerService.submitAIBundlePost(customizations, parentTaskId, partnerId);
        if (this.composerCallback) {
          const mode = isCustomizing ? 'customize' : 'single';
          this.composerCallback(result, mode);
        }
      }),
      finalize(() => {
        this.submitting$$.next(false);
        this.failedServiceReason = [];
      }),
    );
  }

  submitCSVUploadedPost(): Observable<any> {
    this.submitting$$.next(true);
    return this.customizations$.pipe(
      take(1),
      tap((customizations: Customization[]) => {
        this.addMentionIdToPostText(customizations);
        const result = this.composerService.submitCSVUploadedPost(customizations, '', '');
        if (this.composerCallback) {
          this.composerCallback(result, 'single');
        }
      }),
      finalize(() => {
        this.submitting$$.next(false);
        this.failedServiceReason = [];
      }),
      catchError((err) => {
        throw err;
      }),
    );
  }

  submitPost(): Observable<any> {
    this.submitting$$.next(true);
    return this.customizations$.pipe(
      first(),
      withLatestFrom(this.conciergeTaskService.parentTaskId$, this.configService.partnerId$),
      switchMap(([customizations, parentTaskId, partnerId]: [Customization[], string, string]) => {
        this.addMentionIdToPostText(customizations);
        return this.composerService.submitPosts(
          customizations,
          this.postModeSelected(),
          parentTaskId,
          partnerId,
          this.getWorkFlowType(),
        );
      }),
      finalize(() => {
        this.submitting$$.next(false);
        this.failedServiceReason = [];
      }),
      catchError((err) => {
        this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.POST_SUBMITTED_ERROR'));
        throw err;
      }),
      withLatestFrom(this.draftId$, this.smFeaturesService.postOrchestrationProxy$),
      switchMap(([responseData, draftId, postOrchestrationProxyEnabled]: [SchedulePostsResponse, string, boolean]) => {
        if (draftId) {
          if (responseData.exceptions && responseData.exceptions.length > 0) {
            return combineLatest([of(responseData), of(true)]);
          } else {
            // keep draftId to delete draft from calendar after converting it to scheduled post
            responseData['draftId'] = draftId;
            this.publishPostService.postedDraft = draftId;
            return combineLatest([
              of(responseData),
              this.composerService.deleteDraft(draftId),
              of(postOrchestrationProxyEnabled),
            ]);
          }
        } else {
          return combineLatest([of(responseData), of(true), of(postOrchestrationProxyEnabled)]);
        }
      }),
      tap(([responseData, , postOrchestrationProxyEnabled]) => {
        const publishedPostResults = [];
        if (responseData.socialPosts) {
          responseData.socialPosts.forEach((result) => {
            if (result.socialPost) {
              publishedPostResults.push({
                imageUrls: result?.socialPost?.postContent?.medias
                  ?.filter((m) => m.mediaType === 'MEDIA_TYPE_IMAGE')
                  ?.map((m) => m?.mediaUrl)
                  ?.filter(Boolean),
                videoUrl: result?.socialPost?.postContent?.medias
                  ?.filter((m) => m.mediaType === 'MEDIA_TYPE_VIDEO')
                  ?.map((m) => m?.mediaUrl)
                  ?.filter(Boolean),
                postText: result.socialPost?.postContent?.postText,
                scheduledDateTime: result.socialPost?.scheduled,
                socialPostId: result.socialPost.internalPostId,
                ssid: result.socialPost.socialServiceId,
                linkUrl: result.socialPost?.linkUrl,
                username: '',
                linkPreviewImageUrl: result.socialPost?.postContent?.medias?.[0]?.mediaUrl || '',
                gmbPostCustomization: result.socialPost?.gmbPostCustomization,
              });
            }
          });
        } else if (responseData.results) {
          responseData.results.forEach((result) => {
            if (result.post) {
              publishedPostResults.push({
                imageUrls: result.post.imageUrls,
                videoUrl: result.post.videoUrl,
                postText: result.post.postText,
                scheduledDateTime: result.post.scheduled,
                socialPostId: result.post.internalPostId,
                ssid: result.post.socialServiceId,
                linkUrl: result.post.linkUrl,
                username: '',
                linkPreviewImageUrl: result.post.link ? result.post.link.picture : '',
                gmbPostCustomization: result.post.gmbPostCustomization,
              });
            }
          });
        }
        if (publishedPostResults.length > 0) {
          this.socialProfileService.socialProfile$
            .pipe(
              take(1),
              map((socialProfile) => {
                return publishedPostResults.map((publishedPost) => {
                  const username = this.getServiceUsername(publishedPost.ssid, socialProfile);
                  return {
                    ssid: publishedPost.ssid,
                    username: username,
                    postText: publishedPost.postText,
                    imageUrl: publishedPost.imageUrls?.[0] || '',
                    imageUrls: publishedPost.imageUrls,
                    scheduledDateTime: publishedPost.scheduledDateTime,
                    linkUrl: publishedPost.linkUrl,
                    socialPostId: publishedPost.socialPostId,
                  } as TaskManagerPost;
                });
              }),
              withLatestFrom(this.configService.partnerId$),
              switchMap(([sideEffectData, partnerId]: [TaskManagerPost[], string]) => {
                const result1 = this.composerService.handleSocialPostsSideEffects(sideEffectData, false);
                const result2 = this.composerService.saveHashtags(
                  this.parseHashtagsStr(sideEffectData[0].postText),
                  partnerId,
                );
                return combineLatest([result1, result2]);
              }),
            )
            .subscribe(() => {
              top.postMessage('Post Submitted', '*');
            });
          this.publishPostService.publishPost({ data: { results: publishedPostResults } });
        }

        if (responseData.exceptions && responseData.exceptions.length > 0) {
          const failedServices: PostableService[] = [];
          const submitResults: SubmitResults = { failures: [] };
          responseData.exceptions.forEach((exception) => {
            const failedService = this.getServiceById(exception.socialServiceId);
            failedServices.push(failedService);
            this.failedServiceReason.push(exception.message);
            submitResults.failures.push({ serviceId: exception.socialServiceId, reason: exception.message });
          });
          this.failedServices$$.next(failedServices);
          this.submitResults$$.next(submitResults);
          return this.submitDraft();
        } else if (responseData.socialPosts?.some((s) => s.status === PostStatusV2.POST_STATUS_ERROR)) {
          this.handlePostV2Error(responseData, postOrchestrationProxyEnabled);
          return;
        } else {
          this.submitResults$$.next({ failures: [] });
          if (this.postModeSelected$$.getValue() == PostModeEnum.SCHEDULED) {
            this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.POST_SCHEDULED'));
          } else {
            this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.POST_SUBMITTED'));
          }
          const deletedIds = responseData?.draftId ? [responseData.draftId] : [];
          this.postUpdated$$.next({ posts: [{ postId: CONSTANTS.NEW_POST_ID }], deletedIds: deletedIds });
        }
      }),
    );
  }

  private getServiceUsername(ssid: string, socialProfile: SocialProfile): string {
    switch (ssid.split('-')[0]) {
      case 'FBU':
      case 'FBP': {
        let name = this.findServiceName(ssid, socialProfile.facebook);
        if (!name) {
          name = this.findServiceName(ssid, socialProfile.facebook_master_page);
        }
        return name;
      }
      case 'TWU':
        return this.findServiceName(ssid, socialProfile.twitter);
      case 'LIU':
      case 'LIP':
        return this.findServiceName(ssid, socialProfile.linkedin);
      case 'IGU':
        return this.findServiceName(ssid, socialProfile.instagram);
    }
    return this.findServiceName(ssid, socialProfile.google_my_business);
  }

  private findServiceName(ssid: string, services: SocialServiceInfo[]): string {
    let name = '';
    services.forEach((service) => {
      if (service.ssid === ssid) {
        name = service.name;
      } else if (service.pages) {
        service.pages.forEach((page) => {
          if (page.ssid === ssid) {
            name = page.name;
          }
        });
      }
    });
    return name;
  }

  submitDraft(
    successCallback?: (draft?: Draft) => {
      // no-op
    },
    deletedIds?: string[],
  ): void {
    if (this.isCustomizing$$.getValue()) {
      this.bulkCreateDrafts(successCallback);
      return;
    }

    const isHiddenDraft =
      this.postModeSelected() === PostModeEnum.HIDDEN_DRAFT ||
      this.postModeSelected$$.getValue() === PostModeEnum.HIDDEN_DRAFT;

    this.submitting$$.next(true);
    combineLatest([
      this.rootCustomization.postText$,
      this.rootCustomization.scheduledDate$,
      this.rootCustomization.linkUrl$,
      this.rootCustomization.uploadedMediaObjects$,
      this.draftId$,
      this.rootCustomization.gmbOptions$,
      this.customizations$,
      this.rootCustomization.youtubePostCustomization$,
      this.rootCustomization.tiktokPostCustomization$,
      this.configService.partnerId$,
      this.workFlowType$,
    ])
      .pipe(
        take(1),
        switchMap(
          ([
            postText,
            scheduledDate,
            previewLink,
            uploadedMediaObjects,
            draftId,
            gmbOptions,
            customizations,
            youtubePostCustomization,
            tiktokPostCustomization,
            partnerID,
            workflowType,
          ]: [
            string,
            Date,
            string,
            UploadedFile[],
            string,
            GmbOptions,
            Customization[],
            YoutubePostCustomization,
            TiktokPostCustomization,
            string,
            WorkflowType,
          ]) => {
            const services = this.rootCustomization.services$$.getValue();
            return this.composerService
              .submitDraft(
                draftId,
                postText,
                Array.from(services.keys()).map((service) => service.ssid),
                scheduledDate,
                previewLink,
                gmbOptions,
                uploadedMediaObjects,
                customizations,
                youtubePostCustomization,
                isHiddenDraft,
                tiktokPostCustomization,
                partnerID,
                workflowType,
              )
              .pipe(
                tap(() => {
                  if (!draftId) {
                    this.socialPostFeedService.deleteGroupPosts(
                      deletedIds.map((id) => {
                        return { postId: id } as SocialPost;
                      }),
                      true,
                    );
                  }
                }),
              );
          },
        ),
        finalize(() => !successCallback && this.submitting$$.next(false)),
        catchError(() => {
          this.snackBarService.errorSnack(this.translateService.instant('SNACKBAR.SAVE_DRAFT_ERROR'));
          return empty;
        }),
        withLatestFrom(this.draftId$),
      )
      .subscribe(([response, draftId]) => {
        if (!draftId) {
          this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.DRAFT_SUBMITTED'));
        } else {
          this.snackBarService.successSnack(this.translateService.instant('SNACKBAR.DRAFT_UPDATED'));
        }
        this.draftId$$.next(response.draft.draftId);
        const newDraft = SocialDraftsAPIService.fromSocialDraftsSDK(response.draft);
        this.postUpdated$$.next({ posts: [newDraft], deletedIds: deletedIds || [] });
        if (successCallback) {
          // This setTimeout is for delay the process after we save or update draft in social composer.
          // Because, We use elastic search to search the draft and it takes some time to update the draft in elastic search.
          setTimeout(() => {
            this.submitting$$.next(false);
            successCallback(newDraft);
          }, 2000);
        }
        this.publishPostService.publishDraft(newDraft);
      });
  }

  submitTemplate() {
    let temp_template;
    this.submitting$$.next(true);
    return combineLatest([
      this.rootCustomization.postText$,
      this.rootCustomization.scheduledDate$,
      this.rootCustomization.uploadedMediaObjects$,
      this.rootCustomization.gmbOptions$,
      this.rootCustomization.mediaEntries$,
      this.templateTitle$,
      this.templateId$,
    ])
      .pipe(
        take(1),
        switchMap(
          ([postText, scheduledDate, uploadedMediaObjects, gmbOptions, mediaEntries, templateTitle, templateId]: [
            string,
            Date,
            UploadedFile[],
            GmbOptions,
            MediaEntryInterface[],
            string,
            string,
          ]) => {
            const templateMedia: Media = {};
            if (uploadedMediaObjects.length > 0) {
              const media = uploadedMediaObjects[0];
              if (uploadedMediaObjects[0].fileType === FileType.VIDEO) {
                templateMedia.video_url = media.url;
              } else {
                templateMedia.image_url = media.url;
                templateMedia.image_path = media.path;
                templateMedia.image_size = media.size ? media.size.toString() : '';
              }
            }
            return of(
              new PostTemplate(<PostTemplate>{
                account_group_id: this.composerService.accountGroupId,
                template_id: templateId || undefined,
                title: templateTitle,
                updated: new Date().toISOString(),
                post_text: postText,
                post_date_time: scheduledDate,
                gmb_post_customization: JSON.stringify(this.composerService.serializeGmbOptions(gmbOptions)),
                image_url: templateMedia.image_url || '',
                image_path: templateMedia.image_path || '',
                image_size: templateMedia.image_size || '',
                video_url: templateMedia.video_url || '',
                mediaEntries: mediaEntries,
              }),
            );
          },
        ),
      )
      .pipe(
        switchMap((template) => {
          temp_template = template;
          return this.composerService.saveTemplate(template);
        }),
        tap((response) => {
          this.submitting$$.next(false);
          temp_template.template_id
            ? this.templateStoreService.updateTemplate(temp_template)
            : this.templateStoreService.addTemplateToFeed(temp_template);
          this.snackBarService.successSnack(
            this.translateService.instant(
              temp_template.template_id ? 'SNACKBAR.TEMPLATE_UPDATED' : 'SNACKBAR.TEMPLATE_SUBMITTED',
            ),
          );
          temp_template.template_id = temp_template.template_id || response.templateId;
        }),
      );
  }

  setDateToLocalTime(dataBaseTime: Date): Date {
    // Get browser time offset in milliseconds
    const offset = new Date().getTimezoneOffset() * 60 * 1000;

    // subtract offset to get browser local time in milliseconds
    const localTimeMili = dataBaseTime.getTime() - offset;
    return new Date(localTimeMili);
  }

  get startSavingDefaultServices$(): Observable<boolean> {
    return this.startSavingDefaultServices$$.asObservable();
  }

  startSavingDefaultServices(): void {
    this.startSavingDefaultServices$$.next(true);
  }

  fetchCoupons(): void {
    this.composerService.fetchCoupons().subscribe((coupons) => this.coupons$$.next(coupons));
  }

  fetchScheduledPostCount(): void {
    const ssids$ = this.socialProfileService.socialProfile$.pipe(
      take(1),
      skipWhile((socialProfile) => socialProfile === null),
      map((socialProfile: SocialProfile) => {
        const ssids = [];
        if (socialProfile.google_my_business && socialProfile.google_my_business.length > 0) {
          let gmbSsid = socialProfile.google_my_business[0].ssid;
          gmbSsid = 'GPU-' + gmbSsid.split('/')[1] + ':' + gmbSsid;
          ssids.push(gmbSsid);
        }
        if (socialProfile.facebook) {
          socialProfile.facebook.forEach((fbUser) => {
            fbUser.pages.forEach((page) => ssids.push(page.ssid));
          });
        }
        return ssids;
      }),
    );

    const scheduledPostCount$ = ssids$.pipe(
      withLatestFrom(this.configService.config$),
      switchMap(([ssids, config]: [string[], SMConfig]) => {
        return this.composerService.fetchScheduledPostCount(config.partner_id, config.account_group_id, ssids);
      }),
    );
    this.subscriptions.push(
      scheduledPostCount$.subscribe({
        next: (num) => this.scheduledPostCount$$.next(num),
        error: () => this.scheduledPostCount$$.next(null),
      }),
    );
  }

  selectCustomization(index: number): void {
    this.selectedCustomization$$.next(index);
  }

  clearSelectedCustomization(): void {
    this.selectedCustomization$$.next(-1);
  }

  parseHashtagsStr(postText: string): string[] {
    const hashtags = postText?.match(HASHTAG_REGEX);
    if (hashtags) {
      return hashtags.map((hashtag) => hashtag.trim().substr(1));
    } else {
      return [];
    }
  }

  queryHashtag(searchTerm: string, partnerId: string): Observable<string[]> {
    return this.composerService.queryHashtag(searchTerm, partnerId).pipe(
      catchError(() => empty),
      filter((resp) => !!resp.hashtags),
      map((resp: SearchHashtagResponse) => resp.hashtags.map((h) => h.keyword)),
    );
  }

  addToFbMentionMap(key: string, value: string): void {
    this.fbMentionNameToIdMap.set('@' + key, `@[${value}]`);
  }

  addMentionIdToPostText(customizations: Customization[]): void {
    customizations.forEach((el) => {
      const serviceMap = el.services$$.getValue();
      const service = Array.from(serviceMap.keys())[0];

      if (service.serviceType === SocialService.FACEBOOK) {
        el.postTextWithFbMentions = this.replaceMentionNameWithId(el.postText$$.getValue());
      }
    });
  }

  replaceMentionNameWithId(postText: string): string {
    let replacedText = postText;
    let replaced = false;
    const mentionMatch = postText.match(MENTIONS_REGEX);

    if (mentionMatch && mentionMatch.length > 0 && this.fbMentionNameToIdMap && this.fbMentionNameToIdMap.size) {
      this.fbMentionNameToIdMap.forEach((val, key) => {
        const textSplit = replacedText.split(key);
        if (textSplit.length > 1) {
          replacedText = textSplit.join(val);
          replaced = true;
        }
      });
    }

    return replaced ? replacedText : '';
  }

  //AI Content Button

  suggestContentPrompt(topic: string): Observable<string> {
    const contentLength = 'short';

    return this.currSelectedCustomization$.pipe(
      take(1),
      switchMap((customization) =>
        customization.serviceTypes$.pipe(
          take(1),
          map((serviceTypes) => (serviceTypes.length === 1 ? serviceTypes[0] : '')),
          switchMap((serviceType) => {
            const serviceName = serviceTypeFullName(serviceType);
            const textTemplatePrompt = this.getTextPrompt(topic, contentLength, serviceName);
            return this.suggestMessage(textTemplatePrompt, MessageLength.SHORT_FORM, TemplateType.TEMPLATE_CUSTOM).pipe(
              tap((prompt) => this.updatePostText(prompt)),
            );
          }),
        ),
      ),
    );
  }

  handleSuggestMenu(topic: string, option: AIButtonMenuItem) {
    const suguestContentObs = {
      [UpdateOption.MAKE_SHORTER]: this.changePostSize(topic, 'Make shorter'),
      [UpdateOption.MAKE_LONGER]: this.changePostSize(topic, 'Make longer'),
      [UpdateOption.CHANGE_TONE]: this.changePostTone(topic, option.name),
      [UpdateOption.FIX_SPELLING_AND_GRAMMAR]: this.changePostSize(topic, 'fix the spelling and grammar of'),
    };
    return suguestContentObs[option.updateOption];
  }

  changePostSize(topic: string, length: string): Observable<string> {
    const textTemplatePrompt = this.getTextPromptSize(topic, length);
    return this.suggestMessage(textTemplatePrompt, MessageLength.SHORT_FORM, TemplateType.TEMPLATE_CUSTOM).pipe(
      tap((prompt) => this.updatePostText(prompt)),
    );
  }

  changePostTone(topic: string, tone: string): Observable<string> {
    const textTemplatePrompt = this.getTextTone(topic, tone);
    return this.suggestMessage(textTemplatePrompt, MessageLength.SHORT_FORM, TemplateType.TEMPLATE_CUSTOM).pipe(
      tap((prompt) => this.updatePostText(prompt)),
    );
  }

  getTextPrompt(topic: string, length: string, serviceType = '') {
    const contextText = serviceType !== '' ? `. The content must be optimized for ${serviceType}` : '';
    return `Write a ${length} post ${contextText}. The post should be about the provided prompt. Keep the size of the post under ${DEFAULT_MAX_SIZE_AI_CONTENT} characters and well optimized for social networks.\nPrompt: ${topic} \n`;
  }

  getTextPromptSize(topic: string, length: string) {
    return `${length} the post. The post is: ${topic}\n`;
  }

  getTextTone(topic: string, tone: string) {
    return `Change the tone of the post to be ${tone}. Keep the size of the post under ${DEFAULT_MAX_SIZE_AI_CONTENT} characters and well optimized for social networks . The post is .\nPost: ${topic} \n`;
  }

  updateUsedPrompt(prompt: string) {
    this.usedPrompt$$.next(prompt);
  }

  updateYoutubeOptions(option: YoutubePostCustomization) {
    this.currSelectedCustomization$
      .pipe(
        take(1),
        tap((customization) => customization.updateYoutubePostCustomization(option)),
      )
      .subscribe();
  }

  updateTikTokOptions(option: TiktokPostCustomization) {
    this.currSelectedCustomization$
      .pipe(
        take(1),
        tap((customization) => customization.updateTikTokPostCustomization(option)),
      )
      .subscribe();
  }

  private applyCustomizationSetup(customizationSetup: CustomizationSetup) {
    this.clearPostableServices();
    this.customizations$$.next(customizationSetup.customizations);
    this.isCustomizing$$.next(true);
    this.selectServices([], customizationSetup.services);
  }

  private updatePostsAndPublishDrafts(drafts: Draft[], originalDraftId?: string): void {
    const deletedIds = originalDraftId ? [originalDraftId] : [];
    this.postUpdated$$.next({ posts: drafts, deletedIds: deletedIds });
    drafts.forEach((draft) => this.publishPostService.publishDraft(draft));
  }

  private invokeSuccessCallback(successCallback?: (draft?: Draft) => void): void {
    if (successCallback) {
      successCallback();
    }
  }

  private showSuccessSnackBar(): void {
    const message = this.translateService.instant('SNACKBAR.DRAFT_SUBMITTED');
    this.snackBarService.successSnack(message);
  }

  private showErrorSnackBar(): void {
    const message = this.translateService.instant('SNACKBAR.SAVE_DRAFT_ERROR');
    this.snackBarService.errorSnack(message);
  }

  private bulkCreateDrafts(successCallback: (draft?: Draft) => void): void {
    this.submitting$$.next(true);
    this.composerService
      .bulkCreateDrafts(
        this.customizations$$.getValue(),
        this.configService.accountGroupId,
        this.rootCustomization.scheduledDate$$.getValue(),
        this.postModeSelected() === PostModeEnum.HIDDEN_DRAFT,
      )
      .pipe(
        withLatestFrom(this.draftId$),
        mergeMap(([response, draftId]: [any, string]) => {
          const drafts = response.drafts.map((draft) => SocialDraftsAPIService.fromSocialDraftsSDK(draft));
          if (draftId) {
            return this.composerService.deleteDraft(draftId).pipe(
              map(() => ({ drafts, draftId })),
              catchError(() => {
                this.showErrorSnackBar();
                return of({ drafts, draftId }); // Continue with the drafts even if delete fails
              }),
            );
          } else {
            return of({ drafts, draftId });
          }
        }),
        tap(({ drafts, draftId }) => this.updatePostsAndPublishDrafts(drafts, draftId)),
        tap(() => this.invokeSuccessCallback(successCallback)),
        tap(() => this.showSuccessSnackBar()),
        take(1),
        catchError(() => {
          this.showErrorSnackBar();
          return EMPTY; // Prevent further processing in case of an error
        }),
        finalize(() => this.submitting$$.next(false)),
      )
      .subscribe();
  }

  private getWorkFlowType(): WorkflowType {
    return this.workFlowType$$.getValue();
  }
}
