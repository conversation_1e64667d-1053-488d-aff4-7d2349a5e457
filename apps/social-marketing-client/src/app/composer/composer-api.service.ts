import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ComposerService as ComposerMicroserviceService,
  LinkInterface,
  MediaEntryInterface,
  MetaData,
  PostInterface,
  SearchHashtagResponseInterface,
} from '@vendasta/composer';
import { PagePost, ShoppableFeedService } from '@vendasta/shoppable-feed';
import {
  CreateDraftRequestInterface,
  CreateMultiDraftsResponse,
  DraftsService,
  DraftType,
  GMBPostCustomization,
  Media as SDKMedia,
  SSIDDraftTypeInterface,
} from '@vendasta/social-drafts';
import {
  FieldMask,
  MediaEntry,
  MessageLength,
  MetaDataInterface,
  MultilocationPostsService,
  PostCustomization,
  PostTemplatesService,
  PostType,
  SocialPostsService,
  SocialPostsV2Service,
  TemplateType,
} from '@vendasta/social-posts';
import { CreateImageRequestInterface, CreateImageResponse, SSIDPostType } from '@vendasta/social-posts/lib/_internal';
import {
  LinkV2Interface,
  MetadataV2Interface,
} from '@vendasta/social-posts/lib/_internal/interfaces/social-post-v2.interface';
import { UrlShortenerService } from '@vendasta/url-shortener';
import moment from 'moment';
import { combineLatest, Observable, of } from 'rxjs';
import { map, skipWhile, switchMap } from 'rxjs/operators';
import * as uuid from 'uuid';
import { Customization } from '../core/post/customization';
import { SocialPost } from '../core/post/post';
import { SocialPostFeedStoreService } from '../core/post/post-feed.service';
import { PostTemplate } from '../core/post/post-template';
import { Owner } from '../pages/analytics/link-performance/link-metrics';
import { PostableService } from '../shared/social-service/social-service.service';
import { DATE_GMB_FORMAT, NEW_POST_ID } from './constants';
import { Coupon, FileType, TaskManagerPost, UploadedFile } from './interfaces';
import { GmbOptions } from './models/gmb-options';
import {
  privacyStatusStringValueToComposerEnum,
  privacyStatusStringValueToDraftEnum,
  YoutubePostCustomization,
} from './models/youtube-options';
import { SocialService, WorkflowType } from './post';
import { calculatePostType } from './shared-methods';
import { Payload } from './shared/utils/payload';
import { PostModeEnum } from './components/post-type/config';
import { TiktokPostCustomization } from './models/tiktok-option.model';

export interface ShortenLinkResponse {
  data: { shortUrl: string };
}

export interface GMBPostCustomizationInterfaceRequest {
  title?: string;
  eventStart?: Date | string;
  eventEnd?: Date | string;
  ctaType?: string;
  linkUrl?: string;
}

interface DefaultServiceResponse {
  data: string[];
}

@Injectable()
export class SMComposerApiService {
  constructor(
    private http: HttpClient,
    private socialPostsService: SocialPostsService,
    private socialPostsV2Service: SocialPostsV2Service,
    private socialPostFeedService: SocialPostFeedStoreService,
    private urlShortenerService: UrlShortenerService,
    private composerMicroserviceApi: ComposerMicroserviceService,
    private postTemplatesService: PostTemplatesService,
    private draftsService: DraftsService,
    private multilocationPostsService: MultilocationPostsService,
    private shoppableFeedService: ShoppableFeedService,
  ) {}

  loadServicesCall$: Observable<any>;
  accountGroupId: string;
  defaultFBPage: string;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loadServices(reload?: boolean): Observable<any> {
    if (this.loadServicesCall$) {
      return this.loadServicesCall$;
    }

    if (this.accountGroupId) {
      this.loadServicesCall$ = this.http.get(
        '/angular/' + this.accountGroupId + '/fetch-postable-services/?account_group_id=' + this.accountGroupId,
      );
    }
    return this.loadServicesCall$;
  }

  saveDefaultServices(services: string[]): Observable<DefaultServiceResponse> {
    const saveDefaultServiceUrl = `/angular/${this.accountGroupId}/save-default-services/`;
    const body = new FormData();
    services.forEach((service) => {
      body.append('services', service);
    });
    return this.http.post(saveDefaultServiceUrl, body).pipe(map((response: { data: string[] }) => response));
  }

  getDefaultServices(): Observable<DefaultServiceResponse> {
    const getDefaultServiceUrl = `/angular/${this.accountGroupId}/get-default-services/`;
    return this.http.post(getDefaultServiceUrl, {}).pipe(map((response: { data: string[] }) => response));
  }

  fetchPreviewLinkHistory(url: string): Observable<any> {
    const fetchPreviewLinkURL = '/account/' + this.accountGroupId + '/preview-link-history/';
    const body = new FormData();
    body.append('url', url);
    return this.http.post(fetchPreviewLinkURL, body);
  }

  uploadImage(file: File, uploadFolder?: string): Observable<any> {
    const uploadImageURLOverride = '/image-upload/?__vformcmd__=fileupload';
    const body = new FormData();
    const folder = this.buildUploadFolderPath(uploadFolder);
    body.append('agid', this.accountGroupId);
    body.append('name', 'image');
    body.append('uploadFolder', folder);
    body.append('files[]', file);
    return this.http.post(uploadImageURLOverride, body);
  }
  private buildUploadFolderPath(uploadFolder?: string): string {
    const folder = uploadFolder ?? '';
    return `${this.accountGroupId}${folder ? `/${folder}` : ''}`;
  }

  deleteUploadedImage(path: string): Observable<any> {
    const url = '/vcompose/v1/image-upload/?__vformcmd__=filedelete';
    const body = new FormData();
    body.append('agid', this.accountGroupId);
    body.append('filepath', path);
    return this.http.post(url, body);
  }

  createImage(
    prompt: string,
    businessId: string,
    imageSettings: CreateImageRequestInterface,
    metadata?: { [key: string]: string },
  ): Observable<CreateImageResponse> {
    //Dalle 3 request return only one image and it's needed to set imageAmount to 1
    const imageRequest: CreateImageRequestInterface = {
      businessId: businessId,
      prompt: prompt,
      metadata: this.getMetadataV2FromMap(metadata),
      ...imageSettings,
    };
    return this.socialPostsV2Service.createImage(imageRequest);
  }

  createVariations(imageURL: string) {
    return this.socialPostsV2Service.generateVariations(imageURL);
  }

  getBlobFromUrl(imageURL: string) {
    return this.socialPostsV2Service.getImageByUrl(imageURL);
  }

  getMetadataFromMap(options?: { [key: string]: string }): MetaDataInterface[] {
    return Object.keys(options).map((key) => ({ propertyName: key, propertyValue: options[key] }));
  }

  getMetadataV2FromMap(options?: { [key: string]: string }): MetadataV2Interface[] {
    return Object.keys(options).map((key) => ({ name: key, value: options[key] }));
  }

  suggestMessage(
    prompt: string,
    businessId: string,
    postLength: MessageLength,
    templateType: TemplateType,
    metadata?: { [key: string]: string },
  ): Observable<string> {
    const meta = this.getMetadataFromMap(metadata);
    return this.socialPostsService.suggestMessage(businessId, prompt, postLength, templateType, meta).pipe(
      map((response) => {
        return response.message;
      }),
    );
  }

  shortenLink(link: string, description?: string): Observable<ShortenLinkResponse> {
    link = !link.match(/^http/) ? 'https://' + link : link;
    return this.urlShortenerService
      .createLink(this.accountGroupId, link, Owner.OWNER_SOCIAL_MARKETING, description, true)
      .pipe(
        map((result) => {
          const url = `https://${result.domain}${result.shortCode}`;
          return { data: { shortUrl: url } };
        }),
      );
  }

  fetchCoupons(): Observable<Coupon[]> {
    const url = '/account/' + this.accountGroupId + '/coupons/';
    return this.http.get(url).pipe(
      skipWhile((response) => !response),
      map((response: { coupons: Coupon[] }) => response.coupons),
    );
  }

  fetchScheduledPostCount(partnerId: string, businessId: string, socialServiceIds: string[]): Observable<number> {
    return this.socialPostsService.getScheduledPostCount(partnerId, businessId, socialServiceIds).pipe(
      map((response: { count: number }) => {
        if ('count' in response) {
          return response.count;
        }
        return 0;
      }),
    );
  }

  serializeGmbOptions(gmbOptions: GmbOptions): Record<string, unknown> {
    const gmbStart = gmbOptions.eventOptions.startDate;
    const gmbEnd = gmbOptions.eventOptions.endDate;

    const gmbOptionsDict = {
      isEvent: gmbOptions.makeEvent,
      isCallToAction: gmbOptions.addCta,
    };

    if (gmbOptions.makeEvent) {
      gmbOptionsDict['title'] = gmbOptions.eventOptions.title;
    }
    if (gmbOptions.makeEvent && gmbStart) {
      gmbOptionsDict['startDate'] = this.getDateOnly(gmbStart);
      gmbOptionsDict['startTime'] = this.getTimeOnly(gmbStart);
    }
    if (gmbOptions.makeEvent && gmbEnd) {
      gmbOptionsDict['endDate'] = this.getDateOnly(gmbEnd);
      gmbOptionsDict['endTime'] = this.getTimeOnly(gmbEnd);
    }
    if (gmbOptions.addCta) {
      gmbOptionsDict['ctaType'] = gmbOptions.ctaOptions.action;
      gmbOptionsDict['linkUrl'] = gmbOptions.ctaOptions.ctaUrl;
    }

    return gmbOptionsDict;
  }

  editPostsBrand(brandId: string, multilocationId: string, customization: Customization): Observable<any> {
    const mediaObjects = customization.uploadedMediaObjects$$.getValue();
    const mediaEntries = customization.mediaEntries$$.getValue();
    const gmbOptions = customization.gmbOptions$$.getValue();
    let images = mediaObjects.filter((media) => media.fileType === FileType.IMAGE);
    let videos = mediaObjects.filter((media) => media.fileType === FileType.VIDEO);
    let gifs = mediaObjects.filter((media) => media.fileType === FileType.GIF);

    // FACEBOOK specific logic
    if (gifs.length && !videos.length) {
      videos = [gifs[0]];
      gifs = [];
      images = [];
    }

    const postMediaEntries = mediaEntries.map((m) => ({ mediaType: m.mediaType, mediaUrl: m.mediaUrl }) as MediaEntry);
    const postCustomization = this.getPostCustomization(gmbOptions);
    const linkShortCode = customization.shortCodes?.[0];

    return this.multilocationPostsService.editMultilocationPost(
      brandId,
      multilocationId,
      customization.postText$$.getValue(),
      images.map((i) => i.url) || [],
      gifs.map((i) => i.url) || [],
      videos.map((i) => i.url) || [],
      customization.scheduledDate$$.getValue() || new Date(),
      [],
      {
        paths: [
          'original_text',
          'original_scheduled_date',
          'original_media',
          'original_videos',
          'original_gifs',
          'customization',
          'media_entries',
          'link_short_code',
        ],
      } as FieldMask,
      postMediaEntries,
      postCustomization,
      linkShortCode,
    );
  }

  editPosts(
    customizations: Customization[],
    agid?: string,
    existingPostIds?: string[],
    draftId?: string,
    initialValues?: Map<string, string>,
    parentTaskId?: string,
    partnerId?: string,
    workflowType?: WorkflowType,
    posttype?: PostType,
  ): Observable<any> {
    let existingPostGuid = '';
    // If the user select a network when creating a draft the existingPostIds has the pattern NETWORK-ID (IGU-123123)
    // IF the user does not select a network exitsitng postid does not have '-'

    if (existingPostIds && existingPostIds.length > 0 && !draftId) {
      existingPostGuid = existingPostIds[0].split('-')[1];
    }
    const posts = customizations.map((customization) => {
      const gmbOptions = customization.gmbOptions$$.getValue();
      const addEventOptions = gmbOptions && gmbOptions.makeEvent;
      const addCtaOptions = gmbOptions && gmbOptions.addCta;
      const mediaObjects = customization.uploadedMediaObjects$$.getValue();
      let video = mediaObjects.find((media) => media.fileType === FileType.VIDEO);
      let gifs = mediaObjects.filter((media) => media.fileType === FileType.GIF);
      let images = mediaObjects.filter((media) => media.fileType === FileType.IMAGE);
      const serviceMap = customization.services$$.getValue();
      const service = Array.from(serviceMap.keys())[0];
      let postText = customization.postText$$.getValue();
      const scheduled = customization.scheduledDate$$.getValue() || new Date();
      const mediaEntries: MediaEntryInterface[] = customization.mediaEntries$$
        .getValue()
        .map((entries) => ({ ...entries, mediaType: entries.mediaType.toUpperCase() }));

      const postType = calculatePostType(service.serviceType, mediaEntries, customization, posttype);

      if (service?.serviceType === SocialService.FACEBOOK) {
        if (customization.hasGif) {
          video = images[0];
          images = [];
        } else if (gifs.length && !video) {
          video = gifs[0];
          gifs = [];
        }

        postText = customization.postTextWithFbMentions || postText;
      }
      const internalPostIds = Array.from(serviceMap.values()) || [];
      const siteMetaContent = customization.siteMetaContent$$.getValue();
      const metaImageIndex = customization.selectedMetaThumbnails$$.getValue().get(service.ssid);

      const linkUrl = customization.linkUrl$$.getValue();
      if (service.serviceType === SocialService.TWITTER && linkUrl) {
        postText = this.formatTwitterPostTextWithLink(postText, linkUrl);
      }

      if (
        service.serviceType === SocialService.INSTAGRAM &&
        ((!!images && images.length > 0) || (!!gifs && gifs.length > 0)) &&
        !!internalPostIds &&
        internalPostIds.length > 0
      ) {
        const igOption = customization.instagramOptions$$.getValue();
        const imageUrl = !!images && images.length > 0 ? images[0].url : gifs[0].url;
        this.updateIgPagePost(service.ssid, internalPostIds[0], igOption, scheduled, imageUrl);
      }

      //NEW_POST_ID IS A CONSTANT TO LABEL THE DRAFT and has a fixed value
      let postId =
        internalPostIds[0] === NEW_POST_ID && !!existingPostGuid
          ? initialValues.get(service.ssid) || existingPostGuid
          : internalPostIds[0];

      // When moving a draft to a post, we want to generate the post id following the same pattern as we
      // do when posting directly
      if (draftId) {
        postId = this.generatePostIdWithTask(customization.taskId, parentTaskId, partnerId, postId);
      }

      // We only use the picture from the link if there is no media attached to the post
      const shouldSaveLinkPicture =
        !video && gifs.length == 0 && images.length == 0 && mediaEntries.length == 0 && siteMetaContent;

      const youtubeDto = customization.youtubePostCustomization$$?.getValue();
      const title = service.serviceType === SocialService.YOUTUBE_CHANNEL ? youtubeDto?.title : '';
      const youtubePostCustomization = this.getYoutubePostCustomization(youtubeDto);

      return {
        title: title,
        socialServiceId: service.ssid,
        businessId: this.accountGroupId || agid,
        postText: postText,
        scheduled: scheduled,
        linkShortCodes: customization.shortCodes,
        linkUrl: linkUrl,
        imageUrls: images.map((i) => i.url) || [],
        gifUrl: gifs.length > 0 ? gifs[0].url : '',
        videoUrl: video ? video.url : '',
        internalPostId: postId,
        postType: postType,
        mediaEntries: mediaEntries,
        metaData: [
          { propertyName: 'partnerId', propertyValue: partnerId },
          { propertyName: 'workflow_type', propertyValue: workflowType },
        ],
        link: {
          name: siteMetaContent ? siteMetaContent.name : '',
          picture: shouldSaveLinkPicture ? siteMetaContent.images[metaImageIndex] : '',
          description: siteMetaContent ? siteMetaContent.description : '',
          title: siteMetaContent ? siteMetaContent.name : '',
        } as LinkInterface,
        gmbPostCustomization: {
          title: addEventOptions ? gmbOptions.eventOptions.title : '',
          eventStart: addEventOptions ? this.formatDateGMB(gmbOptions.eventOptions.startDate) : null,
          eventEnd: addEventOptions ? this.formatDateGMB(gmbOptions.eventOptions.endDate) : null,
          ctaType: addCtaOptions ? gmbOptions.ctaOptions.action : null,
          linkUrl: addCtaOptions ? gmbOptions.ctaOptions.ctaUrl : '',
        } as GMBPostCustomizationInterfaceRequest,
        brandId: null,
        isMultilocation: false,
        youtubeCustomization: youtubePostCustomization,
        tiktokCustomization: customization.tiktokPostCustomization$$?.getValue(),
      } as PostInterface;
    });
    // newly added network posts will have existingPostGuid as the internalPostId from line 242
    const oldPosts = !draftId ? posts.filter((post) => post.internalPostId !== existingPostGuid) : [];
    const newPosts = draftId ? posts : posts.filter((post) => post.internalPostId === existingPostGuid);
    const postsToBeDeleted = existingPostIds
      ?.filter((ePostId) => oldPosts.findIndex((post) => post.internalPostId === ePostId) === -1)
      .map((id) => ({ postId: id }) as SocialPost);
    if (!!postsToBeDeleted && postsToBeDeleted.length > 0 && !draftId) {
      this.socialPostFeedService.deleteGroupPosts(postsToBeDeleted, true);
    }
    // we always return a response from schedulePost even if there are no new posts , that's why we use of({})

    const postsToUpdate$ = combineLatest([
      this.composerMicroserviceApi.updatePosts(oldPosts),
      this.composerMicroserviceApi.schedulePosts(newPosts),
    ]);

    const postsToUpdateWithoutNewPosts$ = combineLatest([this.composerMicroserviceApi.updatePosts(oldPosts), of({})]);
    // Return the appropriate observable depending on whether new posts exist.
    return newPosts.length ? postsToUpdate$ : postsToUpdateWithoutNewPosts$;
  }

  updateIgPagePost(
    socialServiceId: string,
    internalPostId: string,
    instagramOption?: string,
    scheduled?: Date,
    imageUrl?: string,
  ): void {
    this.shoppableFeedService
      .getActivePage(this.accountGroupId, socialServiceId)
      .pipe(
        switchMap((resp) => {
          // UpdatePagePost requires the pageId and permalink, so it does not make sense to call it without them
          if (!resp?.pageSettings?.pageId || !instagramOption) {
            return of({});
          }
          const pagePost = {
            pageId: resp?.pageSettings?.pageId,
            postId: internalPostId,
            imageUrl: imageUrl,
            permalink: instagramOption,
            postedDate: scheduled,
          } as PagePost;

          const paths = [];
          if (scheduled) {
            paths.push('posted_date');
          }
          if (instagramOption) {
            paths.push('permalink');
          }
          if (imageUrl) {
            paths.push('image_url');
          }

          return this.shoppableFeedService.updatePagePost({ paths: paths } as FieldMask, pagePost);
        }),
      )
      .subscribe();
  }

  removeFromMultilocationPost(
    reason: number,
    brandId: string,
    multilocationId: string,
    locations: any[],
  ): Observable<any> {
    return this.multilocationPostsService.removeFromMultilocationPost(reason, brandId, multilocationId, locations);
  }

  submitPostsBrand(
    brandId: string,
    customization: Customization,
    locations: any[],
    otherCustomizations: Customization[],
  ): Observable<any> {
    const mediaObjects = customization.uploadedMediaObjects$$.getValue();
    const mediaEntries = customization.mediaEntries$$.getValue();
    const gmbOptions = customization.gmbOptions$$.getValue();
    const siteMetaContent = otherCustomizations[0]?.siteMetaContent$$?.getValue();
    let images = mediaObjects.filter((media) => media.fileType === FileType.IMAGE);
    let videos = mediaObjects.filter((media) => media.fileType === FileType.VIDEO);
    let gifs = mediaObjects.filter((media) => media.fileType === FileType.GIF);
    if (!images.length && !videos.length && !gifs.length && !!siteMetaContent?.images?.length) {
      images = [new UploadedFile({ url: siteMetaContent.images[0] })];
    }

    // FACEBOOK specific logic
    if (gifs.length && !videos.length) {
      videos = [gifs[0]];
      gifs = [];
      images = [];
    }

    const postMediaEntries = mediaEntries.map((m) => ({ mediaType: m.mediaType, mediaUrl: m.mediaUrl }) as MediaEntry);
    const postCustomization = this.getPostCustomization(gmbOptions);
    const linkShortCode = customization?.shortCodes[0];
    const ssidPostType = this.getSSIDPostType(customization, locations);

    return this.multilocationPostsService.createMultilocationPost(
      brandId,
      customization.postText$$.getValue(),
      images.map((i) => i.url) || [],
      gifs.map((i) => i.url) || [],
      videos.map((i) => i.url) || [],
      customization.scheduledDate$$.getValue() || new Date(),
      locations,
      postMediaEntries,
      postCustomization,
      linkShortCode,
      null,
      ssidPostType,
    );
  }

  getSSIDPostType(customization: Customization, locations): SSIDPostType[] {
    const igPostType = customization?.postType$$?.getValue();
    const groupedPosType = this.postTypeFromMediaEntries(customization?.mediaEntries$$?.getValue());
    const ssidPostType = locations
      .flatMap((entry) => entry.socialServiceIds)
      .map((location: string) => ({
        socialServiceId: location,
        postType: location.toUpperCase().startsWith('IGU') ? igPostType : groupedPosType,
      }));

    return ssidPostType;
  }

  postTypeFromMediaEntries(mediaEntries: MediaEntryInterface[]): PostType {
    if (!mediaEntries?.length) {
      return PostType.POST_TYPE_TEXT;
    }
    if (mediaEntries.length >= 2) {
      return PostType.POST_TYPE_CAROUSEL;
    }
    const mediaType = mediaEntries[0].mediaType;

    switch (mediaType) {
      case 'IMAGE':
        return PostType.POST_TYPE_IMAGE;
      case 'VIDEO':
        return PostType.POST_TYPE_VIDEO;
      case 'GIF':
        return PostType.POST_TYPE_GIF;
      default:
        return PostType.POST_TYPE_INVALID;
    }
  }

  createIgPagePost(
    socialServiceId: string,
    internalPostId: string,
    scheduled: Date,
    imageUrl: string,
    instagramOption: string,
  ): void {
    this.shoppableFeedService
      .getActivePage(this.accountGroupId, socialServiceId)
      .pipe(
        switchMap((resp) => {
          const pagePost = {
            pageId: resp.pageSettings.pageId,
            postId: internalPostId,
            imageUrl: imageUrl,
            permalink: instagramOption,
            postedDate: scheduled,
          } as PagePost;
          return this.shoppableFeedService.createPagePost(pagePost);
        }),
      )
      .subscribe();
  }

  submitPosts(
    customizations: Customization[],
    postModeEnum: PostModeEnum,
    parentTaskId?: string,
    partnerId?: string,
    workflowType?: WorkflowType,
  ): Observable<any> {
    const socialPostParentId = uuid.v4().replace(new RegExp('-', 'g'), '');

    //TODO: fix related to SOC-2508, once we find the root cause of duplicates on ScheduledPosts it should be removed
    const cache = new Set<string>();
    customizations = customizations.filter((item) => {
      const service = Array.from(item.services$$.getValue().keys())[0];
      return cache.has(service.ssid) ? false : cache.add(service.ssid);
    });

    const posts = this.mapPostToBeSchedule(
      customizations,
      socialPostParentId,
      parentTaskId,
      partnerId,
      postModeEnum,
      workflowType,
    );
    return this.composerMicroserviceApi.schedulePosts(posts);
  }

  submitAIBundlePost(customizations: Customization[], parentTaskId?: string, partnerId?: string): PostInterface[] {
    const socialPostParentId = uuid.v4().replace(new RegExp('-', 'g'), '');
    return this.mapPostToBeSchedule(customizations, socialPostParentId, parentTaskId, partnerId);
  }

  submitCSVUploadedPost(customizations: Customization[], parentTaskId?: string, partnerId?: string): PostInterface[] {
    const socialPostParentId = uuid.v4().replace(new RegExp('-', 'g'), '');
    return this.mapPostToBeSchedule(customizations, socialPostParentId, parentTaskId, partnerId);
  }

  private getYoutubePostCustomization(youtubeDto: YoutubePostCustomization) {
    if (!youtubeDto) {
      return null;
    }

    return { privacyStatus: privacyStatusStringValueToComposerEnum(youtubeDto.privacyStatus) };
  }

  private getDraftYoutubePostCustomization(youtubeDto: YoutubePostCustomization) {
    if (!youtubeDto) {
      return null;
    }

    return { ...youtubeDto, privacyStatus: privacyStatusStringValueToDraftEnum(youtubeDto.privacyStatus) };
  }

  mapPostToBeSchedule(
    customizations: Customization[],
    socialPostParentId: string,
    parentTaskId?: string,
    partnerId?: string,
    postMode?: string,
    workFlowType?: WorkflowType,
  ): PostInterface[] {
    //video,  gifs and images are UploadedFile where all the info about the file is there Ex: Type , size , etc .
    const posts = customizations.map((customization) => {
      const gmbOptions = customization.gmbOptions$$.getValue();
      const addEventOptions = gmbOptions && gmbOptions.makeEvent;
      const addCtaOptions = gmbOptions && gmbOptions.addCta;
      const service = Array.from(customization.services$$.getValue().keys())[0];
      const mediaObjects = customization.uploadedMediaObjects$$.getValue();

      let video = mediaObjects.find((media) => media.fileType === FileType.VIDEO);
      let gifs = mediaObjects.filter((media) => media.fileType === FileType.GIF);
      let images = mediaObjects.filter((media) => media.fileType === FileType.IMAGE);
      let postText = customization.postText$$.getValue();
      const mediaEntries: MediaEntryInterface[] = customization?.mediaEntries$$?.getValue() || [];
      const postType: PostType =
        service.serviceType === SocialService.INSTAGRAM
          ? mediaEntries.length >= 2
            ? PostType.POST_TYPE_CAROUSEL
            : customization.postType$$.getValue()
          : PostType.POST_TYPE_INVALID;
      const scheduled = postMode === PostModeEnum.POST_NOW ? new Date() : customization.scheduledDate$$.getValue();
      const internalPostId = this.generatePostIdWithTask(
        customization.taskId,
        parentTaskId,
        partnerId,
        socialPostParentId,
      );
      const metaImageIndex = customization.selectedMetaThumbnails$$.getValue().get(service.ssid);
      let siteMetaContent = customization.siteMetaContent$$.getValue();

      let linkUrl = customization.linkUrl$$.getValue();

      //Custom maping for FaceBook
      if (service.serviceType === SocialService.FACEBOOK) {
        if (customization.hasGif) {
          video = images[0];
          images = [];
        } else if (gifs.length && !video) {
          video = gifs[0];
          gifs = [];
        }
        postText = customization.postTextWithFbMentions || postText;
      }

      //Custom maping for LI
      if (
        (service.serviceType === SocialService.LINKEDIN || service.serviceType === SocialService.LINKEDIN_COMPANY) &&
        (!!images?.length || !!gifs?.length || !!video?.url?.length)
      ) {
        siteMetaContent = null;
        linkUrl = null;
      }

      //Custom maping for TW
      if (service.serviceType === SocialService.TWITTER && linkUrl) {
        postText = this.formatTwitterPostTextWithLink(postText, linkUrl);
      }

      //Custom maping for IG
      if (
        service.serviceType === SocialService.INSTAGRAM &&
        (!!images?.length || !!gifs?.length) &&
        mediaEntries.length < 2
      ) {
        const igOption = customization.instagramOptions$$.getValue();
        const imageUrl = !!images && images.length > 0 ? images[0].url : gifs[0].url;
        //Bypass clickable.bio entry if no permalink was added
        if (igOption) {
          this.createIgPagePost(service.ssid, 'IG-' + internalPostId, scheduled, imageUrl, igOption);
        }
      }

      const linkPicture =
        siteMetaContent && siteMetaContent?.images && siteMetaContent?.images[metaImageIndex]
          ? siteMetaContent.images[metaImageIndex]
          : '';

      // We only use the picture from the link if there is no media attached to the post
      const shouldSaveLinkPicture = !video && gifs.length == 0 && images.length == 0 && mediaEntries.length == 0;
      const youtubeDto = customization.youtubePostCustomization$$?.getValue();

      const youtubePostCustomization = this.getYoutubePostCustomization(youtubeDto);
      const tiktokPostCustomization = customization.tiktokPostCustomization$$.getValue();

      if (mediaEntries.length === 0 && (video || gifs?.length > 0 || images?.length > 0)) {
        video && mediaEntries.push({ mediaType: FileType.VIDEO, mediaUrl: video?.url });
        images.length > 0 &&
          images.forEach((image) => {
            mediaEntries.push({ mediaType: FileType.IMAGE, mediaUrl: image?.url });
          });
        gifs.length > 0 &&
          gifs.forEach((gif) => {
            mediaEntries.push({ mediaType: FileType.GIF, mediaUrl: gif?.url });
          });
      }

      return {
        title: youtubeDto?.title,
        socialServiceId: service.ssid,
        businessId: this.accountGroupId,
        postText: postText,
        scheduled: scheduled,
        linkShortCodes: customization.shortCodes,
        linkUrl: linkUrl,
        imageUrls: images.map((i) => i.url) || [],
        gifUrl: gifs.length > 0 ? gifs[0].url : '',
        videoUrl: video ? video.url : '',
        internalPostId: internalPostId,
        postType: postType,
        mediaEntries: mediaEntries,
        metaData: [
          { propertyName: 'partnerId', propertyValue: partnerId },
          { propertyName: 'workflow_type', propertyValue: workFlowType },
        ],
        link: {
          name: siteMetaContent ? siteMetaContent.name : '',
          picture: shouldSaveLinkPicture ? linkPicture : '',
          description: siteMetaContent ? siteMetaContent.description : '',
          title: siteMetaContent ? siteMetaContent.name : '',
          url: siteMetaContent ? siteMetaContent.link : linkUrl,
        } as LinkV2Interface,
        gmbPostCustomization: {
          title: addEventOptions ? gmbOptions.eventOptions.title : '',
          eventStart: addEventOptions ? this.formatDateGMB(gmbOptions.eventOptions.startDate) : null,
          eventEnd: addEventOptions ? this.formatDateGMB(gmbOptions.eventOptions.endDate) : null,
          ctaType: addCtaOptions ? gmbOptions.ctaOptions.action : null,
          linkUrl: addCtaOptions ? gmbOptions.ctaOptions.ctaUrl : '',
        } as GMBPostCustomizationInterfaceRequest,
        youtubeCustomization: youtubePostCustomization,
        tiktokCustomization: tiktokPostCustomization,
      } as PostInterface;
    });
    console.log('posts>>>', posts);
    return posts;
  }

  // When handling posted created from task manager, the only way of differentiating them is by adding the task id or
  // the parent task id as part of the post id itself
  generatePostIdWithTask(taskId: string, parentTaskId: string, partnerId: string, currentPostId: string): string {
    // When the task id is present, it contains the parent task id information, which will be enough to identify them
    // as coming from VTM
    if (taskId) {
      currentPostId = partnerId + ':' + taskId.split('-')[1];
    } else if (parentTaskId) {
      // uuid v4 example: ce2d30ec-4543-4032-b13d-8b2e7a5a89dc
      currentPostId =
        partnerId + ':' + parentTaskId + ':' + uuid.v4().replace(new RegExp('-', 'g'), '').substring(0, 8);
      // example of generated post id: ABC:a52ad31d986f4852b197d38ade29ce0f:ce2d30ec
    } else {
      currentPostId =
        currentPostId === NEW_POST_ID ? uuid.v4().replace(new RegExp('-', 'g'), '').substring(0, 32) : currentPostId;
      currentPostId = currentPostId.replace(new RegExp('-', 'g'), '');
    }

    return currentPostId;
  }

  formatDateGMB(date: Date) {
    return moment(date).format(DATE_GMB_FORMAT).toString();
  }

  formatTwitterPostTextWithLink(postText: string, link: string): string {
    if (
      postText.length >= 280 ||
      postText.includes(link) ||
      (postText.length < 280 && !postText.endsWith(' ') && postText.length + 1 + link.length > 280) ||
      (postText.length < 280 && postText.endsWith(' ') && postText.length + link.length > 280)
    ) {
      return postText;
    } else if (postText.endsWith(' ')) {
      postText = postText + link;
    } else if (!postText.endsWith(' ')) {
      postText = postText + ' ' + link;
    }
    return postText;
  }

  handleSocialPostsSideEffects(results: TaskManagerPost[], isEditing?: boolean): Observable<any> {
    const payload = [
      Payload.encodeURLComponent('accountGroupId', this.accountGroupId),
      Payload.encodeURLComponent('postResultJson', JSON.stringify(results)),
      Payload.encodeURLComponent('isEditingFlag', isEditing),
    ];

    const url = '/_api/v2/social-post/task-manager-handler/';
    const body = Payload.constructURLPayload(payload);
    return this.http.post(url, body, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } });
  }

  submitDraft(
    draftId: string,
    draftText: string,
    selectedSsids: string[],
    postDateTime: Date,
    linkUrl?: string,
    gmbOptions?: GmbOptions,
    uploadedMediaObjects?: UploadedFile[],
    customizations?: Customization[],
    youtubeCustomization?: YoutubePostCustomization,
    isHiddenDraft?: boolean,
    tiktokCustomization?: TiktokPostCustomization,
    partnerID?: string,
    workflowType?: WorkflowType,
  ): Observable<any> {
    let requestGMB = new GMBPostCustomization();
    if (!!gmbOptions && (!!gmbOptions.addCta || !!gmbOptions.makeEvent)) {
      requestGMB = new GMBPostCustomization({
        title: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.title : '',
        eventStart: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.startDate : null,
        eventEnd: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.endDate : null,
        ctaType: gmbOptions.addCta && gmbOptions.ctaOptions ? gmbOptions.ctaOptions.action : null,
        linkUrl: gmbOptions.addCta && gmbOptions.ctaOptions ? gmbOptions.ctaOptions.ctaUrl : '',
      });
    }

    const newMedia = uploadedMediaObjects
      .filter(
        (obj: any) =>
          !(!!obj?.errors?.FB_PAGE?.length && !!selectedSsids?.find((ssid) => ssid.includes('FB'))) &&
          !(!!obj?.errors?.GMB?.length && !!selectedSsids?.find((ssid) => ssid.includes('acc'))) &&
          !(!!obj?.errors?.GMB_LOCATION?.length && !!selectedSsids?.find((ssid) => ssid.includes('acc'))) &&
          !(!!obj?.errors?.LI_COMPANY?.length && !!selectedSsids?.find((ssid) => ssid.includes('LI'))) &&
          !(!!obj?.errors?.LI_USER?.length && !!selectedSsids?.find((ssid) => ssid.includes('LI'))) &&
          !(!!obj?.errors?.TW_USER?.length && !!selectedSsids?.find((ssid) => ssid.includes('TW'))),
      )
      .map((media) => {
        switch (media.fileType) {
          case FileType.GIF:
            return { gifUrl: media.url } as SDKMedia;
          case FileType.IMAGE:
            return { imageUrl: media.url } as SDKMedia;
          case FileType.VIDEO:
            return { videoUrl: media.url } as SDKMedia;
        }
      });

    // If there's no uploaded media set and the customizations exist the try to pull the metaContent images from them, if any
    if (!newMedia.length && !!customizations) {
      // Make sure we aren't passing empty or undefined images along, and that we aren't calling images that don't exist.
      let metaImages = customizations.map((c) => {
        const temp = c.siteMetaContent$$.getValue();
        return temp?.images?.length ? temp.images[0] : null;
      });
      metaImages = metaImages.filter((image) => !!image);

      if (metaImages.length) {
        newMedia.push({ imageUrl: this.getMostFrequent(metaImages) } as SDKMedia);
      }
    }

    const temp = customizations.find((c) => c.draftType$$.getValue());
    const draftType = temp?.draftType$$?.getValue() || DraftType.DRAFT_TYPE_INVALID;
    const ssidDraftMap: SSIDDraftTypeInterface[] = selectedSsids.map((ssid) => ({
      ssid,
      draftType:
        ssid.toUpperCase().startsWith('IG') && draftType === DraftType.DRAFT_TYPE_STORIES
          ? DraftType.DRAFT_TYPE_STORIES
          : DraftType.DRAFT_TYPE_INVALID,
    }));
    const metaData = this.getMetaDataFromUploadedMedia(uploadedMediaObjects);
    this.updateDraftMetaDataWithWorFlow(metaData, partnerID, workflowType);
    const draftYoutubeCustomization = this.getDraftYoutubePostCustomization(youtubeCustomization);
    return draftId
      ? this.draftsService.updateDraft(
          this.accountGroupId,
          draftId,
          draftText || '',
          postDateTime || new Date(),
          requestGMB,
          linkUrl,
          [],
          newMedia,
          selectedSsids,
          null,
          metaData,
          draftYoutubeCustomization,
          ssidDraftMap,
          isHiddenDraft ? isHiddenDraft : false,
          tiktokCustomization,
        )
      : this.draftsService.createDraft(
          this.accountGroupId,
          draftText || '',
          postDateTime || new Date(),
          requestGMB,
          linkUrl,
          [],
          newMedia,
          selectedSsids,
          null,
          metaData,
          draftYoutubeCustomization,
          ssidDraftMap,
          isHiddenDraft ? isHiddenDraft : false,
          tiktokCustomization,
        );
  }

  private getDraftType(customization: Customization): DraftType {
    const draftType = customization?.draftType$$.getValue() || DraftType.DRAFT_TYPE_INVALID;
    const ssid = this.getFirstSSID(customization);
    return ssid.toUpperCase().startsWith('IG') && draftType === DraftType.DRAFT_TYPE_STORIES
      ? DraftType.DRAFT_TYPE_STORIES
      : DraftType.DRAFT_TYPE_INVALID;
  }

  private getFirstSSID(customization: Customization): string {
    return Array.from(customization.services$$.getValue().keys())[0].ssid;
  }

  private createGMBPostCustomization(customization: Customization, ssid: string): GMBPostCustomization | undefined {
    const gmbOptions = customization.gmbOptions$$.getValue();
    if (ssid.toUpperCase().startsWith('ACCOUNTS') && gmbOptions && (gmbOptions.addCta || gmbOptions.makeEvent)) {
      return new GMBPostCustomization({
        title: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.title : '',
        eventStart: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.startDate : null,
        eventEnd: gmbOptions.makeEvent && gmbOptions.eventOptions ? gmbOptions.eventOptions.endDate : null,
        ctaType: gmbOptions.addCta && gmbOptions.ctaOptions ? gmbOptions.ctaOptions.action : null,
        linkUrl: gmbOptions.addCta && gmbOptions.ctaOptions ? gmbOptions.ctaOptions.ctaUrl : '',
      });
    }
  }

  private processMedia(customization: Customization): SDKMedia[] {
    const medias =
      customization.uploadedMediaObjects$$?.getValue().map(
        (media) =>
          ({
            gifUrl: media.fileType === FileType.GIF ? media.url : undefined,
            imageUrl: media.fileType === FileType.IMAGE ? media.url : undefined,
            videoUrl: media.fileType === FileType.VIDEO ? media.url : undefined,
          }) as SDKMedia,
      ) || [];

    if (!medias.length) {
      const metaImages = customization.siteMetaContent$$?.getValue()?.images.filter(Boolean);
      if (metaImages?.length) {
        medias.push({ imageUrl: this.getMostFrequent(metaImages) } as SDKMedia);
      }
    }

    return medias;
  }

  bulkCreateDrafts(
    customizations: Customization[],
    accountGroupId: string,
    scheduledDate: Date,
    isHiddenDraft = false,
  ): Observable<CreateMultiDraftsResponse> {
    const drafts = customizations.map((customization) => {
      const postText = customization.postText$$.getValue() || '';
      const ssid = this.getFirstSSID(customization);
      const draftType = this.getDraftType(customization);
      const requestGMB = this.createGMBPostCustomization(customization, ssid);
      const newMedia = this.processMedia(customization);
      const metaData = this.getMetaDataFromUploadedMedia(customization.uploadedMediaObjects$$?.getValue());
      const draftYoutubeCustomization = this.getDraftYoutubePostCustomization(
        customization.youtubePostCustomization$$?.getValue(),
      );

      return {
        accountGroupId,
        draftText: postText,
        postDateTime: scheduledDate,
        gmbPostCustomization: requestGMB,
        options: [],
        media: newMedia,
        ssids: [ssid],
        draftType,
        metadata: metaData,
        youtubeCustomization: draftYoutubeCustomization,
        ssidDraftTypes: [{ ssid, draftType }],
        isHidden: isHiddenDraft,
      } as CreateDraftRequestInterface;
    });

    return this.draftsService.createMultiDrafts({ drafts });
  }

  saveTemplate(template: PostTemplate): Observable<any> {
    const postType: PostType =
      template?.mediaEntries?.length >= 2 ? PostType.POST_TYPE_CAROUSEL : PostType.POST_TYPE_INVALID;
    if (template.template_id) {
      return this.postTemplatesService.updatePostTemplate(
        template.account_group_id,
        template.template_id,
        template.title,
        template.post_text,
        template.post_date_time || new Date(),
        template.gmb_post_customization || '',
        template.image_url || '',
        template.image_path || '',
        template.image_size || '',
        null,
        [],
        template.video_url || '',
        postType,
        (template.mediaEntries as any) || [],
        [],
      );
    } else {
      return this.postTemplatesService.createPostTemplate(
        template.account_group_id,
        template.title,
        template.post_text,
        template.post_date_time || new Date(),
        template.gmb_post_customization || '',
        template.image_url || '',
        template.image_path || '',
        template.image_size || '',
        null,
        [],
        template.video_url || '',
        postType,
        (template.mediaEntries as any) || [],
        [],
      );
    }
  }

  getMostFrequent(arr): any {
    const hashmap = arr.reduce((acc, val) => {
      acc[val] = (acc[val] || 0) + 1;
      return acc;
    }, {});
    return Object.keys(hashmap).reduce((a, b) => (hashmap[a] > hashmap[b] ? a : b));
  }

  getDateOnly(date: Date): string {
    return `${(date.getMonth() + 1).toString()}/${date.getDate()}/${date.getFullYear()}`;
  }

  getTimeOnly(date: Date): string {
    let hours = date.getHours();
    let ampm = 'am';
    if (hours > 11) {
      ampm = 'pm';
      if (hours > 12) {
        hours = hours - 12;
      }
    }
    hours = hours === 0 ? 12 : hours;
    const mins = date.getMinutes();
    return hours.toString() + ':' + (mins > 9 ? mins : '0' + mins) + ampm;
  }

  updateMetaContent(urlToFetchMetaContentFor: string, services: PostableService[]): Observable<any> {
    const url = `/angular/${this.accountGroupId}/fetch-meta-content/`;
    const serviceIds = services.map((service: PostableService) => service.ssid);
    const serviceGroups: string[] = [];
    services.map((service: PostableService) => {
      const sh = getServiceGroupShorthand(service.serviceType);
      if (serviceGroups.indexOf(sh) < 0) {
        serviceGroups.push(sh);
      }
    });
    let body = 'url=' + urlToFetchMetaContentFor + '&agid=' + this.accountGroupId;

    if (this.defaultFBPage) {
      body += '&serviceIds=' + this.defaultFBPage;
    }

    serviceIds.forEach((id) => (body += '&serviceIds=' + id));

    serviceGroups.forEach((group) => (body += '&selectedServiceGroups=' + group));
    return this.http.post(url, body, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } });
  }

  fetchInterestingContentFields(): Observable<any> {
    const fetchFieldsUrl = `/account/${
      this.accountGroupId
    }/interesting-content/feed/?__vformcmd__=fields&__vformctx__=${encodeURI(window.location.toString())}`;
    return this.http.get(fetchFieldsUrl);
  }

  fetchInterestingContent(
    selectedRssCategories?: string[],
    selectedTwitterSearches?: string[],
    cursor?: number,
  ): Observable<any> {
    const fetchContentUrl = `/account/${
      this.accountGroupId
    }/interesting-content/feed/?__vformcmd__=submit&__vformctx__=${encodeURI(window.location.toString())}`;
    const bodyArgs = [];
    if (selectedRssCategories) {
      selectedRssCategories.forEach((category) => bodyArgs.push('rssCategories=' + category));
    }
    if (selectedTwitterSearches) {
      selectedTwitterSearches.forEach((search) => bodyArgs.push('twitterSearches=' + search));
    }
    if (cursor && cursor !== 0) {
      bodyArgs.push('cursor=' + cursor);
    }
    const body = bodyArgs.join('&');
    return this.http.post(fetchContentUrl, body, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } });
  }

  fetchUrltoBlob(Url: string) {
    return this.http.get(Url, { responseType: 'blob' });
  }

  deleteDraft(draftId: string): Observable<any> {
    return this.draftsService.deleteDraft(this.accountGroupId, draftId);
  }

  saveHashtags(hashtags: string[], partnerId: string): Observable<any> {
    return this.composerMicroserviceApi.replaceHashtags(hashtags, this.accountGroupId, partnerId);
  }

  queryHashtag(searchTerm: string, partnerId: string): Observable<SearchHashtagResponseInterface> {
    return this.composerMicroserviceApi.searchHashtag(searchTerm, this.accountGroupId, 10000, partnerId);
  }

  private getPostCustomization({ makeEvent, eventOptions, addCta, ctaOptions }: GmbOptions): PostCustomization {
    const event = makeEvent
      ? {
          title: eventOptions.title,
          start: eventOptions.startDate,
          end: eventOptions.endDate,
        }
      : null;
    const action = addCta ? { type: ctaOptions.action, linkUrl: ctaOptions.ctaUrl } : null;
    return { event, action } as PostCustomization;
  }

  private getMetaDataFromUploadedMedia(uploadedFiles: UploadedFile[]): MetaData[] {
    return uploadedFiles.map((file) => {
      const propertyValue = JSON.stringify({
        naturalWidth: file.naturalWidth,
        naturalHeight: file.naturalHeight,
        duration: file.duration,
        videoHeight: file.videoHeight,
        videoWidth: file.videoWidth,
        size: file.size,
      });

      const metadata: MetaData = {
        propertyName: file.url,
        propertyValue: propertyValue,
        toApiJson: () => ({
          propertyName: file.url,
          propertyValue: propertyValue,
        }),
      };

      return metadata;
    });
  }
  private updateDraftMetaDataWithWorFlow(metaData: MetaData[], partnerId, workFlowType) {
    const partnerData: MetaData = {
      propertyName: 'partnerId',
      propertyValue: partnerId,
      toApiJson: () => ({
        propertyName: 'partnerId',
        propertyValue: partnerId,
      }),
    };
    const workflowData: MetaData = {
      propertyName: 'workflow_type',
      propertyValue: workFlowType,
      toApiJson: () => ({
        propertyName: 'workflow_type',
        propertyValue: workFlowType,
      }),
    };
    metaData.push(partnerData, workflowData);
  }
}

export function getColorFromService(service: string, secondary?: boolean): string {
  switch (service) {
    case SocialService.FACEBOOK:
      return !secondary ? '#3b5998' : '#8b9dc3';
    case SocialService.GOOGLE_MY_BUSINESS:
    case SocialService.GMB:
      return !secondary ? '#4285f4' : '#679df6';
    case SocialService.TWITTER:
      return !secondary ? '#00aced' : '#c0deed';
    case SocialService.LINKEDIN:
    case SocialService.LINKEDIN_COMPANY:
      return !secondary ? '#0077B5' : '#0099e8';
    case SocialService.INSTAGRAM:
      return !secondary ? '#c13584' : '#833ab4';
    case SocialService.CURATED_CONTENT:
      return !secondary ? '#fff' : '#424242';
    default:
      return !secondary ? '#ffab00' : '#ffdd4b';
  }
}

function getServiceGroupShorthand(service: string): string {
  switch (service) {
    case SocialService.FACEBOOK:
      return 'FB';
    case SocialService.GOOGLE_MY_BUSINESS:
    case SocialService.GMB:
      return 'GMB';
    case SocialService.TWITTER:
      return 'TW';
    case SocialService.LINKEDIN:
    case SocialService.LINKEDIN_COMPANY:
      return 'LI';
    case SocialService.INSTAGRAM:
      return 'IG';
    default:
      return null;
  }
}
