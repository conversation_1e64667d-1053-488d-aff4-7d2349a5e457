import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ParsedPost, PreviewUploadedPosts } from '../../../../shared/csv/csv-model';
import { MediaType, MLSocialNetworkType, PostContent } from './../../../../shared/dynamic-posts/dynamic-posts-models';
import { Steps, StepStatus } from '../bulk-upload.enum';
import { UntypedFormGroup } from '@angular/forms';
import { BulkUploadPostsService } from '../bulk-upload-posts.service';
import { Location } from '@vendasta/social-posts';
import { PostType } from '@vendasta/composer';

@Component({
  selector: 'app-bulk-upload-preview',
  standalone: false,
  templateUrl: './bulk-upload-preview.component.html',
  styleUrl: './bulk-upload-preview.component.scss',
})
export class BulkUploadPreviewComponent implements OnInit {
  @Input() set selectedIndex(step: number) {
    if (step === Steps.EDIT) {
      this.nextStep.emit({
        stepName: Steps.EDIT,
        formGroupValue: this.previewForm,
      });
    }
  }
  @Input() posts: PreviewUploadedPosts;
  @Output() nextStep = new EventEmitter<any>();
  @Output() postEdited = new EventEmitter();

  convertedPosts: PostContent[];
  previewForm: UntypedFormGroup;

  constructor(private bulkUploadPostsService: BulkUploadPostsService) {
    this.previewForm = new UntypedFormGroup({});
  }
  ngOnInit() {
    const selectedNetworks = this.posts?.selectedMLNetworks;
    this.convertedPosts = this.convertToDynamicFeed(this.posts?.posts, selectedNetworks, this.posts.locations);
    this.convertedPosts.forEach((n) => {
      const isML = this.posts?.isML;
      n.mlSocialNetworkType = selectedNetworks;
      n.isML = isML;
    });
    this.previewForm?.valueChanges?.subscribe(() => {
      this.bulkUploadPostsService.setFormStatus(Steps.EDIT, this.previewForm.status === StepStatus.VALID);
      this.nextStep.emit({
        stepName: Steps.EDIT,
        formGroupValue: this.previewForm,
      });
    });
  }

  private convertToDynamicFeed(
    posts: ParsedPost[],
    mlSocialNetwork: MLSocialNetworkType[],
    locations: Location[],
  ): PostContent[] {
    return posts.map((post) => ({
      postText: post?.postText,
      scheduleDate: post?.scheduleDate,
      Medias: post?.Medias?.map((media) => ({
        mediaUrl: media.mediaUrl,
        mediaType: media.mediaType.toUpperCase() === 'VIDEO' ? MediaType.VIDEO : MediaType.IMAGE,
      })),
      mlSocialNetworkType: mlSocialNetwork,
      locations: locations,
      gmbCustomization: post.gmbCustomization,
      postType: post?.isIGStory ? PostType.POST_TYPE_STORIES : PostType.POST_TYPE_IMAGE,
    }));
  }

  onPostEdited(event) {
    this.posts.posts[event.index].postText = event.post.postText;
    this.posts.posts[event.index].scheduleDate = event.post.scheduleDate;
    this.posts.posts[event.index].Medias = event.post?.Medias?.map((media) => ({
      mediaUrl: media.mediaUrl,
      mediaType: media.mediaType,
    }));
    this.posts.posts[event.index].gmbCustomization = event?.post?.gmbCustomization;
    this.posts.posts[event.index].postTypes = event.post.postType;
    this.postEdited.emit({ post: this.posts.posts[event.index], index: event.index });
  }
}
