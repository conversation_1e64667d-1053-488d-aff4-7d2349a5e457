import { Injectable } from '@angular/core';
import {
  BulkCreateMultilocationPostRequest,
  BulkCreateMultilocationPostResponse,
  BulkUploadMultilocation,
  Location,
  LocationInterface,
  MediaEntry,
  MetaData,
  MultilocationPostsService,
  PostAction,
  PostCustomization,
  PostCustomizationInterface,
  PostEvent,
} from '@vendasta/social-posts';
import { BehaviorSubject, Observable } from 'rxjs';
import { ConfigService } from '../../../core/config/config.service';
import { gmbCustomization, MediaType, ParsedPost } from '../../../shared/csv/csv-model';
import { v4 as uuidv4 } from 'uuid';
import { Steps } from './bulk-upload.enum';

export interface FormStatus {
  uploadForm?: false;
  previewForm?: false;
}

@Injectable()
export class BulkUploadPostsService {
  metaData: any;
  prevStepvalidity$$ = new BehaviorSubject<FormStatus>(null);
  stepValidity: FormStatus = {};
  emitStepper$ = this.prevStepvalidity$$.asObservable();
  constructor(
    private configService: ConfigService,
    private multilocationPostsService: MultilocationPostsService,
  ) {}

  scheduleMultiplePosts(
    req: ParsedPost[],
    locations: LocationInterface[],
  ): Observable<BulkCreateMultilocationPostResponse> {
    return this.multilocationPostsService.bulkCreateMultilocationPost(
      this.mapMulitRequests(req, locations as Location[]),
    );
  }

  mapMulitRequests(post: ParsedPost[], locations: Location[]): BulkCreateMultilocationPostRequest {
    const bulkCreateMultilocationPostRequest = new BulkCreateMultilocationPostRequest({
      brandId: this.configService.brandId,
      locations: locations,
      request: post.map((p) => this.mapSingleRequest(p)),
    });
    return bulkCreateMultilocationPostRequest;
  }

  mapSingleRequest(post: ParsedPost): BulkUploadMultilocation {
    const bulkUpload = new BulkUploadMultilocation({
      text: post.postText,
      media: post.Medias.map((m) => m.mediaUrl),
      scheduledDate: post.scheduleDate,
      gifs: post.Medias.filter((m) => m.mediaType === MediaType.GIF).map((gif) => gif.mediaUrl),
      videos: post.Medias.filter((m) => m.mediaType === MediaType.VIDEO).map((gif) => gif.mediaUrl),
      mediaEntries: post.Medias.map(
        (m) =>
          ({
            mediaType: m.mediaType,
            mediaUrl: m.mediaUrl,
          }) as MediaEntry,
      ),

      postTypes: post.postTypes,
      multilocationId: uuidv4().replace(/-/g, ''),
      customization: this.mapCustomization(post.gmbCustomization),
    });

    return bulkUpload;
  }

  private mapCustomization(gmb?: gmbCustomization): PostCustomization {
    const customizationPayload: Partial<PostCustomizationInterface> = {};

    if (gmb?.event && (gmb.event.title || gmb.event.start || gmb.event.end)) {
      customizationPayload.event = new PostEvent({
        title: gmb.event.title || '',
        ...(gmb.event.start && { start: gmb.event.start }),
        ...(gmb.event.end && { end: gmb.event.end }),
      });
    }

    if (gmb?.action && (gmb.action.type || gmb.action.linkUrl)) {
      customizationPayload.action = new PostAction({
        type: gmb.action.type || '',
        linkUrl: gmb.action.linkUrl || '',
      });
    }

    return new PostCustomization(customizationPayload as PostCustomizationInterface);
  }

  private appendMetaData(source, workFlowType): MetaData[] {
    const metaData: MetaData[] = [];
    const sourceData: MetaData = {
      propertyName: 'source',
      propertyValue: source,
      toApiJson: () => ({
        propertyName: 'source',
        propertyValue: source,
      }),
    };
    const workflowData: MetaData = {
      propertyName: 'workflow_type',
      propertyValue: workFlowType,
      toApiJson: () => ({
        propertyName: 'workflow_type',
        propertyValue: workFlowType,
      }),
    };
    metaData.push(sourceData, workflowData);
    return metaData;
  }

  setFormStatus(step, status) {
    if (step === Steps.UPLOAD) {
      this.stepValidity.uploadForm = status;
    } else if (step === Steps.EDIT) {
      this.stepValidity.previewForm = status;
    }
    this.prevStepvalidity$$.next(this.stepValidity);
  }
}
