@use 'design-tokens' as *;
@import 'bounded_inputs';

@include bounded-inputs();

:host {
  display: flex;
  flex-direction: column;
  height: 100%;

  ::ng-deep .mat-step-header {
    pointer-events: none;

    &:hover {
      background-color: transparent;
    }
  }

  mat-horizontal-stepper {
    background-color: $glxy-grey-50;
    font-family: <PERSON><PERSON>;
  }
}

.spacer {
  flex-grow: 1;
}

.toolbar {
  min-height: 60px;
  max-height: 60px;
  align-items: center;
  display: flex;
  width: 100%;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
}

.download-link {
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  text-decoration: none;
  flex-wrap: wrap;
  color: $glxy-blue-700;
  padding: 5px 10px;
  border-radius: 5px;

  &:hover {
    background-color: $glxy-grey-50;
  }
}

.main-card {
  padding: 20px 20px;
  width: 100%;
  border: none;
}

.card-description {
  font-weight: 400;
  font-size: 14px;
  line-height: 19.6px;
  color: $glxy-grey-500;
}

.card-title {
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
}

.header-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-wrap: wrap;
}

.button-section {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.how-to-upload-section {
  margin: 30px 0 10px;
}

.how-to-upload-section ol {
  padding-left: 20px;
  margin-top: 16px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 14px;
}

table,
th,
td {
  border: 1px solid $glxy-grey-300;
}

th,
td {
  padding: 12px;
  text-align: left;
  line-height: 12px;
  white-space: nowrap;
  word-break: keep-all;
}

td {
  font-weight: 500;
  font-size: 10px;
  line-height: 12px;
  color: $glxy-grey-700;
}

th {
  background-color: $glxy-grey-50;
  font-weight: 500;
  font-size: 10px;
  line-height: 12px;
}

.title {
  font-weight: 700;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 4px;
}

.description {
  font-weight: 400;
  font-size: 14px;
  line-height: 19.6px;
  letter-spacing: 1%;
  color: $glxy-grey-700;
}

.download-description {
  font-weight: 400;
  font-size: 14px;
  line-height: 19.6px;
  letter-spacing: 1%;
  color: $glxy-grey-700;
  margin-bottom: 8px;
}

.step {
  margin-bottom: 24px;
}

.formatting-btn {
  font-size: 14px;
  cursor: pointer;
  z-index: 0;
  margin-left: 5px;
}

.multilocation-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  width: 100%;
}

.bulk-upload {
  position: fixed;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

mat-sidenav-container {
  flex: 0 0 auto;
}

mat-toolbar.toolbar {
  position: sticky;
  top: 0;
  z-index: 10;
}

.inner-card {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 0 25px 25px 25px;
  width: 100%;
  max-width: 720px;
  margin: 0 auto;

  height: calc(100vh - 170px);

  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.table-wrapper {
  width: 100%;
  overflow-x: auto;
}

.table-wrapper table {
  min-width: 600px;
}

.location-netowrk-section {
  background: $glxy-grey-50;
  border-radius: 5px;
  padding: 15px;
  margin-top: 10px;
}

.disable {
  display: none !important;
}

.first-ul {
  padding-left: 23px;
  list-style: none;

  li.description::before {
    content: '.';
    color: $glxy-grey-700;
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
    font-size: 1.2rem;
    position: relative;
    top: -3px;
    left: 7px;
  }
}

:host ::ng-deep .mat-horizontal-stepper-header-container {
  width: 25%;
  margin: 0 auto;
  @media (max-width: 900px) {
    width: 80%;
  }
}
