<form [formGroup]="uploadForm">
  <div class="bulk-upload">
    <mat-sidenav-container>
      <mat-sidenav-content>
        <mat-toolbar class="toolbar">
          <button [ngStyle]="{ color: '#1e88e5' }" mat-icon-button *ngIf="showClose" (click)="closeCSVComposer()">
            <mat-icon>arrow_back</mat-icon>
          </button>
          {{ 'BULK_UPLOAD.PAGE_TITLE' | translate }}
          <span class="spacer"></span>
        </mat-toolbar>
      </mat-sidenav-content>
    </mat-sidenav-container>

    <ng-template #uploadContainer>
      <div class="inner-card">
        <mat-card appearance="outlined" class="main-card">
          <div>
            <div class="header-section">
              <span class="card-title">{{ 'BULK_UPLOAD.CARD_TITLE' | translate }}</span>
              <span class="card-description">{{ 'BULK_UPLOAD.CARD_DESCRIPTION' | translate }} </span>
            </div>

            <div class="button-section">
              <a (click)="prepareDownload()" class="download-link">
                <mat-icon>download</mat-icon>
                {{ 'BULK_UPLOAD.DOWNLOAD_BTN_LABEL' | translate }}
              </a>
            </div>

            <div class="uploader-section">
              <app-csv-upload></app-csv-upload>
            </div>

            <div class="location-netowrk-section" [ngClass]="{ disable: uploadedPosts().length === 0 }">
              <!-- location section and network selection -->
              <div>
                <span class="composer-title p-b-06">{{ 'COMPOSER.SELECT_LOCATIONS' | translate }}</span>
                <app-brands-filter [isEditing]="processing()"></app-brands-filter>
              </div>
              <div class="multilocation-container">
                <span class="composer-title p-b-06">{{ 'COMPOSER.SOCIAL_ACCOUNTS' | translate }}</span>
                <div class="multilocation-container">
                  <app-brand-social-networks
                    [socialService]="postService.FACEBOOK"
                    [isEditing]="processing()"
                    (isValidPost)="networkValidationEvent($event)"
                  >
                  </app-brand-social-networks>
                  <app-brand-social-networks
                    [socialService]="postService.GMB"
                    [isEditing]="processing()"
                    (isValidPost)="networkValidationEvent($event)"
                  >
                  </app-brand-social-networks>
                  <app-brand-social-networks
                    [socialService]="postService.INSTAGRAM"
                    [isEditing]="processing()"
                    (isValidPost)="networkValidationEvent($event)"
                  >
                  </app-brand-social-networks>
                  <app-brand-social-networks
                    *ngIf="featureFlagService.linkedinMultilocationEnabled$ | async"
                    [socialService]="postService.LINKEDIN || postService.LINKEDIN_COMPANY"
                    [isEditing]="processing()"
                    (isValidPost)="networkValidationEvent($event)"
                  >
                  </app-brand-social-networks>
                </div>
              </div>
            </div>

            <div class="how-to-upload-section">
              <span class="title">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TITLE' | translate }}</span>
              <ol>
                <li>
                  <div class="step">
                    <div class="title">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_ONE' | translate }}</div>
                    <div class="download-description">
                      {{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_ONE_DESC' | translate }}
                    </div>
                    <a class="download-link" (click)="prepareDownload()">{{
                      'BULK_UPLOAD.DOWNLOAD_BTN_LABEL' | translate
                    }}</a>
                  </div>
                </li>
                <li>
                  <div class="step">
                    <div class="title">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_TWO' | translate }}</div>
                    <div class="description">
                      {{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_TWO_DESC' | translate }}
                    </div>
                    <ul class="first-ul">
                      <li class="description">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_TWO_DESC_2' | translate }}</li>
                      <li class="description">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_TWO_DESC_3' | translate }}</li>
                      <li class="description">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_TWO_DESC_4' | translate }}</li>
                    </ul>
                    <div class="table-wrapper">
                      <table>
                        <thead>
                          <tr>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_ONE' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_TWO' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_THREE' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_FOUR' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_FIVE' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_SIX' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_SEVEN' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_EIGHT' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_NINE' | translate }}</th>
                            <th>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.HEAD_TEN' | translate }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_ONE' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_TWO' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_THREE' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_FOUR' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_FIVE' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_SIX' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_SEVEN' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_EIGHT' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_NINE' | translate }}</td>
                            <td>{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.TABLE.DATA_TEN' | translate }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </li>
                <li>
                  <div class="step">
                    <div class="title">{{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_THREE' | translate }}</div>
                    <div class="description">
                      {{ 'BULK_UPLOAD.HOW_TO_UPLOAD.SUB_TITLE_THREE_DESC' | translate }}
                    </div>
                  </div>
                </li>
              </ol>

              <button mat-stroked-button class="formatting-btn" (click)="formatingRule()">
                {{ 'BULK_UPLOAD.HOW_TO_UPLOAD.MODAL_BTN_LABLE' | translate }}
              </button>
            </div>
          </div>
        </mat-card>
        @if (enableStepper$) {
          <glxy-sticky-footer [rightAligned]="true">
            <button mat-flat-button color="primary" [disabled]="disableSubmit()" (click)="goToNextStep()">
              {{ 'BULK_UPLOAD.CSV_UPLOAD.NEXT' | translate }}
            </button>
          </glxy-sticky-footer>
        } @else {
          <ng-container *ngTemplateOutlet="scheduleButton"></ng-container>
        }
      </div>
    </ng-template>

    <ng-template #scheduleButton>
      <app-sticky-footer
        [ctaRight]="true"
        [enableCancelButton]="true"
        [submitLabel]="processing() ? 'Validating...' : 'Schedule posts'"
        (submitClick)="submitPosts()"
        [disabled]="disableSubmit()"
        (cancelClick)="goBack()"
        [cancelLabel]="'Back'"
      ></app-sticky-footer>
    </ng-template>
    @if (enableStepper$) {
      <mat-horizontal-stepper [selectedIndex]="currentStep">
        <mat-step>
          <ng-template matStepLabel>{{ 'BULK_UPLOAD.CSV_UPLOAD.UPLOAD' | translate }}</ng-template>
          <ng-container *ngTemplateOutlet="uploadContainer"></ng-container>
        </mat-step>
        <mat-step>
          <ng-template matStepLabel>{{ 'BULK_UPLOAD.CSV_UPLOAD.PREVIEW' | translate }}</ng-template>
          <div class="inner-card">
            @if (previewPosts) {
              <mat-card appearance="outlined" class="main-card">
                <app-bulk-upload-preview
                  [posts]="previewPosts"
                  (nextStep)="getNextStepEvent($event)"
                  [selectedIndex]="currentStep"
                  (postEdited)="onPostEdited($event)"
                ></app-bulk-upload-preview>
              </mat-card>
            }
            <ng-container *ngTemplateOutlet="scheduleButton"></ng-container>
          </div>
        </mat-step>
      </mat-horizontal-stepper>
    } @else {
      <ng-container *ngTemplateOutlet="uploadContainer"></ng-container>
    }
  </div>
</form>
