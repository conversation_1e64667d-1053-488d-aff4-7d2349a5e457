import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Observable, Subscription, withLatestFrom } from 'rxjs';
import { filter } from 'rxjs/operators';
import { SocialService, serviceIconPath, serviceTypeFullName } from '../../../post';
import { BrandsService } from '../brands.service';
import { PagesDetailsDialogComponent } from './pages-details-dialog/pages-details-dialog.component';

@Component({
  selector: 'app-brand-social-networks',
  templateUrl: './social-networks.component.html',
  styleUrls: ['./social-networks.component.scss'],
  standalone: false,
})
export class BrandSocialNetworksComponent implements OnInit, OnDestroy {
  @Input() isEditing = false;
  @Input() socialService = SocialService.FACEBOOK;
  @Output() isValidPost = new EventEmitter<boolean>();
  networkGroupSelected = true;
  numPages$: Observable<number>;
  _subscriptions: Subscription[] = [];

  constructor(
    private brandsService: BrandsService,
    private dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    this.numPages$ = this.getNumServicesPages(this.socialService);
    this.networkGroupSelected = this.brandsService.getNetworkState(this.socialService);
    //The accounts are filled at the moment the filter component is rendered
    this._subscriptions.push(
      this.brandsService.fullBrandServiceMap$
        .pipe(
          withLatestFrom(this.numPages$),
          filter(
            ([serviceMap, numPages]) =>
              (Boolean(serviceMap) && !this.isEditing) || (Boolean(numPages) && this.isEditing && Boolean(serviceMap)),
          ),
        )
        .subscribe(() => this.brandsService.selectNetwork(this.socialService)),
    );
  }

  serviceIcon(service: string): string {
    return serviceIconPath(service);
  }

  toggleChecked(): void {
    if (!this.isEditing) {
      this.networkGroupSelected = !this.networkGroupSelected;
      this.brandsService.setNetworkState(this.socialService, this.networkGroupSelected);
      this.networkGroupSelected
        ? this.brandsService.selectNetwork(this.socialService)
        : this.brandsService.deselectNetwork(this.socialService);
    }
    this.isValidPost.emit(true);
  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((sub) => sub.unsubscribe());
  }

  onPagesDetails(): void {
    this.dialog.open(PagesDetailsDialogComponent, { data: { socialService: this.socialService } });
  }

  serviceName(serviceType: SocialService): string {
    return serviceTypeFullName(serviceType);
  }

  getNumServicesPages(serviceType: SocialService): Observable<number> {
    const serviceMap: Record<
      | SocialService.FACEBOOK
      | SocialService.GMB
      | SocialService.INSTAGRAM
      | SocialService.LINKEDIN_COMPANY
      | SocialService.LINKEDIN,
      Observable<number>
    > = {
      [SocialService.FACEBOOK]: this.brandsService.numFBPages$,
      [SocialService.GMB]: this.brandsService.numGMBPages$,
      [SocialService.INSTAGRAM]: this.brandsService.numIGPages$,
      [SocialService.LINKEDIN_COMPANY]: this.brandsService.numLIPages$,
      [SocialService.LINKEDIN]: this.brandsService.numLIPages$,
    };

    return serviceMap[serviceType] || this.brandsService.numFBPages$;
  }
}
