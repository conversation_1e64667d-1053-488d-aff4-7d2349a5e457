import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  On<PERSON>estroy,
  OnInit,
  Output,
  QueryList,
  ViewChildren,
} from '@angular/core';
import { BehaviorSubject, Observable, Subscription, combineLatest, of } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  skipWhile,
  startWith,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { Customization } from '../../../core/post/customization';
import { serviceIconPath } from '../../../core/post/post';
import { ComposerStoreService } from '../../composer-store.service';
import {
  DEFAULT_PROFILE_IMAGE,
  HASHTAG_REGEX,
  MENTION_PROMPT_OPTION_MESSAGES,
  POSTHOG_KEYS,
  SERVICE_MAX_CHAR_COUNT,
} from '../../constants';
import { CustomizationValidatorService } from '../../customization-validator.service';

import { TranslateService } from '@ngx-translate/core';
import { MentionConfig } from '@vendasta/angular-social-mentions';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { ConfigService } from '../../../core';
import { SMFeaturesService } from '../../../core/features.service';
import { RepcoreApiService } from '../../../shared/repcore-api/repcore-api.service';
import { PostableService } from '../../../shared/social-service/social-service.service';
import { ValidatorFailure } from '../../composer-validator.service';
import { LinkHistory, MentionPromptOptions, SocialMentionInterface } from '../../interfaces';
import { SocialService } from '../../post';
import { UpdateRequest } from '../ai-menu/ai-menu-settings';
import { PostTextActionsService } from '../post-text-actions/post-text-actions.service';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { SmChatbotComponent } from '../../shared/dialogs/sm-chatbot/sm-chatbot.component';
import * as twitterText from 'twitter-text';
import { UpgradeCTADialogService } from '../../../shared/upgrade-cta-dialog/upgrade-cta-dialog.service';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
@Component({
  selector: 'app-customize-content',
  templateUrl: './customize-content.component.html',
  styleUrls: [
    './customize-content.component.scss',
    '../mention/mention-option.component.scss',
    '../compose/compose-text.component.scss',
  ],
  standalone: false,
})
export class CustomizeContentComponent implements OnInit, OnDestroy {
  @Input() highlightError = false;
  @Output() uploadMediaEvent = new EventEmitter();
  @ViewChildren('video') videos: QueryList<ElementRef<HTMLVideoElement>>;

  defaultProfileImage = DEFAULT_PROFILE_IMAGE;
  customizationContent$: Observable<CustomizationContent[]>;
  selectedCustomization$: Observable<number>;

  isGMBSelected$: Observable<boolean>;

  facebookSelected$: Observable<boolean>;
  twitterSelected$: Observable<boolean>;
  instagramSelected$: Observable<boolean>;
  gmbSelected$: Observable<boolean>;
  linkedinSelected$: Observable<boolean>;
  youtubeSelected$: Observable<boolean>;
  tiktokSelected$: Observable<boolean>;

  instagramLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  twitterLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  linkedInLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  gmbLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  tiktokLength$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);

  instagramValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  tiktokValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  linkedInValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  gmbValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  tweetValid$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  hashtagCount$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  hashtagLinkedIn$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  hashtagInstagram$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  hashtagTwitter$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  hashtagGMB$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  customHint$: Observable<string>;

  igHashtagCountValid$: Observable<boolean>;

  gmbValidatorFailure$: Observable<ValidatorFailure>;
  mediaUploadValidator$: Observable<ValidatorFailure>;

  igSocialService$: Observable<string>;
  igPostIdForEditing$: Observable<string>;

  subscriptions: Subscription[] = [];
  filteredOptions$: Observable<any>;
  hashtagConfig: MentionConfig = {};
  mentionsTerm$$: BehaviorSubject<string> = new BehaviorSubject<string>('');

  generatingContent$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  aiChatbotIsOpen$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  previewLinkHistory$: Observable<LinkHistory>;
  mentionPromptOptions = MentionPromptOptions;
  mentionPromptMessages = MENTION_PROMPT_OPTION_MESSAGES;
  mentionPromptOption$$: BehaviorSubject<number> = new BehaviorSubject<number>(-1);
  triggerChar$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  fbMentionsList$$: BehaviorSubject<SocialMentionInterface[]> = new BehaviorSubject<SocialMentionInterface[]>([]);
  twitterMentionsList$$: BehaviorSubject<SocialMentionInterface[]> = new BehaviorSubject<SocialMentionInterface[]>([]);
  selectedSocialTab$$: BehaviorSubject<string> = new BehaviorSubject<SocialService>(SocialService.TWITTER);
  showMentionTemplate$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  hashtagSearchTerm$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  previewLinkLoading$: Observable<boolean>;
  requirePreviewLink$: Observable<boolean>;
  showShortenLinks$: Observable<boolean>;
  postTextValidator$: Observable<ValidatorFailure>;

  constructor(
    public composerStore: ComposerStoreService,
    private customizationValidatorService: CustomizationValidatorService,
    public featuresService: SMFeaturesService,
    public configService: ConfigService,
    private repcoreService: RepcoreApiService,
    private translateService: TranslateService,
    private postTextActionsService: PostTextActionsService,
    private productAnalyticsService: ProductAnalyticsService,
    private dialog: MatDialog,
    private upgradeCTADialogService: UpgradeCTADialogService,
    public confirmationModal: OpenConfirmationModalService,
  ) {}

  ngOnInit(): void {
    this.customHint$ = this.composerStore.placeHolder$;
    this.updateInitialLength();
    this.initializeNetworkLength();
    this.selectedCustomization$ = this.composerStore.selectedCustomization$;
    this.customizationContent$ = this.composerStore.customizations$.pipe(
      switchMap((customizations) => {
        if (!customizations) {
          return of([]);
        }
        return combineLatest(
          customizations.map((customization) => {
            return combineLatest([
              customization.services$.pipe(
                switchMap((services: Map<PostableService, string>) => {
                  const service = Array.from(services.keys())[0];
                  return combineLatest([
                    this.customizationValidatorService.failingValidators$(customization),
                    of({
                      customization: customization,
                      service: service,
                      serviceIconUrl: serviceIconPath(service.serviceType),
                    } as CustomizationContent),
                  ]);
                }),
                map((result: [ValidatorFailure[], CustomizationContent]) => {
                  const [failingValidators, content] = result;
                  if (failingValidators && failingValidators.length) {
                    content.validatorFailure = failingValidators[0];
                  } else {
                    content.validatorFailure = null;
                  }
                  return content;
                }),
              ),
            ]);
          }),
        );
      }),
      map((contents) => contents.map((content) => content[0])),
    );

    this.selectCustomization(0);

    this.igSocialService$ = combineLatest([
      this.composerStore.availablePostableServices$,
      this.instagramSelected$,
    ]).pipe(
      map(([availableServices, instagramSelected]) => {
        let igSsid = '';
        if (instagramSelected) {
          igSsid = availableServices?.find((svc) => svc.serviceType === 'IG_USER')?.ssid;
          return igSsid;
        }
        return igSsid;
      }),
    );

    this.igPostIdForEditing$ = combineLatest([this.instagramSelected$, this.composerStore.customizations$]).pipe(
      map(([, customizations]) => {
        let igId = '';
        for (const c of customizations) {
          const svc = c.services$$.getValue();
          svc.forEach((value, key) => {
            if (key.serviceType === 'IG_USER') {
              igId = value;
            }
          });
        }
        return igId;
      }),
    );

    this.isGMBSelected$ = this.customizationContent$.pipe(
      map((customizations) => {
        return !!customizations.find((customization) => {
          return (
            customization.service.serviceType === SocialService.GMB ||
            customization.service.serviceType === SocialService.GOOGLE_MY_BUSINESS
          );
        });
      }),
    );

    this.instagramSelected$ = this.composerStore.instagramSelected$.pipe(startWith(false));

    this.gmbValidatorFailure$ = this.composerStore.customizations$.pipe(
      skipWhile((customizations) => !customizations),
      switchMap((customizations) => {
        return combineLatest(
          customizations.map((customization) => {
            return this.customizationValidatorService.failingValidators$(customization);
          }),
        );
      }),
      map((failuresList: ValidatorFailure[][]) => {
        const gmbFailures = [];
        failuresList.forEach((failures) => {
          return failures.forEach((failure) => {
            if (failure.class === 'gmb-option-error') {
              gmbFailures.push(failure);
            }
          });
        });
        return gmbFailures.length > 0 ? gmbFailures[0] : null;
      }),
    );

    this.filteredOptions$ = this.mentionsTerm$$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      withLatestFrom(
        combineLatest([
          this.configService.config$.pipe(take(1)),
          this.composerStore.selectedPostableServices$,
          this.customizationContent$,
          this.composerStore.selectedCustomization$.pipe(take(1)),
        ]),
      ),
      switchMap(([term, [config, services, customizations, selectedCustIdx]]) => {
        if (!term || selectedCustIdx <= -1) {
          return of([]);
        }

        const selectedCustomization = customizations[selectedCustIdx];
        const selectedService = selectedCustomization.service.serviceType;
        // TODO : additional check for test app, remove agid check after test app approval
        const performSearch: boolean =
          selectedService === SocialService.FACEBOOK || selectedService === SocialService.TWITTER;

        if (!performSearch) {
          return of([]);
        }

        const agid = config.account_group_id;
        const spid = config.account.spid;
        const service = services.find((item) => item.serviceType === selectedService);
        const ssid = service ? service.ssid : '';

        if (!ssid) {
          return of([]);
        }

        switch (selectedService) {
          case SocialService.TWITTER:
            return this.repcoreService.getTwitterSocialMentions(term, agid, spid, ssid);
          case SocialService.FACEBOOK:
            return this.repcoreService.getFacebookSocialMentions(term, agid, spid, ssid, config.partner_id);
          default:
            return of([]);
        }
      }),
    );

    this.subscriptions.push(
      this.configService.proFlag$.subscribe((isPro) => {
        if (isPro) {
          // Change maxItems value based on selected network
          const maxItems = 25;

          this.hashtagConfig = {
            useNestedConfig: true,
            mentions: [
              {
                maxItems,
                triggerChar: '@',
                labelKey: 'screenName',
                emptyItemsOnClose: true,
                disableSort: true,
                disableSearch: true,
                useMention: true,
                returnTrigger: true,
              },
            ],
          };
        }
      }),
    );
  }

  updateInitialLength(): void {
    this.composerStore.customizations$$.subscribe((newCustomization) => {
      newCustomization.forEach((customization) => {
        combineLatest([customization.serviceTypes$, customization.postText$])
          .pipe(
            map(([serviceType, postText]) => {
              if (serviceType.includes(SocialService.LINKEDIN)) {
                this.linkedInLength$$.next(postText.length);
                this.linkedInValid$$.next(
                  postText.length <= SERVICE_MAX_CHAR_COUNT.LINKEDIN_CHARS_LIMIT && !!postText?.length,
                );
                this.updateHashTags(postText, 'LINKEDIN');
              }
              if (serviceType.includes(SocialService.LINKEDIN_COMPANY)) {
                this.linkedInLength$$.next(postText.length);
                this.linkedInValid$$.next(
                  postText.length <= SERVICE_MAX_CHAR_COUNT.LINKEDIN_CHARS_LIMIT && !!postText?.length,
                );
                this.updateHashTags(postText, 'LINKEDIN');
              }
              if (serviceType.includes(SocialService.INSTAGRAM)) {
                this.instagramLength$$.next(postText.length);
                this.instagramValid$$.next(
                  postText.length <= SERVICE_MAX_CHAR_COUNT.INSTAGRAM_CHARS_LIMIT && !!postText?.length,
                );
                this.updateHashTags(postText, 'INSTAGRAM');
              }
              if (serviceType.includes(SocialService.TWITTER)) {
                const parsedTweet = twitterText.parseTweet(postText);
                this.twitterLength$$.next(parsedTweet.weightedLength);
                this.tweetValid$$.next(
                  parsedTweet.weightedLength <= SERVICE_MAX_CHAR_COUNT.TWITTER_CHAR_LIMIT &&
                    !!parsedTweet?.weightedLength,
                );
                this.updateHashTags(postText, 'TWITTER');
              }
              if (serviceType.includes(SocialService.GMB)) {
                this.gmbLength$$.next(postText.length);
                this.gmbValid$$.next(postText.length <= SERVICE_MAX_CHAR_COUNT.GMB_CHARS_LIMIT && !!postText?.length);
                this.updateHashTags(postText, 'GMB');
              }
              if (serviceType.includes(SocialService.TIKTOK_ACCOUNT)) {
                this.tiktokLength$$.next(postText.length);
                this.tiktokValid$$.next(
                  postText.length <= SERVICE_MAX_CHAR_COUNT.TIKTOK_CHAR_LIMIT && !!postText?.length,
                );
              }
            }),
          )
          .subscribe();
      });
    });
  }

  initializeNetworkLength(): void {
    this.instagramSelected$ = this.composerStore.instagramSelected$.pipe(startWith(false));
    this.twitterSelected$ = this.composerStore.twitterSelected$.pipe(startWith(false));
    this.gmbSelected$ = this.composerStore.gmbSelected$.pipe(startWith(false));
    this.linkedinSelected$ = this.composerStore.linkedinSelected$.pipe(startWith(false));
    this.youtubeSelected$ = this.composerStore.youtubeSelected$.pipe(startWith(false));
    this.tiktokSelected$ = this.composerStore.tiktokSelected$.pipe(startWith(false));
    this.facebookSelected$ = this.composerStore.facebookSelected$.pipe(startWith(false));
  }

  resetLengthValidators(): void {
    this.linkedInValid$$.next(false);
    this.tiktokValid$$.next(false);
    this.instagramValid$$.next(false);
    this.gmbValid$$.next(false);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.resetLengthValidators();
  }

  selectCustomization(index: number): void {
    this.composerStore.selectCustomization(index);
  }

  clearSelectedCustomization(): void {
    this.composerStore.clearSelectedCustomization();
  }

  updateHashTags(text: string, network: string) {
    const hashtags = text.match(HASHTAG_REGEX);
    const hashtagCount = hashtags ? hashtags.length : 0;
    switch (network) {
      case 'LINKEDIN':
        this.hashtagLinkedIn$$.next(hashtagCount);
        break;
      case 'INSTAGRAM':
        this.hashtagInstagram$$.next(hashtagCount);
        break;
      case 'GMB':
        this.hashtagGMB$$.next(hashtagCount);
        break;
      case 'TWITTER':
        this.hashtagTwitter$$.next(hashtagCount);
        break;
      default:
        this.hashtagCount$$.next(hashtagCount);
    }
  }

  updatePostText(text: string): void {
    const liTextValid = text.length <= SERVICE_MAX_CHAR_COUNT.LINKEDIN_CHARS_LIMIT && !!text?.length;
    const gmbTextValid = text.length <= SERVICE_MAX_CHAR_COUNT.GMB_CHARS_LIMIT && !!text?.length;
    const igTextValid = text.length <= SERVICE_MAX_CHAR_COUNT.INSTAGRAM_CHARS_LIMIT && !!text?.length;
    const twTextValid = text.length <= SERVICE_MAX_CHAR_COUNT.TWITTER_CHAR_LIMIT && !!text?.length;
    const tiktokTextValid = text.length <= SERVICE_MAX_CHAR_COUNT.TIKTOK_CHAR_LIMIT && !!text?.length;
    const updatePostLength = this.composerStore.currSelectedCustomization$
      .pipe(
        map((customization) => customization.serviceTypes$),
        switchMap((services) => services),
        take(1),
        tap((services) => {
          this.updateTextLength(services, text, igTextValid, liTextValid, twTextValid, gmbTextValid, tiktokTextValid);
        }),
      )
      .subscribe();
    this.subscriptions.push(updatePostLength);
    this.composerStore.updatePostText(text);
  }

  private updateTextLength(
    services: SocialService[],
    text: string,
    igTextValid: boolean,
    liTextValid: boolean,
    twTextValid: boolean,
    gmbTextValid: boolean,
    tiktokTextValid: boolean,
  ) {
    const serviceMap = new Set(services);
    if (serviceMap.has(SocialService.INSTAGRAM)) {
      this.instagramLength$$.next(text.length);
      this.instagramValid$$.next(igTextValid);
      this.updateHashTags(text, 'INSTAGRAM');
    }
    if (serviceMap.has(SocialService.LINKEDIN)) {
      this.linkedInLength$$.next(text.length);
      this.linkedInValid$$.next(liTextValid);
      this.updateHashTags(text, 'LINKEDIN');
    }
    if (serviceMap.has(SocialService.TWITTER)) {
      this.twitterLength$$.next(text.length);
      this.tweetValid$$.next(twTextValid);
      this.updateHashTags(text, 'TWITTER');
    }
    if (serviceMap.has(SocialService.LINKEDIN_COMPANY)) {
      this.linkedInLength$$.next(text.length);
      this.linkedInValid$$.next(liTextValid);
      this.updateHashTags(text, 'LINKEDIN');
    }
    if (serviceMap.has(SocialService.GMB)) {
      this.gmbLength$$.next(text.length);
      this.gmbValid$$.next(gmbTextValid);
      this.updateHashTags(text, 'GMB');
    }
    if (serviceMap.has(SocialService.TIKTOK_ACCOUNT)) {
      this.tiktokLength$$.next(text.length);
      this.tiktokValid$$.next(tiktokTextValid);
    }
  }

  toggleVideo(event: Event, index: number): any {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    const videos = this.videos.toArray();
    if (!videos.length) {
      return;
    }
    const video = videos[index].nativeElement;
    return video.paused ? video.play() : video.pause();
  }

  uploadMedia(isCrop: boolean, event: Event): any {
    const data = {
      isCrop: isCrop,
      data: event,
    };
    this.uploadMediaEvent.emit(data);
  }

  searchHashtags(term: string): void {
    this.mentionsTerm$$.next(term);
  }

  mentionItemSelected(mention: SocialMentionInterface, customization: CustomizationContent): void {
    const type = customization ? customization.service.serviceType : null;

    if (type && type === SocialService.FACEBOOK && mention) {
      this.composerStore.addToFbMentionMap(mention.screenName, mention.id);
    }
  }

  openAISidePanel(): void {
    this.productAnalyticsService.trackEvent('ComposerWriteWithAI', 'user', 'click', 0, { composerMode: 'customize' });
    this.postTextActionsService.openSuggestions();
  }

  aiMenuOptionSelected(event: UpdateRequest) {
    this.generatingContent$$.next(true);
    this.composerStore
      .handleSuggestMenu(event.existingValue, event.option)
      .pipe(take(1))
      .subscribe(() => this.generatingContent$$.next(false));
  }

  aiMenuSuggestcontent(event: UpdateRequest) {
    this.generatingContent$$.next(true);
    this.productAnalyticsService.trackEvent('AIContentButtonSuggestContent', 'user', 'click');
    this.composerStore
      .suggestContentPrompt(event.existingValue)
      .pipe(take(1))
      .subscribe(() => this.generatingContent$$.next(false));
  }

  aiChatbotSelected(text: Observable<string>): void {
    text.pipe(take(1)).subscribe((text) => {
      this.productAnalyticsService.trackEvent(POSTHOG_KEYS.COMPOSER_AI_CHATBOT, 'user', 'click');
      this.aiChatbotIsOpen$$.next(true);
      this.dialog
        .open(SmChatbotComponent, {
          data: {
            topic: text,
          },
          autoFocus: false,
          width: '624px',
          height: 'auto',
          panelClass: 'sm-chatbot-dialog',
          disableClose: true,
          enterAnimationDuration: 0,
        } as MatDialogConfig)
        .afterClosed()
        .subscribe(() => {
          this.composerStore.failedServices$$.next([]);
        });
    });
  }

  get instagramLength$(): Observable<number> {
    return this.instagramLength$$.asObservable();
  }

  get twitterLength$(): Observable<number> {
    return this.twitterLength$$.asObservable();
  }

  get linkedInLength$(): Observable<number> {
    return this.linkedInLength$$.asObservable();
  }

  get gmbLength$(): Observable<number> {
    return this.gmbLength$$.asObservable();
  }

  get tiktokLength$(): Observable<number> {
    return this.tiktokLength$$.asObservable();
  }

  get instagramValid$(): Observable<boolean> {
    return this.instagramValid$$.asObservable();
  }

  get tiktokValid$(): Observable<boolean> {
    return this.tiktokValid$$.asObservable();
  }

  get linkedInValid$(): Observable<boolean> {
    return this.linkedInValid$$.asObservable();
  }

  get gmbValid$(): Observable<boolean> {
    return this.gmbValid$$.asObservable();
  }

  get tweetValid$(): Observable<boolean> {
    return this.tweetValid$$.asObservable();
  }

  get hashtagCount$(): Observable<number> {
    return this.hashtagCount$$.asObservable();
  }

  get hashtagCountLinkedIn$(): Observable<number> {
    return this.hashtagLinkedIn$$.asObservable();
  }

  get hashtagCountInstagram$(): Observable<number> {
    return this.hashtagInstagram$$.asObservable();
  }

  get hashtagCountTwitter$(): Observable<number> {
    return this.hashtagTwitter$$.asObservable();
  }

  get hashtagCountGMB$(): Observable<number> {
    return this.hashtagGMB$$.asObservable();
  }

  getIsReels(): boolean {
    return this.composerStore.isReels;
  }

  toggleReels(): void {
    this.composerStore.updateIsReels();
    const sub = this.composerStore.handleMediaForReels().subscribe();
    this.subscriptions.push(sub);
  }

  getTooltipText(): string {
    return this.translateService.instant('POSTS.POST_BANNERS.INSTAGRAM_REELS_INFO');
  }

  get previewLink$(): Observable<string> {
    return this.composerStore.rootCustomization.linkUrl$;
  }

  get hashtagSearchTerm$(): Observable<string> {
    return this.hashtagSearchTerm$$.asObservable();
  }

  get selectedSocialTab$(): Observable<string> {
    return this.selectedSocialTab$$.asObservable();
  }

  get triggerChar$(): Observable<string> {
    return this.triggerChar$$.asObservable();
  }

  get fbMentionsList$(): Observable<SocialMentionInterface[]> {
    return this.fbMentionsList$$.asObservable();
  }

  get twitterMentionsList$(): Observable<SocialMentionInterface[]> {
    return this.twitterMentionsList$$.asObservable();
  }

  get showMentionTemplate$(): Observable<boolean> {
    return this.showMentionTemplate$$.asObservable();
  }

  clearPreviewLink(): void {
    this.composerStore.clearPreviewLink();
  }

  isCtaMentionPrompt(): boolean {
    const prompt = this.mentionPromptOption$$.getValue();
    return prompt === this.mentionPromptOptions.CTA_TWITTER || prompt === this.mentionPromptOptions.CTA_FACEBOOK;
  }

  upgradeLinkClicked(): void {
    this.upgradeCTADialogService
      .openUpgradeModel('account')
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.upgradeCTADialogService.businessCenterUpgrade('composer_mentions');
        }
      });
  }

  closeMentionsDropDown(): void {
    if (!this.isCtaMentionPrompt()) {
      this.showMentionTemplate$$.next(false);
      this.mentionPromptOption$$.next(-1);
    }
  }

  clickSocialMentionsTab(e: string): void {
    if (this.isCtaMentionPrompt()) {
      this.mentionPromptOption$$.next(
        e === SocialService.TWITTER ? MentionPromptOptions.CTA_TWITTER : MentionPromptOptions.CTA_FACEBOOK,
      );
    }
    this.selectedSocialTab$$.next(e);
  }

  parseTweetLength(postText?: string): number {
    const parsedTweet = twitterText.parseTweet(postText);
    return parsedTweet.weightedLength;
  }

  shortenLinks(): void {
    this.composerStore.shortenLinks();
  }
  customizeByAccountToggle() {
    this.showResetCustomizationPrompt();
  }

  getBtnLabel(): string {
    return this.translateService.instant('COMPOSER.AI_CHAT_BOT.BUTTON.LABEL');
  }

  private showResetCustomizationPrompt() {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: 'COMPOSER.CUSTOMIZE_MODAL.TITLE',
        message: 'COMPOSER.CUSTOMIZE_MODAL.MESSAGE',
        hideCancel: false,
        confirmButtonText: 'COMPOSER.CUSTOMIZE_MODAL.GO_BACK',
        cancelButtonText: 'COMPOSER.CUSTOMIZE_MODAL.KEEP_EDITING',
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((response: boolean) => {
        if (response) {
          this.composerStore.stopCustomizingByAccount();
        }
      });
  }
}

export class CustomizationContent {
  customization: Customization;
  service: PostableService;
  serviceIconUrl: string;
  validatorFailure?: ValidatorFailure;
}
