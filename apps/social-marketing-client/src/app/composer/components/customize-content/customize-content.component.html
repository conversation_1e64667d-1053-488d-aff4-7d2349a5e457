<div class="customize-content">
  <div class="post-to-title p-b-06">
    <div class="composer-title">
      <mat-icon>short_text</mat-icon>
      {{ 'COMPOSER.CONTENT' | translate }}
    </div>
    <div class="customize-link" (click)="customizeByAccountToggle()">Discard custom edits</div>
  </div>
  <mat-tab-group (selectedTabChange)="selectCustomization($event.index)">
    <mat-tab *ngFor="let content of customizationContent$ | async; let i = index">
      <ng-template mat-tab-label>
        <app-avatar [service]="content.service" [warning]="!!content.validatorFailure"></app-avatar>
      </ng-template>
      <ng-template matTabContent class="customization customize-by-accounts">
        <div class="compose-post-section">
          <div class="post-to-title"></div>
          <div>
            <!--TEXT CONTAINER-->
            <app-post-text-actions></app-post-text-actions>
            <mat-progress-bar [mode]="'indeterminate'" *ngIf="previewLinkLoading$ | async"></mat-progress-bar>
            <div class="preview-link" *ngIf="previewLink$ | async">
              <span>Preview link</span>
              <mat-form-field class="preview-link-field">
                <input matInput disabled [value]="previewLink$ | async" />
                <i
                  class="material-icons mdc-text-field__icon"
                  tabindex="0"
                  role="button"
                  *ngIf="(requirePreviewLink$ | async) === false"
                  (click)="clearPreviewLink()"
                >
                  close
                </i>
              </mat-form-field>
            </div>

            <app-link-history-composer-warning
              [previewLinkHistory]="previewLinkHistory$ | async"
            ></app-link-history-composer-warning>

            <ng-template #mentionPrompt>
              <div class="mention-upgrade-container" [class.mention-upgrade-image]="isCtaMentionPrompt()">
                <div class="mention-upgrade-bg">
                  <div>
                    <span>&#64;</span>
                  </div>
                  <div>
                    <span>
                      {{ mentionPromptMessages[mentionPromptOption$$ | async] | translate }}
                    </span>
                  </div>
                  <div *ngIf="isCtaMentionPrompt()">
                    <a (mousedown)="upgradeLinkClicked(); $event.preventDefault()">
                      {{ 'UPGRADE_CTA.SOCIAL_MENTIONS.ACTION_TEXT' | translate }}
                    </a>
                  </div>
                  <div *ngIf="(mentionPromptOption$$ | async) === mentionPromptOptions.SELECT_SERVICE">
                    <span>
                      {{ 'COMPOSER.SOCIAL_MENTIONS.SELECT_SERVICE' | translate }}
                    </span>
                  </div>
                </div>
              </div>
            </ng-template>

            <ng-template #mentionTemplate let-item="item">
              <div class="mention-option-selector-row">
                <div class="mention-option-selector-avatar">
                  <img
                    appVaImage
                    class="profile-image"
                    [src]="item.profileImageUrl || item.defaultProfileImageUrl | ssl"
                    [vaImageDefault]="item.defaultProfileImageUrl | ssl"
                  />
                  <img class="social-icon" [src]="item.serviceIcon | ssl" />
                </div>
                <div class="mention-option-selector-title">
                  <mat-card-title class="title">
                    {{ item.name }}
                  </mat-card-title>
                  <mat-card-subtitle class="subtitle">
                    {{ item.formattedScreenName }}
                  </mat-card-subtitle>
                </div>
              </div>
            </ng-template>
          </div>

          <span #textBox class="content-text-area-container">
            <mat-form-field appearance="outline" class="textarea">
              <textarea
                matInput
                [value]="content.customization.postText$ | async"
                (input)="updatePostText($event.target.value)"
                [mention]="filteredOptions$ | async"
                [mentionConfig]="hashtagConfig"
                (searchTerm)="searchHashtags($event)"
                [mentionListTemplate]="mentionTemplate"
                (itemSelected)="mentionItemSelected($event, content)"
                [placeholder]="customHint$ | async"
              ></textarea>
            </mat-form-field>
            <ng-container
              *ngIf="{
                proFlag: configService.proFlag$ | async,
                isLoading: generatingContent$$ | async,
                isProBrand: composerStore.isProBrand$ | async,
                showPopover: featuresService.instagramMultilocationEnabled$ | async,
                textValue: content.customization.postText$ | async,
              } as data"
            >
              <app-ai-menu-button
                [isLoading]="generatingContent$$ | async"
                [textValue]="content.customization.postText$ | async"
                (buttonClick)="openAISidePanel()"
                (optionSelected)="aiMenuOptionSelected($event)"
                *ngIf="(featuresService.enableAiChatbot$ | async) === false && (this.configService.proFlag$ | async)"
              ></app-ai-menu-button>
              <div class="inline-char-hashtag-counter">
                <app-ai-chatbot-button
                  *ngIf="
                    (((configService.proFlag$ | async) === true || (configService.noUpgradePath$ | async) === false) &&
                      (featuresService.enableAiChatbot$ | async)) ||
                    (featuresService.enableAiChatbotML$ | async)
                  "
                  [hidden]="(content.customization.postText$ | async)?.length > 0"
                  [isLoading]="generatingContent$$ | async"
                  [isDisabled]="(this.configService.proFlag$ | async) === false"
                  [textValue]="content.customization.postText$ | async"
                  [containerClassName]="'customize-ai-action'"
                  [placeholderLength]="(customHint$ | async)?.length"
                  [label]="getBtnLabel()"
                  (chatbotTriggered)="aiChatbotSelected(content.customization.postText$)"
                >
                </app-ai-chatbot-button>
                <div class="section-title">
                  <div
                    class="post-length"
                    *ngIf="twitterSelected$ | async"
                    matTooltip="{{ 'COMPOSER.TWEET_LENGTH' | translate }}"
                    [ngClass]="{
                      invalid: (tweetValid$ | async) === false,
                    }"
                    matTooltipPosition="above"
                  >
                    {{ twitterLength$ | async }} / {{ 'COMPOSER.POST_VALIDATION.TWITTER_CHARS_LIMIT' | translate }}
                  </div>
                  <div
                    class="post-length"
                    *ngIf="twitterSelected$ | async"
                    [ngClass]="{
                      invalid: (igHashtagCountValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.HASHTAG_COUNT' | translate }}"
                    matTooltipPosition="above"
                  >
                    # {{ hashtagCountTwitter$ | async }}
                  </div>

                  <div
                    class="post-length"
                    *ngIf="(linkedinSelected$ | async) && (twitterSelected$ | async) === false"
                    [ngClass]="{
                      invalid: (linkedInValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'LinkedIn' } }}"
                    matTooltipPosition="above"
                  >
                    {{ linkedInLength$ | async }} / {{ 'COMPOSER.POST_VALIDATION.LINKEDIN_CHARS_LIMIT' | translate }}
                  </div>
                  <div
                    class="post-length"
                    *ngIf="linkedinSelected$ | async"
                    [ngClass]="{
                      invalid: (igHashtagCountValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.HASHTAG_COUNT' | translate }}"
                    matTooltipPosition="above"
                  >
                    # {{ hashtagCountLinkedIn$ | async }}
                  </div>

                  <div
                    class="post-length"
                    *ngIf="
                      (gmbSelected$ | async) &&
                      (twitterSelected$ | async) === false &&
                      (linkedinSelected$ | async) === false
                    "
                    [ngClass]="{
                      invalid: (gmbValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'Google Business Profile' } }}"
                    matTooltipPosition="above"
                  >
                    {{ gmbLength$ | async }} / {{ 'COMPOSER.POST_VALIDATION.GMB_CHARS_LIMIT' | translate }}
                  </div>

                  <div
                    class="post-length"
                    *ngIf="gmbSelected$ | async"
                    [ngClass]="{
                      invalid: (igHashtagCountValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.HASHTAG_COUNT' | translate }}"
                    matTooltipPosition="above"
                  >
                    # {{ hashtagCountGMB$ | async }}
                  </div>

                  <div
                    class="post-length"
                    *ngIf="
                      (instagramSelected$ | async) &&
                      (twitterSelected$ | async) === false &&
                      (linkedinSelected$ | async) === false &&
                      (gmbSelected$ | async) === false
                    "
                    [ngClass]="{
                      invalid: (instagramValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'Instagram' } }}"
                    matTooltipPosition="above"
                  >
                    {{ instagramLength$ | async }} / {{ 'COMPOSER.POST_VALIDATION.INSTAGRAM_CHARS_LIMIT' | translate }}
                  </div>
                  <div
                    class="post-length"
                    *ngIf="instagramSelected$ | async"
                    [ngClass]="{
                      invalid: (igHashtagCountValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.HASHTAG_COUNT' | translate }}"
                    matTooltipPosition="above"
                  >
                    # {{ hashtagCountInstagram$ | async }}
                  </div>

                  <div
                    class="post-length"
                    *ngIf="
                      (tiktokSelected$ | async) &&
                      (instagramSelected$ | async) === false &&
                      (twitterSelected$ | async) === false &&
                      (gmbSelected$ | async) === false
                    "
                    [ngClass]="{
                      invalid: (tiktokValid$ | async) === false,
                    }"
                    matTooltip="{{ 'COMPOSER.POST_LENGTH' | translate: { serviceType: 'TikTok' } }}"
                    matTooltipPosition="above"
                  >
                    {{ tiktokLength$ | async }} / {{ 'COMPOSER.POST_VALIDATION.TIKTOK_CHARS_LIMIT' | translate }}
                  </div>
                </div>
              </div>
            </ng-container>
          </span>
          <div class="customize-uplaoder">
            <app-media-uploader
              [service]="content?.service?.serviceType"
              (uploadClicked)="uploadMedia(false, $event)"
              (cropImageButtonClicked)="uploadMedia(true, $event)"
            ></app-media-uploader>
          </div>
          <app-disable-message
            *ngIf="!!content?.validatorFailure"
            class="disable-message-bar"
            [postValidation]="content.validatorFailure"
            [highlightError]="true"
          ></app-disable-message>
        </div>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>
<app-disable-message
  [postValidation]="gmbValidatorFailure$ | async"
  [highlightError]="highlightError"
></app-disable-message>
<div class="post-mode-container p-y-1">
  <app-post-type-selector
    *ngIf="(featuresService.composerWithHideDraft$ | async) && (composerStore.isBundleAIPost$ | async) === false"
  ></app-post-type-selector>
</div>
