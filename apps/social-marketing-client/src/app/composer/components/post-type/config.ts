import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

export interface PostMode {
  name: string;
  value: PostModeEnum;
  subtitle: string;
  chipColor: chipColor;
}

export type chipColor = 'default' | 'blue' | 'green' | 'yellow';

export enum PostModeEnum {
  HIDDEN_DRAFT = 'hidden_draft',
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  POST_NOW = 'post_now',
}

@Injectable()
export class PostModeType implements OnDestroy {
  public HIDDEN_POST_MODE: PostMode[];
  public NO_DRAFT_POST_MODE: PostMode[];
  public NO_HIDDEN_DRAFT_MODE: PostMode[];
  private langChangeSubscription: Subscription;

  constructor(private translateService: TranslateService) {
    this.intializeHiddenPostMode();

    this.langChangeSubscription = this.translateService.onLangChange.subscribe(() => {
      this.intializeHiddenPostMode();
    });

    this.NO_DRAFT_POST_MODE = this.HIDDEN_POST_MODE.filter(
      (postMode) => postMode.value === PostModeEnum.SCHEDULED || postMode.value === PostModeEnum.POST_NOW,
    );

    this.NO_HIDDEN_DRAFT_MODE = this.HIDDEN_POST_MODE.filter(
      (postMode) => postMode.value !== PostModeEnum.HIDDEN_DRAFT,
    );
  }
  ngOnDestroy(): void {
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }

  private intializeHiddenPostMode() {
    this.HIDDEN_POST_MODE = [
      {
        name: this.translateService.instant('COMPOSER.HIDDEN_DRAFT'),
        value: PostModeEnum.HIDDEN_DRAFT,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.HIDDEN_DRAFT'),
        chipColor: 'default',
      },
      {
        name: this.translateService.instant('COMPOSER.DRAFT'),
        value: PostModeEnum.DRAFT,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.DRAFT'),
        chipColor: 'yellow',
      },
      {
        name: this.translateService.instant('COMPOSER.SCHEDULED'),
        value: PostModeEnum.SCHEDULED,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.SCHEDULED'),
        chipColor: 'blue',
      },
      {
        name: this.translateService.instant('COMPOSER.POST_NOW'),
        value: PostModeEnum.POST_NOW,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.POST_NOW'),
        chipColor: 'green',
      },
    ];
  }
}
