import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { NavigationStart, Router } from '@angular/router';
import { AtlasLanguageService } from '@galaxy/atlas/core';
import { EnvironmentService, SessionService } from '@galaxy/core';
import { AppPartnerService, AppSettings } from '@galaxy/marketplace-apps';
import { FeatureFlagService as PartnerFeatureFlagService, WhitelabelService } from '@galaxy/partner';
import { TranslateService } from '@ngx-translate/core';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { LanguageChoice, VaProductNavComponent, VaProductNavItem } from '@vendasta/uikit';
import moment from 'moment';
import { BehaviorSubject, combineLatest, Observable, of, Subject, Subscription } from 'rxjs';
import { catchError, filter, map, switchMap, take, takeUntil } from 'rxjs/operators';
import { ComposerSettings } from '../../composer/interfaces';
import { ConfigService, CookieService, SMConfig, SmNavService } from '../../core';
import { AnalyticsService } from '../../core/analytics';
import { ComposerSettingsService } from '../../core/composer-settings/composer-settings.service';
import { NavbarConfigService } from '../../core/config/navbar-config.service';
import { SMFeaturesService } from '../../core/features.service';
import { CarouselDialogComponent } from '../../shared/carousel-dialog/carousel-dialog.component';
import { ConciergeTaskService } from '../../shared/concierge-task/concierge.task.service';
import { GetProService } from '../../shared/marketing-services/get-pro.service';
import { UpgradeCTADialogService } from '../../shared/upgrade-cta-dialog/upgrade-cta-dialog.service';

declare let deployment: string; // Stamped down by vStatic

export const SM_DEFAULT_LOGO = 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/SM.png';

@Component({
  selector: 'app-authed-page',
  templateUrl: './authenticated-page.component.html',
  styleUrls: ['./authenticated-page.component.scss'],
  providers: [PartnerFeatureFlagService],
  standalone: false,
})
export class AuthenticatedPageComponent implements OnInit, OnDestroy {
  accountGroupId: string;
  noUpgradePath$: Observable<boolean>;
  showTopProductNavBar$: Observable<boolean>;
  showAnyTopNavBar$: Observable<boolean>;
  ssoNavBarHTML$: Observable<string>;
  poweredByName$: Observable<string>;
  businessNavFeatureLoading$$ = new BehaviorSubject<boolean>(true);
  clickableBioNewChipText: string;

  @ViewChild(VaProductNavComponent) navProduct: VaProductNavComponent;
  private vaproductFilterEvent: Subscription;

  navItems$: Observable<VaProductNavItem[]>;
  config$: Observable<SMConfig>;
  supportedLangs: LanguageChoice[] = [
    { display: 'Čeština', locale: 'cs' },
    { display: 'Deutsch', locale: 'de' },
    { display: 'English', locale: 'en' },
    { display: 'Español', locale: 'es-419' },
    { display: 'Français', locale: 'fr-ca' },
    { display: 'Français canadien', locale: 'fr-fr' },
    { display: 'Italiano', locale: 'it' },
    { display: 'Nederlands', locale: 'nl' },
  ];

  private _destroyed$$: Subject<void> = new Subject<void>();

  cobrandingLogoUrl$: Observable<string>;
  logoUrl$: Observable<string>;
  brandingLogo$: Observable<string>;

  @HostListener('window:scroll', []) scrollHandler(): void {
    const navBarHeight = 40;
    if (window.scrollY < navBarHeight) {
      document.getElementById('top-navbar').style.marginTop = `-${window.scrollY}px`;
    } else {
      document.getElementById('top-navbar').style.marginTop = `-${navBarHeight}px`;
    }
  }

  constructor(
    public configService: ConfigService,
    private router: Router,
    private badges: SmNavService,
    private cookieService: CookieService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private taskService: ConciergeTaskService,
    private composerSettingsService: ComposerSettingsService,
    private productAnalyticsService: ProductAnalyticsService,
    private environmentService: EnvironmentService,
    private analyticsService: AnalyticsService,
    private getProService: GetProService,
    private upgradeCTADialogService: UpgradeCTADialogService,
    private sessionService: SessionService,
    private atlasLanguageService: AtlasLanguageService,
    private featuresService: SMFeaturesService,
    private readonly whiteLabelService: WhitelabelService,
    private navbarConfigService: NavbarConfigService,
    private appPartnerService: AppPartnerService,
  ) {}

  ngOnInit(): void {
    this.vaproductFilterEvent = this.configService.vaFilterButtonEvent$.subscribe(() => {
      this.handleVaProductNavFilterToogle();
    });

    this.accountGroupId = this.configService.accountGroupId;
    this.config$ = this.configService.config$;

    this.configService.proFlag$.pipe(takeUntil(this._destroyed$$)).subscribe((proFlag) => {
      if (proFlag) {
        this.badges.getBadgeCounts();
      }
    });

    this.logoUrl$ = this.config$.pipe(
      switchMap((config) => {
        // default market doesn't exist for some reason on a lot of pids, use fallback if necessary, and return null if even that doesn't
        // work so we can at least give a default logo instead of the observable failing.
        return this.appPartnerService.getAppSettings(config.app_id, config.partner_id, config.market_id).pipe(
          catchError(() => {
            return this.appPartnerService
              .getAppSettings(config.app_id, config.partner_id, 'all-markets-default-fallback')
              .pipe(catchError(() => of(null)));
          }),
        );
      }),
      map((settings: AppSettings) => {
        return !!settings && !!settings.branding && !!settings.branding.iconUrl && settings?.branding?.enabled
          ? settings.branding.iconUrl
          : SM_DEFAULT_LOGO;
      }),
    );

    this.brandingLogo$ = this.configService.brandingLogo$;

    this.config$.pipe(take(1)).subscribe((config: SMConfig) => {
      if (typeof deployment !== 'undefined') {
        this.productAnalyticsService.initialize({
          environment: this.environmentService.getEnvironment(),
          projectUUID: '4f5db091-6950-43dd-a2ba-7df808ef0991',
          postHogID: 'c8hiFS2OUqB2kzBcuCeo7djLeVc53fXIQGc7XPUeA0k',
          projectName: 'social-marketing-client',
          partner: {
            pid: config.partner_id,
            pidChanges$: this.configService.config$.pipe(map((c) => c.partner_id)),
          },
          businessID: config.account_group_id,
        });
      }
    });

    this.cobrandingLogoUrl$ = this.config$.pipe(
      switchMap((c) => this.whiteLabelService.getConfiguration(c.partner_id, c.market_id)),
      map((c) => {
        if (c.enabledFeatures && !c.enabledFeatures.includes('co-branding-disabled')) {
          return 'https://vstatic-prod.apigateway.co/social-marketing-client/assets/cobranding-logo.png';
        }
        return '';
      }),
    );

    this.showTopProductNavBar$ = this.configService.showTopProductNavBar$;
    this.showAnyTopNavBar$ = this.navbarConfigService.showingAnyTopBar$;
    this.ssoNavBarHTML$ = this.configService.ssoNavBarHTML$;
    this.poweredByName$ = this.configService.poweredByName$;
    this.noUpgradePath$ = this.configService.config$.pipe(
      switchMap((config) =>
        this.appPartnerService.getAppSettings(config.app_id, config.partner_id, config.market_id).pipe(
          catchError((error) => {
            console.error('Error fetching app settings:', error);
            return of(null);
          }),
        ),
      ),
      // The settings are not guaranteed to exist, so we need to check for that (default to true if it doesn't exist)
      map((settings) => settings?.editionChange?.hideUpgradeCta ?? false),
    );

    this.navItems$ = combineLatest([
      this.configService.accountGroupId$,
      this.featuresService.postTemplatesEnabled$,
      this.noUpgradePath$,
      this.configService.proFlag$,
      this.featuresService.displayToolsOption$,
      this.featuresService.displayNewPostFlow$,
    ]).pipe(
      map(
        ([accountGroupId, postTemplatesPageEnabled, noUpgradePath, isPro, displayToolsOption, displayPostsFlow]: [
          string,
          boolean,
          boolean,
          boolean,
          boolean,
          boolean,
        ]) => {
          return this.buildNavItems(
            accountGroupId,
            postTemplatesPageEnabled,
            isPro ? false : noUpgradePath,
            isPro,
            displayToolsOption,
            displayPostsFlow,
          );
        },
      ),
    );
    this.translateService.setDefaultLang('en');
    this.translateService.use('en');
    this.atlasLanguageService.language$.pipe(takeUntil(this._destroyed$$)).subscribe((language) => {
      this.translateService.use(language);
      moment.locale(language);
      if (localStorage) {
        localStorage['locale'] = language;
      }
    });

    this.configService.config$.pipe(takeUntil(this._destroyed$$)).subscribe((config) => {
      this.redirectIfAccountSuspended(config.is_account_suspended, this.router.url);
    });

    const routerEvent$ = this.router.events.pipe(
      filter((e) => e instanceof NavigationStart),
      filter((e: NavigationStart) => e.url.indexOf('limited') === -1),
    );

    combineLatest([this.configService.config$, routerEvent$])
      .pipe(takeUntil(this._destroyed$$))
      .subscribe(([config, e]: [SMConfig, NavigationStart]) => {
        this.redirectIfAccountSuspended(config.is_account_suspended, e.url);
      });

    this.taskService.init().pipe(takeUntil(this._destroyed$$)).subscribe();
  }

  ngOnDestroy(): void {
    this._destroyed$$.next();
    this._destroyed$$.complete();

    if (this.vaproductFilterEvent) {
      this.vaproductFilterEvent.unsubscribe();
    }
  }

  redirectIfAccountSuspended(accountSuspended: boolean, targetUrl: string): void {
    if (accountSuspended && targetUrl.indexOf('limited') === -1) {
      this.router.navigateByUrl(`account/${this.configService.accountGroupId}/limited`);
    }
  }

  handleVaProductNavFilterToogle() {
    this.navProduct.toggleSidebarFilters();
  }

  buildNavItems(
    agid: string,
    postTemplatesPageEnabled: boolean,
    noUpgradePath: boolean,
    isPro: boolean,
    displayToolsOption: boolean,
    displayPostsFlow: boolean,
  ): VaProductNavItem[] {
    let navItems = [];

    navItems.push({
      label: 'NAV.OVERVIEW',
      url: `/account/${agid}/overview`,
      icon: 'explore',
      showUpgradeChip: false,
    });

    if (displayPostsFlow) {
      navItems.push({
        label: 'NAV.POSTS',
        url: `/account/${agid}/posts`,
        icon: 'message',
        showUpgradeChip: false,
      });
      navItems.push({
        label: 'NAV.CALENDAR',
        url: `/account/${agid}/calendar`,
        icon: 'calendar_month',
      });
    }

    const myPostsChildren = [
      { label: 'NAV.RECENT_POSTS', url: `/account/${agid}/posts/recent` },
      { label: 'NAV.SCHEDULED_POSTS', url: `/account/${agid}/posts/scheduled` },
      { label: 'NAV.CALENDAR', url: `/account/${agid}/posts/calendar` },
      { label: 'NAV.DRAFTS', badgeId: 'drafts', url: `/account/${agid}/posts/draft` },
    ] as VaProductNavItem[];

    if (postTemplatesPageEnabled && !displayToolsOption) {
      myPostsChildren.push({ label: 'NAV.TEMPLATES', url: `/account/${agid}/posts/templates` });
    }

    if (!noUpgradePath && !displayToolsOption) {
      myPostsChildren.push({
        label: 'NAV.LINK_DIRECTORY_PAGE',
        url: `/account/${agid}/posts/clickable-bio`,
        icon: 'forum',
        showUpgradeChip: true,
        newChipText: this.clickableBioNewChipText,
      });
    }

    if (noUpgradePath) {
      if (!displayPostsFlow) {
        navItems = navItems.concat([
          {
            label: 'NAV.MY_POSTS',
            url: null,
            icon: 'message',
            children: myPostsChildren,
            showUpgradeChip: false,
          },
        ]);
      }
      if (displayToolsOption) {
        const tools = {
          label: 'NAV.TOOLS',
          url: `/account/${agid}/tools`,
          icon: 'widgets',
          showUpgradeChip: false,
        };
        navItems.push(tools);
      }

      navItems = navItems.concat([
        {
          label: 'NAV.INSIGHTS',
          showUpgradeChip: false,
          url: null,
          icon: 'poll',
          children: [{ label: 'NAV.POST_PERFORMANCE', url: `/account/${agid}/analytics/post-performance` }],
        },
      ]);

      navItems.push({
        label: 'NAV.SETTINGS',
        url: null,
        icon: 'settings',
        children: [{ label: 'NAV.CONNECT_ACCOUNTS', url: `/account/${agid}/settings` }],
        showUpgradeChip: false,
      });
    } else {
      if (!displayPostsFlow) {
        navItems = navItems.concat([
          {
            label: 'NAV.MY_POSTS',
            url: null,
            icon: 'message',
            children: myPostsChildren,
            showUpgradeChip: false,
          },
        ]);
      }

      if (!displayToolsOption) {
        navItems = navItems.concat([
          {
            label: 'NAV.CUSTOMER_POSTS',
            badgeId: 'customerPosts',
            url: `/account/${agid}/customers`,
            icon: 'forum',
            showUpgradeChip: true,
          },
          { label: 'NAV.CONTENT', url: `/account/${agid}/content`, icon: 'rss_feed', showUpgradeChip: true },
        ]);
      }

      if (displayToolsOption) {
        const tools = {
          label: 'NAV.TOOLS',
          url: `/account/${agid}/tools`,
          icon: 'widgets',
          showUpgradeChip: false,
        };
        navItems.push(tools);
      }

      navItems = navItems.concat([
        {
          label: 'NAV.INSIGHTS',
          showUpgradeChip: false,
          url: null,
          icon: 'poll',
          children: [
            {
              label: 'NAV.LINK_PERFORMANCE',
              url: `/account/${agid}/analytics/link-performance`,
              showUpgradeChip: true,
            },
            { label: 'NAV.POST_PERFORMANCE', url: `/account/${agid}/analytics/post-performance` },
          ],
        },
      ]);

      const settings = {
        label: 'NAV.SETTINGS',
        url: null,
        icon: 'settings',
        children: [
          { label: 'NAV.CONNECT_ACCOUNTS', url: `/account/${agid}/settings` },
          { label: 'NAV.INTERESTING_CONTENT', url: `/account/${agid}/settings/articles`, showUpgradeChip: true },
        ],
        showUpgradeChip: false,
      };

      if (isPro) {
        settings.children.push({ label: 'NAV.AI_SETTINGS', url: `/account/${agid}/settings/ai-settings` });
      }

      navItems.push(settings);
    }

    return navItems;
  }

  openCarouselDialog(): void {
    this.dialog.open(CarouselDialogComponent, {
      panelClass: 'app-full-bleed-dialog',
      minHeight: '330px',
      autoFocus: false,
    });
  }

  openComposer(settings?: ComposerSettings): void {
    this.composerSettingsService.showComposer(settings);
  }

  upgradeClicked(): void {
    // used to track user activity within sales and success center
    this.productAnalyticsService.trackEvent('sidebar-upgrade', 'user', 'click');

    this.upgradeCTADialogService
      .openUpgradeModel('account')
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.productAnalyticsService.trackEvent('proUpgradeCtaClicked', 'user', 'click');
          this.upgradeCTADialogService.businessCenterUpgrade();
        }
      });
  }

  get businessNavFeatureLoading$(): Observable<boolean> {
    return this.businessNavFeatureLoading$$.asObservable();
  }
}
