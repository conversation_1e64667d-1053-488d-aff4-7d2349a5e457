import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { VaProductNavItem } from '@vendasta/uikit';
import { PartnerService } from '../../pages/partner/partner.service';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { AppPartnerService, AppSettings } from '@galaxy/marketplace-apps';
import { map } from 'rxjs/operators';
import { SM_DEFAULT_LOGO } from '../authenticated-page/authenticated-page.component';
import { FeatureFlagService } from '@galaxy/partner';
import { SMFeaturesService } from '../../core/features.service';

const SM_APP_ID = 'SM';
const FALLBACK_MARKET = 'all-markets-default-fallback';

@Component({
  selector: 'app-partner-page',
  templateUrl: './partner-page.component.html',
  styleUrls: ['./partner-page.component.scss'],
  standalone: false,
})
export class PartnerPageComponent implements OnInit, OnDestroy {
  navItems: VaProductNavItem[];
  supportedLangs = [
    { display: 'Čeština', locale: 'cs' },
    { display: 'Deutsch', locale: 'de' },
    { display: 'English', locale: 'en' },
    { display: 'Español', locale: 'es-419' },
    { display: 'Français', locale: 'fr-ca' },
    { display: 'Français canadien', locale: 'fr-fr' },
    { display: 'Italiano', locale: 'it' },
    { display: 'Nederlands', locale: 'nl' },
  ];
  autoPostFeatureSubscription: Subscription;
  isAutoPostSettingsEnabled = false;
  logoUrl$: Observable<string>;
  navItems$: Observable<VaProductNavItem[]>;

  constructor(
    private translateService: TranslateService,
    private partnerService: PartnerService,
    private featureFlagService: FeatureFlagService,
    private appPartnerService: AppPartnerService,
    private smFeatureFlagService: SMFeaturesService,
  ) {}

  ngOnInit(): void {
    const lang = 'en';
    this.translateService.setDefaultLang(lang);
    this.translateService.use(lang);

    this.navItems$ = combineLatest([
      this.smFeatureFlagService.autoPost$,
      this.smFeatureFlagService.autoPostSettings$,
      this.smFeatureFlagService.adminDashboard$,
    ]).pipe(
      map(([autoPost, autoPostSettings, adminDashboard]) => {
        return this.buildNavItemsWithFeatureFlag(autoPost, autoPostSettings, adminDashboard);
      }),
    );

    // default market doesn't seem to properly save branding settings, but fallback does.
    this.logoUrl$ = combineLatest([
      this.appPartnerService.getAppSettings(SM_APP_ID, this.partnerService.partnerId, this.partnerService.marketId),
      this.appPartnerService.getAppSettings(SM_APP_ID, this.partnerService.partnerId, FALLBACK_MARKET),
    ]).pipe(
      map(([settings, fallbackSettings]: [AppSettings, AppSettings]) => {
        if (!!settings && !!settings.branding.iconUrl && settings?.branding?.enabled) {
          return settings.branding.iconUrl;
        }
        return fallbackSettings.branding.iconUrl ? fallbackSettings.branding.iconUrl : SM_DEFAULT_LOGO;
      }),
    );
  }

  ngOnDestroy(): void {
    if (this.autoPostFeatureSubscription) {
      this.autoPostFeatureSubscription.unsubscribe();
    }
  }

  buildNavItemsWithFeatureFlag(
    autoPost: boolean,
    autoPostSettings: boolean,
    adminDashboard: boolean,
  ): VaProductNavItem[] {
    const navItems = [];
    const partnerId = this.partnerService.partnerId;

    if (adminDashboard) {
      navItems.push({ label: 'Admin Dashboard', url: `/partner/${partnerId}/admin-dashboard`, icon: 'table_chart' });
    }

    if (autoPost) {
      navItems.push({ label: 'NAV.AUTOPOSTING', url: `/partner/${partnerId}/auto-posting`, icon: 'settings' });
    }

    if (autoPostSettings) {
      navItems.push({ label: 'NAV.AUTOPOSTING_LISTS', url: `/partner/${partnerId}/auto-posting-lists`, icon: 'list' });
    }

    if (autoPost) {
      navItems.push(
        { label: 'NAV.AUTO_SCHEDULED_POSTS', url: `/partner/${partnerId}/auto-scheduled-posts`, icon: 'date_range' },
        { label: 'NAV.RSS_FEED', url: `/partner/${partnerId}/content`, icon: 'rss_feeds' },
        { label: 'NAV.USER_CONTENT', url: `/partner/${partnerId}/user-content`, icon: 'forum' },
        { label: 'NAV.UPLOAD_CONTENT', url: `/partner/${partnerId}/user-content/upload`, icon: 'cloud_upload' },
      );
    }

    return navItems;
  }
}
