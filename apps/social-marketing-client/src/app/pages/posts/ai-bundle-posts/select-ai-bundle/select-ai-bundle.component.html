<div class="header">
  <div class="header__subtitle">
    {{ 'AI_BUNDLE.SELECT_PAGE.SUB_HEADING' | translate }}
  </div>
</div>
<div class="card-container">
  <app-individual-post
    *ngFor="let selectSocialPost of bundlePosts"
    [mode]="mode"
    [bundleSocialPost]="selectSocialPost"
    (invalidPost)="showValidationMessage($event)"
    (editPost)="editBundlePost($event)"
  ></app-individual-post>
  <mat-card class="action">
    <mat-card-content>
      <div class="right-container">
        <button mat-stroked-button (click)="openBackWarningModal()">
          {{ 'AI_BUNDLE.SELECT_PAGE.BACK' | translate }}
        </button>
        <button
          mat-raised-button
          color="primary"
          [disabled]="this.selectedPosts.length === 0"
          (click)="handleNextClick()"
        >
          {{ 'AI_BUNDLE.SELECT_PAGE.NEXT' | translate }}
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
