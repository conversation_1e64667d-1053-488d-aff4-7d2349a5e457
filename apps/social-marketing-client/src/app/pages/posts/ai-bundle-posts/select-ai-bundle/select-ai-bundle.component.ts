import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog } from '@angular/material/dialog';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyStickyFooterModule } from '@vendasta/galaxy/sticky-footer';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { POSTHOG_KEYS } from '../../../../composer/constants';
import { SnackBarService } from '../../../../shared/snack-bar/snack-bar.service';
import { AI_BUNDLE_POSTS_STEPS, BundleSocialPosts, ErrorCode, TechnicalPromptData } from '../model';
import { IndividualPostComponent } from './individual-post/individual-post.component';
@Component({
  selector: 'app-select-ai-bundle',
  templateUrl: './select-ai-bundle.component.html',
  styleUrls: ['./select-ai-bundle-component.scss'],
  imports: [
    CommonModule,
    MatCheckboxModule,
    GalaxyFormFieldModule,
    IndividualPostComponent,
    MatButtonModule,
    MatCardModule,
    GalaxyStickyFooterModule,
    GalaxyPageModule,
    TranslateModule,
    GalaxyAlertModule,
  ],
})
export class SelectAiBundleComponent {
  @Input() mode: 'content' | 'content_and_images';
  @Input() bundlePosts: BundleSocialPosts[];
  @Input() technicalPromptData: TechnicalPromptData;
  @Output() editPost = new EventEmitter<BundleSocialPosts>();
  @Output() regenerateContent = new EventEmitter<TechnicalPromptData>();
  @Output() next = new EventEmitter<BundleSocialPosts[]>();
  @Output() back = new EventEmitter<number>();
  errorMessageDelay = 10000;
  errorCodeMapping = {
    [ErrorCode.IG_USER_NO_MEDIA]: 'AI_BUNDLE.SELECT_PAGE.INSTAGRAM_ERROR',
    [ErrorCode.TW_USER_TEXT_TOO_LONG]: 'AI_BUNDLE.SELECT_PAGE.TWITTER_ERROR',
  };

  constructor(
    public confirmationModal: OpenConfirmationModalService,
    private translateService: TranslateService,
    private snackBarService: SnackBarService,
    private dialog: MatDialog,
    private productAnalyticsService: ProductAnalyticsService,
  ) {}

  get selectedPosts(): BundleSocialPosts[] {
    return this.bundlePosts.filter((post) => post.selected);
  }

  handleNextClick() {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.BUNDLE_AI_SAVE_SELECT, 'user', 'click', 0);
    this.next.emit(this.selectedPosts);
  }

  openBackWarningModal() {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_TITLE',
        message: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CONTENT',
        hideCancel: false,
        confirmButtonText: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_GO_BACK',
        cancelButtonText: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CANCEL',
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((userDidAction: boolean) => {
        if (userDidAction) {
          this.back.emit(AI_BUNDLE_POSTS_STEPS.GenerateContent);
        }
      });
  }

  editBundlePost(bundlePost: BundleSocialPosts) {
    this.editPost.emit(bundlePost);
  }

  showValidationMessage(event: ErrorCode) {
    const message = this.translateService.instant(this.errorCodeMapping[event]);
    this.snackBarService.errorSnack(message, null, { duration: this.errorMessageDelay, horizontalPosition: 'center' });
  }
}
