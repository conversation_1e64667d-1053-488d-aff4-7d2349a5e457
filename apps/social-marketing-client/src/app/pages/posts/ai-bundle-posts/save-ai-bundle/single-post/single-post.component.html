<div class="parent-page">
  <div class="page">
    <div class="network-box">
      <mat-tab-group [class.regular-post]="bundle.posts.length === 1">
        <mat-tab *ngFor="let post of bundle?.posts" class="avatar-tab">
          <ng-container *ngIf="post?.services?.length === 1; else multipleServices">
            <ng-template matTabLabel>
              <app-avatar [service]="post?.services[0]"></app-avatar>
            </ng-template>
          </ng-container>
          <ng-template #multipleServices>
            <ng-template matTabLabel>
              <app-avatar-group [services]="post?.services"></app-avatar-group>
            </ng-template>
          </ng-template>

          <!--Content-->
          <mat-card class="ai-post-content-card">
            <div class="ai-post-content">
              <div *ngIf="post?.medias?.length > 0">
                <div>
                  <img
                    *ngIf="post?.medias[0]?.mediaType === 'IMAGE'"
                    src="{{ post?.medias[0]?.mediaUrl }}"
                    alt="Image"
                    class="image-content"
                  />

                  <video *ngIf="post?.medias[0]?.mediaType === 'VIDEO'" controls class="video-content">
                    <source src="{{ post?.medias[0]?.mediaUrl }}" />
                  </video>
                </div>
                <div class="media-number-chip">
                  <mat-chip *ngIf="post?.medias?.length > 1" class="chip">
                    {{ post?.medias?.length }}
                    items
                  </mat-chip>
                </div>
              </div>
              <div>
                {{ post?.postText }}
              </div>
            </div>
          </mat-card>
          <!--Status & Scheduling-->
          <mat-card class="ai-post-schedule-card">
            <mat-card-content class="ai-scheduler-card">
              <div class="scheduler-card">
                <div class="save-row-container">
                  <mat-radio-group [(ngModel)]="bundle.postType" class="ai-schedule-type">
                    <ng-container *ngFor="let scheduleType of postStatusOptions; index as i">
                      <mat-radio-button [value]="scheduleType.value" class="radio-buttons">
                        <span [ngClass]="'ai-schedule-' + scheduleType.key" [className]="'ai-schedule-kinds'">
                          {{ scheduleType.value }}
                        </span>
                      </mat-radio-button>
                    </ng-container>
                  </mat-radio-group>
                </div>
                <div class="date-and-time">
                  <app-date-time-selector
                    class="composer-date-time-title"
                    [date]="dateValue"
                    [showTitle]="false"
                    (dateChange)="onDateChange($event)"
                  ></app-date-time-selector>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>
