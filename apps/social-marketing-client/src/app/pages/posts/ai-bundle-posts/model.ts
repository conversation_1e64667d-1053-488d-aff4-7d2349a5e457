import { PostableService } from '../../../shared/social-service/social-service.service';
import { GenerateType } from '../../../composer/components/suggestions/generate-type';

export enum ErrorCode {
  IG_USER_NO_MEDIA = 'IG_USER_NO_MEDIA',
  TW_USER_TEXT_TOO_LONG = 'TW_USER_TEXT_TOO_LONG',
}

export type ValidationResult = { isValid: boolean; errorCode?: ErrorCode };
export type TechnicalPromptData = { technicalPrompt: string; commonInstructions?: string };

export interface GenerateContentSetup {
  topic: string;
  imageTopic?: string;
  contentType: 'content' | 'content_and_images';
  useAiInstructions: boolean;
  networks: SocialConnection[];
  numberOfPosts: number;
  tone?: string;
  contentLength: number;
}

export const DefaultGenerateContentSetup: GenerateContentSetup = {
  topic: '',
  imageTopic: '',
  useAiInstructions: true,
  networks: [],
  numberOfPosts: 2,
  contentLength: 0,
  contentType: GenerateType.CONTENT_AND_IMAGES,
};

// Generate state object to handle posts, generating, and errors
export interface AIBundlePostsState {
  allowedNetworks?: SocialConnection[];
  generateContentSetup: GenerateContentSetup;
  technicalPromptData?: TechnicalPromptData;
  currentEditPost?: BundleSocialPosts;
  content: BundleSocialPosts[];
  selectedContent: BundleSocialPosts[];
  generating: boolean;
  currentStep: number;
}

export const AI_BUNDLE_POSTS_INITIAL_STATE: AIBundlePostsState = {
  content: [],
  selectedContent: [],
  generateContentSetup: DefaultGenerateContentSetup,
  generating: false,
  currentStep: 0,
};

export enum AI_BUNDLE_POSTS_STEPS {
  GenerateContent = 0,
  ListContent = 1,
  CreatePosts = 2,
}

export interface BundleSocialPosts {
  posts: PostsInterface[];
  selected: boolean;
  postType?: PostScheduleType;
  scheduleDate?: Date;
}

export interface PostsInterface {
  postText: string;
  medias?: Media[];
  services?: SocialConnection[];
  isCustomizedByAccount?: boolean;
  status?: 'scheduled' | 'draft';
  scheduleDate?: Date;
}

export type SocialConnection = PostableService & { iconPath: string };

export interface Media {
  mediaUrl: string;
  mediaType: MediaType;
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
}

export enum PostScheduleType {
  HIDDEN_DRAFT = 'Hidden Draft',
  DRAFT = 'Draft',
  SCHEDULE = 'Schedule',
}
