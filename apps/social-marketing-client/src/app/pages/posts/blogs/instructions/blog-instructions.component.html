<div class="heading-style">
  {{ 'BLOG.STEP_ONE_TITLE' | translate }}
</div>
<form [formGroup]="instructionForm" class="blog-instruct-form">
  <mat-card>
    <mat-card-content>
      <glxy-form-row>
        <glxy-form-field class="blog-goal-title col-xs-12 col-sm-12">
          <glxy-label>{{ 'BLOG.GOAL_TITLE' | translate }}</glxy-label>
          <mat-select formControlName="goalType">
            <mat-option *ngFor="let option of generateTypeOptions" [value]="option.name"
              ><b>{{ option.name }}</b> - {{ option.desc }}
            </mat-option>
          </mat-select>
        </glxy-form-field>
        <glxy-form-field class="blog-cust-input" [hideRequiredLabel]="true" *ngIf="isCustomGoal()">
          <input
            formControlName="customGoal"
            #focus
            matInput
            placeholder="{{ 'BLOG.BUTTONS.CUSTOM_GOAL' | translate }}"
          />
        </glxy-form-field>
      </glxy-form-row>
      <glxy-form-row>
        <glxy-form-field class="col-xs-12 col-sm-12">
          <glxy-label>{{ 'BLOG.GOAL_PROMPT' | translate }}</glxy-label>
          <textarea formControlName="goalPrompt" placeholder="{{ 'BLOG.BUTTONS.BLOG_TOPIC' | translate }}"></textarea>
        </glxy-form-field>
      </glxy-form-row>
      <glxy-form-row>
        <glxy-form-field>
          <glxy-label>{{ 'BLOG.GOAL_KEYWORDS' | translate }}</glxy-label>
          <mat-chip-grid #goalKeywords aria-label="Enter blog keywords">
            <mat-chip-row *ngFor="let keyword of keywords" (removed)="removeKeyword(keyword)">
              {{ keyword }}
              <button matChipRemove [attr.aria-label]="'remove ' + keyword">
                <mat-icon>cancel</mat-icon>
              </button>
            </mat-chip-row>
            <input
              placeholder="{{ 'BLOG.BUTTONS.ADD_KEYWORD' | translate }}"
              [matChipInputFor]="goalKeywords"
              [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
              [matChipInputAddOnBlur]="true"
              (matChipInputTokenEnd)="addKeywordFromInput($event)"
              maxlength="50"
            />
          </mat-chip-grid>
        </glxy-form-field>
      </glxy-form-row>
      <app-advanced-blog-options (advancedOptionsEvent)="getAdvancedOptions($event)"></app-advanced-blog-options>
    </mat-card-content>
  </mat-card>
</form>
