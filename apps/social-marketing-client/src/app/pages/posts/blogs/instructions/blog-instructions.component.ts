import { COMM<PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, EventEmitter, Output } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { MatChipInputEvent } from '@angular/material/chips';
import { BlogStoreService } from '../blog-store.service';
import { goalType, StepInfo } from '../blog-config';
import { FormType, StepName, Steps, StepStatus } from '../blog.enum';

@Component({
  selector: 'app-blog-instructions',
  templateUrl: './blog-instructions.component.html',
  styleUrl: './blog-instructions.component.scss',
  standalone: false,
})
export class BlogInstructionsComponent {
  @Output() nextStep = new EventEmitter<StepInfo>();
  generateTypeOptions = goalType;
  keywords = new Set([]);
  readonly separatorKeysCodes = [ENTER, COMMA] as const;
  instructionForm: UntypedFormGroup;
  details: StepInfo;

  constructor(private blogStoreService: BlogStoreService) {
    this.instructionForm = new UntypedFormGroup({
      goalType: new UntypedFormControl(goalType[1].name),
      customGoal: new UntypedFormControl(null),
      goalPrompt: new UntypedFormControl(null, Validators.required),
      goalKeywords: new UntypedFormControl(this.keywords),
      advancedOptionsForm: new UntypedFormGroup({
        tone: new UntypedFormControl(null),
        contentLength: new UntypedFormControl(null),
      }),
    });
    this.instructionForm.valueChanges.subscribe((_) => {
      this.blogStoreService.setFormStatus(Steps.INSTRUCTION, this.instructionForm.status === StepStatus.VALID);
      this.nextStep.emit(
        (this.details = {
          step: Steps.INSTRUCTION,
          status: this.instructionForm.status === StepStatus.VALID,
          formGroupValue: this.instructionForm,
          type: FormType.INSTRUCTION,
        }),
      );
    });
  }

  addKeywordFromInput(event: MatChipInputEvent): void {
    if (event.value) {
      this.keywords.add(event.value);
      event.chipInput?.clear();
    }
  }

  removeKeyword(keyword: string): void {
    this.keywords.delete(keyword);
  }
  getAdvancedOptions(val: { formGroupValue: UntypedFormGroup }) {
    this.instructionForm.get(StepName.ADV_OPTS).patchValue(val.formGroupValue.value);
  }

  isCustomGoal(): boolean {
    if (this.instructionForm.get('goalType')?.value === 'Custom') {
      this.instructionForm.get('customGoal').setValidators([Validators.required]);
      this.instructionForm.get('customGoal').updateValueAndValidity();
    } else {
      this.instructionForm?.get('customGoal')?.setValidators(null);
      this.instructionForm?.get('customGoal')?.updateValueAndValidity();
    }
    return this.instructionForm.get('goalType')?.value === 'Custom';
  }
}
