<form [formGroup]="titleForm">
  <div class="heading-style">
    {{ 'BLOG.STEP_TWO_TITLE' | translate }}
  </div>
  <mat-card class="mat-card-settings">
    <mat-card-content class="title-settings">
      <glxy-form-row>
        <glxy-form-field class="col-xs-12 col-sm-12">
          <mat-radio-group formControlName="blogTitle" aria-labelledby="blog-title" class="blog-radio-group">
            <mat-radio-button
              class="title-option"
              *ngFor="let option of titleOptions"
              [value]="option.name"
              [checked]="option.checked"
              (change)="addCustomTitleEvent(false)"
            >
              {{ option.name }}
            </mat-radio-button>
            <div class="link-text-color c-pointer" (click)="addCustomTitleEvent(true)">Write your own title</div>
            <mat-radio-button
              *ngIf="isCustomTitle"
              [checked]="true"
              class="title-option align-items-center blog-custm-radio"
            >
              <glxy-form-field class="blog-cust-input">
                <input formControlName="blogCustomTitle" #focus matInput />
              </glxy-form-field>
            </mat-radio-button>
          </mat-radio-group>
        </glxy-form-field>
      </glxy-form-row>
    </mat-card-content>
  </mat-card>
</form>
