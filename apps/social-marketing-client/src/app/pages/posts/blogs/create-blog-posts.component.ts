import {
  Component,
  EventEmitter,
  HostListener,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { UntypedFormArray, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationModalComponent, OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { map, Observable, Subscription } from 'rxjs';
import { POSTHOG_KEYS } from '../../../composer/constants';
import { ConfigService } from '../../../core';
import { GenerateTitleSetup, StepInfo } from './blog-config';
import { BlogStoreService } from './blog-store.service';
import { FormType, StepName, Steps } from './blog.enum';
import { BlogMediaService } from './media/blog-media.service';
import { AIInstruction } from '../../../core/post/post';
import { BlogPlannerComponent } from './planner/blog-planner.component';
import { ConnectionSettingsService } from '../../settings/connection-settings/connection-settings.service';
import { ComposerSettings } from '../../../composer/interfaces';

@Component({
  selector: 'app-create-blog-posts',
  templateUrl: './create-blog-posts.component.html',
  styleUrl: './create-blog-posts.component.scss',
  standalone: false,
})
export class CreateBlogPostsComponent implements OnInit, OnDestroy {
  blogService: BlogStoreService = inject(BlogStoreService);
  blogMediaService: BlogMediaService = inject(BlogMediaService);
  private subscriptions: Subscription[] = [];

  @Input() visible = false;
  @Input() showTopNavBar = false;
  @Input() showClose = true;
  @Input() set data(result) {
    if (result) {
      this.updatePlannerData = result;
      this.blogService.blogTitle = result?.title || result?.blogPostCustomization?.title;
      this.blogService.blogContent = result?.postText || result?.post_text;
      if (result?.media) {
        this.blogMediaService.currentImg(result.media[0] ? result.media[0]?.image_url : null);
      } else {
        this.blogMediaService.currentImg(result?.imageUrls ? result?.imageUrls[0] : null);
      }
    }
  }
  @Output() visibleEvent = new EventEmitter<boolean>();
  @ViewChild('aiSettings') aiSettings;
  isUWM$: Observable<boolean>;
  blogPostForm: UntypedFormGroup;

  accountGroupId: string;
  showDraft: boolean;
  getStepInfo: StepInfo;
  blogTitles: string;
  isedited: boolean;
  currentStep = Steps.INSTRUCTION;
  topNavBarOffset = 0;
  composerLoaded = false;
  aiLoader = false;
  displayToolbar = true;
  nxtBtnStatus = true;
  genBlogStep = Steps.GEN_BLOG;
  blogData: GenerateTitleSetup;
  updatePlannerData = null;
  currentImgUrl: string;
  wpServices = [];

  wpServices$ = this.connectionSettingsService
    .listBlogConnections()
    .pipe(map((connections) => connections.blogConnections.map(this.connectionSettingsService.mapToSocialService)));

  constructor(
    private translateService: TranslateService,
    private productAnalyticsService: ProductAnalyticsService,
    private connectionSettingsService: ConnectionSettingsService,
    protected dialog: MatDialog,
    public configService: ConfigService,
    public snackbar: SnackbarService,
    public confirmationModal: OpenConfirmationModalService,
  ) {
    this.blogPostForm = new UntypedFormGroup({
      instructionForm: new UntypedFormGroup({
        goalType: new UntypedFormControl(null),
        customGoal: new UntypedFormControl(null),
        goalPrompt: new UntypedFormControl(null),
        goalKeywords: new UntypedFormControl(null),
        advancedOptionsForm: new UntypedFormGroup({
          tone: new UntypedFormControl(null),
          contentLength: new UntypedFormControl(null),
        }),
      }),
      titleForm: new UntypedFormGroup({
        blogTitle: new UntypedFormControl(null),
        blogCustomTitle: new UntypedFormControl(null),
      }),
      outlineForm: new UntypedFormGroup({
        blogTitle: new UntypedFormControl(null),
        sectionTitles: new UntypedFormArray([]),
      }),
      generateBlogForm: new UntypedFormGroup({
        blogTitle: new UntypedFormControl(null),
        blogContent: new UntypedFormControl(null),
      }),
    });
    this.blogService.initializeSubscriptions();
  }

  ngOnInit() {
    this.subscriptions.push(
      this.blogService.isedited$.subscribe((value) => {
        this.isedited = value;
      }),
    );

    this.composerLoaded = true;
    this.visibleEvent.emit(this.visible);

    this.topNavBarOffset = this.showTopNavBar ? 40 : 0;

    this.subscriptions.push(
      this.blogMediaService.currentImageUrl$.subscribe((img) => {
        this.currentImgUrl = this.updatePlannerData ? img : '';
      }),
    );

    this.wpServices$.subscribe((wpService) => {
      this.wpServices = wpService;
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  initialize(ssids: string[]) {}

  showComposer(setting?: ComposerSettings) {
    this.blogService.setComposerSettings(setting);
    this.visible = true;
    this.visibleEvent.emit(true);
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification() {
    if (this.currentStep === Steps.INSTRUCTION) return false;
  }

  getNextStepEvent(step: StepInfo) {
    this.getStepInfo = step;
    this.getBtnStatus();
    if (step.type === FormType.INSTRUCTION) {
      this.blogPostForm.get(StepName.INSTRUCTIONS).patchValue(step.formGroupValue.value);
    } else if (step.type === FormType.TITLE) {
      this.blogPostForm.get(StepName.TITLE).patchValue(step.formGroupValue.value);
    } else if (step.type === FormType.OUTLINE) {
      this.blogPostForm.get(`${StepName.OUTLINE}.blogTitle`)?.patchValue(step.formGroupValue.value.blogTitle);
      const sectionTitlesArray = this.blogPostForm.get(`${StepName.OUTLINE}.sectionTitles`) as UntypedFormArray;
      sectionTitlesArray.clear();

      step.formGroupValue.value.sectionTitles.forEach((sectionTitle: string) => {
        sectionTitlesArray.push(new UntypedFormControl(sectionTitle));
      });
    }
    this.blogPostForm.get(StepName.OUTLINE).valueChanges.subscribe((instructionValue) => {
      this.blogData = instructionValue;
    });
  }

  getTitleFormValue(): string {
    const titleForm = this.blogPostForm.get(StepName.TITLE);
    const blogTitle = titleForm?.get('blogTitle')?.value;
    const blogCustomTitle = titleForm?.get('blogCustomTitle')?.value;
    return blogTitle || blogCustomTitle;
  }
  getLatestTitle(): string {
    const outlineForm = this.blogPostForm.get(StepName.OUTLINE);
    return outlineForm?.get('blogTitle')?.value;
  }

  goNext() {
    this.aiLoader = true;
    this.currentStep = this.currentStep + 1;
    this.getBtnStatus();

    switch (this.currentStep) {
      case Steps.TITLE: {
        const value = this.blogPostForm.get(StepName.INSTRUCTIONS).getRawValue();
        if (value.goalType == 'Custom') value.goalType = value.customGoal;
        this.blogService.generateTitle(value).subscribe({
          next: (titles) => {
            this.blogService.titleList = titles?.multiResult?.items;
            this.productAnalyticsService.trackEvent(POSTHOG_KEYS.GENERATE_TITLE, 'user', 'click');
            this.aiLoader = false;
          },
          error: () => {
            this.blogService.titleList = [];
            this.aiLoader = false;
            this.snackbar.openErrorSnack(this.translateService.instant('BLOG.ERORR.GENERATION'));
            this.goBack();
          },
        });
        break;
      }

      case Steps.OUTLINE: {
        this.blogService
          .generateOutline(this.getTitleFormValue(), this.blogPostForm.get(StepName.INSTRUCTIONS).getRawValue())
          .subscribe({
            next: (outline) => {
              this.blogService.outlineList = outline?.multiResult?.items;
              this.productAnalyticsService.trackEvent(POSTHOG_KEYS.GENERATE_OUTLINE, 'user', 'click');
              this.aiLoader = false;
            },
            error: () => {
              this.aiLoader = false;
              this.blogService.outlineList = [];
              this.snackbar.openErrorSnack(this.translateService.instant('BLOG.ERORR.GENERATION'));
              this.goBack();
            },
          });

        break;
      }

      case Steps.GEN_BLOG: {
        this.blogService
          .generateBlogContent(
            this.blogPostForm.get(StepName.INSTRUCTIONS).getRawValue(),
            this.blogPostForm.get(StepName.OUTLINE).getRawValue(),
          )
          .subscribe({
            next: (blog) => {
              this.aiLoader = false;
              this.blogService.blogContent = blog?.result?.content;
              this.productAnalyticsService.trackEvent(POSTHOG_KEYS.GENERATE_BLOG, 'user', 'click');
            },
            error: () => {
              this.aiLoader = false;
              this.snackbar.openErrorSnack(this.translateService.instant('BLOG.ERORR.GENERATION'));
              this.goBack();
            },
          });
        break;
      }
      default:
        this.aiLoader = false;
        break;
    }
  }

  public hideBlogScreen(): void {
    let msg;
    if (this.currentStep !== Steps.INSTRUCTION) {
      if (this.currentStep === Steps.TITLE) {
        msg = this.translateService.instant('BLOG.DISCARD_MODAL.MESSAGE', {
          stepName: FormType.TITLE,
        });
      } else if (this.currentStep === Steps.OUTLINE) {
        msg = this.translateService.instant('BLOG.DISCARD_MODAL.MESSAGE', {
          stepName: FormType.OUTLINE,
        });
      } else {
        msg = this.translateService.instant('BLOG.DISCARD_MODAL.MESSAGE', {
          stepName: 'blog post',
        });
      }

      const data = {
        type: 'confirm',
        title: 'BLOG.DISCARD_MODAL.TITLE',
        message: msg,
        hideCancel: false,
        confirmButtonText: 'AI_BUNDLE.SELECT_PAGE.AI_DIALOG_CONFIRM',
        cancelButtonText: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CANCEL',
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      };

      this.dialog.open && this.dialog.closeAll(); //To restrict multiple confirm modals being triggered
      this.dialog
        .open(ConfirmationModalComponent, {
          data: data,
          width: '540px',
          maxWidth: 'calc( 100vw - 8px )',
          autoFocus: false,
          disableClose: true,
        })
        .afterClosed()
        .subscribe((response: string) => {
          if (!response || response === data.cancelButtonText) {
            return;
          } else {
            this.currentStep = this.currentStep - 1;
            this.blogMediaService.croppedImageSubject$$.next(null);
            this.productAnalyticsService.trackEvent('blogDiscardClicked', 'user', 'click');
          }
        });
      return;
    }
    this.visible = false;
    this.visibleEvent.emit(false);
  }

  getStepButton(): string {
    switch (this.currentStep) {
      case Steps.INSTRUCTION:
        return this.translateService.instant('BLOG.BUTTONS.GENERATE_TITLES');
      case Steps.TITLE:
        return this.translateService.instant('BLOG.BUTTONS.GENERATE_OUTLINE');
      case Steps.OUTLINE:
        return this.translateService.instant('BLOG.BUTTONS.GENERATE_BLOG');
      default:
        return '';
    }
  }

  goBack() {
    this.confirmationModal
      .openModal({
        type: 'confirm',
        title: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_TITLE',
        message: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CONTENT',
        hideCancel: false,
        confirmButtonText: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_GO_BACK',
        cancelButtonText: 'AI_BUNDLE.SELECT_PAGE.BACK_DIALOG_CANCEL',
        actionOnEnterKey: false,
        cancelOnEscapeKeyOrBackgroundClick: true,
      })
      .subscribe((userDidAction: boolean) => {
        if (userDidAction) {
          this.currentStep = this.currentStep - 1;
          this.blogMediaService.croppedImageSubject$$.next(null);
        }
      });
    this.getBtnStatus();
  }

  getBtnStatus() {
    this.blogService.emitStepper$.subscribe((step) => {
      if (step) {
        switch (this.currentStep) {
          case Steps.INSTRUCTION:
            this.nxtBtnStatus = !step?.instructionForm;
            break;
          case Steps.TITLE:
            this.nxtBtnStatus = !step?.titleForm;
            break;
          case Steps.OUTLINE:
            this.nxtBtnStatus = !step?.outLineForm;
            break;
        }
      }
    });
  }

  openBlogPlanner(): void {
    const data = {
      blogData: this.updatePlannerData || {},
      wpServices: this.wpServices,
    };
    this.dialog
      .open(BlogPlannerComponent, {
        data: data,
        autoFocus: false,
        width: '600px',
        panelClass: 'blog-planner-modal',
        disableClose: true,
      } as MatDialogConfig)
      .afterClosed()
      .subscribe((result) => {
        if (result && result.closeParent) {
          this.visibleEvent.emit(false);
        }
      });
  }

  toggleSetting(): void {
    this.aiSettings.toggle();
  }
  ngOnDestroy() {
    this.blogService.cleanupSubscriptions();
    this.subscriptions.forEach((s) => s.unsubscribe());
    this.blogService.setIsEdited(false);
    this.blogService.selectedServiceSubject$$.next([]);
    this.blogService.postModeSelected.set(null);
  }

  protected readonly AIInstruction = AIInstruction;
}
