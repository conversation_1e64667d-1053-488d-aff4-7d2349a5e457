<form [formGroup]="outlineForm">
  <div class="heading-style">
    {{ 'BLOG.STEP_THREE_TITLE' | translate }}
  </div>
  <mat-card>
    <mat-card-content class="outline-settings">
      <glxy-form-row class="blog-title">
        <glxy-label class="align-title sub-heading-style">{{ 'POSTS.BLOG.BLOG_TITLE' | translate }}</glxy-label>
        <glxy-form-field class="blog-title">
          <input formControlName="blogTitle" />
        </glxy-form-field>
      </glxy-form-row>
      <glxy-form-row>
        <glxy-form-field class="col-xs-12 col-sm-12">
          <glxy-label class="blog-sections">{{ 'POSTS.BLOG.BLOG_SECTIONS' | translate }}</glxy-label>
          <div formArrayName="sectionTitles">
            <div
              cdkDropList
              #sectionList="cdkDropList"
              [cdkDropListData]="sectionTitles.controls"
              class="section-list"
              (cdkDropListDropped)="drop($event)"
            >
              <div
                class="d-flex align-items drag-box"
                *ngFor="let sectionTitle of sectionTitles.controls; let i = index"
                cdkDrag
              >
                <mat-icon class="drag-icon-settings" cdkDragHandle>drag_indicator</mat-icon>
                <glxy-form-field class="section-input title-option">
                  <input [formControlName]="i" value="{{ sectionTitle }}" matInput />
                </glxy-form-field>
                <mat-icon class="delete-icon-settings" (click)="removeSection(i)">delete_outline</mat-icon>
              </div>
              <div class="link-text-color c-pointer" (click)="addNewSection()">+ Add Section</div>
            </div>
          </div>
        </glxy-form-field>
      </glxy-form-row>
    </mat-card-content>
  </mat-card>
</form>
