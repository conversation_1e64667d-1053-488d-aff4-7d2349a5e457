import { Injectable, OnDestroy } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { GenerateType } from '@vendasta/social-posts/lib/_internal/enums/api-v2.enum';
import { MetadataV2Interface } from '@vendasta/social-posts/lib/_internal/interfaces/social-post-v2.interface';
import { Subscription } from 'rxjs';
import { ContentLength, FormType, PostMode, StepName, Steps, StepStatus } from './blog.enum';

export const goalType = [
  { name: 'Promotional', desc: 'Generate interest in a product or service' },
  { name: 'Informational', desc: 'Educate readers about a subject' },
  { name: 'Narrative', desc: 'Share details about your brand and story' },
  { name: 'Ranked List', desc: 'Provide a ranking on a particular subject' },
  { name: 'Comparison', desc: 'Show how you stack up against the competition' },
  { name: 'Custom', desc: 'Write your own goal' },
];

export const WP_PLUGIN_URL =
  'https://github.com/vplugins/blog-post-connector/releases/download/v1.0.2/blog-post-connector-package.zip';

export interface titleOption {
  name: string;
  value: number;
  checked: boolean;
}

export interface GenerateTitleSetup {
  goal?: string;
  instruction: string;
  keyword?: string;
  tone?: string;
  length?: ContentLength;
  generateType: GenerateType;
  metadata?: MetadataV2Interface[];
}

export interface StepDetails {
  stepName: StepName;
  stepNum: Steps;
  stepStatus: StepStatus;
}

export interface StepInfo {
  step: number;
  status: boolean;
  formGroupValue: UntypedFormGroup;
  type: FormType;
}

export interface FormStatus {
  instructionForm?: false;
  titleForm?: false;
  outLineForm?: false;
}

export interface PostTypeSettings {
  name: string;
  value: PostMode;
  subtitle: string;
  checked: boolean;
  chipColor: chipColor;
}

export interface ImageUpload {
  url?: string;
  path?: string;
}

export type chipColor = 'default' | 'blue' | 'green' | 'yellow';

@Injectable()
export class PostModeType implements OnDestroy {
  public HIDDEN_POST_MODE: PostTypeSettings[];
  public NO_DRAFT_POST_MODE: PostTypeSettings[];
  public NO_HIDDEN_DRAFT_MODE: PostTypeSettings[];
  private langChangeSubscription: Subscription;

  constructor(private translateService: TranslateService) {
    this.intializeHiddenPostMode();

    this.langChangeSubscription = this.translateService.onLangChange.subscribe(() => {
      this.intializeHiddenPostMode();
    });

    this.NO_DRAFT_POST_MODE = this.HIDDEN_POST_MODE.filter(
      (postMode) => postMode.value === PostMode.SCHEDULED || postMode.value === PostMode.POST_NOW,
    );

    this.NO_HIDDEN_DRAFT_MODE = this.HIDDEN_POST_MODE.filter((postMode) => postMode.value !== PostMode.HIDDEN_DRAFT);
  }
  ngOnDestroy(): void {
    if (this.langChangeSubscription) {
      this.langChangeSubscription.unsubscribe();
    }
  }

  private intializeHiddenPostMode() {
    this.HIDDEN_POST_MODE = [
      {
        name: this.translateService.instant('COMPOSER.HIDDEN_DRAFT'),
        value: PostMode.HIDDEN_DRAFT,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.HIDDEN_DRAFT'),
        checked: false,
        chipColor: 'default',
      },
      {
        name: this.translateService.instant('COMPOSER.DRAFT'),
        value: PostMode.DRAFT,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.DRAFT'),
        checked: false,
        chipColor: 'yellow',
      },
      {
        name: this.translateService.instant('COMPOSER.SCHEDULED'),
        value: PostMode.SCHEDULED,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.SCHEDULED'),
        checked: true,
        chipColor: 'blue',
      },
      {
        name: this.translateService.instant('COMPOSER.POST_NOW'),
        value: PostMode.POST_NOW,
        subtitle: this.translateService.instant('COMPOSER.POST_TYPE_DEFINITION.POST_NOW'),
        checked: false,
        chipColor: 'green',
      },
    ];
  }
}
