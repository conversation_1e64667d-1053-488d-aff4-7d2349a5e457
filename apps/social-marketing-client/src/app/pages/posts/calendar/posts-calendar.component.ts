import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import {
  CalendarEvent,
  CalendarEventTimesChangedEvent,
  CalendarEventTitleFormatter,
  CalendarMonthViewBeforeRenderEvent,
} from 'angular-calendar';
import { BehaviorSubject, combineLatest, EMPTY, Observable, Subject, Subscription } from 'rxjs';
import { catchError, filter, map, startWith, switchMap, take, takeUntil } from 'rxjs/operators';
import moment from 'moment';
import { Overlay } from '@angular/cdk/overlay';

import { PostCalendarEventTitleFormatter } from './event-title-formatter.provider';
import { CardType, SocialPostPreviewComponent, SocialProfileService } from '../../../shared';
import { getColorFromService } from '../../../composer/composer-api.service';
import { ComposerStoreService } from '../../../composer/composer-store.service';
import { PostsCalendarService } from './posts-calendar.service';
import { CalendarView, EventGroup } from './interface';
import { SocialPost, SocialServiceType } from '../../../core/post/post';
import { SnackBarService } from '../../../shared/snack-bar/snack-bar.service';
import { ConfigService } from '../../../core';
import { MonthViewDay, WeekView, WeekViewTimeEvent } from 'calendar-utils';
import { ComposerSettingsService } from '../../../core/composer-settings/composer-settings.service';
import { NEW_POST_ID } from '../../../composer/constants';
import { GroupedPostsService } from '../../../shared/post/grouped-posts.service';
import { ManyPostsComponent } from '../../../shared/post/many-posts-dialog/many-posts.component';
import { CreateReportRequest, CreateReportResponse, FeedItem, FeedListItem } from '../../../core/post/interface';
import { GroupedPostsDialogComponent } from '../../../shared/post/grouped-posts-dialog/grouped-posts-dialog.component';
import { SocialPostFeedStoreService } from '../../../core/post/post-feed.service';
import { ComposerSettings } from '../../../composer/interfaces';
import { SMFeaturesService } from '../../../core/features.service';
import { Draft } from '../../../core/post/draft';
import { CreateReportV2DialogComponent } from '../../../core/post/create-report-dialog-v2/create-report-dialog/create-report-dialog.component';
import { SocialPostFeedAPIService } from '../../../core/post/post-feed.api.service';
import { CopyReportDialogComponent } from '../../../core/post/create-report-dialog-v2/copy-report-dialog/copy-report-dialog.component';
import { ActivatedRoute } from '@angular/router';
import { SMActionButton } from '../../../shared/app-page/app-page.component';
import { SplitFlowButtonComponent } from '../../overview/compose-banner/split-flow-banner/split-flow-button/splitFlowButton.component';
import { SocialServiceService } from '../../../shared/social-service/social-service.service';

// Constants
const HOURS_IN_DAY = 24;
const DAYS_IN_WEEK = 7;
const DIALOG_WIDTH = 300;
const DIALOG_HEIGHT = 400;
const DIALOG_MARGIN = 20;
const MIN_DRAG_THRESHOLD = 20;

// Interfaces for untyped parameters
interface DialogPosition {
  x: number;
  y: number;
}

@Component({
  selector: 'app-posts-calendar',
  templateUrl: './posts-calendar.component.html',
  styleUrls: ['./posts-calendar.component.scss'],
  providers: [
    {
      provide: CalendarEventTitleFormatter,
      useClass: PostCalendarEventTitleFormatter,
    },
  ],
  standalone: false,
})
export class PostsCalendarComponent implements OnInit, OnDestroy {
  calendarView = CalendarView;
  events: CalendarEvent[] = [];
  viewDate: Date = new Date();
  viewDate$$: BehaviorSubject<Date> = new BehaviorSubject<Date>(new Date());
  activeDayIsOpen = false;
  refresh = new Subject<void>();

  currentView$: Observable<CalendarView>;
  calTitle$: Observable<string>;
  selectedView: CalendarView;
  draggedEvent: any;

  showPreview: boolean;
  showComposer: boolean;

  isLoading$: Observable<boolean>;
  locale$: Observable<string>;

  private destroy$ = new Subject<void>();
  subscriptions: Subscription[] = [];
  isPro$: Observable<boolean>;
  noUpgradePath$: Observable<boolean>;
  today = new Date();
  proFlag: boolean;
  noUpgradePath: boolean;

  authUrls$: Observable<Map<string, string>>;
  viewBulkCreateButtons$: Observable<boolean>;

  showCreateReportButton$: Observable<boolean>;

  createReportAction: SMActionButton = {
    label: 'POSTS.REPORT.CREATE_DIALOG.CREATE_REPORT',
    handler: () => this.handleCalendarCreateReport(),
    icon: 'note_add',
  };

  isUWM = false;

  constructor(
    public menudialog: MatDialog,
    private overlay: Overlay,
    public featuresService: SMFeaturesService,
    private socialPostFeedService: SocialPostFeedStoreService,
    private groupedPostsService: GroupedPostsService,
    private postsCalendarService: PostsCalendarService,
    private socialProfileService: SocialProfileService,
    private composerService: ComposerStoreService,
    protected configService: ConfigService,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private composerSettingsService: ComposerSettingsService,
    private snackBarService: SnackBarService,
    private socialPostFeedAPIService: SocialPostFeedAPIService,
    private route: ActivatedRoute,
    private socialServiceService: SocialServiceService,
  ) {
    this.initializeCalendarData();
  }

  private initializeCalendarData(): void {
    this.postsCalendarService
      .fetchFeedItems(this.getStartDate(), this.getEndDate())
      .pipe(takeUntil(this.destroy$))
      .subscribe();

    this.postsCalendarService
      .fetchDraftItems(this.getStartDate(), this.getEndDate())
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  openWorkflowDialog(xPos: number, yPos: number, date: Date, triggered: string): void {
    const { left, top } = this.calculateDialogPosition(xPos, yPos);

    this.menudialog.open(SplitFlowButtonComponent, {
      autoFocus: false,
      position: {
        left: `${left}px`,
        top: `${top}px`,
      },
      data: { isDialog: true, date: date, triggeredFrom: triggered },
    });
  }

  private calculateDialogPosition(xPos: number, yPos: number): { left: number; top: number } {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;

    // Adjust X and Y if the dialog would go off-screen
    let left = xPos;
    let top = yPos;

    if (xPos + DIALOG_WIDTH > screenWidth) {
      left = screenWidth - DIALOG_WIDTH - DIALOG_MARGIN;
    }
    if (yPos + DIALOG_HEIGHT > screenHeight) {
      top = screenHeight - DIALOG_HEIGHT - DIALOG_MARGIN;
    }

    return { left, top };
  }

  ngOnInit(): void {
    this.configService.partnerId$.pipe(take(1), takeUntil(this.destroy$)).subscribe((pid: string) => {
      this.isUWM = pid === 'USFS';
    });

    this.initializeObservables();
    this.setupSubscriptions();
    this.handleRouteParams();
  }

  private initializeObservables(): void {
    this.authUrls$ = this.socialProfileService.authUrls$;
    this.currentView$ = this.postsCalendarService.currentView$;
    this.isPro$ = this.configService.proFlag$;
    this.noUpgradePath$ = this.configService.noUpgradePath$.pipe(startWith(false));
    this.showCreateReportButton$ = this.featuresService.calendarReportEnabled$;
    this.isLoading$ = this.postsCalendarService.isFeedLoading$;

    this.locale$ = this.translateService.onLangChange.pipe(
      map((event: LangChangeEvent) => event.lang),
      startWith(this.translateService.currentLang),
    );

    this.today.setHours(0);
    this.today.setMinutes(-1);

    this.viewBulkCreateButtons$ = combineLatest([this.featuresService.aiBundleCreate$, this.isPro$]).pipe(
      map(([aiBundleCreate, isPro]) => aiBundleCreate && isPro),
    );

    this.calTitle$ = this.createCalendarTitleObservable();
  }

  private createCalendarTitleObservable(): Observable<string> {
    return combineLatest([
      this.postsCalendarService.currentView$,
      this.translateService.stream('POSTS.CALENDAR.WEEK_OF'),
      this.viewDate$$,
    ]).pipe(
      map(([currentView, weekOf, viewDate]) => {
        this.selectedView = currentView;
        switch (currentView) {
          case CalendarView.MONTH:
            return moment(viewDate).format('MMMM Y');
          case CalendarView.WEEK: {
            const sunday: Date = new Date(viewDate);
            sunday.setHours(-HOURS_IN_DAY * viewDate.getDay());
            return `${weekOf} ${moment(sunday).format('MMM DD, Y')}`;
          }
          default:
            return moment(viewDate).format('dddd, MMMM DD Y');
        }
      }),
    );
  }

  private setupSubscriptions(): void {
    // Subscribe to features and configuration
    this.featuresService.calendarWeekImprovements$.pipe(takeUntil(this.destroy$)).subscribe((showFeatures) => {
      this.showPreview = showFeatures;
      this.showComposer = showFeatures;
    });

    this.configService.proFlag$.pipe(takeUntil(this.destroy$)).subscribe((flag) => (this.proFlag = flag));

    this.configService.noUpgradePath$.pipe(takeUntil(this.destroy$)).subscribe((path) => (this.noUpgradePath = path));

    // Track view date changes
    this.viewDate$$.pipe(takeUntil(this.destroy$)).subscribe((viewDate: Date) => (this.viewDate = viewDate));

    // Subscribe to posts data
    this.postsCalendarService.posts$.pipe(takeUntil(this.destroy$)).subscribe((posts: SocialPost[]) => {
      this.events = posts
        .map((post: SocialPost) => this.makeEventFromPost(post))
        .sort((a: CalendarEvent, b: CalendarEvent) => (a.start > b.start ? 1 : -1));
    });

    // Handle post updates
    this.composerService.postUpdated$
      .pipe(
        filter(
          (updateResponse: any) =>
            (!!updateResponse?.posts && updateResponse?.posts?.length > 0) ||
            (!!updateResponse?.deletedIds && updateResponse?.deletedIds?.length > 0),
        ),
        switchMap(({ posts, deletedIds }) => this.handlePostUpdates(posts, deletedIds)),
        takeUntil(this.destroy$),
      )
      .subscribe();
  }

  private handlePostUpdates(posts: any[], deletedIds: string[]): Observable<any> {
    if (!!posts && posts.length > 0) {
      let hasDraft = false;
      const postIds = [];

      for (let i = 0; i < posts.length; i++) {
        const post = posts[i];
        if ('draft_id' in post) {
          this.postsCalendarService.updateDraftInUI(post);
          hasDraft = true;
        } else if (post.postId !== NEW_POST_ID) {
          postIds.push(post.postId);
        }
      }

      if (deletedIds.length > 0) {
        this.postsCalendarService.removeItemFromUI(deletedIds);
      }

      if (hasDraft) {
        return EMPTY;
      } else if (postIds.length > 0) {
        return this.postsCalendarService.updatePost(postIds);
      } else {
        return this.postsCalendarService.fetchPosts();
      }
    }
    return EMPTY;
  }

  private handleRouteParams(): void {
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      if ('month' in params) {
        const currentViewDate = new Date(this.viewDate.getFullYear(), params['month'], this.viewDate.getDate());
        this.viewDate = currentViewDate;
        this.setDate(currentViewDate, false);
      }
    });
  }

  makeEventFromPost(post: SocialPost): CalendarEvent {
    const scheduled = post.scheduledDateTime > new Date(1971, 0) ? post.scheduledDateTime : null;
    const posted = post.postCreatedDateTime > new Date(1971, 0) ? post.postCreatedDateTime : null;
    const start = posted || scheduled;
    const startDate = new Date(start);
    const postText =
      post?.blogPostCustomization?.siteType.toLowerCase() === SocialServiceType.WORDPRESS
        ? post?.blogPostCustomization?.title
        : post.postText;
    return {
      start: startDate,
      title: postText,
      color: {
        primary: getColorFromService(post.service, false),
        secondary: getColorFromService(post.service, true),
      },
      meta: {
        postServiceUserName: post.name || post.username,
        postService: post.serviceName,
        postServiceIcon: post.serviceIcon,
        postId: post.postId,
        draftId: post.draftId,
        itemId: post.itemId,
        errors: post.isError,
        isDraft: post.isDraft,
        eventType: post.isDraft ? 'DRAFT' : 'POST',
        images: post.imageUrl || post.imageUrls?.toString() || '',
        video: post.videoUrl || '',
      },
      draggable: post.isDraft ? true : startDate > new Date(),
    };
  }

  changeCurrentView(view: CalendarView): void {
    combineLatest([this.postsCalendarService.currentView$, this.viewDate$$])
      .pipe(take(1))
      .subscribe(([previousView, viewDate]) => {
        if (previousView === CalendarView.WEEK && view === CalendarView.DAY) {
          const sunday: Date = new Date(viewDate);
          sunday.setHours(-HOURS_IN_DAY * viewDate.getDay());
          this.setDate(sunday, true);
        }
        this.postsCalendarService.setCurrentView(view);
      });
  }

  getStartDate(): Date {
    const date = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth(), 1, 0, 0, 0, 0);
    date.setHours(-HOURS_IN_DAY * date.getDay());
    this.setToUTCMidnight(date);
    return date;
  }

  getEndDate(): Date {
    const date = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth() + 1, 1, 0, 0, 0, 0);
    date.setHours(HOURS_IN_DAY * (DAYS_IN_WEEK - date.getDay()));
    this.setToUTCMidnight(date);
    return date;
  }

  getTimeFromDate(date: Date): string {
    let ampm = 'am';
    let hours = date.getHours();
    if (hours >= 12) {
      ampm = 'pm';
    }
    if (hours > 12) {
      hours = hours - 12;
    }
    return hours.toString() + ':' + ('0' + date.getMinutes()).slice(-2) + ampm;
  }

  eventClicked(event: CalendarEvent): void {
    const posts = this.postsCalendarService.feedItems$$.getValue();
    const { isDraft: eIsDraft, postId: ePostId, draftId: eDraftId, itemId: eItemId } = event.meta;
    const foundPost = eIsDraft
      ? posts.find((post: SocialPost) => post.draftId === eDraftId && post.itemId === eItemId)
      : posts.find((post: SocialPost) => post.postId === ePostId);

    const postsInGroup = posts.filter((post: SocialPost) => post.draftId === eDraftId) || [];
    const isGroupEvent = eIsDraft && postsInGroup.length > 1;

    this.dialog.open(SocialPostPreviewComponent, {
      data: {
        itemData: foundPost,
        isReadOnly: this.selectedView === CalendarView.DAY && isGroupEvent,
        triggeredFrom: 'Calendar',
      },
      autoFocus: false,
      maxHeight: '90vh',
    } as MatDialogConfig);
  }

  dayClicked(day: MonthViewDay, clickEvent: MouseEvent): void {
    if (this.draggedEvent) {
      this.draggedEvent = undefined;
      return;
    }

    if (day.date.getMonth() !== this.viewDate.getMonth()) {
      this.setDate(day.date);
      return;
    }

    const clickedDate = new Date(day.date.getFullYear(), day.date.getMonth(), day.date.getDate());
    if (clickedDate < this.today) {
      return;
    }

    const currentViewDate = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth(), this.viewDate.getDate());
    if (currentViewDate.getTime() !== clickedDate.getTime()) {
      this.setDate(clickedDate, true);
    }

    const date = new Date(day.date);
    const now = new Date();
    date.setHours(now.getHours());
    date.setMinutes(now.getMinutes());
    date.setSeconds(now.getSeconds());

    if (this.isUWM || (!this.proFlag && this.noUpgradePath)) {
      this.composerSettingsService.showComposer({
        date: date,
        triggeredFrom: 'Calendar',
      });
    } else {
      const xPos = clickEvent?.clientX;
      const yPos = clickEvent?.clientY;
      this.openWorkflowDialog(xPos, yPos, date, 'Calendar');
    }
  }

  hourSegmentClicked(date: Date, clickEvent: MouseEvent): void {
    if (date.getMonth() !== this.viewDate.getMonth()) {
      this.setDate(date);
      return;
    }

    const clickedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    if (clickedDate < this.today) {
      return;
    }

    const currentViewDate = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth(), this.viewDate.getDate());
    if (currentViewDate.getTime() !== clickedDate.getTime()) {
      this.setDate(clickedDate);
    }

    const composerDate = new Date(date);
    const now = new Date();
    composerDate.setSeconds(now.getSeconds());

    if (this.isUWM || (!this.proFlag && this.noUpgradePath)) {
      this.composerSettingsService.showComposer({
        date: composerDate,
        triggeredFrom: 'Calendar',
      });
    } else {
      const xPos = clickEvent?.clientX;
      const yPos = clickEvent?.clientY;
      this.openWorkflowDialog(xPos, yPos, composerDate, 'Calendar');
    }
  }

  getFeedItem(socialPost: SocialPost): FeedItem {
    return {
      data: socialPost,
      deleting: false,
      commentSubmitting: false,
      statsFetched: true,
      isDraft: false,
    };
  }

  /**
   * dayMoreClicked opens a pop-up (modal) when user clicks on `+n more` on a calendar day with more than 3 posts scheduled,
   * or use the group paramenter as source
   */
  dayMoreClicked(day: MonthViewDay, event?: Event, group: any = null): void {
    event.stopPropagation();
    if (day.date.getMonth() !== this.viewDate.getMonth()) {
      this.setDate(day.date);
      return;
    }

    const currentViewDate = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth(), this.viewDate.getDate());
    const clickedDate = new Date(day.date.getFullYear(), day.date.getMonth(), day.date.getDate());
    if (currentViewDate.getTime() !== clickedDate.getTime()) {
      this.viewDate$$.next(clickedDate);
    }

    const groupedPostsList = this.buildGroupedPostsList(day, group);

    this.authUrls$.subscribe((authUrls) => {
      const data = {
        authUrls: authUrls,
        groupedPostsList: groupedPostsList,
        date: day.date,
        triggeredFrom: 'Calendar',
      };
      this.dialog.open(ManyPostsComponent, {
        data: data,
        autoFocus: false,
        panelClass: 'many-posts-dialog',
      } as MatDialogConfig);
    });
  }

  private buildGroupedPostsList(day: MonthViewDay, group: any): FeedItem[][] {
    const groupedPostsList = [];
    const posts = this.postsCalendarService.feedItems$$.getValue();

    for (const eventGroup of (day as any).eventGroups) {
      const postGroup = [];
      for (const singleEvent of eventGroup.events) {
        const { isDraft: eIsDraft, postId: ePostId, draftId: eDraftId, itemId: eItemId } = singleEvent.event.meta;

        // We want to detect if current day event is part of a click group (Group was clicked)
        if (group) {
          const isPartOfGroup = eIsDraft
            ? group.events.some((ev) => ev.event.meta.draftId === eDraftId && ev.event.meta.itemId === eItemId)
            : group.events.some((ev) => ev.event.meta.postId === ePostId);

          if (!isPartOfGroup) break;
        }

        const foundPost = eIsDraft
          ? posts.find((post: SocialPost) => post.draftId === eDraftId && post.itemId === eItemId)
          : posts.find((post: SocialPost) => post.postId === ePostId);

        if (foundPost) {
          const fullPost = this.getFeedItem(foundPost);

          // Separate out errored posts of a group, like the feeds.
          if ((fullPost.data as SocialPost).isError) {
            groupedPostsList.push([fullPost]);
          } else {
            postGroup.push(fullPost);
          }
        }
      }

      if (postGroup.length > 0) {
        groupedPostsList.push(postGroup);
      }
    }

    return groupedPostsList;
  }

  /**
   * groupClicked opens a pop-up (modal) when user clicks on a grouped post in the calendar week view.
   */
  groupClicked(event: any): void {
    if (event.start.getMonth() !== this.viewDate.getMonth()) {
      this.setDate(event.start);
      return;
    }

    const currentViewDate = new Date(this.viewDate.getFullYear(), this.viewDate.getMonth(), this.viewDate.getDate());
    const clickedDate = new Date(event.start.getFullYear(), event.start.getMonth(), event.start.getDate());
    if (currentViewDate.getTime() !== clickedDate.getTime()) {
      this.viewDate$$.next(clickedDate);
    }

    const postGroup = this.buildPostGroupFromEvent(event);
    const feedListItem = {
      landingPageData: postGroup,
    } as FeedListItem;

    this.authUrls$.subscribe((authUrls) => {
      const data = {
        cardType: CardType.calendar,
        feedListItem: feedListItem,
        authUrls: authUrls,
      };

      const dialog = this.dialog.open(GroupedPostsDialogComponent, {
        data: data,
        autoFocus: false,
      } as MatDialogConfig);

      dialog.componentInstance.deleteFeedListItemEvent.pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.removeFeedItem(feedListItem);
      });

      dialog.componentInstance.deleteSelectedFeedListItemEvent
        .pipe(takeUntil(this.destroy$))
        .subscribe((posts: SocialPost[]) => {
          this.removeSelectedPosts(posts);
          this.socialPostFeedService.deleteGroupPosts(posts);
        });

      dialog.componentInstance.editAllGroupedPostsEvent.pipe(takeUntil(this.destroy$)).subscribe((composerSettings) => {
        this.editPosts(composerSettings);
      });
    });
  }

  private buildPostGroupFromEvent(event: any): FeedItem[] {
    const postGroup = [];
    const posts = this.postsCalendarService.feedItems$$.getValue();

    for (const innerEvent of event.eventGroups.events) {
      const { isDraft: eIsDraft, postId: ePostId, draftId: eDraftId, itemId: eItemId } = innerEvent.event2.meta;
      const foundPost = eIsDraft
        ? posts.find((post: SocialPost) => post.draftId === eDraftId && post.itemId === eItemId)
        : posts.find((post: SocialPost) => post.postId === ePostId);

      if (foundPost) {
        postGroup.push(this.getFeedItem(foundPost));
      }
    }

    return postGroup;
  }

  removeSelectedPosts(posts: SocialPost[]): void {
    const socialPostIds = posts.map((post) => post.postId);
    this.postsCalendarService.removePosts(socialPostIds);
  }

  removeFeedItem(feedItem: FeedListItem): void {
    const socialPosts = feedItem.landingPageData.filter((f) => !f.isDraft).map((f) => f.data as SocialPost) || [];

    const socialDrafts = feedItem.landingPageData.filter((f) => f.isDraft) || [];

    if (socialPosts.length > 0) {
      this.socialPostFeedService.deleteGroupPosts(socialPosts);
      this.postsCalendarService.removePosts(socialPosts.map((s) => s.postId));
    }

    if (socialDrafts.length > 0) {
      // drafts are coming in groups which has same draft_id and separate service,
      // so deleting one will delete all in the group
      this.socialPostFeedService.deleteDraft(socialDrafts[0]);
      this.postsCalendarService.removeDrafts(socialDrafts.map((s) => (s.data as Draft)?.draft_id));
    }
  }

  showEmptyEvent(events: any[]): boolean {
    return events.length === 1 && events[0].event.meta?.isDraft && !events[0].event.meta?.postServiceIcon;
  }

  editPosts(composerSettings: ComposerSettings): void {
    this.composerSettingsService.showComposer(composerSettings);
  }

  dateTimeChanged({ event, newStart, newEnd }: CalendarEventTimesChangedEvent): void {
    const events = event['siblings'] || [{ event: event }];

    if (newStart < new Date()) {
      this.snackBarService.errorSnack('Cannot schedule for a time in the past.');
      return;
    }

    for (let i = 0; i < events.length; i++) {
      const event = events[i].event;

      if (!event.draggable || newStart.getTime() === event.start.getTime()) {
        this.draggedEvent = event;
        return;
      }

      event.start = newStart;
      event.end = newEnd;
      this.refresh.next();
    }

    this.postsCalendarService.editPostDate(events, newStart).pipe(takeUntil(this.destroy$)).subscribe();
  }

  isActiveDay(date: Date): boolean {
    return date.getMonth() === this.viewDate.getMonth() && date.getDate() === this.viewDate.getDate();
  }

  beforeMonthViewRender(event: CalendarMonthViewBeforeRenderEvent): void {
    event.body.forEach((cell: MonthViewDay) => {
      const groups = this.groupEventsByKey(cell.events);

      cell['eventGroups'] = Object.keys(groups).map((key) => {
        const events = groups[key] as { event: CalendarEvent }[];
        const text = events[0].event.title;
        const draggable = events.filter((e) => e.event.start <= new Date()).length === 0;

        events.forEach((e) => {
          e.event['siblings'] = events;
        });

        return {
          text: text,
          events: events,
          isDraggable: draggable || events[0].event.draggable,
        } as EventGroup;
      });
    });
  }

  private groupEventsByKey(events: CalendarEvent[]): Record<string, { event: CalendarEvent }[]> {
    const groups = {};

    events.forEach((ev: CalendarEvent) => {
      const idx = this.postsCalendarService.getEventCalendarGroupKey(ev);
      groups[idx] = groups[idx] || [];
      groups[idx].push({ event: ev });
    });

    return groups;
  }

  beforeWeekViewRender(weekView: WeekView): void {
    const groups = this.groupEventsForWeekView(weekView.period.events);
    const doneArray = [];

    // For each set of events, give the first the eventGroup information, remove the rest.
    let i = 0;
    while (i < weekView.period.events.length) {
      const cEvent = weekView.period.events[i];
      const eventKey = this.postsCalendarService.getEventCalendarGroupKey(cEvent);

      if (doneArray.indexOf(eventKey) < 0) {
        Object.keys(groups).map((k) => {
          if (k === eventKey) {
            const text = cEvent.title;
            const events = groups[k].map((item) => ({ event: item.event2 }));
            doneArray.push(k);
            cEvent['eventGroups'] = { text: text, events: events, isDraggable: false } as EventGroup;
          }
        });

        // Adjust width for the new event
        this.adjustWeekViewEventWidth(weekView, cEvent);
        i++;
      } else {
        weekView.period.events.splice(i, 1);
      }
    }
  }

  private groupEventsForWeekView(events: CalendarEvent[]): Record<string, { event2: CalendarEvent }[]> {
    const groups = {};

    events.forEach((cEvent: CalendarEvent) => {
      const key = this.postsCalendarService.getEventCalendarGroupKey(cEvent);
      groups[key] = groups[key] || [];
      groups[key].push({ event2: cEvent });
    });

    return groups;
  }

  private adjustWeekViewEventWidth(weekView: WeekView, cEvent: CalendarEvent): void {
    const length = cEvent['eventGroups'].events.length;
    const weekDay = cEvent.start.getDay();
    const objects = weekView.hourColumns[weekDay].events;

    objects.forEach((widthEvent: WeekViewTimeEvent) => {
      if (cEvent === widthEvent.event) {
        widthEvent.width = widthEvent.width * length;
      }
    });
  }

  setDate(date: Date, keepLoaded?: boolean): void {
    keepLoaded = keepLoaded || false;
    this.viewDate$$.next(date);

    if (!keepLoaded) {
      this.postsCalendarService
        .fetchFeedItems(this.getStartDate(), this.getEndDate())
        .pipe(takeUntil(this.destroy$))
        .subscribe();

      this.postsCalendarService
        .fetchDraftItems(this.getStartDate(), this.getEndDate())
        .pipe(takeUntil(this.destroy$))
        .subscribe();
    }
  }

  navPrevious(): void {
    combineLatest([this.currentView$, this.viewDate$$])
      .pipe(take(1))
      .subscribe(([currentView, viewDate]) => {
        switch (currentView) {
          case CalendarView.MONTH:
            this.setDate(new Date(viewDate.getFullYear(), viewDate.getMonth() - 1, 1, 0, 0, 0, 0), false);
            break;
          case CalendarView.WEEK: {
            const keepLoadedWeek = viewDate.getDate() > 7;
            const newDateWeek = new Date(viewDate);
            newDateWeek.setHours(-HOURS_IN_DAY * 6);
            this.setToUTCMidnight(newDateWeek);
            this.setDate(newDateWeek, keepLoadedWeek);
            break;
          }
          default: {
            const keepLoaded = viewDate.getDate() > 1;
            const newDate = new Date(viewDate);
            newDate.setHours(-HOURS_IN_DAY);
            this.setDate(newDate, keepLoaded);
          }
        }
      });
  }

  navNext(): void {
    combineLatest([this.currentView$, this.viewDate$$])
      .pipe(take(1))
      .subscribe(([currentView, viewDate]) => {
        switch (currentView) {
          case CalendarView.MONTH:
            this.setDate(new Date(viewDate.getFullYear(), viewDate.getMonth() + 1, 1, 0, 0, 0, 0), false);
            break;
          case CalendarView.WEEK: {
            const endOfMonthWeek = new Date(viewDate.getFullYear(), viewDate.getMonth() + 1, 0, 0, 0, 0, 0).getDate();
            const keepLoadedWeek = endOfMonthWeek - viewDate.getDate() > 6;
            const newDateWeek = new Date(viewDate);
            const addDays = viewDate.getDay() < 4 ? 9 : viewDate.getDay() + 2;
            newDateWeek.setHours(HOURS_IN_DAY * addDays);
            this.setToUTCMidnight(newDateWeek);
            this.setDate(newDateWeek, keepLoadedWeek);
            break;
          }
          default: {
            const endOfMonth = new Date(viewDate.getFullYear(), viewDate.getMonth() + 1, 0, 0, 0, 0, 0).getDate();
            const keepLoaded = viewDate.getDate() < endOfMonth;
            const newDate = new Date(viewDate);
            newDate.setHours(HOURS_IN_DAY);
            this.setDate(newDate, keepLoaded);
          }
        }
      });
  }

  navToday(): void {
    this.setDate(new Date());
  }

  refreshPosts(): void {
    this.setDate(this.viewDate, false);
  }

  setToUTCMidnight(date: Date): void {
    date.setUTCHours(0);
    date.setUTCMinutes(0);
    date.setUTCSeconds(0);
    date.setUTCMilliseconds(0);
  }

  handleCalendarCreateReport(): void {
    this.dialog
      .open(CreateReportV2DialogComponent, {
        autoFocus: false,
        width: '500px',
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          const req = this.buildCreateReportRequest(result);

          this.socialPostFeedAPIService
            .createReport(req)
            .pipe(
              catchError(() => {
                this.snackBarService.errorSnack(this.translateService.instant('POSTS.REPORT.CREATE_DIALOG.ERROR'));
                return EMPTY;
              }),
              takeUntil(this.destroy$),
            )
            .subscribe((response: CreateReportResponse) => {
              const data = {
                URL: response.report_url,
                expires: response.expires_datetime,
              };
              this.dialog.open(CopyReportDialogComponent, {
                data,
                maxWidth: '500px',
              });
            });
        }
      });
  }

  buildCreateReportRequest(data: any): CreateReportRequest {
    const request: CreateReportRequest = {
      accountGroupId: this.configService.accountGroupId,
      actionType: data?.postType,
      startDateTime: data?.dateRange?.start,
      endDateTime: data?.dateRange?.end,
      socialServiceIds: [],
      gmbLocationInfo: '',
    };

    if (request.startDateTime) {
      request.startDateTime = moment(request.startDateTime).format('YYYY-MM-DDTHH:mm:ss') + 'Z';
    }

    if (request.endDateTime) {
      request.endDateTime = moment(request.endDateTime).format('YYYY-MM-DDTHH:mm:ss') + 'Z';
    }

    if (request.socialServiceIds.length === 0) {
      this.socialServiceService.availablePostableServices$.pipe(take(1)).subscribe((services: any[]) => {
        services.forEach((service) => {
          if (service.ssid.startsWith('accounts/')) {
            request.gmbLocationInfo = `${service.ssid}:${service.googleUserId}`;
          } else {
            request.socialServiceIds.push(service.ssid);
          }
        });
      });
    }

    return request;
  }

  /*Only interpret the event as drag/drop if the coordinates are out of the threshold
   * We can improve this check when revamping this component using OnPush to avoid a lot of event handling and pass the
   * actual date*/
  public dragThresholdValidator({ x, y }: DialogPosition): boolean {
    return Math.abs(x) > MIN_DRAG_THRESHOLD || Math.abs(y) > MIN_DRAG_THRESHOLD;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
