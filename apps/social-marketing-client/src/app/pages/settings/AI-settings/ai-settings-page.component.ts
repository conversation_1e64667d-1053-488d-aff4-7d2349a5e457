import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { Router } from '@angular/router';
import { DataUsageSettingComponent } from './data-usage/data-usage-setting.component';
import { SMFeaturesService } from '../../../core/features.service';
import { SharedModule } from '../../../shared/shared.module';
import { SmAiKnowledgeBaseComponent } from './sm-ai-knowledge-base/sm-ai-knowledge-base.component';
import { MatCardModule } from '@angular/material/card';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { StickyFooterComponent } from '../../../shared/sticky-footer/sticky-footer.component';
import { AISettingsService } from './ai-settings.service';
import { CustomInstructionsComponent } from './custom-instructions/custom-instructions.component';
import { MatIcon } from '@angular/material/icon';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { PopoverPositions } from '@vendasta/galaxy/popover';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-ai-settings-page',
  imports: [
    CommonModule,
    TranslateModule,
    DataUsageSettingComponent,
    SharedModule,
    SmAiKnowledgeBaseComponent,
    MatCardModule,
    GalaxyPageModule,
    StickyFooterComponent,
    CustomInstructionsComponent,
    MatIcon,
    GalaxyTooltipModule,
  ],
  providers: [MatTabsModule],
  templateUrl: './ai-settings-page.component.html',
  styleUrl: './ai-settings-page.component.scss',
  standalone: true,
})
export class AiSettingsPageComponent {
  constructor(
    private router: Router,
    public featuresService: SMFeaturesService,
    private sharedService: AISettingsService,
  ) {}

  tabChanged(event: MatTabChangeEvent): void {
    const agid = 'AG-LMSQZXG2DG';
    if (event.index === 0) {
      this.router.navigateByUrl(`/account/${agid}/settings/custom-instructions`);
    } else if (event.index === 1) {
      this.router.navigate(['/data-usage-route']);
    }
  }

  submit(): void {
    this.sharedService.triggerSave();
  }

  protected readonly PopoverPositions = PopoverPositions;
}
