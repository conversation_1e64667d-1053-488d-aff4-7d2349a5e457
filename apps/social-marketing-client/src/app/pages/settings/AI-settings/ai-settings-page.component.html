<app-page [pageTitle]="'NAV.AI_SETTINGS' | translate">
  <div class="main-wrapper">
    <div class="config-cards">
      <app-sm-ai-knowledge-base *ngIf="featuresService.enableAiKnowledge$ | async"></app-sm-ai-knowledge-base>

      <div>
        <mat-card>
          <mat-card-header>
            <mat-card-title
              >{{ 'AI_SETTINGS.CONTENT_INSTRUCTION.AI_TITLE' | translate }}
              <mat-icon
                [glxyTooltip]="'AI_SETTINGS.CONTENT_INSTRUCTION.DESCRIPTION' | translate"
                [tooltipPositions]="[PopoverPositions.Right, PopoverPositions.Bottom]"
                [highContrast]="false"
              >
                info_outline
              </mat-icon></mat-card-title
            >
          </mat-card-header>
        </mat-card>
        <div>
          <app-custom-instructions></app-custom-instructions>
        </div>
      </div>

      <div *ngIf="featuresService.enableAiDataConsent$ | async">
        <app-data-usage-setting></app-data-usage-setting>
      </div>
    </div>
    <app-sticky-footer (submitClick)="submit()" [ctaRight]="true" [enableCancelButton]="false"></app-sticky-footer>
  </div>
</app-page>
