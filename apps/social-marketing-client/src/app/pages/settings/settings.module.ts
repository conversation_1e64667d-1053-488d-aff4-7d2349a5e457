import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';

import { ConnectionSettingsComponent } from './connection-settings/connection-settings.component';
import { InterestingContentSettingsComponent } from './interesting-content/interesting-content.component';
import { InterestingContentService } from './interesting-content/interesting-content.service';
import { RssFeedComponent } from './interesting-content/rss-feed.component';
import { LeadSearchesSettingsComponent } from './lead-searches/lead-searches.component';
import { LeadResponsesSettingsComponent } from './lead-responses/lead-responses.component';
import { LeadResponseComponent } from './lead-responses/lead-response.component';
import { LeadResponsesInfoDialogComponent } from './lead-responses/dialogs/lead-responses-info.component';
import { LeadResponsesService } from './lead-responses/lead-responses.service';
import { ServiceContainerComponent } from './connection-settings/service-container.component';
import { ServiceComponent } from './connection-settings/service.component';
import { DeleteServiceDialogComponent } from './connection-settings/delete-dialog.component';
import { SocialProfileService } from '../../shared/social-profile/social-profile.service';
import { RssFeedInfoDialogComponent } from './interesting-content/dialogs/rss-feed-info.component';
import { AddSocialPagesComponent } from './add-social-pages/add-social-pages.component';
import { AddSocialPagesService } from './add-social-pages/add-social-pages.service';
import { SharedModule } from '../../shared/shared.module';
import { RssLibraryDialogComponent } from './interesting-content/rss-library/rss-library.component';
import { ComposerModule } from '../../composer/composer.module';
import { AddPagesResultsDialogComponent } from './add-social-pages/results-dialog/results-dialog.component';
import { InstagramApiService } from './add-social-pages/instagram.service';
import { DebugAccountDialogComponent } from './connection-settings/debug/debug.component';
import { TroubleshootDialogComponent } from './add-social-pages/troubleshoot-dialog/troubleshoot-dialog.component';
import { IgIncorrectlyConnectedDialogComponent } from './add-social-pages/ig-incorrectly-connected-dialog/ig-incorrectly-connected-dialog.component';
import { NetworkErrorDialogComponent } from './add-social-pages/network-error-dialog/network-error-dialog.component';
import { CircularLogoComponent } from '../../shared/circular-logo/circular-logo.component';
import { CuratedContentChannelComponent } from './curated-content-channel/curated-content-channel.component';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { CustomInstructionsComponent } from './AI-settings/custom-instructions/custom-instructions.component';
import { MatTabsModule } from '@angular/material/tabs';
import { AiKnowledgeModule } from '@galaxy/ai-knowledge';
import { AI_KNOWLEDGE_CONFIG } from './AI-settings/sm-ai-knowledge-base/providers';
import { WordpressBlogChannelComponent } from './wordpress-blog-channel/wordpress-blog-channel.component';
import { WordpressBlogChannelService } from './wordpress-blog-channel/wordpress-blog-channel.service';
import { ConnectionSettingsService } from './connection-settings/connection-settings.service';
import { WordpressPluginService } from '../../shared/wordpress-plugin/wordpress-plugin.service';
import { WarningBannerComponent } from '../../shared/warning-banner/warning-banner.component';
import { AiSettingsPageComponent } from './AI-settings/ai-settings-page.component';

@NgModule({
  imports: [
    ComposerModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    MatCardModule,
    MatTabsModule,
    MatButtonModule,
    MatDialogModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatChipsModule,
    MatRadioModule,
    MatCheckboxModule,
    MatSelectModule,
    MatMenuModule,
    SharedModule,
    TranslateModule,
    GalaxyTooltipModule,
    CircularLogoComponent,
    GalaxyFormFieldModule,
    WarningBannerComponent,
    AiKnowledgeModule.forRoot({
      config: AI_KNOWLEDGE_CONFIG,
    }),
    CustomInstructionsComponent,
    AiSettingsPageComponent,
  ],
  declarations: [
    ConnectionSettingsComponent,
    InterestingContentSettingsComponent,
    RssFeedComponent,
    ServiceContainerComponent,
    ServiceComponent,
    DeleteServiceDialogComponent,
    RssFeedInfoDialogComponent,
    RssLibraryDialogComponent,
    LeadSearchesSettingsComponent,
    LeadResponsesSettingsComponent,
    LeadResponsesInfoDialogComponent,
    LeadResponseComponent,
    AddSocialPagesComponent,
    AddPagesResultsDialogComponent,
    DebugAccountDialogComponent,
    TroubleshootDialogComponent,
    IgIncorrectlyConnectedDialogComponent,
    NetworkErrorDialogComponent,
    CuratedContentChannelComponent,
    WordpressBlogChannelComponent,
  ],
  exports: [AddSocialPagesComponent, ConnectionSettingsComponent],
  providers: [
    SocialProfileService,
    InterestingContentService,
    AddSocialPagesService,
    LeadResponsesService,
    InstagramApiService,
    WordpressBlogChannelService,
    ConnectionSettingsService,
    WordpressPluginService,
  ],
})
export class SettingsModule {}
