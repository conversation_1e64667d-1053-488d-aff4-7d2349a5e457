import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { EmbedComposeComponent } from './compose/compose.component';
import { EmbedConnectionSettingsComponent } from './connection-settings/connection-settings.component';
import { EmbedAddSocialPagesComponent } from './add-social-pages/add-social-pages.component';
import { ComposerModule } from '../../composer/composer.module';
import { SharedModule } from '../../shared/shared.module';
import { CommonModule } from '@angular/common';
import { SettingsModule } from '../settings/settings.module';
import { EmbedPostPerformanceComponent } from './post-performance/post-performance.component';
import { PostPerformanceModule } from '../analytics/post-performance/post-performance.module';
import { EmbedPostsCalendarComponent } from './posts/calendar/posts-calendar.component';
import { PostsModule } from '../posts/posts.module';
import { MatIconModule } from '@angular/material/icon';
import { OverlayModule } from '@angular/cdk/overlay';
import { BulkUploadPostsService } from '../../composer/components/bulk-upload/bulk-upload-posts.service';

@NgModule({
  imports: [
    ComposerModule,
    SharedModule,
    CommonModule,
    SettingsModule,
    PostPerformanceModule,
    SettingsModule,
    PostsModule,
    MatIconModule,
    OverlayModule,
  ],
  declarations: [
    EmbedAddSocialPagesComponent,
    EmbedConnectionSettingsComponent,
    EmbedComposeComponent,
    EmbedPostPerformanceComponent,
    EmbedComposeComponent,
    EmbedPostsCalendarComponent,
  ],
  providers: [BulkUploadPostsService],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class EmbedModule {}
