import { Overlay } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap, Params } from '@angular/router';
import {
  Location,
  MultilocationPost,
  MultilocationPostsService,
  PostCustomizationV2Interface,
} from '@vendasta/social-posts';
import { Observable, combineLatest, iif, of } from 'rxjs';
import { filter, map, mapTo, skipWhile, switchMap, take, tap } from 'rxjs/operators';
import { ComposerStoreService } from '../../../composer/composer-store.service';
import { ComposerComponent } from '../../../composer/composer.component';
import { ComposerSettings, FileType } from '../../../composer/interfaces';
import { GmbCtaOptions, GmbEventOptions, GmbOptions } from '../../../composer/models/gmb-options';
import { ConfigService, WhitelabelInfo } from '../../../core';
import { Customization } from '../../../core/post/customization';
import { SocialPost, SocialServiceName } from '../../../core/post/post';
import { SocialPostActionService } from '../../../core/post/post-action.service';
import { PostableService } from '../../../shared/social-service/social-service.service';
import { WorkflowType } from '../../../composer/post';
import { BulkUploadPostsComponent } from '../../../composer/components/bulk-upload/bulk-upload-posts.component';
import { CommonUtils } from '../../../composer/shared/utils/common-utils';

@Component({
  templateUrl: './compose.component.html',
  styleUrls: ['./compose.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class EmbedComposeComponent implements OnInit {
  whiteLabelInfo$: Observable<WhitelabelInfo>;
  showSuccess$: Observable<boolean>;

  constructor(
    private configService: ConfigService,
    private route: ActivatedRoute,
    private socialPostActionService: SocialPostActionService,
    private composerStoreService: ComposerStoreService,
    private cdr: ChangeDetectorRef,
    private overlayService: Overlay,
    private multilocationPostsService: MultilocationPostsService,
  ) {}

  ngOnInit(): void {
    const multiLocationId$ = this.route.queryParamMap.pipe(map((x: ParamMap) => x.get('multilocationId')));
    const singleLocationId$ = this.route.params.pipe(map((params: Params) => this.getPostId(params)));
    const workflow$ = this.route.queryParamMap.pipe(map((x: ParamMap) => x.get('workflow')));
    this.showSuccess$ = combineLatest([multiLocationId$, singleLocationId$, workflow$]).pipe(
      switchMap(([multiLocationId, singleLocationId, workflow]) => {
        if (multiLocationId) {
          return this.multilocationPostsService.getMultilocationPost(this.configService.brandId, multiLocationId).pipe(
            map((getMultilocationPostsResponse) => {
              const multilocationPost = getMultilocationPostsResponse['multilocationPost'];
              let media = [];
              if (multilocationPost.originalMedia && multilocationPost.originalMedia.length) {
                media = multilocationPost.originalMedia.map((imageUrl) => {
                  return { url: imageUrl, fileType: FileType.IMAGE };
                });
              }

              if (multilocationPost.originalVideos && multilocationPost.originalVideos.length) {
                let fileType = FileType.VIDEO;
                if (multilocationPost.originalVideos[0].indexOf('video') === -1) {
                  fileType = FileType.GIF;
                }
                media.push({ url: multilocationPost.originalVideos[0], fileType: fileType });
              }

              return {
                isEditing: true,
                activeLocations: this.getActiveLocations(multilocationPost),
                groupedCustomization: new Customization({
                  postText: multilocationPost.originalText,
                  scheduledDate: multilocationPost.originalScheduledDate,
                  uploadedMediaObjects: media,
                  gmbOptions: multilocationPost?.customization
                    ? this.buildMLGMBOptions(multilocationPost.customization)
                    : null,
                }),
                multilocationId: multiLocationId,
              } as ComposerSettings;
            }),
            switchMap((composerSettings) => this.showComposer(composerSettings)),
          );
        } else if (CommonUtils.convertRequestWorkflowType(workflow) === WorkflowType.BULK_UPLOAD_WORKFLOW) {
          const setting: ComposerSettings = {
            workFlowType: WorkflowType.BULK_UPLOAD_WORKFLOW,
          };
          return this.showComposer(setting);
        }
        return iif(
          () => !!singleLocationId,
          this.socialPostActionService.fetchPostByPostId(singleLocationId),
          of(null),
        ).pipe(
          switchMap((post) => {
            const settings = this.buildEditComposerSettings(post);
            return this.showComposer(settings);
          }),
        );
      }),
    );

    this.whiteLabelInfo$ = this.configService.config$.pipe(
      skipWhile((config) => !config.whitelabel_info),
      map((config) => config.whitelabel_info),
    );
  }

  getActiveLocations(mlPost: MultilocationPost): Location[] {
    const activeLocations = mlPost.originalLocations;
    if (mlPost.editedLocations && mlPost.editedLocations.length) {
      mlPost.editedLocations.forEach((editedLoc) => {
        const found = activeLocations.findIndex((i) => i.accountGroupId === editedLoc.accountGroupId);
        if (found !== -1) {
          activeLocations.splice(found, 1);
        }
      });
    }

    if (mlPost.deletedLocations && mlPost.deletedLocations.length) {
      mlPost.deletedLocations.forEach((editedLoc) => {
        const found = activeLocations.findIndex((i) => i.accountGroupId === editedLoc.accountGroupId);
        if (found !== -1) {
          activeLocations.splice(found, 1);
        }
      });
    }
    return activeLocations;
  }

  buildEditComposerSettings(post?: SocialPost): ComposerSettings {
    if (!post) {
      return null;
    }
    let media = [];
    if (post.imageUrls && post.imageUrls.length) {
      media = post.imageUrls.map((imageUrl) => {
        return { url: imageUrl, fileType: FileType.IMAGE };
      });
    }
    if (post.videoUrl) {
      let fileType = FileType.VIDEO;
      if (post.videoUrl.indexOf('video') === -1) {
        fileType = FileType.GIF;
      }
      media.push({ url: post.videoUrl, fileType: fileType });
    }
    const gmbOptions = new GmbOptions();
    const services = new Map<PostableService, string>();
    services.set({ ssid: post.ssid } as PostableService, post.postId);
    if (
      (post.service === SocialServiceName.GMB || post.service === SocialServiceName.GOOGLE_MY_BUSINESS) &&
      (post.callToAction || post.event)
    ) {
      if (post.callToAction) {
        gmbOptions.addCta = !!post.callToAction.url;
        gmbOptions.ctaOptions = {
          ctaUrl: post.callToAction.url,
          action: post.callToAction.type,
        } as GmbCtaOptions;
      }
    }
    if (post.event) {
      gmbOptions.makeEvent = !!post.event.title;
      gmbOptions.eventOptions = {
        endDate: new Date(post.event.endDateTime),
        startDate: new Date(post.event.startDateTime),
        title: post.event.title,
      } as GmbEventOptions;
    }
    return {
      groupedCustomization: new Customization({
        services: services,
        postText: post.postText,
        scheduledDate: post.scheduledDateTime,
        uploadedMediaObjects: media,
        gmbOptions: gmbOptions,
      }),
      isEditing: true,
    };
  }

  buildMLGMBOptions(postCustomization: PostCustomizationV2Interface): GmbOptions {
    const gmbOptions = new GmbOptions();

    if (postCustomization.event) {
      gmbOptions.makeEvent = !!postCustomization.event.title;
      gmbOptions.eventOptions = {
        endDate: new Date(postCustomization.event.end),
        startDate: new Date(postCustomization.event.start),
        title: postCustomization.event.title,
      } as GmbEventOptions;
    }

    if (postCustomization.action) {
      gmbOptions.addCta = !!postCustomization.action.linkUrl;
      gmbOptions.ctaOptions = {
        ctaUrl: postCustomization.action.linkUrl,
        action: postCustomization.action.type,
      } as GmbCtaOptions;
    }

    return gmbOptions;
  }

  showComposer(settings?: ComposerSettings): Observable<boolean> {
    const overlayRef = this.overlayService.create();
    let portal = null;
    switch (settings?.workFlowType) {
      case WorkflowType.BULK_UPLOAD_WORKFLOW: {
        portal = new ComponentPortal(BulkUploadPostsComponent);
        break;
      }
      default:
        portal = new ComponentPortal(ComposerComponent);
    }
    const compRef = overlayRef.attach(portal);
    if (this.configService.brandId) {
      compRef.instance.brandId = this.configService.brandId;
      compRef.instance.showClose = this.route.snapshot.queryParams.showClose === 'true' || true;
      compRef.instance.setAgidOnStore();
      settings?.workFlowType === WorkflowType.BULK_UPLOAD_WORKFLOW
        ? (compRef.instance.workflow = this.route.snapshot.queryParams.workflow)
        : '';
      this.configService.getAgidFromBrand(this.configService.brandId);
    } else {
      compRef.instance.accountGroupId = this.configService.accountGroupId;
      compRef.instance.showClose = this.route.snapshot.queryParams.showClose === 'true' || false;
      compRef.instance.visible = true;
      compRef.instance.showTopNavBar = false;
      compRef.instance.showDraft = false;
      compRef.instance.showTemplate = false;
    }
    const ssids = [];
    if (settings && settings.groupedCustomization) {
      for (const service of Array.from(settings.groupedCustomization.services$$.getValue().keys())) {
        ssids.push(service.ssid);
      }
    }

    compRef.instance.initialize(ssids);
    compRef.instance.showComposer(settings);

    return compRef.instance.visibleEvent.pipe(
      filter((isComposerVisible) => !isComposerVisible),
      mapTo(true),
      tap(() => {
        overlayRef.detach();
      }),
      take(1),
    );
  }

  private getPostId(params: Params): string {
    return params['gmbPostId']
      ? `${params['gmbPostId']}/${params['accountId']}/locations/${params['locationId']}`
      : params['postId'];
  }
}
