import { InjectionToken, inject } from '@angular/core';
import { DiscoverProductsDependencies } from 'discover-products/shared';
import { CountryStateService } from './services/country-state.service';
import { WordpressContentService } from './services/wordpress-content.service';
import { ProductCategoryService } from './services/product-category.service';
import { ProductChangeService } from './services/product-change.service';
import { AppConfigService } from './services/app-config.service';
import { ResellerItemService } from './services/reseller-item.service';
import { MarketsService } from './services/markets.service';
import { StubAccessService } from './services/access.service';
import { MarketplaceApiService } from './services/marketplace.api.service';
import { MarketplaceAppService } from './services/marketplace-app.service';
import { VConfigService } from './services/vconfig.service';

// TODO: add env logic to determine which apps have blank edition id
const appsWithBlankEditionIdDemo = new Set([
  'RM', // Reputation Management
  'MS', // Local SEO
  'SM', // Social Marketing
  'MP-fba21121b71148c9bb33e11fcd92d520', // Customer Voice
  'MP-9cc9f21f0a234a46ad78087fc09f16bc', // Website Pro
]);

export const DiscoverProductsInjectionToken = new InjectionToken<DiscoverProductsDependencies>(
  'dependencies given to discover-products shell client',
  {
    factory: (): DiscoverProductsDependencies => {
      const marketplaceApiService = inject(MarketplaceApiService); // TODO: fix error injecting
      const resellerItemService = inject(ResellerItemService);
      const productChangeService = inject(ProductChangeService);
      const wordpressContentService = inject(WordpressContentService);
      const countryStateService = inject(CountryStateService);
      const marketplaceAppService = inject(MarketplaceAppService);
      const vconfigService = inject(VConfigService);
      const productCategoryService = inject(ProductCategoryService);
      const appConfigService = inject(AppConfigService);
      const marketsService = inject(MarketsService);
      const accessService = inject(StubAccessService);

      // const appEnableDialogService = inject(AppEnableDialogService);
      // const unifiedTOSService = inject(UnifiedTOSService);

      return {
        services: {
          productChangeService: productChangeService,
          wordpressContentService: wordpressContentService,
          countryStateService: countryStateService,
          resellerItemService: resellerItemService,
          marketplaceApiService: marketplaceApiService,
          marketplaceAppService: marketplaceAppService,
          vconfigService: vconfigService,
          appEnableDialogService: null,
          unifiedTOSService: null,
          productCategoryService: productCategoryService,
          appConfigService: appConfigService,
          marketsService: marketsService,
          accessService: accessService,
        },
        appsWithBlankEditionId: appsWithBlankEditionIdDemo,
      };
    },
  },
);
