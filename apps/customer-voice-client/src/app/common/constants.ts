import { Environment } from '@galaxy/core';

export const CUSTOMER_VOICE_DOMAINS = new Map<Environment, string>([
  [Environment.PROD, 'https://customervoice.biz'],
  [Environment.DEMO, 'https://steprep-demo-hrd.appspot.com'],
]);

export const NO_PREFERRED_SITES_SELECTED_ERROR = 'no_preferred_sites_selected';
export const NO_LISTING_BUILDER_ERROR = 'no_listing_builder_activated_for_account';
export const AT_LEAST_ONE_CUSTOMER_DOESNT_HAVE_VALID_PHONE = 'at_least_one_customer_doesnt_have_valid_phone';
export const TOO_MANY_CUSTOMER_SELECTED_FOR_SMS = 'too_many_customer_selected_for_sms';
export const WOULD_GO_OVER_MONTHLY_LIMIT_FOR_SMS = 'would_go_over_monthly_limit_for_sms';

export const MAXIMUM_DAILY_SMS_LIMIT = 150;

export enum ReviewRequestType {
  email = 'email',
  sms = 'sms',
}

export const FACEBOOK_SOURCE_ID = 10050;
export const GOOGLE_SOURCE_ID = 10010;

export const EXPRESS_SOURCES = [FACEBOOK_SOURCE_ID, GOOGLE_SOURCE_ID];

export const VendorAppNames: Map<Environment, Map<string, string>> = new Map<Environment, Map<string, string>>([
  [
    Environment.PROD,
    new Map<string, string>([
      ['VBC', 'Customer List'],
      ['Mobile Kiosk', 'Mobile Kiosk'],
      ['MP-c4974d390a044c28aec31e421aa662b2', 'Customer Voice'],
      ['', 'Customer Voice'],
      ['MP-ee4ea04e553a4b1780caf7aad7be07cd', 'Website Pro'],
      ['MP-HVWNH26HWM8HB45VZFMN46BDQRQXSGH3', 'Movylo'],
      ['MP-VN8Z4RDGHJBJM5VCFQS5TZXPC4B5MBD5', 'ZyraTalk'],
      ['MP-136d75c201bb4c1c894e26c835e6a924', 'Prod Test App'],
      ['Inbox', 'Inbox'],
    ]),
  ],
  [
    Environment.DEMO,
    new Map<string, string>([
      ['VBC', 'Customer List'],
      ['Mobile Kiosk', 'Mobile Kiosk'],
      ['MP-fba21121b71148c9bb33e11fcd92d520', 'Customer Voice'],
      ['', 'Customer Voice'],
      ['MP-FPTX3NN36KNF563MDCKTQNJ86FC5VWDL', 'QuickBooks'],
      ['MP-9cc9f21f0a234a46ad78087fc09f16bc', 'Website Pro'],
      ['Inbox', 'Inbox'],
    ]),
  ],
]);

export enum FilterDateRange {
  LAST_30_DAYS = 'COMMON.DATE_RANGES.LAST_30_DAYS',
  LAST_60_DAYS = 'COMMON.DATE_RANGES.LAST_60_DAYS',
  LAST_90_DAYS = 'COMMON.DATE_RANGES.LAST_90_DAYS',
  LAST_6_MONTHS = 'COMMON.DATE_RANGES.LAST_6_MONTHS',
  LAST_YEAR = 'COMMON.DATE_RANGES.LAST_YEAR',
  ALL_TIME = 'COMMON.DATE_RANGES.ALL_TIME',
}

export const WEBLATE_COMPONENT_NAME = 'customer-voice/customer-voice-client';
