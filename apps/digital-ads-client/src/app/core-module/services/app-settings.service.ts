import { Injectable } from '@angular/core';
import { AppPartnerService, AppSettings } from '@galaxy/marketplace-apps';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { filter, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { catchHttpError } from '../../helpers';
import { AddOnService } from './add-on.service';
import { BusinessInfoService } from './business-info.service';

@Injectable()
export class AppSettingsService {
  private appSettings$$: BehaviorSubject<AppSettings> = new BehaviorSubject<AppSettings>(undefined);
  public appSettings$ = this.appSettings$$.asObservable();
  public appName$: Observable<string>;

  private localAdsAppSettings$$: BehaviorSubject<AppSettings> = new BehaviorSubject<AppSettings>(undefined);
  public localAdsAppSettings$ = this.localAdsAppSettings$$.asObservable();

  constructor(
    private businessInfoService: BusinessInfoService,
    private marketplaceAppService$: AppPartnerService,
    private addonService: AddOnService,
  ) {
    this.businessInfoService.accountGroup$
      .pipe(
        switchMap((ag) =>
          this.marketplaceAppService$.getAppSettings(
            this.addonService.getMarketplaceInfo().appID,
            ag.accountGroupExternalIdentifiers.partnerId,
            ag.accountGroupExternalIdentifiers.marketId,
          ),
        ),
        tap((appSetting) => this.appSettings$$.next(appSetting)),
      )
      .subscribe();

    this.businessInfoService.accountGroup$
      .pipe(
        switchMap((ag) => {
          if (!this.addonService.getMarketplaceInfo()?.localAdsAppID) {
            return of(null);
          }
          return this.marketplaceAppService$
            .getAppSettings(
              this.addonService.getMarketplaceInfo()?.localAdsAppID,
              ag.accountGroupExternalIdentifiers.partnerId,
              ag.accountGroupExternalIdentifiers.marketId,
            )
            .pipe(catchHttpError(400, () => of(null)));
        }),
        tap((appSetting) => this.localAdsAppSettings$$.next(appSetting)),
      )
      .subscribe();

    this.appName$ = this.appSettings$.pipe(
      map((settings) => settings?.branding?.name || 'Advertising Intelligence'),
      shareReplay(1),
    );
  }

  get hideUpgradeTab$() {
    return this.appSettings$.pipe(
      filter((settings) => !!settings),
      map((settings) => settings.editionChange?.hideUpgradeCta ?? false),
    );
  }
}
