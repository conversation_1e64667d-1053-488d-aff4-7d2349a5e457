@use 'design-tokens' as *;

:host {
  display: block;

  ::ng-deep {
    mat-expansion-panel {
      box-sizing: border-box;
    }
  }
}

img {
  height: 24px;
  border-radius: 50%;
}

.custom-card-header {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.card-alert {
  margin-top: $spacing-3;
}

.no-account {
  padding: $spacing-3;
}

.ads-header {
  > glxy-alert {
    margin: $spacing-3 (-$spacing-3) (-$spacing-3);
  }
}

.channel-card-divider {
  padding: $spacing-3 0 $spacing-3 0;
}

.channel-label {
  display: flex;
  align-items: center;
  margin-left: $spacing-2;
}

.ads-channel {
  width: 90%;
  display: flex;
  font-size: $font-preset-3-size;
}

.accounts {
  display: flex;
  flex-direction: column;
}

.mapped-account {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-2 $spacing-3 $spacing-2 $spacing-4;
}

.spinner {
  margin: $spacing-3;
  display: flex;
  height: 48px;
}
