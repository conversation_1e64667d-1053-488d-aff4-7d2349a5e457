<mat-card appearance="outlined" class="ads-header">
  <mat-card-content>
    <div class="custom-card-header">
      <div class="ads-channel">
        <img *ngIf="!!channelInfo.iconPath?.length" [src]="channelInfo.iconPath" alt="icon" />
        <div class="channel-label">{{ channelInfo.channelName }}</div>
      </div>
      <app-account-connector
        [oauthRedirectURL]="oauthRedirectURL$ | async"
        [tokenIsInvalid]="tokenIsInvalid"
        [channelName]="channelInfo.channelName"
      ></app-account-connector>
    </div>
    <glxy-alert
      class="card-alert"
      [type]="'tip'"
      [borderRadius]="false"
      [border]="false"
      *ngIf="(showLoader$ | async) === false && (channelService.connectedAccountsX.results$ | async)?.length === 0"
      i18n="A message prompting the user to connect an account@@SETTINGS.UI.ACCOUNT.CONNECT_AN_ACCOUNT"
    >
      Connect an account to get started.
    </glxy-alert>
  </mat-card-content>
</mat-card>
<ng-container *ngIf="(showLoader$ | async) === false; then account_data; else loading"></ng-container>

<ng-container *ngIf="mappedAccounts?.length > 0">
  <mat-card appearance="outlined" *ngFor="let mappedAccount of mappedAccounts" class="mapped-account">
    <div>{{ mappedAccount.name }}</div>
    <div class="add-account-link">
      <a
        (click)="connectMappedAccount(mappedAccount)"
        mat-button
        color="primary"
        i18n="A button that will take the user to the Connect Account workflow@@SETTINGS.UI.ACCOUNT.CONNECT"
      >
        Connect
      </a>
    </div>
  </mat-card>
</ng-container>

<ng-template #loading>
  <div class="spinner">
    <mat-spinner [diameter]="32"></mat-spinner>
  </div>
</ng-template>

<ng-template #account_data>
  <mat-accordion *ngFor="let account of channelService.connectedAccountsX.results$ | async" class="accounts">
    <app-account-card
      [channelInfo]="channelInfo"
      [oauthRedirectURL]="oauthRedirectURL$ | async"
      [showLoader]="showLoader$ | async"
      [accountInfo]="account"
      (disconnectAccount)="clearAccount($event)"
      (updateAccount)="updateAccount($event)"
      [tokenIsInvalid]="tokenIsInvalid"
      [showDisconnectButton]="showDisconnectButton"
      [channelService]="channelService"
    ></app-account-card>
  </mat-accordion>
</ng-template>
