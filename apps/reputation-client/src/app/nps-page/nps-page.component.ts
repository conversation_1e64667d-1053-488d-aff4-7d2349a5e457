import { Component, computed, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PageModule } from '../shared';
import { combineLatest, Observable } from 'rxjs';
import { CheckboxFilterField, Filters, VaFilterModule } from '@vendasta/uikit';
import { TranslateModule } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { NPSPageService } from './nps-page.service';
import { DateRangePresetPeriod, GalaxyDatepickerModule } from '@vendasta/galaxy/datepicker';
import { DateRange } from '@vendasta/uikit';
import { startWith, switchMap, map } from 'rxjs/operators';
import { FormControl } from '@angular/forms';
import { ProviderDetails } from '@vendasta/reputation';
import { MatChipInputEvent } from '@angular/material/chips';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { NPSCardComponent, NPSProviderComponent } from '@vendasta/reviews';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { MatIcon } from '@angular/material/icon';
import { MatSidenav, MatSidenavModule } from '@angular/material/sidenav';
import { MOBILE_BREAKPOINT } from '../core/constants';
import { BreakpointObserver } from '@angular/cdk/layout';
import { ImageService } from '../core/image.service';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { NgApexchartsModule } from 'ng-apexcharts';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { NpsOverallScoreChartComponent } from './nps-overall-score-chart/nps-overall-score-chart';
import { NpsRollingAverageGraphComponent } from './nps-rolling-average-graph/nps-rolling-average-graph.component';
import { AppConfigService } from '../core';
import { FeatureFlagService } from '@vendasta/businesses';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MODULE_IMPORTS } from '@vendasta/galaxy/tags';

export interface NPSData {
  netPromoterScoreId: string;
  accountGroupId: string;
  score: string;
  comment: string;
  scoreLeftTime: Date;
  created: DateRange;
  updated: DateRange;
  Name: string;
  Email: string;
  PhoneNumber: string;
  Providers?: Observable<string[]>;
}

@Component({
  selector: 'app-nps-page',
  imports: [
    PageModule,
    CommonModule,
    TranslateModule,
    MatCardModule,
    GalaxyDatepickerModule,
    MatButtonModule,
    NPSCardComponent,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyLoadingSpinnerModule,
    GalaxyPageModule,
    MatIcon,
    MatSidenavModule,
    VaFilterModule,
    GalaxyEmptyStateModule,
    NgApexchartsModule,
    GalaxyPopoverModule,
    NpsOverallScoreChartComponent,
    NpsRollingAverageGraphComponent,
    NPSProviderComponent,
    MODULE_IMPORTS,
  ],
  templateUrl: './nps-page.component.html',
  styleUrl: './nps-page.component.scss',
})
export class NPSPageComponent implements OnInit {
  @ViewChild('providerInput') providerInput?: ElementRef<HTMLInputElement>;

  @ViewChild('sidebar', { static: true }) sidebar: MatSidenav;

  showPopover = false;
  filters$: Observable<Filters>;
  npsReviews$: Observable<NPSData[]>;
  enableResendNPSRequest$: Observable<boolean>;
  protected filteredProviders$: Observable<ProviderDetails[]>;
  protected enableDropdownFilter$: Observable<boolean>;
  defaultDatePeriod: DateRangePresetPeriod;
  dateRangeForCustomDefault: DateRange;
  loadingFeed$: Observable<boolean>;
  hasMore$: Observable<boolean>;
  mobile = false;
  noNPSReviewFoundImageURL = this.imageService.getImageSrc('nps_empty_state.svg');
  public isPanelOpen = true;
  public selectedProviders: ProviderDetails[] = [];
  public providerCtrl = new FormControl('');
  public separatorKeysCodes: number[] = [ENTER, COMMA];

  readonly chartData = toSignal(this.npsPageService.npsScoreCard$, { initialValue: null });
  readonly hasNPSData = computed(() => {
    const data = this.chartData();
    return !!data && Object.values(data.current || {}).some((value) => value > 0);
  });

  constructor(
    private breakpointObserver: BreakpointObserver,
    private npsPageService: NPSPageService,
    private imageService: ImageService,
    private configService: AppConfigService,
    private featureFlagService: FeatureFlagService,
  ) {
    this.breakpointObserver
      .observe([`(max-width: ${MOBILE_BREAKPOINT}px)`])
      .subscribe((result) => (this.mobile = result.matches));
    this.enableResendNPSRequest$ = this.configService.config$.pipe(
      switchMap((config) => {
        return this.featureFlagService.checkFeatureFlag(config.partnerId, '', 'nps_resend_request');
      }),
    );

    this.enableDropdownFilter$ = this.configService.config$.pipe(
      switchMap((config) => {
        return this.featureFlagService.checkFeatureFlag(config.partnerId, '', 'ml_team_scorecard_dropdown_filter');
      }),
    );

    this.filteredProviders$ = combineLatest([
      this.npsPageService.providerDetails$,
      this.providerCtrl.valueChanges.pipe(startWith(null)),
    ]).pipe(
      map(([providers, searchValue]) => {
        if (typeof searchValue !== 'string' || !searchValue) {
          return providers;
        }
        const filterValue = searchValue.toLowerCase();
        return providers.filter(
          (p) =>
            `${p.firstName} ${p.lastName}`.toLowerCase().includes(filterValue) ||
            p.providerId.toLowerCase().includes(filterValue),
        );
      }),
    );
  }

  ngOnInit(): void {
    this.dateRangeForCustomDefault = this.npsPageService.dateRangeForCustomDefault;
    this.defaultDatePeriod = DateRangePresetPeriod.custom;
    this.filters$ = this.npsPageService.filters$;
    this.npsReviews$ = this.npsPageService.npsReviews$;
    this.loadingFeed$ = this.npsPageService.loadingReviews$.pipe(startWith(true));
    this.hasMore$ = this.npsPageService.hasMoreReviews$;
    if (!this.mobile && this.filters$) {
      this.sidebar.open();
    }
  }

  get filterType(): string {
    return this.hasNPSData() ? 'filters' : 'date range';
  }

  loadMore(): void {
    this.npsPageService.loadMore();
  }

  filterChanged(field: CheckboxFilterField): void {
    this.npsPageService.filterValueChanged(field);
  }

  dateRangeChanged(field: DateRange): void {
    this.npsPageService.dateRangeChanged(field);
  }

  toggleSidebar(): void {
    this.sidebar.toggle();
    this.npsPageService.toggleSidebar();
  }

  closeMobileSidebar(): void {
    if (this.mobile && this.sidebar.opened) {
      this.sidebar.close();
    }
  }

  togglePanel(): void {
    this.isPanelOpen = !this.isPanelOpen;
  }

  protected addProvider(event: MatChipInputEvent): void {
    const value = (event.value || '').trim().toLowerCase();
    if (!value) return;

    this.npsPageService.providerDetails$.pipe(takeUntilDestroyed()).subscribe((providers) => {
      const match = providers.find(
        (p) => `${p.firstName} ${p.lastName}`.toLowerCase() === value || p.providerId.toLowerCase() === value,
      );
      if (match && !this.isSelected(match.providerId)) {
        this.selectedProviders.push(match);
      }
    });
  }

  protected removeProvider(provider: ProviderDetails): void {
    const index = this.selectedProviders.findIndex((p) => p.providerId === provider.providerId);
    if (index >= 0) {
      this.selectedProviders.splice(index, 1);
      this.npsPageService.selectedProviderIds$$.next(this.selectedProviders);
    }
  }

  public selected(event: MatAutocompleteSelectedEvent): void {
    const provider = event.option.value as ProviderDetails;
    if (!this.isSelected(provider.providerId)) {
      this.selectedProviders.push(provider);
    }
    if (this.providerInput) this.providerInput.nativeElement.value = '';
    this.providerCtrl.setValue(null);
    this.npsPageService.selectedProviderIds$$.next(this.selectedProviders);
  }

  private isSelected(providerId: string): boolean {
    return this.selectedProviders.some((p) => p.providerId === providerId);
  }
}
