export const environment: string = globalThis.environment || 'prod';
export const deployment: string = globalThis.deployment || null;
export const devServer: boolean = globalThis.devServer || false;

const appsWithBlankEditionIdProd = new Set([
  'RM', // Reputation Management
  'MS', // Local SEO
  'SM', // Social Marketing
  'MP-c4974d390a044c28aec31e421aa662b2', // Customer Voice
  'MP-ee4ea04e553a4b1780caf7aad7be07cd', // Website Pro
]);

const appsWithBlankEditionIdDemo = new Set([
  'RM', // Reputation Management
  'MS', // Local SEO
  'SM', // Social Marketing
  'MP-fba21121b71148c9bb33e11fcd92d520', // Customer Voice
  'MP-9cc9f21f0a234a46ad78087fc09f16bc', // Website Pro
]);

// OAndO apps that have a blank pro edition id
export const appsWithBlankEditionId: Set<string> =
  environment === 'demo' ? appsWithBlankEditionIdDemo : appsWithBlankEditionIdProd;
