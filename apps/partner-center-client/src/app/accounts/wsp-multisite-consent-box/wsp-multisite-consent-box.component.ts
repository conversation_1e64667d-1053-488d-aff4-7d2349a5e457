import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateService, TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-wsp-multisite-consent-box',
  templateUrl: './wsp-multisite-consent-box.component.html',
  imports: [CommonModule, TranslateModule],
  styleUrls: ['./wsp-multisite-consent-box.component.scss'],
})
export class MultisiteConsentBoxComponent {
  selectedEditionId = input<string>();
  currentEditionId = input<string>();
  wspMultisiteEditionDemo = input<string>();
  wspMultisiteEditionProd = input<string>();
  wspConsentMessages: { title: string; message: string }[] = [];

  constructor(private translate: TranslateService) {
    this.setWspConsentMessages();
  }

  isMultisiteConsentBox(): boolean {
    const isCurrentMultisiteEdition =
      this.currentEditionId() === this.wspMultisiteEditionDemo() ||
      this.currentEditionId() === this.wspMultisiteEditionProd();
    const isSelectedMultisiteEdition =
      this.selectedEditionId() === this.wspMultisiteEditionDemo() ||
      this.selectedEditionId() === this.wspMultisiteEditionDemo();
    if (!isCurrentMultisiteEdition && isSelectedMultisiteEdition) return true;
    return false;
  }

  isDowngradeConsentBox(): boolean {
    return (
      this.currentEditionId() === this.wspMultisiteEditionDemo() ||
      this.currentEditionId() === this.wspMultisiteEditionProd()
    );
  }

  setWspConsentMessages(): void {
    this.wspConsentMessages = [
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.DOWNGRADE'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.DOWNGRADE'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.BACKUP'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.BACKUP'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.DOMAIN'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.DOMAIN'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.IP_ADDRESS'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.IP_ADDRESS'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.EMAIL'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.EMAIL'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.STAGING'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.STAGING'),
      },
      {
        title: this.translate.instant('MULTISITE_CONSENT_BOX.TITLE.MIGRATION'),
        message: this.translate.instant('MULTISITE_CONSENT_BOX.CONSENT_POINT.MIGRATION'),
      },
    ];
  }
}
