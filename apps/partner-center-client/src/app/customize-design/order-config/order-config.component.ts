import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { Component, Inject, OnInit, QueryList, ViewChildren } from '@angular/core';
import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, ValidationErrors, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { StartManualAutomationDialogComponent } from '@galaxy/automata/shared';
import { Environment, EnvironmentService } from '@galaxy/core';
import { OrderFormField } from '@galaxy/marketplace-apps/v1';
import { TranslateService } from '@ngx-translate/core';
import { Context, EntityType } from '@vendasta/automata';
import { FeatureFlagService } from '@vendasta/businesses';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { SalesOrdersService } from '@vendasta/sales-orders';
import { BUSINESS_USER, FieldBuilderComponent, FORM_FIELDS } from '@vendasta/store';
import { Observable } from 'rxjs';
import { map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { FeatureFlags } from '../../core/features';
import { PartnerService } from '../../core/partner.service';
import {
  CustomSalespersonActionInterface,
  EmailContentInterface,
  OrderConfig,
  TermsOfServiceInterface,
} from './order-config';
import { PreviewEmailDialogComponent } from './preview-email-dialog/preview-email-dialog.component';
import { RestoreWarningDialogComponent } from './restore-warning-dialog/restore-warning-dialog.component';

function getSalesToolHost(env: Environment): string {
  switch (env) {
    case Environment.DEMO:
      return 'https://salestool-demo.appspot.com';
    case Environment.PROD:
      return 'https://salestool-prod.appspot.com';
    default:
      return 'https://salestool-demo.appspot.com';
  }
}

function setInitialValuesForEmailContent(group: UntypedFormGroup, content: EmailContentInterface): void {
  group.setValue({
    subject: content.subject,
    heading: content.heading,
    body: content.body,
    button: content.button,
  });
}

@Component({
  styleUrls: ['order-config.component.scss'],
  templateUrl: './order-config.component.html',
  standalone: false,
})
export class OrderConfigComponent implements OnInit {
  @ViewChildren(FieldBuilderComponent) orderFormFieldFormComponents: QueryList<FieldBuilderComponent>;
  partnerId: string;
  marketId$: Observable<string>;
  config$: Observable<OrderConfig>;
  configForm: UntypedFormGroup;
  customSalespersonActions$: Observable<boolean>;
  salespersonAcceptsPayment$: Observable<boolean>;
  saving = false;
  loading = true;
  uploadUrl: string;
  private uploadPath = '/ajax/v1/sales-orders/file/upload';
  supportedFiledTypes = FORM_FIELDS.filter((f) => f.id !== BUSINESS_USER);

  constructor(
    private readonly salesOrdersService: SalesOrdersService,
    private readonly snackbarService: SnackbarService,
    private readonly partnerService: PartnerService,
    private readonly _route: ActivatedRoute,
    private environmentService: EnvironmentService,
    private feature: FeatureFlagService,
    private translate: TranslateService,
    private readonly dialog: MatDialog,
    @Inject('markets$') readonly markets$: Observable<any[]>,
  ) {}

  static validateWorkflowStepOptions(formGroup: UntypedFormGroup): ValidationErrors | null {
    if (
      !(
        formGroup.controls.allowSendDirectToAdmins.value ||
        formGroup.controls.allowSendToCustomers.value ||
        formGroup.controls.allowOrderFormEditing.value ||
        (formGroup.controls.customAutomationActions as UntypedFormArray).length > 0
      )
    ) {
      return { noStepsSelected: true };
    }
    return null;
  }

  static createCustomAutomationActionFormGroup(customAction?: CustomSalespersonActionInterface): UntypedFormGroup {
    return new UntypedFormGroup({
      label: new UntypedFormControl(customAction?.label, Validators.required),
      automationId: new UntypedFormControl(customAction?.automationId, Validators.required),
    });
  }

  ngOnInit(): void {
    this.uploadUrl = getSalesToolHost(this.environmentService.getEnvironment()) + this.uploadPath;
    this.partnerId = this.partnerService.partnerId;

    this.configForm = new UntypedFormGroup({
      defaultNotesContent: new UntypedFormControl('', []),
      extraFields: new UntypedFormArray([]),
      termsOfServiceFormControl: new UntypedFormControl([], []),
      workflowStepConfig: new UntypedFormGroup(
        {
          allowSendDirectToAdmins: new UntypedFormControl(false, []),
          canChargeSmbOnOrderSubmission: new UntypedFormControl(false, []),
          allowSendToCustomers: new UntypedFormControl(false, []),
          allowCollectPaymentFromCustomers: new UntypedFormControl(false, []),
          maintainTermsAndConditionsAgreement: new UntypedFormControl(false, []),
          customAutomationActions: new UntypedFormArray([]),
          allowOrderFormEditing: new UntypedFormControl(false, []),
        },
        OrderConfigComponent.validateWorkflowStepOptions,
      ),
      salespersonOptions: new UntypedFormGroup({
        validateRequiredFields: new UntypedFormControl(false, []),
        disableSellingStandaloneProducts: new UntypedFormControl(false, []),
        disableTagging: new UntypedFormControl(false, []),
      }),
      customerOptions: new UntypedFormGroup({
        allowCustomerInitiatedOrders: new UntypedFormControl(false, []),
      }),
      customerTermsOfServiceOptions: new UntypedFormGroup({
        hideDefaultTermsOfService: new UntypedFormControl(false, []),
        termsOfService: new UntypedFormArray([]),
      }),
      emailOptions: new UntypedFormGroup({
        contractAwaitingApproval: new UntypedFormGroup({
          subject: new UntypedFormControl('', []),
          heading: new UntypedFormControl('', []),
          body: new UntypedFormControl('', []),
          button: new UntypedFormControl('', []),
        }),
        orderProcessed: new UntypedFormGroup({
          subject: new UntypedFormControl('', []),
          heading: new UntypedFormControl('', []),
          body: new UntypedFormControl('', []),
          button: new UntypedFormControl('', []),
        }),
        orderDeclined: new UntypedFormGroup({
          subject: new UntypedFormControl('', []),
          heading: new UntypedFormControl('', []),
          body: new UntypedFormControl('', []),
          button: new UntypedFormControl('', []),
        }),
      }),
    });

    this.marketId$ = this._route.params.pipe(
      map((p) => {
        if (Object.prototype.hasOwnProperty.call(p, 'marketId')) {
          return p.marketId;
        }
        return '';
      }),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.customSalespersonActions$ = this.marketId$.pipe(
      switchMap((market) =>
        this.feature.checkFeatureFlag(this.partnerId, market, FeatureFlags.CUSTOM_SALESPERSON_ACTIONS),
      ),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.salespersonAcceptsPayment$ = this.marketId$.pipe(
      switchMap((market) =>
        this.feature.checkFeatureFlag(this.partnerId, market, FeatureFlags.SALESPERSON_ORDER_APPROVAL_ACCEPTS_PAYMENT),
      ),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.config$ = this.marketId$.pipe(
      tap((_) => (this.loading = true)),
      switchMap((marketId) => this.salesOrdersService.getConfig(this.partnerId, marketId)),
      map((c) => new OrderConfig(c)),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.config$.subscribe((c) => {
      this.configForm.controls.defaultNotesContent.setValue(c.defaultNotesContent);
      c.extraFields.map((f) => this.getExtraFieldsArray().push(FieldBuilderComponent.createOrderFormFieldGroup(f)));
      if (c.termsOfServiceUrl) {
        this.configForm.controls.termsOfServiceFormControl.setValue([
          {
            url: c.termsOfServiceUrl,
            name: c.termsOfServiceUrl,
          },
        ]);
      }
      (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls.allowSendDirectToAdmins.setValue(
        c.workflowStepOptions.allowSendDirectToAdmin,
      );
      (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls.canChargeSmbOnOrderSubmission.setValue(
        c.workflowStepOptions.canChargeSmbOnOrderSubmission,
      );
      (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls.allowSendToCustomers.setValue(
        c.workflowStepOptions.allowSendToCustomer,
      );
      (
        this.configForm.get('workflowStepConfig') as UntypedFormGroup
      ).controls.allowCollectPaymentFromCustomers.setValue(c.workflowStepOptions.allowCollectPaymentFromCustomer);
      (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls.allowOrderFormEditing.setValue(
        c.workflowStepOptions.allowOrderFormEditing,
      );
      (
        this.configForm.get('workflowStepConfig') as UntypedFormGroup
      ).controls.maintainTermsAndConditionsAgreement.setValue(
        c.workflowStepOptions.maintainTermsAndConditionsAgreement,
      );
      c.workflowStepOptions.customSalespersonActions.map((a) => this.addCustomAutomationAction(a));
      (this.configForm.get('salespersonOptions') as UntypedFormGroup).controls.validateRequiredFields.setValue(
        c.salespersonOptions.validateRequiredFields,
      );
      (
        this.configForm.get('salespersonOptions') as UntypedFormGroup
      ).controls.disableSellingStandaloneProducts.setValue(c.salespersonOptions.disableSellingStandaloneProducts);
      (this.configForm.get('salespersonOptions') as UntypedFormGroup).controls.disableTagging.setValue(
        c.salespersonOptions.disableTagging,
      );
      (this.configForm.get('customerOptions') as UntypedFormGroup).controls.allowCustomerInitiatedOrders.setValue(
        c.customerOptions.allowCustomerInitiatedOrders,
      );
      (
        this.configForm.get('customerTermsOfServiceOptions') as UntypedFormGroup
      ).controls.hideDefaultTermsOfService.setValue(c.customerTermsOfServiceOptions.hideDefaultTermsOfService);
      const emailOptions = this.configForm.get('emailOptions') as UntypedFormGroup;
      const contractAwaitingApproval = emailOptions.get('contractAwaitingApproval') as UntypedFormGroup;
      setInitialValuesForEmailContent(
        contractAwaitingApproval,
        c.emailOptions.emailCustomContent.contractAwaitingApproval,
      );
      const orderProcessed = emailOptions.get('orderProcessed') as UntypedFormGroup;
      setInitialValuesForEmailContent(orderProcessed, c.emailOptions.emailCustomContent.orderProcessed);
      const orderDeclined = emailOptions.get('orderDeclined') as UntypedFormGroup;
      setInitialValuesForEmailContent(orderDeclined, c.emailOptions.emailCustomContent.orderDeclined);
      c.customerTermsOfServiceOptions.termsOfService.map((tos) =>
        this.getTermsOfServiceArray().push(this.createTermsOfService(tos)),
      );
      this.loading = false;
    });
  }

  submit(): void {
    if (this.configForm.invalid) {
      return;
    }

    this.marketId$
      .pipe(
        take(1),
        tap(() => (this.saving = true)),
        map((marketId) => {
          const data = { partnerId: this.partnerId, marketId: marketId };
          /* tslint:disable */
          if (this.configForm.controls.defaultNotesContent.dirty) {
            data['defaultNotesContent'] = this.configForm.controls.defaultNotesContent.value;
          }
          // The form array isn't marked as dirty when expected so we will always send it's values
          data['extraFields'] = this.orderFormFieldFormComponents.map((component) => component.getFormValues());

          // The termsOfServiceFormControl also doesn't get marked as dirty
          const tosValue = this.configForm.controls.termsOfServiceFormControl.value;
          data['termsOfServiceUrl'] = tosValue && tosValue.length > 0 ? tosValue[0]['url'] : '';

          data['workflowStepOptions'] = {
            allowSendDirectToAdmin: (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
              .allowSendDirectToAdmins.value,
            canChargeSmbOnOrderSubmission: (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
              .canChargeSmbOnOrderSubmission.value,
            allowSendToCustomer: (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
              .allowSendToCustomers.value,
            allowCollectPaymentFromCustomer: this.validateAllowCollectPaymentFromCustomer(),
            maintainTermsAndConditionsAgreement: (this.configForm.get('workflowStepConfig') as UntypedFormGroup)
              .controls.maintainTermsAndConditionsAgreement.value,
            customSalespersonActions: this.getCustomAutomationActionsArray().controls.map((control) => {
              const fg = control as UntypedFormGroup;
              return {
                label: fg.controls.label.value,
                automationId: fg.controls.automationId.value,
              };
            }),
            allowOrderFormEditing: (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
              .allowOrderFormEditing.value,
          };

          data['salespersonOptions'] = {
            validateRequiredFields: (this.configForm.get('salespersonOptions') as UntypedFormGroup).controls
              .validateRequiredFields.value,
            disableSellingStandaloneProducts: (this.configForm.get('salespersonOptions') as UntypedFormGroup).controls
              .disableSellingStandaloneProducts.value,
            disableTagging: (this.configForm.get('salespersonOptions') as UntypedFormGroup).controls.disableTagging
              .value,
          };

          data['customerOptions'] = {
            allowCustomerInitiatedOrders: (this.configForm.get('customerOptions') as UntypedFormGroup).controls
              .allowCustomerInitiatedOrders.value,
          };

          data['customerTermsOfServiceOptions'] = {
            hideDefaultTermsOfService: (this.configForm.get('customerTermsOfServiceOptions') as UntypedFormGroup)
              .controls.hideDefaultTermsOfService.value,
            termsOfService: this.getTermsOfServiceArray().controls.map((c) => {
              const group = c as UntypedFormGroup;
              return {
                text: group.controls.text.value,
                termsOfServiceUrl:
                  group.controls.termsOfServiceUrl.value && group.controls.termsOfServiceUrl.value.length > 0
                    ? group.controls.termsOfServiceUrl.value[0]['url']
                    : '',
                linkTitle: group.controls.linkTitle.value,
                required: group.controls.required.value,
              };
            }),
          };

          const emailOptions = this.configForm.get('emailOptions') as UntypedFormGroup;
          const contractAwaitingApproval = emailOptions.get('contractAwaitingApproval') as UntypedFormGroup;
          const orderProcessed = emailOptions.get('orderProcessed') as UntypedFormGroup;
          const orderDeclined = emailOptions.get('orderDeclined') as UntypedFormGroup;
          data['emailOptions'] = {
            emailCustomContent: {
              contractAwaitingApproval: {
                subject: contractAwaitingApproval.controls.subject.value,
                heading: contractAwaitingApproval.controls.heading.value,
                body: contractAwaitingApproval.controls.body.value,
                button: contractAwaitingApproval.controls.button.value,
              },
              orderProcessed: {
                subject: orderProcessed.controls.subject.value,
                heading: orderProcessed.controls.heading.value,
                body: orderProcessed.controls.body.value,
                button: orderProcessed.controls.button.value,
              },
              orderDeclined: {
                subject: orderDeclined.controls.subject.value,
                heading: orderDeclined.controls.heading.value,
                body: orderDeclined.controls.body.value,
                button: orderDeclined.controls.button.value,
              },
            },
          };
          /* tslint:enable */
          this.salesOrdersService
            .updateConfig(data)
            .pipe(take(1))
            .subscribe(
              () => {
                this.saving = false;
                this.snackbarService.successSnack(
                  this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.SNACK_MESSAGES.SAVED_CONFIG_SUCCESS'),
                );
                this.ngOnInit();
              },
              () => {
                this.saving = false;
                this.snackbarService.errorSnack(
                  this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.SNACK_MESSAGES.SAVED_CONFIG_ERROR'),
                );
              },
            );
        }),
      )
      .subscribe();
  }

  delete(): void {
    this.marketId$
      .pipe(
        take(1),
        tap(() => (this.saving = true)),
        map((marketId) => {
          return this.salesOrdersService.deleteConfig(this.partnerId, marketId).subscribe(
            () => {
              this.saving = false;
              this.snackbarService.successSnack(
                this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.SNACK_MESSAGES.RESTORED_CONFIG_SUCCESS'),
              );
              this.ngOnInit();
            },
            () => {
              this.saving = false;
              this.snackbarService.errorSnack(
                this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.SNACK_MESSAGES.RESTORED_CONFIG_ERROR'),
              );
            },
          );
        }),
      )
      .subscribe();
  }

  restore(): void {
    this.dialog
      .open(RestoreWarningDialogComponent, {
        maxWidth: '500px',
      })
      .afterClosed()
      .subscribe((r) => {
        if (r) {
          this.delete();
        }
      });
  }

  previewEmail(templateId: string, formGroupName: string): void {
    this.marketId$
      .pipe(
        take(1),
        map((marketId) => {
          const emailContent = this.configForm.get('emailOptions').get(formGroupName) as UntypedFormGroup;
          this.dialog.open(PreviewEmailDialogComponent, {
            minWidth: '50%',
            data: {
              partnerId: this.partnerId,
              marketId: marketId,
              templateId: templateId,
              subject: emailContent.controls.subject.value,
              heading: emailContent.controls.heading.value,
              body: emailContent.controls.body.value,
              button: emailContent.controls.button.value,
            },
          });
        }),
      )
      .subscribe();
  }

  onDrop(event: CdkDragDrop<UntypedFormArray>): void {
    const ctrls = this.getExtraFieldsArray().controls;
    const tmp = ctrls[event.previousIndex];
    ctrls[event.previousIndex] = ctrls[event.currentIndex];
    ctrls[event.currentIndex] = tmp;
    this.getExtraFieldsArray().controls = ctrls;
  }

  getExtraFieldsArray(): UntypedFormArray {
    return this.configForm.controls.extraFields as UntypedFormArray;
  }

  addExtraFieldsFormField(): void {
    this.getExtraFieldsArray().push(FieldBuilderComponent.createOrderFormFieldGroup(new OrderFormField()));
  }

  getCustomAutomationActionsArray(): UntypedFormArray {
    return (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
      .customAutomationActions as UntypedFormArray;
  }

  addCustomAutomationAction(customAction?: CustomSalespersonActionInterface): void {
    this.getCustomAutomationActionsArray().push(
      OrderConfigComponent.createCustomAutomationActionFormGroup(customAction),
    );
  }

  chooseAutomation(control: UntypedFormControl): void {
    const dialogRef = this.dialog.open(StartManualAutomationDialogComponent, {
      data: {
        namespace: this.partnerId,
        entityType: EntityType.ENTITY_TYPE_ORDER,
        context: Context.AUTOMATION_CONTEXT_PARTNER,
        options: {
          onlySelectDontStart: true,
          hideSettingsLink: true,
          onlySalespersonAutomations: true,
        },
        warnings: [this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.WORKFLOW_STEP_SETTINGS.AUTOMATION_WARNING')],
      },
    });
    dialogRef
      .afterClosed()
      .pipe(take(1))
      .subscribe((res) => {
        if (res) {
          control.setValue(res.id);
        }
      });
  }

  removeCustomAutomationAction(index: number): void {
    this.getCustomAutomationActionsArray().removeAt(index);
  }

  removeExtraFieldsFormField(index: number): void {
    this.getExtraFieldsArray().removeAt(index);
  }

  createTermsOfService(t?: TermsOfServiceInterface): UntypedFormGroup {
    return new UntypedFormGroup({
      text: new UntypedFormControl(t ? t.text : '', Validators.required),
      termsOfServiceUrl: new UntypedFormControl(
        t && t.termsOfServiceUrl
          ? [
              {
                url: t.termsOfServiceUrl,
                name: t.termsOfServiceUrl,
              },
            ]
          : [],
      ),
      linkTitle: new UntypedFormControl(t ? t.linkTitle : ''),
      required: new UntypedFormControl(t ? t.required : false),
    });
  }

  getTermsOfServiceArray(): UntypedFormArray {
    return (this.configForm.controls.customerTermsOfServiceOptions as UntypedFormGroup).controls
      .termsOfService as UntypedFormArray;
  }

  addTermsOfServiceFormField(): void {
    this.getTermsOfServiceArray().push(this.createTermsOfService());
  }

  removeTermsOfServiceFormField(index: number): void {
    this.getTermsOfServiceArray().removeAt(index);
  }

  validateAllowCollectPaymentFromCustomer(): boolean {
    const allowSendToCustomer = (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
      .allowSendToCustomers.value;
    const allowCollectPaymentFromCustomer = (this.configForm.get('workflowStepConfig') as UntypedFormGroup).controls
      .allowCollectPaymentFromCustomers.value;
    return allowSendToCustomer && allowCollectPaymentFromCustomer;
  }

  toggleCollectPaymentFromCustomers(event: boolean): void {
    if (event === false) {
      (
        this.configForm.get('workflowStepConfig') as UntypedFormGroup
      ).controls.allowCollectPaymentFromCustomers.setValue(false);
    }
  }

  handleFileUploadError(_err: Error): void {
    this.snackbarService.errorSnack(
      this.translate.instant('CUSTOMIZE_SALES_ORDERS_CONFIG.SNACK_MESSAGES.UPLOAD_ATTACHMENT_ERROR'),
    );
  }
}
