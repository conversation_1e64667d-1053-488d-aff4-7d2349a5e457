export interface OrderFormFieldOptionInterface {
  label?: string;
  value?: string;
}

export interface OrderFormFieldInterface {
  label?: string;
  id?: string;
  type?: string;
  options?: string[];
  optionsWithLabels?: OrderFormFieldOptionInterface[];
  description?: string;
  required?: boolean;
  uploadUrl?: string;
  prefix?: string;
  suffix?: string;
  regexValidator?: string;
  regexErrorMessage?: string;
  forOfficeUseOnly?: boolean;
  officeEditableOnly?: boolean;
  allowDuplicates?: boolean;
  allowMultiples?: boolean;
  maxChoices?: number;
}

export class OrderFormField {
  label: string;
  id: string;
  type: string;
  options: string[];
  optionsWithLabels: OrderFormFieldOptionInterface[];
  description: string;
  required: boolean;
  uploadUrl: string;
  prefix: string;
  suffix: string;
  regexValidator: string;
  regexErrorMessage: string;
  forOfficeUseOnly: boolean;
  officeEditableOnly: boolean;
  allowDuplicates: boolean;
  allowMultiples: boolean;
  maxChoices: number;

  constructor(i: OrderFormFieldInterface) {
    this.label = i.label || '';
    this.id = i.id || '';
    this.type = i.type || '';
    this.options = i.options ? i.options : [];
    this.optionsWithLabels = i.optionsWithLabels ? i.optionsWithLabels : [];
    this.description = i.description || '';
    this.required = i.required || false;
    this.uploadUrl = i.uploadUrl || '';
    this.prefix = i.prefix || '';
    this.suffix = i.suffix || '';
    this.regexValidator = i.regexValidator || '';
    this.regexErrorMessage = i.regexErrorMessage || '';
    this.forOfficeUseOnly = i.forOfficeUseOnly || false;
    this.officeEditableOnly = i.officeEditableOnly || false;
    this.allowDuplicates = i.allowDuplicates || false;
    this.allowMultiples = i.allowMultiples || false;
    this.maxChoices = i.maxChoices || 1;
  }
}

export interface CustomSalespersonActionInterface {
  label?: string;
  automationId?: string;
}

export interface WorkflowStepOptionsInterface {
  allowSendDirectToAdmin?: boolean;
  allowSendToCustomer?: boolean;
  maintainTermsAndConditionsAgreement?: boolean;
  customSalespersonActions?: CustomSalespersonActionInterface[];
  allowOrderFormEditing?: boolean;
  allowCollectPaymentFromCustomer?: boolean;
  canChargeSmbOnOrderSubmission?: boolean;
}

export class WorkflowStepOptions {
  allowSendDirectToAdmin: boolean;
  allowSendToCustomer: boolean;
  maintainTermsAndConditionsAgreement: boolean;
  customSalespersonActions: CustomSalespersonActionInterface[];
  allowOrderFormEditing: boolean;
  allowCollectPaymentFromCustomer: boolean;
  canChargeSmbOnOrderSubmission: boolean;

  constructor(i: WorkflowStepOptionsInterface) {
    this.allowSendDirectToAdmin = i?.allowSendDirectToAdmin || false;
    this.allowSendToCustomer = i?.allowSendToCustomer || false;
    this.maintainTermsAndConditionsAgreement = i?.maintainTermsAndConditionsAgreement || false;
    this.customSalespersonActions =
      i?.customSalespersonActions?.map((a) => ({
        label: a?.label,
        automationId: a?.automationId,
      })) || [];
    this.allowOrderFormEditing = i?.allowOrderFormEditing || false;
    this.allowCollectPaymentFromCustomer = i?.allowCollectPaymentFromCustomer || false;
    this.canChargeSmbOnOrderSubmission = i?.canChargeSmbOnOrderSubmission || false;
  }
}

export interface SalespersonOptionsInterface {
  validateRequiredFields?: boolean;
  disableSellingStandaloneProducts?: boolean;
  disableTagging?: boolean;
}

export class SalespersonOptions {
  validateRequiredFields: boolean;
  disableSellingStandaloneProducts: boolean;
  disableTagging: boolean;

  constructor(salespersonInterface: SalespersonOptionsInterface) {
    this.validateRequiredFields = (salespersonInterface && salespersonInterface.validateRequiredFields) || false;
    this.disableSellingStandaloneProducts =
      (salespersonInterface && salespersonInterface.disableSellingStandaloneProducts) || false;
    this.disableTagging = (salespersonInterface && salespersonInterface.disableTagging) || false;
  }
}

export interface TaxOptionInterface {
  label?: string;
  percentageMultiplier?: number;
}

export class TaxOption {
  label?: string;
  percentageMultiplier?: number;

  constructor(taxOptionInterface: TaxOptionInterface) {
    this.label = taxOptionInterface.label;
    // Conversion will ensure that for example when JS messes a number up like 0.07 * 100 = 7.00000000001, so it is 7
    // toFixed will generate a string version with 7.00
    // + will convert it back to 7
    this.percentageMultiplier = +(taxOptionInterface.percentageMultiplier * 100).toFixed(2);
  }
}

export interface CustomerOptionsInterface {
  allowCustomerInitiatedOrders?: boolean;
}

export class CustomerOptions {
  allowCustomerInitiatedOrders: boolean;

  constructor(customerInterface: CustomerOptionsInterface) {
    this.allowCustomerInitiatedOrders = (customerInterface && customerInterface.allowCustomerInitiatedOrders) || false;
  }
}

export interface TermsOfServiceInterface {
  text?: string;
  termsOfServiceUrl?: string;
  linkTitle?: string;
  required?: boolean;
}

export class TermsOfService {
  text: string;
  termsOfServiceUrl: string;
  linkTitle: string;
  required: boolean;

  constructor(i: TermsOfServiceInterface) {
    this.text = i.text || '';
    this.termsOfServiceUrl = i.termsOfServiceUrl || '';
    this.linkTitle = i.linkTitle || '';
    this.required = i.required || false;
  }
}

export interface TermsOfServiceOptionsInterface {
  hideDefaultTermsOfService?: boolean;
  termsOfService?: TermsOfServiceInterface[];
}

export class TermsOfServiceOptions {
  hideDefaultTermsOfService: boolean;
  termsOfService: TermsOfServiceInterface[];

  constructor(i: TermsOfServiceOptionsInterface) {
    this.hideDefaultTermsOfService = (i && i.hideDefaultTermsOfService) || false;
    this.termsOfService = i && i.termsOfService ? i.termsOfService.map((tos) => new TermsOfService(tos)) : [];
  }
}

export interface EmailContentInterface {
  subject?: string;
  heading?: string;
  body?: string;
  button?: string;
}

export class EmailContent {
  subject: string;
  heading: string;
  body: string;
  button: string;

  constructor(i: EmailContentInterface) {
    this.subject = i.subject || '';
    this.heading = i.heading || '';
    this.body = i.body || '';
    this.button = i.button || '';
  }
}

export interface EmailCustomContentInterface {
  contractAwaitingApproval?: EmailContentInterface;
  orderProcessed?: EmailContentInterface;
  orderDeclined?: EmailContentInterface;
}

export class EmailCustomContent {
  contractAwaitingApproval: EmailContent;
  orderProcessed: EmailContent;
  orderDeclined: EmailContent;

  constructor(i: EmailCustomContentInterface) {
    this.contractAwaitingApproval = new EmailContent(i.contractAwaitingApproval);
    this.orderProcessed = new EmailContent(i.orderProcessed);
    this.orderDeclined = new EmailContent(i.orderDeclined);
  }
}

export interface EmailOptionsInterface {
  emailCustomContent?: EmailCustomContentInterface;
}

export class EmailOptions {
  emailCustomContent: EmailCustomContent;

  constructor(i: EmailOptionsInterface) {
    this.emailCustomContent = new EmailCustomContent(i.emailCustomContent);
  }
}

export interface OrderConfigInterface {
  partnerId?: string;
  marketId?: string;
  defaultNotesContent?: string;
  extraFields?: OrderFormFieldInterface[];
  termsOfServiceUrl?: string;
  workflowStepOptions?: WorkflowStepOptionsInterface;
  salespersonOptions?: SalespersonOptionsInterface;
  taxOptions?: TaxOptionInterface[];
  customerOptions?: CustomerOptionsInterface;
  customerTermsOfServiceOptions?: TermsOfServiceOptionsInterface;
  emailOptions?: EmailOptionsInterface;
}

export class OrderConfig {
  partnerId: string;
  marketId: string;
  defaultNotesContent: string;
  extraFields: OrderFormField[];
  termsOfServiceUrl?: string;
  workflowStepOptions: WorkflowStepOptions;
  salespersonOptions: SalespersonOptions;
  taxOptions: TaxOption[];
  customerOptions: CustomerOptions;
  customerTermsOfServiceOptions: TermsOfServiceOptions;
  emailOptions: EmailOptions;

  constructor(i: OrderConfigInterface) {
    this.partnerId = i.partnerId || '';
    this.marketId = i.marketId || '';
    this.defaultNotesContent = i.defaultNotesContent || '';
    this.extraFields = i.extraFields ? i.extraFields.map((x) => new OrderFormField(x)) : [];
    this.termsOfServiceUrl = i.termsOfServiceUrl || '';
    this.workflowStepOptions = new WorkflowStepOptions(i.workflowStepOptions);
    this.salespersonOptions = new SalespersonOptions(i.salespersonOptions);
    this.taxOptions = i.taxOptions ? i.taxOptions.map((x) => new TaxOption(x)) : [];
    this.customerOptions = new CustomerOptions(i.customerOptions);
    this.customerTermsOfServiceOptions = new TermsOfServiceOptions(i.customerTermsOfServiceOptions);
    this.emailOptions = new EmailOptions(i.emailOptions);
  }
}
