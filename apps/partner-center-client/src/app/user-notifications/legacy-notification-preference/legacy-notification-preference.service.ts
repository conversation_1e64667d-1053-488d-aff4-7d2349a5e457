import { Injectable, inject } from '@angular/core';
import { SessionService } from '@galaxy/core';
import { WhitelabelService } from '@galaxy/partner';
import {
  LegacyNotificationPreferenceService as LegacyNotificationPreferenceSDKService,
  LegacyStatus,
  LegacyGetResponseInterface,
  LegacyPreferenceInterface,
} from '@vendasta/notifications-sdk';
import { combineLatest, Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { AppIDs } from '../../constants';
import { ProductService } from '../../core/product.service';
import { Product } from '../../core/product/product';
import { NotificationChange } from '../../users/user-bulk-actions/notification-change';
import { VbcHostService } from '../../vbc/vbc.host.service';
import { DAILY_DIGEST_TYPE, EXECUTIVE_REPORT_TYPE, INSTANT_EMAIL_SECTION_TYPE } from '../constants';
import { fetchWithCache$ } from './cache-storage';

export interface ProductNotifications {
  productId: string;
  name: string;
  iconUrl?: string;
  iconType?: string;
  sections: AppSettingSection[];
}
export interface AppSettingSection {
  productId: string;
  sectionType: string;
  sectionId: string;
  title: string;
  value: LegacyStatus;
  defaultValue: LegacyStatus;
  children?: AppSettingSection[];
}

const EXECUTIVE_REPORT_SECTION_ID = 'executiveReportFlag';
const VBC_PRODUCT_ICON = 'https://vbc-prod.appspot.com/static/images/file-upload.png';
const SORT_ORDER = [
  'VBC',
  'RM',
  'SM',
  'LB',
  AppIDs.AdvertisingIntelligence, // Ad Intel
  'MP-ee4ea04e553a4b1780caf7aad7be07cd', // Website Pro
].reverse(); // Reversing so it orders it in the needed way for the sort function

@Injectable({ providedIn: 'root' })
export class LegacyNotificationPreferenceService {
  private readonly sessionService = inject(SessionService);
  private readonly productService = inject(ProductService);
  private readonly vbcHostService = inject(VbcHostService);
  private readonly preferenceService = inject(LegacyNotificationPreferenceSDKService);
  private readonly whitelabelService = inject(WhitelabelService);

  getSettings(
    partnerId: string,
    marketId?: string,
    userId?: string,
    businessId?: string,
    bulk?: boolean,
  ): Observable<ProductNotifications[]> {
    return combineLatest([
      this.getAvailableSections(partnerId, marketId, businessId),
      bulk
        ? of({
            components: [
              {
                instant: LegacyStatus.STATUS_UNSET,
                digest: LegacyStatus.STATUS_UNSET,
                monthlyTaskReport: LegacyStatus.STATUS_UNSET,
                instantSettings: [],
                digestSettings: [],
                executiveReportSettings: [],
              },
            ],
            defaults: {
              instant: LegacyStatus.STATUS_UNSET,
              digest: LegacyStatus.STATUS_UNSET,
              monthlyTaskReport: LegacyStatus.STATUS_UNSET,
              instantSettings: [],
              digestSettings: [],
              executiveReportSettings: [],
            },
            userNotificationsDisabled: false,
          } as LegacyGetResponseInterface)
        : this.preferenceService.get$(partnerId, marketId, userId, businessId),
      this.productService.products,
      this.whitelabelService.getBranding(partnerId, marketId),
    ]).pipe(
      map(([data, preferences, products, vbcDetails]) =>
        this.extractSettingSections(data, preferences, products, vbcDetails.apps.VBC.name, bulk),
      ),
    );
  }

  private extractSettingSections(
    res: any,
    preferences: LegacyGetResponseInterface,
    products: Product[],
    vbcName: string,
    bulk?: boolean,
  ): ProductNotifications[] {
    const specificPreferences = preferences.components[0];
    const defaultPreferences = preferences.defaults;
    //Build map of notification preferences from list
    const preferencesMap = {
      instant: new Map<string, LegacyStatus>(),
      digest: new Map<string, LegacyStatus>(),
      report: new Map<string, LegacyStatus>(),
    };
    specificPreferences.instantSettings?.forEach((setting) =>
      preferencesMap.instant.set(setting.notificationId, setting.status),
    );
    specificPreferences.digestSettings?.forEach((setting) =>
      preferencesMap.digest.set(setting.notificationId, setting.status),
    );
    specificPreferences.executiveReportSettings?.forEach((setting) =>
      preferencesMap.report.set(setting.notificationId, setting.status),
    );
    //Build map of default notification preference from list
    const defaultsMap = {
      instant: new Map<string, LegacyStatus>(),
      digest: new Map<string, LegacyStatus>(),
      report: new Map<string, LegacyStatus>(),
    };
    defaultPreferences.instantSettings?.forEach((setting) =>
      defaultsMap.instant.set(
        setting.notificationId,
        setting.status !== LegacyStatus.STATUS_DEFAULT && setting.status !== LegacyStatus.STATUS_UNSET
          ? setting.status
          : LegacyStatus.STATUS_ENABLED,
      ),
    );
    defaultPreferences.digestSettings?.forEach((setting) =>
      defaultsMap.digest.set(
        setting.notificationId,
        setting.status !== LegacyStatus.STATUS_DEFAULT && setting.status !== LegacyStatus.STATUS_UNSET
          ? setting.status
          : LegacyStatus.STATUS_ENABLED,
      ),
    );
    defaultPreferences.executiveReportSettings?.forEach((setting) =>
      defaultsMap.report.set(
        setting.notificationId,
        setting.status !== LegacyStatus.STATUS_DEFAULT && setting.status !== LegacyStatus.STATUS_UNSET
          ? setting.status
          : LegacyStatus.STATUS_ENABLED,
      ),
    );
    //Build sections
    let productSections: ProductNotifications[] = [];
    const baseSections: ProductNotifications = {
      productId: 'custom',
      name: 'Global Settings',
      iconType: 'public',
      sections: [],
    };
    res.data.forEach((details) => {
      // Add the base section data to global settings (Except for exec report)
      if (details.section.id !== EXECUTIVE_REPORT_SECTION_ID) {
        let d =
          details.section.id === INSTANT_EMAIL_SECTION_TYPE ? defaultPreferences.instant : defaultPreferences.digest;
        if (!d || d === LegacyStatus.STATUS_DEFAULT) {
          d = LegacyStatus.STATUS_ENABLED;
        }
        baseSections.sections.push({
          productId: baseSections.productId,
          sectionType: 'globalSettingsFlag',
          sectionId: details.section.id,
          title: details.section.title,
          value:
            details.section.id === INSTANT_EMAIL_SECTION_TYPE
              ? specificPreferences.instant
              : specificPreferences.digest,
          defaultValue: d,
        });
      }
      details.products.forEach((productDetails) => {
        // Add the products to the products array if not already there
        let productSection = productSections.find((product) => product.productId === productDetails.productId);

        if (!productSection) {
          if (productDetails.productId === 'VBC') {
            productSection = {
              productId: productDetails.productId,
              name: vbcName,
              iconUrl: VBC_PRODUCT_ICON,
              sections: [],
            };
          } else {
            const marketplaceProduct = products.find((product) => product.product_id === productDetails.productId);
            if (!marketplaceProduct) {
              return;
            }
            productSection = {
              productId: productDetails.productId,
              name: marketplaceProduct.name,
              iconUrl: marketplaceProduct.icon_url,
              sections: [],
            };
          }
          productSections.push(productSection);
        }

        // Add the sections to the product
        productSection.sections.push(
          ...productDetails.activities.map((activity) => {
            let typeMap: Map<string, LegacyStatus>;
            let defaultTypeMap: Map<string, LegacyStatus>;
            switch (details.section.id) {
              case INSTANT_EMAIL_SECTION_TYPE: {
                typeMap = preferencesMap.instant;
                defaultTypeMap = defaultsMap.instant;
                break;
              }
              case DAILY_DIGEST_TYPE: {
                typeMap = preferencesMap.digest;
                defaultTypeMap = defaultsMap.digest;
                break;
              }
              case EXECUTIVE_REPORT_TYPE: {
                typeMap = preferencesMap.report;
                defaultTypeMap = defaultsMap.report;
                break;
              }
            }
            const defaultValue = bulk ? LegacyStatus.STATUS_UNSET : LegacyStatus.STATUS_DEFAULT;
            return {
              productId: productSection.productId,
              sectionType: details.section.id,
              sectionId: activity.id,
              title: activity.title,
              value: typeMap.get(activity.id) ?? defaultValue,
              defaultValue: defaultTypeMap.get(activity.id) ?? LegacyStatus.STATUS_ENABLED,
              children: activity.tags.map((tag) => {
                return {
                  sectionId: tag.id,
                  title: tag.title,
                  value: typeMap.get(tag.id) ?? LegacyStatus.STATUS_DEFAULT,
                  defaultValue: defaultTypeMap.get(tag.id) ?? LegacyStatus.STATUS_ENABLED,
                };
              }),
            };
          }),
        );
      });
    });

    productSections = productSections.sort(function (a: ProductNotifications, b: ProductNotifications): number {
      if (SORT_ORDER.indexOf(a.productId) * -1 < SORT_ORDER.indexOf(b.productId) * -1) {
        return -1;
      } else if (SORT_ORDER.indexOf(a.productId) * -1 > SORT_ORDER.indexOf(b.productId) * -1) {
        return 1;
      }
      return 0;
    });

    return [baseSections, ...productSections];
  }

  deleteSectionSetting(partnerId: string, marketId: string, userId: string, accountGroupId: string): Observable<any> {
    return this.preferenceService.delete$(partnerId, marketId, userId, accountGroupId);
  }

  updateSectionSetting(
    sectionId: string,
    settingId: string,
    status: LegacyStatus,
    partnerId: string,
    marketId?: string,
    userId?: string,
    accountGroupId?: string,
  ): Observable<any> {
    const changes: LegacyPreferenceInterface = { instantSettings: [], digestSettings: [], executiveReportSettings: [] };
    if (settingId === INSTANT_EMAIL_SECTION_TYPE) {
      changes.instant = status;
    } else if (settingId === DAILY_DIGEST_TYPE) {
      changes.digest = status;
    } else if (sectionId === EXECUTIVE_REPORT_TYPE) {
      changes.executiveReportSettings.push({
        notificationId: settingId,
        status: status,
      });
    } else if (sectionId === INSTANT_EMAIL_SECTION_TYPE) {
      changes.instantSettings.push({
        notificationId: settingId,
        status: status,
      });
    } else if (sectionId === DAILY_DIGEST_TYPE) {
      changes.digestSettings.push({
        notificationId: settingId,
        status: status,
      });
    }
    return this.preferenceService.update$(changes, partnerId, marketId, userId, accountGroupId);
  }

  bulkUpdatePermissions(
    partnerId: string,
    userIds: string[],
    permissions: NotificationChange[],
    partialIdentifiers: boolean,
  ): Observable<any> {
    const changes: LegacyPreferenceInterface = { instantSettings: [], digestSettings: [], executiveReportSettings: [] };
    permissions.forEach((permission) => {
      if (permission.setting === INSTANT_EMAIL_SECTION_TYPE) {
        changes.instant = permission.valueFlag;
      } else if (permission.setting === DAILY_DIGEST_TYPE) {
        changes.digest = permission.valueFlag;
      } else if (permission.section === EXECUTIVE_REPORT_TYPE) {
        changes.executiveReportSettings.push({
          notificationId: permission.setting,
          status: permission.valueFlag,
        });
      } else if (permission.section === INSTANT_EMAIL_SECTION_TYPE) {
        changes.instantSettings.push({
          notificationId: permission.setting,
          status: permission.valueFlag,
        });
      } else if (permission.section === DAILY_DIGEST_TYPE) {
        changes.digestSettings.push({
          notificationId: permission.setting,
          status: permission.valueFlag,
        });
      }
    });
    // if partialIdentifiers is true, update business
    // if false, update user
    if (!userIds || userIds.length == 0) {
      if (partialIdentifiers) {
        return this.preferenceService.bulkUpdateBusinessByPartner(changes, partnerId);
      } else {
        return this.preferenceService.bulkUpdateUsersByPartner(changes, partnerId);
      }
    } else {
      if (partialIdentifiers) {
        return this.preferenceService.bulkUpdateBusinessByUser(changes, partnerId, userIds);
      } else {
        return this.preferenceService.bulkUpdatePreferenceByIdentifier(
          changes,
          userIds.map((userId) => ({ partnerId, userId })),
        );
      }
    }
  }

  private apiOptions(sessionId: string): RequestInit {
    return {
      headers: {
        Authorization: `Bearer ${sessionId}`,
        'Content-Type': 'application/json',
      },
    };
  }

  private getAvailableSections(partnerId: string, marketId: string, businessId: string): Observable<any> {
    let url = `${this.vbcHostService.hostWithScheme()}/nsInternalApi/v3/notificationSettings/getAvailableSections/?partnerId=${partnerId}`;
    if (marketId && marketId !== '') {
      url += `&marketId=${marketId}`;
    }
    if (businessId && businessId !== '') {
      url += `&accountGroupId=${businessId}`;
    }
    return this.sessionService.getSessionId().pipe(
      switchMap((sessionId) => {
        return fetchWithCache$(
          new Request(url, this.apiOptions(sessionId)),
          'legacy-notification-preferences',
          1000 * 60 * 60 * 2,
        );
      }),
      map((res) => {
        if (res.statusCode != 200) {
          throw res;
        }
        return res;
      }),
    );
  }
}
