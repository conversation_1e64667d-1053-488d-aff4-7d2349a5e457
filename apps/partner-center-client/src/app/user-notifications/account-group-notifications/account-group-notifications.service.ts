import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SessionService } from '@galaxy/core';
import { combineLatest, Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { AppIDs } from '../../constants';
import { PartnerService } from '../../core/partner.service';
import { ProductService } from '../../core/product.service';
import { Product } from '../../core/product/product';
import { NotificationChange } from '../../users/user-bulk-actions/notification-change';
import { VbcHostService } from '../../vbc/vbc.host.service';
import { VConfigService } from '../../vconfig/vconfig.service';

export interface ProductNotifications {
  productId: string;
  name: string;
  iconUrl?: string;
  iconType?: string;
  sections: AppSettingSection[];
}
export interface AppSettingSection {
  sectionType: string;
  sectionId: string;
  title: string;
  value: boolean;
  children?: AppSettingSection[];
}

const EXECUTIVE_REPORT_SECTION_ID = 'executiveReportFlag';
const VBC_PRODUCT_ICON = 'https://vbc-prod.appspot.com/static/images/file-upload.png';
const SORT_ORDER = [
  'VBC',
  'RM',
  'SM',
  'LB',
  AppIDs.AdvertisingIntelligence, // Ad Intel
  'MP-ee4ea04e553a4b1780caf7aad7be07cd', // Website Pro
].reverse(); // Reversing so it orders it in the needed way for the sort function

@Injectable({ providedIn: 'root' })
export class AccountGroupNotificationsService {
  constructor(
    private partnerService: PartnerService,
    private sessionService: SessionService,
    private httpClient: HttpClient,
    private productService: ProductService,
    private vConfigService: VConfigService,
    private vbcHostService: VbcHostService,
  ) {}

  getSettings(accountGroupId: string, subjectId: string): Observable<ProductNotifications[]> {
    let url = `${this.vbcHostService.hostWithScheme()}/nsInternalApi/v3/notificationSettings/getSections/`;
    url += `?accountGroupId=${accountGroupId}&subjectId=${subjectId}&partnerId=${this.partnerService.partnerId}`;
    return combineLatest([
      this.sessionService.getSessionId(),
      this.productService.products,
      this.vConfigService.getConfigurations(this.partnerService.partnerId, ['vbc_product_name']),
    ]).pipe(
      switchMap(([sessionId, products, vbcDetails]) => {
        return this.httpClient
          .get(url, this.apiOptions(sessionId))
          .pipe(map((data) => this.extractSettingSections(data, products, vbcDetails.vbc_product_name)));
      }),
    );
  }

  private extractSettingSections(res: any, products: Product[], vbcName: string): any {
    let productSections: ProductNotifications[] = [];
    const baseSections: ProductNotifications = {
      productId: 'custom',
      name: 'Global Settings',
      iconType: 'public',
      sections: [],
    };
    res.data.forEach((details) => {
      // Add the base section data to global settings (Except for exec report)
      if (details.section.id !== EXECUTIVE_REPORT_SECTION_ID) {
        baseSections.sections.push({
          sectionType: 'globalSettingsFlag',
          sectionId: details.section.id,
          title: details.section.title,
          value: details.section.value,
        });
      }
      details.products.forEach((productDetails) => {
        // Add the products to the products array if not already there
        let productSection = productSections.find((product) => product.productId === productDetails.productId);

        if (!productSection) {
          const marketplaceProduct = products.find((product) => product.product_id === productDetails.productId);
          productSection = {
            productId: productDetails.productId,
            name: productDetails.productId !== 'VBC' ? marketplaceProduct.name : vbcName,
            iconUrl: productDetails.productId !== 'VBC' ? marketplaceProduct.icon_url : VBC_PRODUCT_ICON,
            sections: [],
          };
          productSections.push(productSection);
        }

        // Add the sections to the product
        productSection.sections.push(
          ...productDetails.activities.map((activitiy) => {
            return {
              sectionType: details.section.id,
              sectionId: activitiy.id,
              title: activitiy.title,
              value: activitiy.value,
              children: activitiy.tags.map((tag) => {
                return {
                  sectionId: tag.id,
                  title: tag.title,
                  value: tag.value,
                };
              }),
            };
          }),
        );
      });
    });

    productSections = productSections.sort(function (a: ProductNotifications, b: ProductNotifications): number {
      if (SORT_ORDER.indexOf(a.productId) * -1 < SORT_ORDER.indexOf(b.productId) * -1) {
        return -1;
      } else if (SORT_ORDER.indexOf(a.productId) * -1 > SORT_ORDER.indexOf(b.productId) * -1) {
        return 1;
      }
      return 0;
    });

    return [baseSections, ...productSections];
  }

  updateSectionSetting(
    accountGroupId: string,
    subjectId: string,
    sectionId: string,
    settingId: string,
    valueFlag: boolean,
  ): Observable<any> {
    const url = `${this.vbcHostService.hostWithScheme()}/nsInternalApi/v3/notificationSettings/sectionSetting/update/`;

    return this.sessionService.getSessionId().pipe(
      switchMap((sessionId) =>
        this.httpClient.post(
          url,
          {
            accountGroupId,
            subjectId,
            section: sectionId,
            setting: settingId,
            valueFlag,
          },
          this.apiOptions(sessionId),
        ),
      ),
    );
  }

  bulkUpdatePermissions(partnerId: string, subjectIds: string[], permissions: NotificationChange[]): Observable<any> {
    const url = `${this.vbcHostService.hostWithScheme()}/nsInternalApi/v3/notificationSettings/bulkUpdate/`;
    return this.sessionService.getSessionId().pipe(
      switchMap((sessionId) =>
        this.httpClient.post(
          url,
          {
            partnerId,
            subjectIds,
            permissionsJson: permissions,
          },
          this.apiOptions(sessionId),
        ),
      ),
    );
  }

  private apiOptions(sessionId: string): any {
    return {
      headers: new HttpHeaders({
        Authorization: `Bearer ${sessionId}`,
        'Content-Type': 'application/json',
      }),
      withCredentials: true,
    };
  }
}
