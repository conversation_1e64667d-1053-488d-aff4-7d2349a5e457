import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Brand } from '@vendasta/multi-location';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { VConfigService } from '../../../vconfig/vconfig.service';
import { BrandsService } from '../../brands.service';

export class Feature {
  key?: string;
  order: number;
  label: string;
  toggleValue: boolean;
  enabled: boolean;
}

export interface FeatureType {
  [key: string]: Feature;
}

@Injectable()
export class BrandFeaturesService {
  private features$$: BehaviorSubject<FeatureType> = new BehaviorSubject<FeatureType>({
    reportTabEnabled: {
      order: 0,
      label: 'Executive Report',
      toggleValue: true,
      enabled: false,
    },
    reviewsTabEnabled: {
      order: 1,
      label: 'Reputation',
      toggleValue: true,
      enabled: true,
    },
    requestReviewPageEnabled: {
      order: 2,
      label: 'Reputation – Review Requesting',
      toggleValue: true,
      enabled: false,
    },
    customerVoiceExecutiveReportEnabled: {
      order: 3,
      label: 'Customer Voice in the Executive Report',
      toggleValue: true,
      enabled: true,
    },
    listingsTabEnabled: {
      order: 4,
      label: 'Listings',
      toggleValue: true,
      enabled: true,
    },
    insightsTabEnabled: {
      order: 5,
      label: 'Google Business Profile',
      toggleValue: true,
      enabled: true,
    },
    socialTabEnabled: {
      order: 6,
      label: 'Social',
      toggleValue: true,
      enabled: true,
    },
    advertisingTabEnabled: {
      order: 7,
      label: 'Advertising',
      toggleValue: true,
      enabled: true,
    },
    websiteTabEnabled: {
      order: 8,
      label: `Website`,
      toggleValue: true,
      enabled: false,
    },
    dataExportTabEnabled: {
      order: 9,
      label: 'Data Exporter',
      toggleValue: true,
      enabled: true,
    },
    mapTabEnabled: {
      order: 10,
      label: 'Map Tab (Legacy Report)',
      toggleValue: true,
      enabled: true,
    },
    googleQAndATabEnabled: {
      order: 11,
      label: 'Google Q&A',
      toggleValue: true,
      enabled: true,
    },
    keywordTrackingTabEnabled: {
      order: 12,
      label: 'Keyword Tracking',
      toggleValue: true,
      enabled: true,
    },
  });
  public feature$: Observable<FeatureType> = this.features$$.asObservable();

  constructor(
    private vConfigService: VConfigService,
    private brandsService: BrandsService,
    private matSnackBar: MatSnackBar,
  ) {}

  public initFeatureDetails(brand: Brand, partnerId: string): void {
    const toggleValues = this.features$$.value;

    toggleValues.reviewsTabEnabled.toggleValue = brand.tabStatuses.reviewsTabEnabled;
    toggleValues.listingsTabEnabled.toggleValue = brand.tabStatuses.listingsTabEnabled;
    toggleValues.socialTabEnabled.toggleValue = brand.tabStatuses.socialTabEnabled;
    toggleValues.insightsTabEnabled.toggleValue = brand.tabStatuses.insightsTabEnabled;
    toggleValues.mapTabEnabled.toggleValue = brand.tabStatuses.mapTabEnabled;
    toggleValues.advertisingTabEnabled.toggleValue = brand.tabStatuses.advertisingTabEnabled;
    toggleValues.requestReviewPageEnabled.toggleValue = brand.featureStatuses.requestReviewPageEnabled;
    toggleValues.reportTabEnabled.toggleValue = brand.tabStatuses.reportTabEnabled;
    toggleValues.dataExportTabEnabled.toggleValue = brand.tabStatuses.dataExportTabEnabled;
    toggleValues.websiteTabEnabled.toggleValue = brand.tabStatuses.websiteTabEnabled;
    toggleValues.customerVoiceExecutiveReportEnabled.toggleValue =
      brand.featureStatuses.customerVoiceExecutiveReportEnabled;
    toggleValues.googleQAndATabEnabled.toggleValue = brand.tabStatuses.googleQAndATabEnabled;
    toggleValues.keywordTrackingTabEnabled.toggleValue = brand.tabStatuses.keywordTrackingTabEnabled;

    this.vConfigService
      .isFeatureEnabled(['multilocation_customer_voice', 'multilocation_report'], partnerId, 'default')
      .pipe(
        tap((features) => {
          toggleValues.requestReviewPageEnabled.enabled = features.multilocation_customer_voice;
          toggleValues.reportTabEnabled.enabled = features.multilocation_report;
          toggleValues.websiteTabEnabled.enabled = features.multilocation_report;
          this.features$$.next(toggleValues);
        }),
      )
      .subscribe();
  }

  public toggleFeature(featureId: string, partnerId: string, brandName: string, brand: Brand): void {
    const FeatureIdList = ['requestReviewPageEnabled', 'customerVoiceExecutiveReportEnabled'];

    const toggleValues = this.features$$.value;
    toggleValues[featureId].toggleValue = !toggleValues[featureId].toggleValue;
    this.features$$.next(toggleValues);

    const tabStatus = {};
    const featureStatus = {};
    if (FeatureIdList.includes(featureId)) {
      featureStatus[featureId] = toggleValues[featureId].toggleValue;
    } else {
      tabStatus[featureId] = toggleValues[featureId].toggleValue;
    }

    this.brandsService.updateFeatureList(partnerId, brandName, tabStatus, featureStatus).subscribe((ret) => {
      if (ret === null) {
        if (FeatureIdList.includes(featureId)) {
          brand.featureStatuses[featureId] = !brand.featureStatuses[featureId];
        } else {
          brand.tabStatuses[featureId] = !brand.tabStatuses[featureId];
        }
        this.matSnackBar.open('Settings saved!', null, {
          duration: 2000,
        });
      } else {
        toggleValues[featureId].toggleValue = !toggleValues[featureId].toggleValue;
        this.features$$.next(toggleValues);
        this.matSnackBar.open('Settings not saved, please try again.', null, {
          duration: 5000,
        });
      }
    });
  }
}
