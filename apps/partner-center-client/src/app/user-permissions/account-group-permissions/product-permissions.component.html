<div *ngIf="!error; else errorMessage">
  <div class="association-loader" *ngIf="loadingProducts || loadingBusinessCenterTabs">
    <mat-spinner [strokeWidth]="3" [diameter]="24"></mat-spinner>
  </div>
  <div *ngIf="!loadingProducts && !loadingBusinessCenterTabs">
    <div class="permissions">
      <div class="permission" *ngFor="let tab of businessCenterTabs">
        <mat-icon>{{ tab.icon }}</mat-icon>
        <div class="product-name">
          {{ tab.name | translate }}
          <div class="product-version {{ tab?.version }}" *ngIf="tab?.version">
            {{ tab?.version }}
          </div>
        </div>
        <div class="permission-toggle">
          <mat-slide-toggle
            [checked]="tabEnabled(tab.id)"
            [disabled]="disableToggle()"
            (change)="changeTabPermission($event.checked, tab.id)"
          ></mat-slide-toggle>
        </div>
      </div>
      <div class="product-permission" *ngIf="tabEnabled(myProductsTabId)">
        <div class="permission tab-permissions-enabled" *ngFor="let product of products">
          <va-icon [iconUrl]="product.icon_url" [iconName]="product.name" diameter="30"></va-icon>
          <div class="product-name">
            {{ product.name }}
            <span *ngIf="isDemo(product.product_id)">(Demo)</span>
          </div>
          <div class="permission-toggle">
            <mat-slide-toggle
              [checked]="hasProductPermission(product)"
              (change)="changeProductPermission($event.checked, product)"
              [disabled]="disableToggle()"
            ></mat-slide-toggle>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<ng-template #errorMessage>
  <div class="error-message">Could not load permissions</div>
</ng-template>
