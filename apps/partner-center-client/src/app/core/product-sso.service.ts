import { Injectable } from '@angular/core';
import { Environment, EnvironmentService } from '@galaxy/core';
import { SSOService } from '@vendasta/sso';
import { AppIDs } from '../constants';
import {
  activeCampaignAppId,
  activeCampaignAppIdV2,
  activeCampaignUrl,
  constantContactAppId,
  constantContactUrl,
  ecwidMonthlyAppId,
  ecwidUrl,
  ecwidYearlyAppId,
  godaddyAppId,
  godaddyUrl,
  googleTransferAppId,
  googleWorkspaceBusinessBasicAppId,
  googleWorkspaceBusinessPlusAppId,
  googleWorkspaceBusinessStandardAppId,
  googleWorkspaceEnterpriseEssentialsAppId,
  googleWorkspaceEnterprisePlusAppId,
  googleWorkspaceEnterpriseStandardAppId,
  googleWorkspaceUrl,
  msOfficeAppId,
  msOfficePlan,
  msOfficeRequestResellerRelationshipAppId,
  msOfficeUrl,
  whitelabelDomainAppId,
} from '../marketplace-app/global-brands-constants';
import { PartnerService } from './partner.service';

const websiteProProductID = {
  [Environment.DEMO]: 'MP-9cc9f21f0a234a46ad78087fc09f16bc',
  [Environment.PROD]: 'MP-ee4ea04e553a4b1780caf7aad7be07cd',
};
const websiteProMultiSiteProductID = {
  [Environment.DEMO]: 'MP-BF4SVXX68FQ5FDV3MKMR7BMVXM63LTLQ',
  [Environment.PROD]: 'MP-6F78GXV7K2MWR2LM8XWNG353MVJQLNBX',
};
const customerVoiceProductID = {
  [Environment.DEMO]: 'MP-fba21121b71148c9bb33e11fcd92d520',
  [Environment.PROD]: 'MP-c4974d390a044c28aec31e421aa662b2',
};
const rmProductID = 'RM';
const smProductID = 'SM';
const msProductID = AppIDs.ListingBuilder;

const hosts = {
  [AppIDs.AdvertisingIntelligence]: {
    [Environment.LOCAL]: 'not-supported',
    [Environment.DEMO]: 'https://advertising-accounts-demo.apigateway.co',
    [Environment.PROD]: 'https://www.advertisingintelligence.io',
  },
  [googleWorkspaceBusinessBasicAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceBusinessBasicAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleWorkspaceBusinessStandardAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceBusinessStandardAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleWorkspaceBusinessPlusAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceBusinessPlusAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleWorkspaceEnterpriseEssentialsAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceEnterpriseEssentialsAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleWorkspaceEnterpriseStandardAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceEnterpriseStandardAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleWorkspaceEnterprisePlusAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleWorkspaceEnterprisePlusAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [googleTransferAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.DEMO],
    [Environment.DEMO]: googleWorkspaceUrl[Environment.DEMO],
  },
  [googleTransferAppId[Environment.PROD]]: {
    [Environment.LOCAL]: googleWorkspaceUrl[Environment.PROD],
    [Environment.PROD]: googleWorkspaceUrl[Environment.PROD],
  },
  [constantContactAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: constantContactUrl[Environment.DEMO],
    [Environment.DEMO]: constantContactUrl[Environment.DEMO],
  },
  [constantContactAppId[Environment.PROD]]: {
    [Environment.LOCAL]: constantContactUrl[Environment.PROD],
    [Environment.PROD]: constantContactUrl[Environment.PROD],
  },
  [msOfficeAppId[msOfficePlan.CUSTOMER][Environment.DEMO]]: {
    [Environment.LOCAL]: msOfficeUrl[Environment.DEMO],
    [Environment.DEMO]: msOfficeUrl[Environment.DEMO],
  },
  [msOfficeAppId[msOfficePlan.CUSTOMER][Environment.PROD]]: {
    [Environment.LOCAL]: msOfficeUrl[Environment.PROD],
    [Environment.PROD]: msOfficeUrl[Environment.PROD],
  },
  [msOfficeRequestResellerRelationshipAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: msOfficeUrl[Environment.DEMO],
    [Environment.DEMO]: msOfficeUrl[Environment.DEMO],
  },
  [msOfficeRequestResellerRelationshipAppId[Environment.PROD]]: {
    [Environment.LOCAL]: msOfficeUrl[Environment.PROD],
    [Environment.PROD]: msOfficeUrl[Environment.PROD],
  },
  [godaddyAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: godaddyUrl[Environment.DEMO],
    [Environment.DEMO]: godaddyUrl[Environment.DEMO],
  },
  [godaddyAppId[Environment.PROD]]: {
    [Environment.LOCAL]: godaddyUrl[Environment.PROD],
    [Environment.PROD]: godaddyUrl[Environment.PROD],
  },
  [whitelabelDomainAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: godaddyUrl[Environment.DEMO],
    [Environment.DEMO]: godaddyUrl[Environment.DEMO],
  },
  [whitelabelDomainAppId[Environment.PROD]]: {
    [Environment.LOCAL]: godaddyUrl[Environment.PROD],
    [Environment.PROD]: godaddyUrl[Environment.PROD],
  },
  [activeCampaignAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: activeCampaignUrl[Environment.DEMO],
    [Environment.DEMO]: activeCampaignUrl[Environment.DEMO],
  },
  [activeCampaignAppId[Environment.PROD]]: {
    [Environment.LOCAL]: activeCampaignUrl[Environment.PROD],
    [Environment.PROD]: activeCampaignUrl[Environment.PROD],
  },
  [activeCampaignAppIdV2[Environment.DEMO]]: {
    [Environment.LOCAL]: activeCampaignUrl[Environment.DEMO],
    [Environment.DEMO]: activeCampaignUrl[Environment.DEMO],
  },
  [activeCampaignAppIdV2[Environment.PROD]]: {
    [Environment.LOCAL]: activeCampaignUrl[Environment.PROD],
    [Environment.PROD]: activeCampaignUrl[Environment.PROD],
  },
  [ecwidMonthlyAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: ecwidUrl[Environment.DEMO],
    [Environment.DEMO]: ecwidUrl[Environment.DEMO],
  },
  [ecwidMonthlyAppId[Environment.PROD]]: {
    [Environment.LOCAL]: ecwidUrl[Environment.PROD],
    [Environment.PROD]: ecwidUrl[Environment.PROD],
  },
  [ecwidYearlyAppId[Environment.DEMO]]: {
    [Environment.LOCAL]: ecwidUrl[Environment.DEMO],
    [Environment.DEMO]: ecwidUrl[Environment.DEMO],
  },
  [ecwidYearlyAppId[Environment.PROD]]: {
    [Environment.LOCAL]: ecwidUrl[Environment.PROD],
    [Environment.PROD]: ecwidUrl[Environment.PROD],
  },
  [websiteProProductID[Environment.DEMO]]: 'https://websiteprodashboard-demo.com',
  [websiteProProductID[Environment.PROD]]: 'https://www.websiteprodashboard.com',
  [websiteProMultiSiteProductID[Environment.DEMO]]: 'https://websiteprodashboard-demo.com',
  [websiteProMultiSiteProductID[Environment.PROD]]: 'https://www.websiteprodashboard.com',
  [customerVoiceProductID[Environment.DEMO]]: 'https://steprep-demo-hrd.appspot.com',
  [customerVoiceProductID[Environment.PROD]]: 'https://customervoice.biz',
};

@Injectable({ providedIn: 'root' })
export class ProductSsoService {
  customSsoConfig: Map<string, (businessID: string, productID: string) => string>;
  constructor(
    private environmentService: EnvironmentService,
    private partnerService: PartnerService,
    private ssoService: SSOService,
  ) {
    this.customSsoConfig = new Map([
      [AppIDs.AdvertisingIntelligence, this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessBasicAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessBasicAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessStandardAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessStandardAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessPlusAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceBusinessPlusAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterpriseEssentialsAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterpriseEssentialsAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterpriseStandardAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterpriseStandardAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterprisePlusAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [googleWorkspaceEnterprisePlusAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [googleTransferAppId[Environment.DEMO], this.getGoogleTransferSsoURL.bind(this)],
      [googleTransferAppId[Environment.PROD], this.getGoogleTransferSsoURL.bind(this)],
      [constantContactAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [constantContactAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [godaddyAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [godaddyAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [msOfficeAppId[msOfficePlan.CUSTOMER][Environment.DEMO], this.getMsOfficeSsoURL.bind(this)],
      [msOfficeAppId[msOfficePlan.CUSTOMER][Environment.PROD], this.getMsOfficeSsoURL.bind(this)],
      [
        msOfficeRequestResellerRelationshipAppId[Environment.DEMO],
        this.getMsOfficeResellerRelationshipSsoURL.bind(this),
      ],
      [
        msOfficeRequestResellerRelationshipAppId[Environment.PROD],
        this.getMsOfficeResellerRelationshipSsoURL.bind(this),
      ],
      [whitelabelDomainAppId[Environment.DEMO], this.getCustomSsoURL.bind(this)],
      [whitelabelDomainAppId[Environment.PROD], this.getCustomSsoURL.bind(this)],
      [activeCampaignAppId[Environment.DEMO], this.getActiveCampaignSsoURL.bind(this)],
      [activeCampaignAppId[Environment.PROD], this.getActiveCampaignSsoURL.bind(this)],
      [activeCampaignAppIdV2[Environment.DEMO], this.getActiveCampaignSsoURLV2.bind(this)],
      [activeCampaignAppIdV2[Environment.PROD], this.getActiveCampaignSsoURLV2.bind(this)],
      [ecwidMonthlyAppId[Environment.DEMO], this.getEcwidSsoURL.bind(this)],
      [ecwidMonthlyAppId[Environment.PROD], this.getEcwidSsoURL.bind(this)],
      [ecwidYearlyAppId[Environment.DEMO], this.getEcwidSsoURL.bind(this)],
      [ecwidYearlyAppId[Environment.PROD], this.getEcwidSsoURL.bind(this)],
      [websiteProProductID[Environment.TEST], this.getWebsiteProSsoURL.bind(this)],
      [websiteProProductID[Environment.DEMO], this.getWebsiteProSsoURL.bind(this)],
      [websiteProProductID[Environment.PROD], this.getWebsiteProSsoURL.bind(this)],
      [websiteProMultiSiteProductID[Environment.DEMO], this.getWebsiteProSsoURL.bind(this)],
      [websiteProMultiSiteProductID[Environment.PROD], this.getWebsiteProSsoURL.bind(this)],
      [customerVoiceProductID[Environment.DEMO], this.getCustomerVoiceSsoUrl.bind(this)],
      [customerVoiceProductID[Environment.PROD], this.getCustomerVoiceSsoUrl.bind(this)],

      [rmProductID, this.getSSOServiceGatewayURL.bind(this)],
      [smProductID, this.getSSOServiceGatewayURL.bind(this)],
      [msProductID, this.getSSOServiceGatewayURL.bind(this)],
    ]);
  }

  hasCustomSso(productID: string): boolean {
    return this.customSsoConfig.has(productID);
  }

  getCustomSsoLink(productID: string, businessID: string): string {
    if (!this.hasCustomSso(productID)) {
      throw new Error(`Product ${productID} does not have a custom SSO link`);
    }
    return this.customSsoConfig.get(productID)(businessID, productID);
  }

  private getCustomSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/entry/partner/${partnerId}/business/${businessID}`;
    return host + path;
  }

  // Special case for website pro SSO
  private getWebsiteProSsoURL(accountID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID];
    const path = `/vdc-session/transfer/${partnerId}/${accountID}`;
    return `${host}${path}`;
  }

  private getGoogleTransferSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/entry/partner/${partnerId}/business/${businessID}/transfer/`;
    return host + path;
  }

  private getCustomerVoiceSsoUrl(businessID: string, productID: string): string {
    const host = hosts[productID];
    const path = `/cv/entry/${businessID}/`;
    return host + path;
  }

  private getMsOfficeSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/sso/entry/partner/${partnerId}/business/${businessID}`;
    return host + path;
  }

  private getMsOfficeResellerRelationshipSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/sso/partner/${partnerId}/business/${businessID}/transfer`;
    return host + path;
  }

  private getActiveCampaignSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/sso/entry/partner/${partnerId}/business/${businessID}`;
    return host + path;
  }

  private getActiveCampaignSsoURLV2(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const path = `/sso-v2/entry/partner/${partnerId}/business/${businessID}`;
    return host + path;
  }

  private getEcwidSsoURL(businessID: string, productID: string): string {
    const partnerId = this.partnerService.partnerId;
    const host = hosts[productID][this.environmentService.getEnvironment()];
    const actionID = 'awesome-sprites-8r7f';
    const path = `/oauth/login/app/${productID}/partner/${partnerId}/business/${businessID}/action/${actionID}`;
    return host + path;
  }

  private getSSOServiceGatewayURL(businessID: string, productID: string): string {
    return this.ssoService.getServiceGatewayURLForAccount(productID, businessID);
  }
}
