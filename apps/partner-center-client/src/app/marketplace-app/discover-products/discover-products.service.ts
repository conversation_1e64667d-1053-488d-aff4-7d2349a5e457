import { Inject, Injectable, OnDestroy } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { BillingService, Currency } from '@galaxy/billing';
import {
  AppDataService,
  AppPrices,
  AppPricingService,
  FieldMask,
  KindValues,
  MarketplaceCategory,
  Tag,
} from '@galaxy/marketplace-apps';
import { M3_TIER_VERSION, SubscriptionTiersService, VBP2022_VERSION, WhitelabelService } from '@galaxy/partner';
import { convertProductPricingToAppPrice } from '@vendasta/billing-ui';
import { CountryStateService } from '@vendasta/country-state';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AppPrice } from '@vendasta/marketplace-apps';
import {
  AddonOptions,
  AppEnablementStatus,
  DiscoverableAppSummary,
  MarketplaceAppService as PackagesAppsService,
  PartnerAppEnablementStatusService,
  DiscoverableAppSummary as PkgsDiscoverableAppSummary,
} from '@vendasta/marketplace-packages';
import { SubscriptionTierInterface } from '@vendasta/partner';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { BehaviorSubject, EMPTY, Observable, Subject, asyncScheduler, combineLatest, forkJoin, of } from 'rxjs';
import {
  concatMap,
  distinctUntilChanged,
  expand,
  filter,
  map,
  observeOn,
  publishReplay,
  reduce,
  refCount,
  scan,
  shareReplay,
  startWith,
  switchMap,
  take,
  takeUntil,
  takeWhile,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { UnifiedTOSDialogComponent } from '../../common/tos-unified/unified-tos-dialog.component';
import { GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES } from '../../constants';
import { DiscoverableItem, DiscoverableItemType } from '../../core/discoverable-item/discoverable-item.model';
import { ProductChangeService } from '../../core/product-change.service';
import { ResellerItemService } from '../../core/reseller/reseller-item.service';
import { WordpressContentItem, WordpressContentService } from '../../core/wordpress-content.service';
import { AppEnableDialogService } from '../app-enable/app-enable-dialog.service';
import { analyticsCategory, newReleasesTag } from './shared';

const productDetailsPath = 'marketplace/products/';

const knownKinds = [
  'tag/whitelabel',
  'tag/pricing',
  'tag/source',
  'tag/service',
  'tag/country',
  'tag/product_stats',
  'tag/fulfillment',
  'tag/marketingservices',
  'tag/category',
];

const featuredTag = [
  new Tag({
    kind: 'tag/marketplace',
    value: 'featured',
  }),
];
const freemiumTag = [
  new Tag({
    kind: 'tag/pricing',
    value: 'Freemium',
  }),
  new Tag({
    kind: 'tag/pricing',
    value: 'Trial_version',
  }),
];
const trendingTag = [
  new Tag({
    kind: 'tag/product_stats',
    value: 'trending',
  }),
];
const topSellingTag = [
  new Tag({
    kind: 'tag/product_stats',
    value: 'top_selling',
  }),
];

export interface AppSummaryWithPricing extends DiscoverableAppSummary {
  paidTierPricing?: AppPrice;
  freeTierPricing?: AppPrice;
  appPrices?: AppPrices;
  partnerCurrency?: Currency;
  hasSavings?: boolean;
}

export interface DiscoverMarketplaceCategory extends MarketplaceCategory {
  tag: Tag;
  parent?: DiscoverMarketplaceCategory;
}

export type View = 'featured-view' | 'all-products-view';

interface State {
  selectedCountry?: string;
  selectedCategory?: DiscoverMarketplaceCategory;
  selectedFilters: Tag[];
  searchTerm?: string;
  view: View;
  listLoading: boolean;
  listTotal: number;
  priceLoading: boolean;
}

const initialState: State = {
  selectedFilters: [],
  view: 'featured-view',
  listLoading: false,
  listTotal: null,
  priceLoading: false,
  selectedCountry: null,
};

const previousViewKey = 'discover_products--previous_view';

@Injectable({ providedIn: 'root' })
export class DiscoverProductsService implements OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  private store$$ = new BehaviorSubject<State>(initialState);
  private currentSubscriptionTier$: Observable<SubscriptionTierInterface>;
  private freeSubscriptionTier$: Observable<SubscriptionTierInterface>;
  private paidSubscriptionTier$: Observable<SubscriptionTierInterface>;
  public showTieredCost$: Observable<boolean>;
  public showUpgrade$: Observable<boolean>;

  private get state(): State {
    return this.store$$.getValue();
  }

  private set state(newState: State) {
    this.store$$.next(newState);
  }

  public kindValues$: Observable<KindValues[]>;

  public readonly selectedCountry$ = this.store$$.pipe(
    map((s) => s.selectedCountry),
    filter((c) => !!c),
    distinctUntilChanged(),
  );

  public readonly selectedFilters$ = this.store$$.pipe(
    map((s) => s.selectedFilters),
    distinctUntilChanged((a, b) => a === b || (a?.length === 0 && b?.length === 0)),
  );

  public get selectedFilters(): Tag[] {
    return this.store$$.getValue().selectedFilters;
  }

  public readonly searchTerm$ = this.store$$.pipe(
    map((s) => s.searchTerm),
    distinctUntilChanged(),
  );

  public readonly selectedCategory$ = this.store$$.pipe(
    map((s) => s.selectedCategory),
    distinctUntilChanged(),
  );

  public readonly activeCategoryIds$ = this.selectedCategory$.pipe(
    map((category) => (category ? (category.parent ? [category.parent.id, category.id] : [category.id]) : [])),
  );

  public readonly tags$ = combineLatest([this.selectedCategory$, this.selectedCountry$, this.selectedFilters$]).pipe(
    map(([selectedCategory, selectedCountry, selectedFilters]) => {
      const tags = [...selectedFilters];
      if (selectedCategory) {
        tags.push(selectedCategory.tag);
      }
      tags.push(
        new Tag({
          kind: 'tag/country',
          value: selectedCountry,
        }),
      );
      return tags;
    }),
  );

  public enablement$: Observable<Map<string, AppEnablementStatus>>;

  public readonly view$ = this.store$$.pipe(
    map((s) => s.view),
    distinctUntilChanged(),
  );

  public readonly listLoading$ = this.store$$.pipe(
    map((s) => s.listLoading),
    distinctUntilChanged(),
  );

  public readonly listTotal$ = this.store$$.pipe(
    map((s) => s.listTotal),
    distinctUntilChanged(),
  );

  public readonly pricePreviewLoading$ = this.store$$.pipe(
    map((s) => s.priceLoading),
    distinctUntilChanged(),
  );

  public readonly getCountriesOptions$ = this.countryStateService
    .getCountriesOptions()
    .pipe(publishReplay(1), refCount());

  public readonly helpContent$: Observable<WordpressContentItem>;

  private readonly loadMore$$ = new Subject<boolean>();

  unifiedTOSDialog: MatDialogRef<UnifiedTOSDialogComponent>;

  private partnerCurrency$: Observable<Currency>;

  constructor(
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    @Inject('MARKET_ID') private readonly marketId$: Observable<string>,
    private appDataService: AppDataService,
    private packagesAppsService: PackagesAppsService,
    private billingService: BillingService,
    private readonly analyticsService: ProductAnalyticsService,
    private readonly wordpressContentService: WordpressContentService,
    private readonly countryStateService: CountryStateService,
    private paesService: PartnerAppEnablementStatusService,
    private snackbarService: SnackbarService,
    private router: Router,
    private resellerItemService: ResellerItemService,
    private dialog: MatDialog,
    private productChangeService: ProductChangeService,
    private readonly whitelabelService: WhitelabelService,
    private appEnableDialogService: AppEnableDialogService,
    public pricingService: AppPricingService,
    private subscriptionTiersService: SubscriptionTiersService,
  ) {
    this.wordpressContentService.loadContent(
      'https://product-ads.websitepro.hosting/wp-json/wp/v2/posts?_embed=1&status=publish&include=1331',
    );

    this.helpContent$ = this.wordpressContentService.content.pipe(
      filter((items) => items?.length > 0),
      map((items) => items[0]),
    );

    this.kindValues$ = this.appDataService.listValuesByMultiKind('/', knownKinds).pipe(
      map((kvs) => {
        (kvs || []).forEach((item) => {
          if (item.kind === 'tag/product_stats' && !item.values.includes(newReleasesTag.value)) {
            item.values.push(newReleasesTag.value);
          }
        });
        return kvs;
      }),
    );

    const view = this.getPreviousView();
    if (view) {
      this.state = { ...this.state, view };
    }
    // Start with the status map constructed from the list response.
    // Then, update the status map as status changes occur.
    const statusMap$ = this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.paesService.listPartnerAppEnablementStatus(partnerId);
      }),
      map((status) =>
        !!status && status.length > 0 ? new Map(status.map((obj) => [obj.appId, obj.status])) : new Map(),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.enablement$ = statusMap$.pipe(
      switchMap((statusMap) =>
        this.productChangeService.changedPartnerAppEnablementStatus$.pipe(
          scan((sm, sc) => {
            if (!sc) {
              return sm;
            }

            sm.set(sc.appId, sc.status);
            return sm;
          }, statusMap),
          startWith(statusMap),
        ),
      ),
    );

    this.partnerCurrency$ = this.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.billingService
          .getMultiContract([partnerId])
          .pipe(map((contracts) => contracts[partnerId].currency));
      }),
    );

    // the partner configuration is used to determine the current subscription tier of a partner
    const partnerConfiguration$ = this.partnerId$.pipe(
      switchMap((partnerId) => this.whitelabelService.getConfiguration(partnerId)),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const partnerCountry$ = partnerConfiguration$.pipe(
      map((config) => config?.mailingConfiguration?.mailingCountry),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    combineLatest([partnerCountry$, this.getCountriesOptions$])
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(([partnerCountry, countryOptions]) => {
        // The partner's country can either be a country name or country code, so try to find the partner's corresponding
        // country option and use that to get their country code.
        // If the partner's corresponding country option can't be found, default to 'All'.
        const defaultCountry = countryOptions.find((co) => co.name === partnerCountry || co.code === partnerCountry);
        const selectedCountry = defaultCountry?.code ?? 'All';
        this.state = { ...this.state, selectedCountry };
      });

    // the current subscription tier is what is used to show prices when not showing price comparisons
    this.currentSubscriptionTier$ = combineLatest([this.partnerId$, partnerConfiguration$]).pipe(
      switchMap(([partnerId, partnerConfig]) => {
        return this.subscriptionTiersService.getSubscriptionTier(partnerConfig?.subscriptionTierId, partnerId);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // the paid subscription is used if we are showing cost comparisons
    this.paidSubscriptionTier$ = this.subscriptionTiersService.subscriptionTierList$.pipe(
      map((tiers) => tiers.find((tier) => tier.id === 'vbp_2022_essentials_subscription')),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // the free subscription tier displayed if we are showing cost comparisons
    this.freeSubscriptionTier$ = this.subscriptionTiersService.subscriptionTierList$.pipe(
      map((tiers) => tiers.find((tier) => tier.id === 'free_subscription')),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    // determine if we should show cost comparisons on the discover products page
    this.showTieredCost$ = this.currentSubscriptionTier$.pipe(
      map((currentTier) => {
        return (
          currentTier.id == 'free_subscription' ||
          currentTier.tierVersion == M3_TIER_VERSION ||
          currentTier.tierVersion == VBP2022_VERSION
        );
      }),
    );

    // show the upgrade banner only if the partner is on free
    this.showUpgrade$ = this.currentSubscriptionTier$.pipe(
      map((currentTier) => {
        return currentTier.id == 'free_subscription';
      }),
    );
  }

  ngOnDestroy(): void {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  private getPreviousView(): View {
    const view = localStorage.getItem(previousViewKey);
    return (view || null) as View;
  }

  private getAllDiscoverableApps(
    partnerId: string,
    marketId: string,
    whitelabelOverrideKey: string,
    tagFilters: Tag[],
  ): Observable<PkgsDiscoverableAppSummary[]> {
    const pageSize = 1000;
    return this.packagesAppsService
      .listDiscoverableApps(partnerId, pageSize, '', marketId, whitelabelOverrideKey, tagFilters)
      .pipe(
        expand((resp) => {
          if ((resp?.discoverableAppSummary || []).length >= pageSize) {
            return this.packagesAppsService.listDiscoverableApps(
              partnerId,
              pageSize,
              resp.nextCursor,
              marketId,
              whitelabelOverrideKey,
              tagFilters,
            );
          } else {
            return EMPTY;
          }
        }),
        reduce((prev, curr) => {
          const discoApps = curr?.discoverableAppSummary || [];
          return prev.concat(discoApps);
        }, [] as PkgsDiscoverableAppSummary[]),
      );
  }

  public listItems(): Observable<AppSummaryWithPricing[]> {
    const listWithWholesale$ = combineLatest([
      this.partnerId$,
      this.marketId$,
      this.tags$,
      this.searchTerm$,
      this.enablement$,
    ]).pipe(
      observeOn(asyncScheduler),
      switchMap(([partnerId, marketId, tags, searchTerm, enablement]) => {
        let cursor = null,
          hasMore = true,
          listItems = [];
        this.state = { ...this.state, listTotal: null, listLoading: true };

        return this.loadMore$$.pipe(
          startWith(true),
          takeWhile(() => hasMore),
          concatMap(() => {
            let sortOrder = 0;
            (tags || []).forEach((item) => {
              if (item.kind === 'tag/product_stats' && item.value === newReleasesTag.value) {
                sortOrder = 1;
              }
            });
            const addonOptions = {
              groupAddonsWithParent: true,
              // displayNonDiscoverableAddons: true // TODO this is still wonky
            } as AddonOptions;

            return this.packagesAppsService
              .listDiscoverableApps(
                partnerId,
                30,
                cursor,
                marketId,
                GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
                tags,
                searchTerm,
                sortOrder,
                addonOptions,
              )
              .pipe(
                switchMap((r) => {
                  cursor = r.nextCursor;
                  hasMore = r.hasMore;
                  const newItems = r.discoverableAppSummary || [];
                  newItems.forEach((app) => (app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED));

                  this.state = { ...this.state, listTotal: r.totalResults };

                  return forkJoin([of(newItems)]);
                }),
                switchMap(([newItems]) => {
                  return forkJoin([of(newItems)]);
                }),
                map(([newItems]) => {
                  this.state = { ...this.state };
                  listItems = [...listItems, ...newItems];
                  return listItems;
                }),
              );
          }),
        );
      }),
    );

    const listItemsWithGetMultiPricing$: Observable<AppSummaryWithPricing[]> = combineLatest([
      this.marketId$,
      listWithWholesale$,
      this.partnerCurrency$,
    ]).pipe(
      tap(() => (this.state = { ...this.state, priceLoading: true })),
      withLatestFrom(this.partnerId$),
      switchMap(([[marketId, listItemsWithWholesale, partnerCurrency], partnerId]) => {
        if (!partnerCurrency) {
          partnerCurrency = Currency.USD;
        }

        const appIds = listItemsWithWholesale.map((listItem) => {
          return listItem.appId;
        });
        return forkJoin([
          appIds.length
            ? this.pricingService.getMultiPricing(
                appIds,
                new FieldMask({ paths: ['msrp', 'wholesale'] }),
                partnerId,
                marketId,
              )
            : of([]),
          of(listItemsWithWholesale),
          of(partnerCurrency),
        ]);
      }),
      map(([pricing, visibleItems, partnerCurrency]) => {
        if (Object.keys(pricing).length) {
          const listItemsWithGetMultiPricing = visibleItems.map((item: AppSummaryWithPricing, index: number) => {
            const curPricing = pricing[index];
            item.appPrices = curPricing;
            item.partnerCurrency = partnerCurrency;
            return item;
          });
          return listItemsWithGetMultiPricing;
        }
        return visibleItems;
      }),
      tap(() => (this.state = { ...this.state })),
    );

    return combineLatest([
      this.showTieredCost$,
      this.currentSubscriptionTier$,
      this.freeSubscriptionTier$,
      this.paidSubscriptionTier$,
      listItemsWithGetMultiPricing$,
    ]).pipe(
      tap(() => (this.state = { ...this.state })),
      withLatestFrom(this.partnerId$),
      switchMap(
        ([
          [showTieredCosts, currentPricingTier, freePricingTier, paidPricingTier, listWithGetMultiPricing],
          partnerId,
        ]) => {
          let defaultTier = paidPricingTier.pricingPlanId;
          if (!showTieredCosts) {
            defaultTier = currentPricingTier?.pricingPlanId;
          }

          const billingSkus = listWithGetMultiPricing.map((thing) => {
            return thing.billingId;
          });

          return forkJoin([
            billingSkus.length
              ? this.billingService.getMultiProductPricing(partnerId, billingSkus, defaultTier)
              : of([]),
            billingSkus.length
              ? this.billingService.getMultiProductPricing(partnerId, billingSkus, freePricingTier.pricingPlanId)
              : of([]),
            of(listWithGetMultiPricing),
          ]);
        },
      ),
      withLatestFrom(this.partnerCurrency$),
      map(([[pricing, freePricing, visibleItems], partnerCurrency]) => {
        if (Object.keys(pricing).length && Object.keys(freePricing).length) {
          const listItemsWithTierPrice = visibleItems.map((item: AppSummaryWithPricing) => {
            const tierPricing = pricing[item.billingId];
            const tierFreePricing = freePricing[item.billingId];

            let editionId = '';
            const billingIdSplit = item.billingId.split(':');
            if (billingIdSplit.length > 1 && billingIdSplit[1].startsWith('ED')) {
              editionId = billingIdSplit[1];
            }
            item.hasSavings = tierPricing?.pricingRules[0]?.price < tierFreePricing?.pricingRules[0]?.price;
            if (tierPricing) {
              item.paidTierPricing = convertProductPricingToAppPrice(
                item.appId,
                editionId,
                editionId !== '' || this.isOAndO(item.appId),
                item.usesCustomPricing,
                partnerCurrency,
                tierPricing,
              );
            }

            if (tierFreePricing) {
              item.freeTierPricing = convertProductPricingToAppPrice(
                item.appId,
                editionId,
                editionId !== '' || this.isOAndO(item.appId),
                item.usesCustomPricing,
                partnerCurrency,
                tierFreePricing,
              );
            }

            return item;
          });

          return listItemsWithTierPrice;
        }

        return visibleItems;
      }),
      tap(() => (this.state = { ...this.state, listLoading: false, priceLoading: false })),
    );
  }

  private isOAndO(appID: string): boolean {
    //check if an o&o app
    const OAndO = new Map([
      ['RM', 'RM'], //reputation management
      ['MS', 'MS'], //listing builder
      ['SM', 'SM'], //social marketing
      ['MP-c4974d390a044c28aec31e421aa662b2', 'MP-c4974d390a044c28aec31e421aa662b2'], //customer voice
      ['MP-fba21121b71148c9bb33e11fcd92d520', 'MP-fba21121b71148c9bb33e11fcd92d520'], //customer voice demo
      ['MP-ee4ea04e553a4b1780caf7aad7be07cd', 'MP-ee4ea04e553a4b1780caf7aad7be07cd'], //website pro
    ]);

    return OAndO.has(appID);
  }

  public featuredItems(): Observable<DiscoverableAppSummary[]> {
    return combineLatest([this.partnerId$, this.marketId$, this.enablement$]).pipe(
      switchMap(
        ([pid, market_id, enablement]) =>
          this.getAllDiscoverableApps(
            pid,
            market_id,
            GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
            featuredTag,
          ).pipe(
            map((apps) =>
              apps.map((app) => {
                app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED;
                return app;
              }),
            ),
          ) || [],
      ),
    );
  }

  public freemiumAndTrialItems(): Observable<DiscoverableAppSummary[]> {
    return combineLatest([this.partnerId$, this.marketId$, this.enablement$]).pipe(
      switchMap(
        ([pid, market_id, enablement]) =>
          this.getAllDiscoverableApps(
            pid,
            market_id,
            GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
            freemiumTag,
          ).pipe(
            map((apps) =>
              apps.map((app) => {
                app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED;
                return app;
              }),
            ),
          ) || [],
      ),
    );
  }

  public topTrendingItems(): Observable<DiscoverableAppSummary[]> {
    return combineLatest([this.partnerId$, this.marketId$, this.enablement$]).pipe(
      switchMap(([pid, market_id, enablement]) =>
        this.packagesAppsService
          .listDiscoverableApps(
            pid,
            30,
            '',
            market_id,
            GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
            trendingTag,
            '',
            2,
          )
          .pipe(
            map((discoverableAppsResponse) => {
              return discoverableAppsResponse.discoverableAppSummary || [];
            }),
            map((apps) =>
              apps.map((app) => {
                app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED;
                return app;
              }),
            ),
          ),
      ),
    );
  }

  public topSellingItems(): Observable<DiscoverableAppSummary[]> {
    return combineLatest([this.partnerId$, this.marketId$, this.enablement$]).pipe(
      switchMap(([pid, market_id, enablement]) =>
        this.packagesAppsService
          .listDiscoverableApps(
            pid,
            30,
            '',
            market_id,
            GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
            topSellingTag,
            '',
            3,
          )
          .pipe(
            map((discoverableAppsResponse) => {
              return discoverableAppsResponse.discoverableAppSummary || [];
            }),
            map((apps) =>
              apps.map((app) => {
                app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED;
                return app;
              }),
            ),
          ),
      ),
    );
  }

  public newlyReleasedItems(): Observable<DiscoverableAppSummary[]> {
    return combineLatest([this.partnerId$, this.marketId$, this.enablement$]).pipe(
      switchMap(([pid, market_id, enablement]) =>
        this.packagesAppsService
          .listDiscoverableApps(
            pid,
            30,
            '',
            market_id,
            GET_VENDOR_APP_NAMES_INSTEAD_OF_WHITELABELED_NAMES,
            [newReleasesTag],
            '',
            1,
          )
          .pipe(
            map((discoverableAppsResponse) => {
              return discoverableAppsResponse.discoverableAppSummary || [];
            }),
            map((apps) =>
              apps.map((app) => {
                app.enabled = enablement.get(app.appId) === AppEnablementStatus.ENABLED;
                return app;
              }),
            ),
          ),
      ),
    );
  }

  handleCategorySelect(selectedCategory: DiscoverMarketplaceCategory): void {
    this.state = { ...this.state, selectedCategory, view: 'all-products-view', searchTerm: null };
  }

  removeCategorySelect(): void {
    this.state = { ...this.state, selectedCategory: null };
  }

  handleFilterSelect(tag: Tag): void {
    const curSelectedFilters = [...this.state.selectedFilters];
    let newFilters: Tag[];
    if (curSelectedFilters.filter((el) => el.value === tag.value).length > 0) {
      newFilters = curSelectedFilters.filter((el) => el.value !== tag.value);
    } else {
      newFilters = curSelectedFilters.concat(tag);
    }
    this.state = { ...this.state, selectedFilters: newFilters, view: 'all-products-view', searchTerm: null };
  }

  handleCountrySelect(selectedCountry: string): void {
    this.state = { ...this.state, selectedCountry, view: 'all-products-view', searchTerm: '' };
  }

  handleNewSearchTerm(searchTerm: string): void {
    // If we have a category or filters selected and the search term is empty,
    // don't update the state to avoid overwriting them when redirecting from
    // the featured section to the all section.
    if (this.state.selectedFilters.length > 0 && searchTerm.length === 0) {
      return;
    }
    this.state = {
      ...this.state,
      searchTerm,
      view: searchTerm?.length > 0 ? 'all-products-view' : this.state.view,
      selectedFilters: [],
      selectedCategory: null,
    };
  }

  handleViewSelect(view: View, selectedFilters?: Tag[]): void {
    this.state = {
      ...this.state,
      view,
      selectedFilters: selectedFilters || [],
      selectedCategory: null,
      searchTerm: '',
    };
    if (view) {
      localStorage.setItem(previousViewKey, view.toString());
    }
  }

  handleLoadMore(): void {
    this.loadMore$$.next(true);
  }

  trackStructEvent(label: string, value?: string): void {
    this.analyticsService.trackEvent(analyticsCategory, label, value);
  }

  public navigateToDetails(discoverableItem: DiscoverableItem): void {
    switch (discoverableItem.type) {
      case DiscoverableItemType.AddonType:
        this.getParentAppId(discoverableItem)
          .pipe(take(1))
          .subscribe((parentAppId) => {
            if (!parentAppId) {
              this.snackbarService.errorSnack("Oops! Sorry we weren't able to load the details for this product!");
            } else {
              this.router.navigateByUrl(productDetailsPath + `${parentAppId}/addon/${discoverableItem.appId}`);
            }
          });
        break;
      case DiscoverableItemType.AppType:
        this.router.navigateByUrl(productDetailsPath + discoverableItem.appId);
        break;
      default:
        this.snackbarService.errorSnack("Oops! Sorry we weren't able to load the details for this product!");
    }
  }

  public getRouterLink(appSummary: AppSummaryWithPricing): string {
    switch (appSummary.type) {
      case DiscoverableItemType.AddonType:
        return `${appSummary.parent.appId}/addon/${appSummary.appId}`;
      case DiscoverableItemType.AppType:
        return appSummary.appId;
      default:
        this.snackbarService.errorSnack("Oops! Sorry we weren't able to load the details for this product!");
    }
  }

  public navigateToAppSummary(appSummary: AppSummaryWithPricing): void {
    this.router.navigateByUrl(productDetailsPath + this.getRouterLink(appSummary));
  }

  private getParentAppId(discoverableItem: DiscoverableItem | DiscoverableAppSummary): Observable<string> {
    return this.resellerItemService.getAddonData().pipe(
      tap((addons) => {
        if (!addons || addons.length === 0) {
          this.snackbarService.openWithOptions('opening addon details...');
        }
      }),
      filter((addons) => Boolean(addons) && addons.length > 0),
      map((resellerItems) => {
        const addon = resellerItems.find((re) => {
          return re.addonId === discoverableItem.appId;
        });
        if (!addon || !addon.appId) {
          return undefined;
        }
        return addon.appId;
      }),
    );
  }

  handleAddToStore(appSummary: DiscoverableAppSummary | DiscoverableItem): void {
    this.appEnableDialogService.openDialog({
      productId: appSummary.appId,
      productName: appSummary.name,
      sku: appSummary.billingId,
      origin: 'discover products page',
    });
  }

  openUnifiedTOSDialog(): void {
    if (this.unifiedTOSDialog) {
      return;
    }
    this.unifiedTOSDialog = this.dialog.open(UnifiedTOSDialogComponent, new MatDialogConfig());
  }
}
