import { Inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BillingService } from '@galaxy/billing';
import { Environment, EnvironmentService } from '@galaxy/core';
import { AppPartnerService, AppPricingService, AppSettings } from '@galaxy/marketplace-apps';
import { AppPrice, FieldMask } from '@vendasta/marketplace-apps';
import { ActivationInterface } from '@vendasta/snapshot-widget';
import { Observable, combineLatest, of } from 'rxjs';
import { catchError, map, publishReplay, refCount, share, switchMap, take, withLatestFrom } from 'rxjs/operators';
import { AppEditionService } from '../../../activation/core/app-edition.service';
import { AppIdToEditionIdToEditionMap } from '../../../activation/core/interface';
import { IncludedProduct, convertToIncludedProducts } from '../../../business/easy-account-create/utils';
import { AppIDs } from '../../../constants';
import { BillingApiService, PurchaseCost } from '../../../core/billing';
import { ProductService } from '../../../core/product.service';
import { Product } from '../../../core/product/product';

const DEFAULT_INCLUDED_PRODUCTS_PROD = [
  { appId: 'RM', editionId: 'EDITION-F7JZ5TV8' },
  { appId: AppIDs.ListingBuilder, editionId: '' },
  { appId: 'SM', editionId: 'EDITION-FVGBNLVZ' },
  { appId: 'MP-c4974d390a044c28aec31e421aa662b2', editionId: 'EDITION-TC8HJZNS' }, // Customer Voice
  { appId: AppIDs.AdvertisingIntelligence, editionId: '' }, // AdIntel
  { appId: 'MP-ee4ea04e553a4b1780caf7aad7be07cd', editionId: 'EDITION-VFNL43ZF' }, // Website | Express
  { appId: AppIDs.CalendarHeroAppIDs[Environment.PROD], editionId: '' },
];

const DEFAULT_INCLUDED_PRODUCTS_DEMO = [
  { appId: 'RM', editionId: 'EDITION-38SMW45H' },
  { appId: AppIDs.ListingBuilder, editionId: '' },
  { appId: 'SM', editionId: 'EDITION-SWVF3WH8' },
  { appId: 'MP-fba21121b71148c9bb33e11fcd92d520', editionId: 'EDITION-4WWZC3RJ' }, // Customer Voice
  { appId: AppIDs.AdvertisingIntelligence, editionId: '' }, // AdIntel
  { appId: 'MP-9cc9f21f0a234a46ad78087fc09f16bc', editionId: 'EDITION-RC58KN73' }, // Website | Express
  { appId: AppIDs.CalendarHeroAppIDs[Environment.DEMO], editionId: '' },
];

export const DEFAULT_INCLUDED_PRODUCTS = {
  [Environment.PROD]: DEFAULT_INCLUDED_PRODUCTS_PROD,
  [Environment.DEMO]: DEFAULT_INCLUDED_PRODUCTS_DEMO,
};

export interface SkuToPurchaseCostMap {
  sku: string;
  purchaseCost: PurchaseCost;
}

@Injectable()
export class IncludedProductsService {
  includedProducts$: Observable<IncludedProduct[]>;
  currencyCode$: Observable<string>;

  constructor(
    @Inject('PARTNER_ID') private readonly partnerId$: Observable<string>,
    private appPartnerService: AppPartnerService,
    private environmentService: EnvironmentService,
    private productService: ProductService,
    private appEditionService: AppEditionService,
    private billingApiService: BillingApiService,
    private appPricingService: AppPricingService,
    private billingService: BillingService,
  ) {
    this.currencyCode$ = this.partnerId$.pipe(
      takeUntilDestroyed(),
      switchMap((partnerId) => {
        return this.billingService.getMultiContract([partnerId]).pipe(
          map((contracts) => {
            const contract = contracts[partnerId];
            return (contract ? contract.currency : '') as string;
          }),
        );
      }),
    );
  }

  initialize(marketSelected$: Observable<string>, includedProducts: ActivationInterface[]): void {
    // List App Settings
    const appSettings$: Observable<AppSettings[]> = combineLatest([this.partnerId$, marketSelected$]).pipe(
      switchMap(([partnerId, marketId]) => {
        const defaultPageSize = 1000;
        return this.appPartnerService.listAppSettings(partnerId, marketId, '', defaultPageSize).pipe(
          take(1),
          catchError(() => of(null)),
        );
      }),
      map((resp) => (resp ? resp.appSettings : [])),
      share(),
    );

    // Get all Products
    const env = this.environmentService.getEnvironment();
    const defaultIncludedAppIds = DEFAULT_INCLUDED_PRODUCTS[env].map((p) => p.appId);

    const products$: Observable<Product[]> = appSettings$.pipe(
      switchMap((appSettings) => {
        const allAppIds = defaultIncludedAppIds.concat(
          appSettings.reduce((list, as) => {
            if (
              as.autoActivation &&
              as.autoActivation.autoActivateByDefault &&
              !defaultIncludedAppIds.some((id) => id === as.appId)
            ) {
              list.push(as.appId);
            }
            return list;
          }, []),
        );
        return this.productService.getProducts(allAppIds);
      }),
      map((products) => products.filter((p) => p.product_id)),
      publishReplay(1),
      refCount(),
    );

    // Get Editions Map, AppSettings doesn't YET support choosing an editionID
    const includedEditionIdentifier = DEFAULT_INCLUDED_PRODUCTS[env];
    const editionMap$: Observable<AppIdToEditionIdToEditionMap> =
      this.appEditionService.getEditionsForEditionIdentifiers(includedEditionIdentifier);

    // Get Pricing
    const pricingMap$: Observable<Map<string, AppPrice>> = combineLatest([
      products$,
      this.partnerId$,
      marketSelected$,
    ]).pipe(
      switchMap(([products, partnerId]) => {
        const productIds: Array<string> = [];
        products.map((product) => {
          productIds.push(product.product_id);
        });
        return this.appPricingService.getMultiPricing(
          productIds,
          new FieldMask({ paths: ['wholesale'] }),
          partnerId,
          '',
        );
      }),
      map((appPrices) => {
        return appPrices.reduce((acc, appPrice) => {
          if (!appPrice) {
            return acc;
          }
          acc.set(appPrice?.appId, appPrice?.pricesForContexts['wholesale']);
          return acc;
        }, new Map<string, AppPrice>());
      }),
    );

    // Combine data for Included Products table
    this.includedProducts$ = combineLatest([editionMap$, pricingMap$]).pipe(
      withLatestFrom(products$, appSettings$),
      map(([[editions, pricingMap], products, appSettings]) => {
        return convertToIncludedProducts(appSettings, products, editions, pricingMap).map((product) => ({
          ...product,
          checked: includedProducts.some(
            (includedProduct) =>
              includedProduct.appKey?.appId === product.appId &&
              (includedProduct.appKey?.editionId ?? '') === (product.editionId ?? ''),
          ),
        }));
      }),
    );
  }
}
