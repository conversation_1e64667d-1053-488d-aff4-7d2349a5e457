<glxy-form-field id="crm-category-form">
  <glxy-label>
    Business categories
    <crm-sync-icon [fieldId]="_fieldId()"></crm-sync-icon>
  </glxy-label>
  <mat-chip-grid [disabled]="loadingValues()" #categoryGrid aria-label="Business category selection">
    @if (loadingValues()) {
      <mat-spinner [diameter]="24"></mat-spinner>
    } @else {
      @let _loadingNewValueId = loadingNewValueId();
      @for (category of selectedCategories(); track category) {
        @if (_loadingNewValueId !== category.externalId) {
          <mat-chip-row (removed)="remove(category.externalId); saveField()">
            {{ category.name }}
            <button matChipRemove [attr.aria-label]="'remove ' + category.name">
              <mat-icon>cancel</mat-icon>
            </button>
          </mat-chip-row>
        } @else {
          <mat-chip-row class="stencil-shimmer"></mat-chip-row>
        }
      }
    }
    <input
      matInput
      #categoryInput
      [placeholder]="hasMaxCategories() ? '' : 'Select...'"
      [formControl]="searchControl"
      [matChipInputFor]="categoryGrid"
      [matAutocomplete]="auto"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      [matChipInputAddOnBlur]="false"
      [readonly]="hasMaxCategories()"
      (focus)="isInputActive = true"
      (blur)="isInputActive = false; saveField()"
      data-testid="filter-categories-input"
    />
  </mat-chip-grid>
  @if (hasMaxCategories() && isInputActive) {
    <glxy-error>
      {{ 'CRM.FORMS.MAXIMUM_CATEGORIES' | translate: { maximumCategories: maxCategories } }}
    </glxy-error>
  }
  <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" (optionSelected)="selected($event)">
    @if (availableOptions() | excludeCategories: selectedCategoriesIDs(); as filteredOptions) {
      @for (option of filteredOptions; track option.categoryId; let isLast = $last) {
        <mat-option [value]="option?.externalId" (mousedown)="$event.preventDefault()">
          @if (isLast) {
            <glxy-infinite-scroll-trigger (isVisible)="fetchMoreCategories()"></glxy-infinite-scroll-trigger>
          }
          {{ option?.fullName }}
        </mat-option>
      }
    }
  </mat-autocomplete>
  @if (_control.hasError('invalidCategory')) {
    <glxy-error>
      {{ 'CRM.FORMS.INVALID_CATEGORY' | translate }}
    </glxy-error>
  }
</glxy-form-field>
