import {
  CreateAssociationFields,
  CrmObjectDependencies,
  CustomObjectAssociationsComponent,
  CustomObjectToCompanyAssociationsPanelComponent,
  CustomObjectToContactAssociationsPanelComponent,
  ProfileCard,
  StandardIds,
} from '@galaxy/crm/static';
import { Services } from './interface';
import { Observable, of } from 'rxjs';
import { CrmObjectInterface } from '@vendasta/crm';

export class CustomObjectDependencies {
  constructor(private readonly services: Services) {}

  build(): CrmObjectDependencies {
    return {
      baseColumnIds: this.baseColumnIds(),
      getDisplayName: (crmObject: CrmObjectInterface) => {
        return this.services.crmObjectDisplayService.customObjectDisplayName(crmObject);
      },
      createAssociationFields: this.createAssociationFields(),
      profileCards$: this.profileCards$(),
    };
  }

  static build(services: Services): CrmObjectDependencies {
    const instance = new CustomObjectDependencies(services);
    return instance.build();
  }

  private baseColumnIds(): string[] {
    return [StandardIds.CustomObjectName];
  }

  private profileCards$(): Observable<ProfileCard[]> {
    const profileCards = [
      { component: CustomObjectToContactAssociationsPanelComponent },
      { component: CustomObjectToCompanyAssociationsPanelComponent },
      { component: CustomObjectAssociationsComponent },
    ] as ProfileCard[];
    return of(profileCards);
  }

  private createAssociationFields(): CreateAssociationFields {
    return {
      objectTypes: ['Contact', 'Company', 'Opportunity', 'CustomObject'],
      required: false,
    };
  }
}
