import {
  AutomationMultiRowAction,
  AutomationSelectAllRowAction,
  AutomationSingleRowAction,
} from '@galaxy/crm/integrations/automation';
import {
  CompanyCustomFilterChipComponent,
  CompanyCustomFilterInputComponent,
  ContactCampaignsCardComponent,
  ContactCustomColumns,
  CreateAssociationFields,
  CRMFilterOverride,
  CrmObjectDependencies,
  CRMSelectAllOptions,
  CustomObjectAssociationsComponent,
  FormCustomInput,
  FormDefaultInputValue,
  HiddenTextFormInputComponent,
  InitialFilters,
  MultiRowAction,
  ProfileCard,
  SelectAllAction,
  SingleRowAction,
  StandardExternalIds,
  StandardIds,
  SystemFieldIds,
  TableCustomCell,
} from '@galaxy/crm/static';
import { Row } from '@vendasta/galaxy/table';
import { SubjectParticipant, ConversationChannel, GlobalParticipantType } from '@vendasta/conversation';
import { combineLatest, Observable, of } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { AdditionalSalespeopleCellComponent } from '../custom-cells/additional-salespeople/additional-salespeople.component';
import { SalespersonCellComponent } from '../custom-cells/salesperson/salesperson.component';
import { AdditionalSalespeopleFilterInputComponent } from '../custom-filters/additional-salespeople/additional-salespeople-filter-input.component';
import { MarketFilterChipComponent, MarketFilterInputComponent } from '../custom-filters/market';
import { SalespersonFilterChipComponent } from '../custom-filters/salesperson/salesperson-filter-chip.component';
import { SalespersonFilterInputComponent } from '../custom-filters/salesperson/salesperson-filter-input.component';
import { AdditionalSalespeopleFormInputComponent } from '../custom-form-inputs/additional-salespeople/additional-salespeople-form-input.component';
import { SalespersonFormInputComponent } from '../custom-form-inputs/salesperson/salesperson-form-input.component';
import { PccContactToCompanyAssociationsCardComponent } from '../profile-cards/pcc-contact-to-company-associations-card/pcc-contact-to-company-associations-card.component';
import { Config, Services } from './interface';
import { CrmObjectInterface } from '@vendasta/crm';
import { GroupIdFormInputComponent } from '../custom-form-inputs/group-id/group-id-form-input.component';
import { CRMMarketContext } from '../salesperson-market.service';
import { GetMultiFieldSchemaRequestInterface } from '@vendasta/crm/lib/_internal/interfaces';
import {
  AddToListMultiRowAction,
  AddToListSelectAllAction,
  AddToListSingleRowAction,
} from '@galaxy/crm/integrations/dynamic-lists-actions';
import { FeatureFlags } from '../../core/features';
import { ExportSelectAllRowAction } from '@galaxy/crm/dynamic';
import { Feature } from 'marketplace-ui';
import { AddToCampaignMultiRowAction, AddToCampaignSingleRowAction } from '@galaxy/crm/integrations/yesware';
import { objectViewPresets$ } from './factory';

export class ContactDependencies {
  constructor(
    private readonly services: Services,
    private readonly config: Config,
  ) {}

  private readonly fieldIDs = {
    contactPhone: this.services.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber),
    email: this.services.crmFieldService.getFieldId(StandardExternalIds.Email),
    tags: this.services.crmFieldService.getFieldId(StandardExternalIds.Tags),
  };

  build(): CrmObjectDependencies {
    return {
      services: {
        campaignService: this.services.contactCampaignService,
      },
      selectAllTableActions$: this.selectAllTableActions$(),
      multiRowTableActions$: this.multiRowTableActions$(),
      singleRowTableActions$: this.singleRowTableActions$(),
      profileCards$: this.profileCards$(),
      filterInputOverrides$: this.filterInputOverrides$(),
      initialFilters$: this.initialFilters$(),
      tableCustomCells: this.tableCustomCells(),
      formCustomInputs: this.formCustomInput(),
      formDefaultOnCreateInputValues$: this.formDefaultOnCreateInputValues$(),
      baseColumnIds: this.baseColumnIds(),
      presetFilters$: objectViewPresets$(
        this.services.translationService,
        this.services.translateForObjectService,
        this.config.salespersonId$,
        'Contact',
        this.services.crmFieldService.getFieldId(StandardIds.ContactLastEngagementDate),
        this.services.crmFieldService.getFieldId(StandardIds.ContactPrimarySalespersonID),
      ),
      onProfilePageInit: (object: CrmObjectInterface) => {
        this.services.marketsService.selectMarketByID(CRMMarketContext.EditContext, object?.groupId);
      },
      createAssociationFields: this.createAssociationFields(),
      additionalBaseFormFieldIds$: of([SystemFieldIds.ContactGroupID]),
    };
  }

  static build(services: Services, config: Config): CrmObjectDependencies {
    const instance = new ContactDependencies(services, config);
    return instance.build();
  }

  private createAssociationFields(): CreateAssociationFields {
    return {
      objectTypes: ['Company'],
      required: false,
    };
  }

  private profileCards$(): Observable<ProfileCard[]> {
    const profileCards = [
      { component: PccContactToCompanyAssociationsCardComponent },
      { component: ContactCampaignsCardComponent },
    ] as ProfileCard[];
    return combineLatest([of(profileCards), this.config.featureFlags$[FeatureFlags.PCC_CUSTOM_OBJECTS]]).pipe(
      map(([profileCards, hasCustomObjects]) => {
        if (hasCustomObjects) {
          profileCards.push({ component: CustomObjectAssociationsComponent });
        }
        return profileCards;
      }),
    );
  }

  private filterInputOverrides$(): Observable<CRMFilterOverride[]> {
    return of([
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.PRIMARY_COMPANY'),
        fieldId: StandardIds.ContactPrimaryCompanyID,
        filterInput: CompanyCustomFilterInputComponent,
        filterChip: CompanyCustomFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.SALESPERSON'),
        fieldId: StandardIds.ContactPrimarySalespersonID,
        filterInput: SalespersonFilterInputComponent,
        filterChip: SalespersonFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.ADDITIONAL_SALESPEOPLE'),
        fieldId: StandardIds.ContactAdditionalSalespersonIDs,
        filterInput: AdditionalSalespeopleFilterInputComponent,
        filterChip: SalespersonFilterChipComponent,
      },
      {
        title: this.services.translationService.instant('COMMON.MARKET_SELECTOR.LABEL'),
        fieldId: SystemFieldIds.ContactGroupID,
        filterInput: MarketFilterInputComponent,
        filterChip: MarketFilterChipComponent,
      },
    ] as CRMFilterOverride[]);
  }

  private initialFilters$(): Observable<InitialFilters[]> {
    return this.config.partnerId$.pipe(
      switchMap((partnerId) => {
        return this.services.crmFieldSchemaApiService.getMultiFieldSchema({
          namespace: partnerId,
          crmObjectType: 'Contact',
          fieldId: [
            StandardExternalIds.Email,
            StandardIds.ContactPrimarySalespersonID,
            SystemFieldIds.ContactCreated,
            SystemFieldIds.ContactLastActivityDate,
            StandardIds.ContactLifecycleStage,
          ],
        } as GetMultiFieldSchemaRequestInterface);
      }),
      map((response) => {
        return response.fieldSchemas
          .filter((schema) => !!schema && schema.fieldId)
          .map((schema) => {
            return {
              fieldId: schema.fieldId,
              filterId: 'filterContactBy' + schema.fieldName,
            };
          });
      }),
      shareReplay(1),
    );
  }

  private tableCustomCells(): TableCustomCell[] {
    return [
      {
        fieldIds: [StandardIds.ContactPrimarySalespersonID],
        columnDefinition: {
          id: 'customSalespersonColumn',
          title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.SALESPERSON'),
        },
        customCellComponent: SalespersonCellComponent,
      },
      {
        fieldIds: [StandardIds.ContactAdditionalSalespersonIDs],
        columnDefinition: {
          id: 'customAdditionalSalespeopleColumn',
          title: this.services.translationService.instant('CRM.TABLE.CUSTOM_COLUMNS.ADDITIONAL_SALESPEOPLE'),
        },
        customCellComponent: AdditionalSalespeopleCellComponent,
      },
    ] as TableCustomCell[];
  }

  private formCustomInput(): FormCustomInput[] {
    return [
      {
        fieldId: SystemFieldIds.ContactGroupID,
        component: GroupIdFormInputComponent,
      },
      {
        fieldId: StandardIds.ContactPrimarySalespersonID,
        component: SalespersonFormInputComponent,
      },
      {
        fieldId: StandardIds.ContactAdditionalSalespersonIDs,
        component: AdditionalSalespeopleFormInputComponent,
      },
      {
        fieldId: SystemFieldIds.ContactOwnerID,
        component: HiddenTextFormInputComponent,
      },
      {
        fieldId: StandardIds.ContactAdditionalTeamMemberIDs,
        component: HiddenTextFormInputComponent,
      },
    ] as FormCustomInput[];
  }

  private formDefaultOnCreateInputValues$(): Observable<FormDefaultInputValue[]> {
    return combineLatest([
      this.config.salespersonId$,
      this.services.appConfigService.config$,
      this.services.marketsService.userAccessibleMarkets$,
    ]).pipe(
      map(([salespersonId, appConfig, markets]) => {
        const defaults = [
          { fieldId: StandardIds.ContactLifecycleStage, value: 'Lead' },
          { fieldId: SystemFieldIds.ContactOwnerID, value: appConfig.unifiedUserId },
          { fieldId: StandardIds.ContactSourceName, value: 'CRM UI' },
          { fieldId: StandardIds.ContactRecordSourceDrill1, value: window.location.href },
        ];
        if (!!markets && markets.length === 1 && markets[0].market_id) {
          this.services.marketsService.selectMarket(CRMMarketContext.EditContext, markets[0]);
          defaults.push({
            fieldId: SystemFieldIds.ContactGroupID,
            value: markets[0].market_id,
          });
        }
        if (salespersonId) {
          defaults.push({
            fieldId: StandardIds.ContactPrimarySalespersonID,
            value: salespersonId,
          });
        }
        return defaults;
      }),
    );
  }

  private baseColumnIds(): string[] {
    return [
      ContactCustomColumns.FullName,
      ContactCustomColumns.PrimaryCompanyName,
      this.fieldIDs.contactPhone,
      this.fieldIDs.email,
      this.fieldIDs.tags,
      StandardIds.ContactSourceName,
      SystemFieldIds.ContactCreated,
    ];
  }

  private singleRowTableActions$(): Observable<SingleRowAction[]> {
    return combineLatest([
      this.config.canAddToList$,
      this.config.canStartManualAutomation$,
      this.services.partnerService.getPartnerId(),
      this.config.canAccessMarketingAutomation$,
      this.config.featureFlags$[FeatureFlags.INBOX_SMS],
      this.services.accessService.hasAccessToFeature(Feature.myProducts),
    ]).pipe(
      map(
        ([
          canAddToList,
          canStartManualAutomation,
          partnerId,
          canAccessMarketingAutomation,
          canInboxSMS,
          canAddYeswareCampaigns,
        ]) => {
          const actions: SingleRowAction[] = [];
          if (canInboxSMS) {
            actions.push(this.goToConversationSingleRowAction(partnerId));
          }

          if (canAddToList) {
            actions.push(
              AddToListSingleRowAction(
                partnerId,
                this.services.listActionsService,
                'Contact',
                'CRM.ADD_TO_STATIC_LIST',
              ),
            );
          }

          if (canAccessMarketingAutomation) {
            actions.push({
              label: 'CONTACT_TABLE_ADD_TO_CAMPAIGN',
              callback: (row: Row) => {
                this.services.productAnalyticsService.trackEvent(
                  'user-clicked-crm-table-add-single-contact',
                  'send-campaign-workflow',
                  'click',
                  null,
                );
                this.services.contactCampaignService.navigateToSendCampaign([row.id]);
              },
            });
          }
          if (canStartManualAutomation) {
            actions.push(
              AutomationSingleRowAction(
                partnerId,
                this.services.automationActionsService,
                'Contact',
                'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION',
              ),
            );
          }
          if (canAddYeswareCampaigns) {
            actions.push(
              AddToCampaignSingleRowAction(
                { partnerId: partnerId },
                this.services.yeswareCampaignActionsService,
                'YESWARE.ACTIONS.ADD_TO_CAMPAIGN',
              ),
            );
          }
          return actions;
        },
      ),
      shareReplay(1),
    );
  }

  private multiRowTableActions$(): Observable<MultiRowAction[]> {
    return combineLatest([
      this.config.canAddToList$,
      this.config.canAccessMarketingAutomation$,
      this.config.canStartManualAutomation$,
      this.services.partnerService.getPartnerId(),
      this.services.accessService.hasAccessToFeature(Feature.myProducts),
    ]).pipe(
      map(
        ([canAddToList, canAccessMarketingAutomation, canStartManualAutomation, partnerId, canAddYeswareCampaigns]) => {
          const actions: MultiRowAction[] = [];

          if (canAddToList) {
            actions.push(
              AddToListMultiRowAction(
                partnerId,
                this.services.listActionsService,
                'Contact',
                this.services.translationService.instant('CRM.ADD_TO_STATIC_LIST'),
              ),
            );
          }

          if (canAccessMarketingAutomation) {
            actions.push({
              label: this.services.translationService.instant('CONTACT_TABLE_ADD_TO_CAMPAIGN'),
              callback: (rows: Row[]) => {
                this.services.productAnalyticsService.trackEvent(
                  'user-clicked-crm-table-add-multi-contacts',
                  'send-campaign-workflow',
                  'click',
                  null,
                );
                this.services.contactCampaignService.navigateToSendCampaign(rows.map((c) => c.id));
              },
            });
          }
          if (canStartManualAutomation) {
            actions.push(
              AutomationMultiRowAction(
                partnerId,
                this.services.automationActionsService,
                'Contact',
                'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION',
              ),
            );
          }
          if (canAddYeswareCampaigns) {
            actions.push(
              AddToCampaignMultiRowAction(
                { partnerId: partnerId },
                this.services.yeswareCampaignActionsService,
                'YESWARE.ACTIONS.ADD_TO_CAMPAIGN',
              ),
            );
          }
          return actions;
        },
      ),
    );
  }

  private selectAllTableActions$(): Observable<SelectAllAction[]> {
    return combineLatest([
      this.config.canAddToList$,
      this.services.partnerService.getPartnerId(),
      this.config.canAccessMarketingAutomation$,
      this.config.canStartManualAutomation$,
    ]).pipe(
      map(([canAddToList, partnerId, canAccessMarketingAutomation, canStartManualAutomation]) => {
        const actions: SelectAllAction[] = [];
        if (canAddToList) {
          actions.push(
            AddToListSelectAllAction(partnerId, this.services.listActionsService, 'Contact', 'CRM.ADD_TO_STATIC_LIST'),
          );
        }
        if (canAccessMarketingAutomation) {
          actions.push({
            label: this.services.translationService.instant('CONTACT_TABLE_ADD_TO_CAMPAIGN'),
            callback: (rows: Row[], selectOptions?: CRMSelectAllOptions) => {
              this.services.productAnalyticsService.trackEvent(
                'user-clicked-crm-table-select-all-contacts',
                'send-campaign-workflow',
                'click',
                null,
              );
              this.services.contactCampaignService.navigateToSendCampaign(
                rows.map((c) => c.id),
                selectOptions.filters,
                selectOptions.search,
                selectOptions.useSelectAll,
                selectOptions.totalObjects,
              );
            },
            selectAllVisible: true,
          });
        }
        if (canStartManualAutomation) {
          actions.push(
            AutomationSelectAllRowAction(
              partnerId,
              this.services.automationActionsService,
              'Contact',
              this.services.translationService.instant('COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION'),
            ),
          );
        }
        actions.push(
          ExportSelectAllRowAction(
            partnerId,
            this.services.exportActionsService,
            'Contact',
            this.services.translationService.instant('ACTIONS.CRM.EXPORT.LABEL'),
          ),
        );
        return actions;
      }),
    );
  }

  private goToConversationSingleRowAction(partnerId: string): SingleRowAction {
    return {
      label: 'CRM.SEND_MESSAGE',
      callback: (row: Row) => {
        const phone = row.data[this.fieldIDs.contactPhone]?.value;
        // TODO(WARP): allow more channels when available
        if (!phone) {
          this.services.snackbar.openErrorSnack('CRM.UNABLE_TO_SEND_MESSAGE');
          return;
        }
        const channel = ConversationChannel.CONVERSATION_CHANNEL_SMS;
        const participants = [
          new SubjectParticipant({
            internalParticipantId: partnerId,
            participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_PARTNER,
          }),
          new SubjectParticipant({
            internalParticipantId: row.id,
            participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_CUSTOMER,
          }),
        ];
        this.services.inboxNavigationService.gotoConversationChannel(participants, channel);
      },
    };
  }
}
