import { AsyncPipe } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { Router } from '@angular/router';
import { PaymentFacilitatorError, PaymentFacilitatorType, PaymentService } from '@galaxy/billing';
import { AccountRejectedBannerComponent } from '@galaxy/partner-center-client/billing/static/account/account-rejected-banner/account-rejected-banner.component';
import { PaymentFacilitatorErrorStateComponent } from '@galaxy/partner-center-client/billing/static/payment-facilitator-error-state/payment-facilitator-error-state.component';
import { RetailStatusAlertComponent } from '@galaxy/partner-center-client/billing/static/retail-status-alert';
import { VPaymentService } from '@galaxy/partner-center-client/billing/static/v-payments';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { PayoutsComponent, SMBInvoicingModule } from '@vendasta/smb-invoicing';
import { EMPTY, Observable, ReplaySubject, take } from 'rxjs';
import { catchError, map, shareReplay, switchMap } from 'rxjs/operators';
import { getImageSrc } from '../../core/images';
import { OnboardingHandlerService } from '../shared/onboarding-handler.service';

@Component({
  templateUrl: './payouts-page.component.html',
  styleUrls: ['./payouts-page.component.scss'],
  standalone: true,
  imports: [
    PayoutsComponent,
    MatIcon,
    TranslateModule,
    AsyncPipe,
    RetailStatusAlertComponent,
    AccountRejectedBannerComponent,
    PaymentFacilitatorErrorStateComponent,
    GalaxyPageModule,
    SMBInvoicingModule,
  ],
})
export class PayoutsPageComponent implements OnInit {
  isVPaymentsAccountRejected$: Observable<boolean>;
  redirectToStripe$: Observable<boolean>;
  deferToStripeImgUrl: string;
  paymentFacilitatorError$$: ReplaySubject<PaymentFacilitatorError> = new ReplaySubject(1);

  constructor(
    @Inject('PARTNER_ID') protected readonly partnerId$: Observable<string>,
    private paymentService: PaymentService,
    private settingsService: VPaymentService,
    private router: Router,
    private onboardingHandler: OnboardingHandlerService,
  ) {}

  ngOnInit(): void {
    this.partnerId$
      .pipe(
        take(1),
        switchMap((id) =>
          this.paymentService.retailStatus(id).pipe(
            catchError((err) => {
              this.paymentFacilitatorError$$.next(err?.error?.details?.[0]?.reason);
              return EMPTY;
            }),
          ),
        ),
      )
      .subscribe();

    this.redirectToStripe$ = this.partnerId$.pipe(
      switchMap((id) => this.paymentService.getRetailProvider(id)),
      map((provider) => provider.paymentFacilitatorType === PaymentFacilitatorType.STANDARD_STRIPE),
      shareReplay(1),
    );
    this.isVPaymentsAccountRejected$ = this.settingsService.isAccountRejected$;
    this.deferToStripeImgUrl = getImageSrc('images/defer-payouts-to-stripe.png');
  }

  navigateToPayout(selectedPayoutId: string) {
    this.router.navigate(['/payouts', selectedPayoutId]);
  }

  onboardingStateContactSupportActionClicked(event: boolean): void {
    if (!event) {
      return;
    }
    this.onboardingHandler.handleContactSupportClick();
  }

  onboardingStateSetupVendastaPaymentsActionClicked(event: boolean): void {
    if (!event) {
      return;
    }
    this.onboardingHandler.handleSetupVendastaPaymentsClick();
  }
}
