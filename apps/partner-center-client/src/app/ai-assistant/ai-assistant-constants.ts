import {
  AiAssistant,
  ConnectionType,
  DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON,
  DEFAULT_CONNECTION_PREFIX,
  WEBCHAT_ICON,
} from '@galaxy/ai-assistant';
import { ASSISTANT_ID_CHAT_RECEPTIONIST, ASSISTANT_ID_VOICE_RECEPTIONIST } from '@galaxy/ai-assistant';
import { AssistantType, Connection } from '@vendasta/ai-assistants';
import { AiConnection } from '@galaxy/ai-assistant';

export const NEW_WIDGET_CONFIGURATION_URL = 'inbox/widgets/new';
const DEFAULT_WIDGET_CONFIGURATION_URL = 'restricted/inbox-ai-webchat';

export const AI_DEFAULT_PARTNERS_ASSISTANT_WORKFORCE: AiAssistant[] = [
  {
    assistant: {
      id: ASSISTANT_ID_CHAT_RECEPTIONIST,
      name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.TITLE',
      type: AssistantType.ASSISTANT_TYPE_INBOX,
    },
    subtitleKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.SUBTITLE',
    descriptionKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.DESCRIPTION',
    decoration: {
      defaultAvatarIcon: DEFAULT_CHAT_RECEPTIONIST_AVATAR_SVG_ICON,
      gradientColor: '#22C0CA',
    },
    isDefault: true,
    showConfigButtonOnDefault: true,
  },
  {
    assistant: {
      id: ASSISTANT_ID_VOICE_RECEPTIONIST,
      name: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.TITLE',
      type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
    },
    descriptionKey: 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.DESCRIPTION',
    decoration: {
      defaultAvatarIcon: 'ai-voice-receptionist',
      gradientColor: '#A16EEF',
    },
    isDefault: true,
    showConfigButtonOnDefault: false,
    computeCTA: (assistant: AiAssistant) => {
      // If the assistant is the default (i.e. no assistant has been provisioned), then the CTA is to upgrade
      if (
        assistant.isDefault ||
        !assistant.connections?.some((connection) => connection.connection.connectionType === ConnectionType.Voice)
      ) {
        return {
          label: 'UPGRADE_SUBSCRIPTION.UPGRADE_NOW',
          action: {
            pathCommands: ['/upgrade'],
            showButton: true,
          },
        };
      }
    },
  },
];

export const AI_DEFAULT_PARTNERS_ASSISTANT_CONNECTIONS: AiConnection[] = [
  {
    connection: new Connection({
      id: DEFAULT_CONNECTION_PREFIX + 'webchat',
      name: 'Webchat',
      connectionTypeName: 'Webchat',
      iconUrl: WEBCHAT_ICON,
      connectionType: ConnectionType.WebchatWidget,
      supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
      configurationUrl: DEFAULT_WIDGET_CONFIGURATION_URL,
      isConnectionLocked: true,
    }),
    isDefault: true,
    cta: {
      action: {
        url: DEFAULT_WIDGET_CONFIGURATION_URL,
        showButton: true,
      },
    },
  },
];
