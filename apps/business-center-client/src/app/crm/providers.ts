import { inject, InjectionToken } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import {
  CompanyAssociationPanelComponent,
  CompanyCustomFilterChipComponent,
  CompanyCustomFilterInputComponent,
  CompanyToContactAssociationsCardComponent,
  CompanyToCustomObjectAssociationsCardComponent,
  CompanyToOpportunityAssociationsPanelComponent,
  ContactAssociationPanelComponent,
  ContactCampaignsCardComponent,
  ContactCustomColumns,
  CrmDependencies,
  CrmFieldService,
  CRMFilterOverride,
  CrmMultiLocationDependencies,
  CrmObjectDisplayService,
  CrmPipelineService,
  CustomObjectAssociationsComponent,
  CustomObjectToCompanyAssociationsPanelComponent,
  CustomObjectToContactAssociationsPanelComponent,
  CustomObjectToOpportunityAssociationsPanelComponent,
  DueDateCellComponent,
  FormDefaultInputValue,
  HiddenTextFormInputComponent,
  MultiLocationContext,
  ObjectType,
  OpportunityAssociationPanelComponent,
  ParentChildCompanyAssociationsCardComponent,
  PresetFilter,
  ProfileCard,
  ProfileHeader,
  StandardExternalIds,
  StandardIds,
  SystemFieldIds,
  TableCustomCell,
  TaskCustomColumns,
  TeamMemberFilterChipComponent,
  TeamMemberFilterInputComponent,
  TranslateForCrmObjectService,
  UserCellComponent,
  UserFilterChipComponent,
  UserFilterInputComponent,
} from '@galaxy/crm/static';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { AccountGroupApiService, GetMultiResponse } from '@vendasta/account-group';
import { BusinessCenterApiService } from '@vendasta/business-center';
import { combineLatest, filter, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { editionIds, partnerId, productIds } from '../../globals';
import { AccountGroupService } from '../account-group';
import { AccountsService } from '@vendasta/accounts/legacy';
import { AuthService } from '../auth.service';
import { FeatureFlagService } from '../core/feature-flag.service';
import { catchError, debounceTime, distinctUntilChanged, startWith } from 'rxjs/operators';
import { AddContactsToCampaignRedirectService } from '@galaxy/campaign/shared';
import { TranslateService } from '@ngx-translate/core';
import { ProductService } from '../core/product.service';
import { isBrand, LocationsService } from '../locations';
import { CRMUserService } from './user.service';
import { AdditionalTeamMemberFilterInputComponent } from './custom-filters/team-member/team-member-filter-input.component';
import { TeamMemberFormInputComponent } from './custom-form-inputs/team-member/team-member-form-input.component';
import { AdditionalTeamMemberFormInputComponent } from './custom-form-inputs/team-member/additional-team-member-form-input.component';
import { OwnerCellComponent } from './custom-cells/team-member/owner.component';
import { TeamMemberCellComponent } from './custom-cells/team-member/team-member.component';
import { CRMAccessService } from './access.service';
import { WhitelabelService } from '@galaxy/partner';
import { Feature, PartnerMarketConfigService } from '../partner-market-config';
import { ConversationChannel } from '@vendasta/conversation';
import {
  CrmUserColumnCellComponent,
  CrmUserFilterChipComponent,
  CrmUserFilterInputComponent,
  CrmUserInputComponent,
} from '@galaxy/crm/components/user';
import { addMonths } from 'date-fns';
import { CurrencyService } from '../currency';
import {
  buildContactMultiRowTableActions,
  buildContactSelectAllTableActions,
  buildContactSingleRowTableActions,
} from './contact-actions';
import { ListActionsService } from '@galaxy/crm/integrations/dynamic-lists-actions';
import { CampaignsProDemo, CampaignsProProd } from './constants';
import {
  buildCompanyMultiRowTableActions,
  buildCompanySelectAllTableActions,
  buildCompanySingleRowTableActions,
} from './company-actions';
import { HostService, IsCalendarConfiguredRequestInterface } from '@vendasta/meetings';
import { MeetingEventService } from '@galaxy/crm/integrations/meeting-event';
import { InboxService } from './inbox.service';
import { CacheFactory, CacheInjectionOptions, CacheStrategy } from '@galaxy/crm/components/cache';
import { ExportActionsService } from '@galaxy/crm/dynamic';
import {
  CrmPipelineColumnCellComponent,
  CrmPipelineFilterChipComponent,
  CrmPipelineFilterInputComponent,
  CrmStageColumnCellComponent,
} from '@galaxy/crm/components/pipeline';
import { NpsBadgeComponent } from '@galaxy/crm/integrations/reputation';
import { LanguageService } from '../core/language-service.service';
import { AutomationActionsService } from '@galaxy/crm/integrations/automation';
import { LeadScoreBadgeComponent } from '@galaxy/crm/components/badge';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ReputationService } from '../reputation/reputation.service';
import { Brand } from '../brands/brand';
import { CrmObjectInterface } from '@vendasta/crm';
import { GuardsCheckStart, Router } from '@angular/router';
import { CampaignActionsService } from '@galaxy/crm/integrations/yesware';
import { GalaxyDateDefault, GalaxyFilterInterface, GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';

export const CrmInjectionToken = new InjectionToken<CrmDependencies>('dependencies given to crm library', {
  factory: () => {
    const cacheStrategy: CacheStrategy = { maxAge: 20000, observable: { share: true }, autoCleanup: true };
    const crmUserServiceCacheOptions: CacheInjectionOptions<CRMUserService> = {
      methods: [{ method: 'getMultiUsers', strategy: cacheStrategy }],
    };
    const userService = CacheFactory.inject(CRMUserService, crmUserServiceCacheOptions);

    const pipelineCacheStrategy: CacheStrategy = { maxAge: 20000, autoCleanup: true };
    const crmPipelineServiceCacheOptions: CacheInjectionOptions<CrmPipelineService> = {
      methods: [
        { method: 'getMultiPipeline$', strategy: pipelineCacheStrategy },
        { method: 'getPipeline$', strategy: pipelineCacheStrategy },
      ],
    };
    const pipelineService = CacheFactory.inject(CrmPipelineService, crmPipelineServiceCacheOptions);

    const translationService = inject(TranslateService);
    const translateForObjectService = inject(TranslateForCrmObjectService);
    const accountGroupService = inject(AccountGroupService);
    const featureFlagService = inject(FeatureFlagService);
    const accountGroupApiService = inject(AccountGroupApiService);
    const businessCenterApiService = inject(BusinessCenterApiService);
    const contactCampaignService = inject(AddContactsToCampaignRedirectService);
    const authService = inject(AuthService);
    const activeAppService = inject(ProductService);
    const accessService = inject(CRMAccessService);
    const whitelabelService = inject(WhitelabelService);
    const locationsService = inject(LocationsService);
    const crmFieldService = inject(CrmFieldService);
    const productAnalyticsService = inject(ProductAnalyticsService);
    const partnerMarketConfigService = inject(PartnerMarketConfigService);
    const currencyService = inject(CurrencyService);
    const crmInboxService = inject(InboxService);
    const listActionsService = inject(ListActionsService);
    const meetingService = inject(HostService);
    const meetingEventService = inject(MeetingEventService);
    const exportActionsService = inject(ExportActionsService);
    const languageService = inject(LanguageService);
    const automationActionsService = inject(AutomationActionsService);
    const accountsService = inject(AccountsService);
    const reputationService = inject(ReputationService);
    const confirmationModalService = inject(OpenConfirmationModalService);
    const snackbarService = inject(SnackbarService);
    const crmObjectDisplayService = inject(CrmObjectDisplayService);
    const router = inject(Router);
    const contactPhoneFieldID = crmFieldService.getFieldId(StandardExternalIds.PhoneNumber);
    const emailFieldID = crmFieldService.getFieldId(StandardExternalIds.Email);
    const tagsFieldID = crmFieldService.getFieldId(StandardExternalIds.Tags);
    const yeswareCampaignActionsService = inject(CampaignActionsService);

    const accountGroupIdFromRoute$ = router.events.pipe(
      startWith(location.pathname),
      filter((event) => {
        return event instanceof GuardsCheckStart || typeof event === 'string';
      }),
      map((event) => {
        let url = '';
        if (typeof event === 'string') {
          url = event as string;
        } else {
          url = (event as GuardsCheckStart).url;
        }
        return extractAccountGroupIdFromRoute(url);
      }),
      // when coming from a page outside of the CRM module
      // the provider won't have initialized yet
      // and location.pathname won't be matching the regex.
      debounceTime(100),
    );

    const namespace$ = locationsService.currentLocation$.pipe(
      switchMap((location) => {
        if (isBrand(location)) {
          return accountGroupIdFromRoute$;
        } else {
          return of(location.accountGroupId);
        }
      }),
      distinctUntilChanged(),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const parentNamespace$ = namespace$.pipe(
      switchMap((accountGroupId) => {
        if (!accountGroupId) {
          return of({
            accountGroups: [],
          } as GetMultiResponse);
        }
        return accountGroupApiService.getMulti({
          accountGroupIds: [accountGroupId],
          projectionFilter: { accountGroupExternalIdentifiers: true },
        });
      }),
      map((response) => response.accountGroups[0]?.accountGroup?.accountGroupExternalIdentifiers?.partnerId),
    );

    const configuration$ = locationsService.marketId$.pipe(
      switchMap((marketId) => whitelabelService.getConfiguration(partnerId, marketId)),
    );
    const showContacts$ = configuration$.pipe(map((config) => config.businessCenterConfiguration.showCustomers));
    const showCompanies$ = configuration$.pipe(map((config) => config.businessCenterConfiguration.showCrmCompanies));

    const companyToCompanyFeatureFlag$ = featureFlagService.checkFeatureFlag(
      partnerId,
      '',
      'crm_company_company_association',
    );
    const crmOpportunitiesFeatureFlag$ = featureFlagService.checkFeatureFlag(
      partnerId,
      '',
      'crm_opportunity_business_app',
    );
    const crmCustomObjectFeatureFlag$ = featureFlagService.checkFeatureFlag(partnerId, '', 'bcc_crm_custom_objects');

    const showOpportunities$ = combineLatest([configuration$, crmOpportunitiesFeatureFlag$]).pipe(
      map(([config, featureFlag]) => {
        return config.businessCenterConfiguration.showCrmOpportunities && featureFlag;
      }),
    );

    const showCustomObjects$ = combineLatest([configuration$, crmCustomObjectFeatureFlag$]).pipe(
      map(([config, featureFlag]) => {
        return config.businessCenterConfiguration.showCrmCustomObjects && featureFlag;
      }),
    );

    const companyProfileCards$: Observable<ProfileCard[]> = combineLatest([
      companyToCompanyFeatureFlag$,
      showOpportunities$,
      showContacts$,
      showCustomObjects$,
    ]).pipe(
      map(([companyCompanyAssociationsFlag, showOpportunities, showContacts, showCustomObjects]) => {
        const cards = [] as ProfileCard[];
        if (showContacts) {
          cards.push({ component: CompanyToContactAssociationsCardComponent });
        }
        if (showOpportunities) {
          cards.push({ component: CompanyToOpportunityAssociationsPanelComponent });
        }
        if (companyCompanyAssociationsFlag) {
          cards.push({ component: ParentChildCompanyAssociationsCardComponent });
        }
        if (showCustomObjects) {
          cards.push({ component: CompanyToCustomObjectAssociationsCardComponent });
        }
        return cards;
      }),
    );
    const contactProfileCards$ = combineLatest([
      showCompanies$,
      showOpportunities$,
      activeAppService.activeProducts$.pipe(startWith([])),
      showCustomObjects$,
    ]).pipe(
      map(([showCompanies, showOpportunities, activeProducts, showCustomObjects]) => {
        const cards = [] as ProfileCard[];
        if (showCompanies) {
          cards.push({ component: CompanyAssociationPanelComponent });
        }
        if (showOpportunities) {
          cards.push({ component: OpportunityAssociationPanelComponent });
        }
        if (
          activeProducts.filter((app) => app.productId === CampaignsProProd || app.productId === CampaignsProDemo)
            .length != 0
        ) {
          cards.push({ component: ContactCampaignsCardComponent });
        }
        if (showCustomObjects) {
          cards.push({ component: CustomObjectAssociationsComponent });
        }
        return cards;
      }),
    );
    const opportunityProfileCards$ = combineLatest([showCompanies$, showContacts$, showCustomObjects$]).pipe(
      map(([showCompanies, showContacts, showCustomObjects]) => {
        const cards = [] as ProfileCard[];
        if (showContacts) {
          cards.push({ component: ContactAssociationPanelComponent });
        }
        if (showCompanies) {
          cards.push({ component: CompanyAssociationPanelComponent });
        }
        if (showCustomObjects) {
          cards.push({ component: CustomObjectAssociationsComponent });
        }
        return cards;
      }),
    );

    const customObjectProfileCards$ = combineLatest([
      showCompanies$,
      showContacts$,
      showOpportunities$,
      showCustomObjects$,
    ]).pipe(
      map(([showCompanies, showContacts, showOpportunities, showCustomObjects]) => {
        const cards = [] as ProfileCard[];
        if (showContacts) {
          cards.push({ component: CustomObjectToContactAssociationsPanelComponent });
        }
        if (showCompanies) {
          cards.push({ component: CustomObjectToCompanyAssociationsPanelComponent });
        }
        if (showOpportunities) {
          cards.push({ component: CustomObjectToOpportunityAssociationsPanelComponent });
        }
        if (showCustomObjects) {
          cards.push({ component: CustomObjectAssociationsComponent });
        }
        return cards;
      }),
    );

    const isMeetingSchedulerConfigured$ = combineLatest([
      accountGroupService.currentAccountGroupId$,
      toObservable(authService.effectiveUserId),
    ]).pipe(
      map(([namespace, currentUser]) => {
        const request: IsCalendarConfiguredRequestInterface = {
          applicationContextProperties: {
            business_id: namespace,
            user_context: 'SMB',
          },
          userId: currentUser,
        };
        return request;
      }),
      switchMap((request) => meetingService.IsCalendarConfigured(request)),
      map((response) => {
        return response.isConfigured == true;
      }),
    );

    const hasMeetingFeatureFlag$ = parentNamespace$.pipe(
      filter((parentNamespace) => !!parentNamespace),
      switchMap((parentNamespace) => {
        return featureFlagService.checkFeatureFlag(parentNamespace, '', 'email_trigger_crm_contact');
      }),
      startWith(false),
    );

    const currentUserId$ = toObservable(authService.effectiveUserId);
    const locale$ = languageService.currentLocale$;
    const rmPremiumActive$: Observable<boolean> = accountGroupService.currentAccountGroupId$.pipe(
      switchMap((accountGroupId) => accountsService.list(accountGroupId, partnerId)),
      map((res) => res.accounts || []),
      map((accounts) => accounts.filter((a) => !a.trial && !a.deactivation)),
      map((accounts) =>
        accounts.some(
          (account) =>
            account.productId === productIds.reputationManagement &&
            account.editionId === editionIds.reputationManagementPremium,
        ),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
    const yeswareActive$: Observable<boolean> = accountGroupService.currentAccountGroupId$.pipe(
      switchMap((accountGroupId) => accountsService.list(accountGroupId, partnerId)),
      map((res) => res.accounts || []),
      map((accounts) => accounts.filter((a) => !a.trial && !a.deactivation)),
      map((accounts) => {
        return accounts.some((account) => account.productId === 'MP' && account.appId === productIds.yesware);
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    return {
      appID: 'business-center-client',
      routePrefix$: accountGroupService.currentAccountGroupId$.pipe(
        map((accountGroupId) => `/account/location/${accountGroupId}/crm`),
      ),
      namespace$: namespace$,
      parentNamespace$: parentNamespace$,
      currentUserId$: currentUserId$,
      locale$: locale$,
      viewProfileFlag$: of(true),
      hasCrmAssociationModalFeatureFlag$: parentNamespace$.pipe(
        filter((parentNamespace) => !!parentNamespace),
        switchMap((parentNamespace) => {
          return featureFlagService.checkFeatureFlag(parentNamespace, '', 'crm_association_modal');
        }),
        startWith(false),
      ),
      customObjectFeatureFlag: 'bcc_crm_custom_objects',
      hasTimelineActivitiesFeatureFlag$: parentNamespace$.pipe(
        filter((parentNamespace) => !!parentNamespace),
        switchMap((parentNamespace) => {
          return featureFlagService.checkFeatureFlag(parentNamespace, '', 'crm_timeline_activities');
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      ),
      activity: {
        filterInputOverrides$: filterActivityInputOverrides$(translationService),
      },
      contact: {
        services: {
          campaignService: contactCampaignService,
        },
        showRightHandProfilePanel$: contactProfileCards$.pipe(map((cards) => cards.length > 0)),
        profileCards$: contactProfileCards$,
        createAssociationFields: {
          objectTypes: ['Company'],
          required: false,
        },
        presetFilters$: objectViewPresets$(
          translationService,
          translateForObjectService,
          currentUserId$,
          'Contact',
          crmFieldService.getFieldId(StandardIds.ContactLastEngagementDate),
          crmFieldService.getFieldId(SystemFieldIds.ContactOwnerID),
        ),
        filterInputOverrides$: filterContactInputOverrides$(translationService),
        initialFilters$: of([
          { fieldId: StandardExternalIds.Email, filterId: 'filterContactByEmail' },
          { fieldId: SystemFieldIds.ContactCreated, filterId: 'filterContactByCreated' },
          { fieldId: SystemFieldIds.ContactLastActivityDate, filterId: 'filterContactByLastActivity' },
          { fieldId: StandardIds.ContactLifecycleStage, filterId: 'filterContactByLifecycleStage' },
        ]),
        baseColumnIds: [
          ContactCustomColumns.FullName,
          ContactCustomColumns.PrimaryCompanyName,
          contactPhoneFieldID,
          emailFieldID,
          tagsFieldID,
          StandardIds.ContactSourceName,
          SystemFieldIds.ContactCreated,
        ],
        formCustomInputs: [
          {
            fieldId: SystemFieldIds.ContactGroupID,
            component: HiddenTextFormInputComponent,
          },
          {
            fieldId: SystemFieldIds.ContactOwnerID,
            component: TeamMemberFormInputComponent,
          },
          {
            fieldId: StandardIds.ContactAdditionalTeamMemberIDs,
            component: AdditionalTeamMemberFormInputComponent,
          },
        ],
        formDefaultOnCreateInputValues$: of([
          { fieldId: StandardIds.ContactSourceName, value: 'CRM UI' },
          { fieldId: StandardIds.ContactRecordSourceDrill1, value: window.location.href },
        ] as FormDefaultInputValue[]),
        tableCustomCells: contactTableCustomCells(translationService),
        singleRowTableActions$: buildContactSingleRowTableActions(
          contactCampaignService,
          productAnalyticsService,
          accountGroupService,
          featureFlagService,
          listActionsService,
          crmInboxService,
          accessService,
          automationActionsService,
          translationService,
          confirmationModalService,
          reputationService,
          snackbarService,
          rmPremiumActive$,
          yeswareCampaignActionsService,
          yeswareActive$,
          activeAppService,
        ),
        multiRowTableActions$: buildContactMultiRowTableActions(
          contactCampaignService,
          accountGroupService,
          translationService,
          activeAppService,
          productAnalyticsService,
          featureFlagService,
          listActionsService,
          accessService,
          automationActionsService,
          confirmationModalService,
          reputationService,
          snackbarService,
          rmPremiumActive$,
          yeswareCampaignActionsService,
          yeswareActive$,
        ),
        selectAllTableActions$: buildContactSelectAllTableActions(
          contactCampaignService,
          translationService,
          activeAppService,
          productAnalyticsService,
          accountGroupService,
          exportActionsService,
          featureFlagService,
          listActionsService,
          accessService,
          automationActionsService,
          confirmationModalService,
          businessCenterApiService,
          snackbarService,
          rmPremiumActive$,
        ),
        profileHeader$: buildContactHeaders$(partnerMarketConfigService),
      },
      company: {
        showRightHandProfilePanel$: companyProfileCards$.pipe(map((cards) => cards.length > 0)),
        profileCards$: companyProfileCards$,
        createAssociationFields: {
          objectTypes: ['Contact'],
          required: false,
        },
        presetFilters$: objectViewPresets$(
          translationService,
          translateForObjectService,
          currentUserId$,
          'Company',
          crmFieldService.getFieldId(StandardIds.CompanyLastEngagementDate),
          crmFieldService.getFieldId(SystemFieldIds.CompanyOwnerID),
        ),
        filterInputOverrides$: filterCompanyInputOverride$(translationService),
        initialFilters$: of([
          { fieldId: StandardIds.CompanyName, filterId: 'filterCompanyByName' },
          { fieldId: SystemFieldIds.CompanyCreated, filterId: 'filterCompanyByCreated' },
          { fieldId: SystemFieldIds.CompanyLastActivityDate, filterId: 'filterCompanyByLastActivity' },
          { fieldId: StandardIds.CompanyLifecycleStage, filterId: 'filterCompanyByLifecycleStage' },
        ]),
        formCustomInputs: [
          {
            fieldId: SystemFieldIds.CompanyGroupID,
            component: HiddenTextFormInputComponent,
          },
          {
            fieldId: SystemFieldIds.CompanyOwnerID,
            component: TeamMemberFormInputComponent,
          },
          {
            fieldId: StandardIds.CompanyAdditionalTeamMemberIDs,
            component: AdditionalTeamMemberFormInputComponent,
          },
        ],
        formDefaultOnCreateInputValues$: of([
          { fieldId: StandardIds.CompanySourceName, value: 'CRM UI' },
          { fieldId: StandardIds.CompanyRecordSourceDrill1, value: window.location.href },
        ] as FormDefaultInputValue[]),
        tableCustomCells: companyTableCustomCells(translationService),
        showCampaigns$: activeAppService.activeProducts$.pipe(
          map((apps) => {
            return (
              apps.filter((app) => app.productId === CampaignsProProd || app.productId === CampaignsProDemo).length > 0
            );
          }),
        ),
        singleRowTableActions$: buildCompanySingleRowTableActions(
          accountGroupService,
          featureFlagService,
          listActionsService,
          accessService,
          automationActionsService,
        ),
        multiRowTableActions$: buildCompanyMultiRowTableActions(
          accountGroupService,
          featureFlagService,
          translationService,
          listActionsService,
          accessService,
          automationActionsService,
        ),
        selectAllTableActions$: buildCompanySelectAllTableActions(
          accountGroupService,
          featureFlagService,
          translationService,
          listActionsService,
          exportActionsService,
          accessService,
          automationActionsService,
        ),
      },
      task: {
        filterInputOverrides$: of([
          {
            title: translationService.instant('CRM.CUSTOM_FILTERS.ASSIGNED_TO'),
            fieldId: SystemFieldIds.ActivityOwnerID,
            filterInput: UserFilterInputComponent,
            filterChip: UserFilterChipComponent,
          },
        ]),
        tableCustomCells: [
          {
            fieldIds: [SystemFieldIds.ActivityOwnerID],
            columnDefinition: {
              id: TaskCustomColumns.AssignedTo,
              title: translationService.instant('CRM.CUSTOM_CELLS.ASSIGNED_TO'),
            },
            customCellComponent: UserCellComponent,
          },
          {
            fieldIds: [StandardIds.ActivityTaskDueDate, StandardIds.ActivityTaskStatus],
            columnDefinition: {
              id: StandardIds.ActivityTaskDueDate,
            },
            customCellComponent: DueDateCellComponent,
          },
        ],
      },
      opportunity: {
        profileCards$: opportunityProfileCards$,
        formCustomInputs: [
          {
            fieldId: SystemFieldIds.OpportunityOwnerID,
            component: CrmUserInputComponent,
          },
        ],
        filterInputOverrides$: of([
          {
            title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
            fieldId: SystemFieldIds.OpportunityOwnerID,
            filterInput: CrmUserFilterInputComponent,
            filterChip: CrmUserFilterChipComponent,
          },
          {
            title: translationService.instant('CRM.CUSTOM_FILTERS.PIPELINE'),
            fieldId: StandardIds.OpportunityPipelineID,
            filterInput: CrmPipelineFilterInputComponent,
            filterChip: CrmPipelineFilterChipComponent,
          },
        ]),
        tableCustomCells: [
          {
            fieldIds: [SystemFieldIds.OpportunityOwnerID],
            columnDefinition: {
              id: SystemFieldIds.OpportunityOwnerID,
            },
            customCellComponent: CrmUserColumnCellComponent,
          },
          {
            fieldIds: [StandardIds.OpportunityPipelineID],
            columnDefinition: {
              id: StandardIds.OpportunityPipelineID,
            },
            customCellComponent: CrmPipelineColumnCellComponent,
          },
          {
            fieldIds: [StandardIds.OpportunityCalculatedStageID, StandardIds.OpportunityPipelineID],
            columnDefinition: {
              id: StandardIds.OpportunityCalculatedStageID,
            },
            customCellComponent: CrmStageColumnCellComponent,
          },
        ],
        createAssociationFields: {
          objectTypes: ['Contact', 'Company'],
          required: true,
        },
        formDefaultOnCreateInputValues$: combineLatest([currentUserId$, currencyService.getMarketCurrency()]).pipe(
          map(([userId, currencyCode]) => [
            {
              fieldId: StandardIds.OpportunityAmount,
              value: {
                currencyCode: currencyCode,
                amount: 0,
              },
            },
            {
              fieldId: SystemFieldIds.OpportunityOwnerID,
              value: userId,
            },
            {
              fieldId: StandardIds.OpportunityExpectedCloseDate,
              value: addMonths(new Date(), 1),
            },
            {
              fieldId: StandardIds.OpportunityStatus,
              value: 'Open',
            },
            {
              fieldId: StandardIds.OpportunitySourceName,
              value: 'CRM UI',
            },
          ]),
        ),
      },
      lists: {
        canStartManualAutomation$: accessService.canRunManualAutomations$,
      },
      customObject: {
        showRightHandProfilePanel$: customObjectProfileCards$.pipe(map((cards) => cards.length > 0)),
        profileCards$: customObjectProfileCards$,
        baseColumnIds: [StandardIds.CustomObjectName],
        getDisplayName: (crmObject: CrmObjectInterface) => {
          return crmObjectDisplayService.customObjectDisplayName(crmObject);
        },
        createAssociationFields: {
          objectTypes: ['Contact', 'Company', 'Opportunity', 'CustomObject'],
          required: false,
        },
      },
      services: {
        userService: userService,
        accessService: accessService,
        pipelineService: pipelineService,
      },

      meetingAction: (contactId, currentUserId, namespace, parentNamespace) => {
        meetingEventService.openMeetingEventListDialog(contactId, currentUserId, namespace, parentNamespace);
      },

      isMeetingIconNeeded$: combineLatest([isMeetingSchedulerConfigured$, hasMeetingFeatureFlag$]).pipe(
        map(
          ([isMeetingSchedulerConfigured, hasMeetingFeatureFlag]) =>
            isMeetingSchedulerConfigured && hasMeetingFeatureFlag,
        ),
      ),

      hasAccessToFeature$: (feature) => hasAccessToFeature$(feature, partnerMarketConfigService),
      hasInboxAccess$: of(true),
      canShowInboxForContact$: partnerMarketConfigService.businessAppConfig$.pipe(
        map((config) => config.showInboxMessage),
        catchError(() => of(false)),
      ),
      openContactConversation: async (
        namespace: string,
        parentNamespace: string,
        contactId: string,
        channel: ConversationChannel,
      ) => {
        await crmInboxService.goToContactConversation(namespace, parentNamespace, contactId, channel);
      },
      showLeadProspector$: of(false),
    } as CrmDependencies;
  },
});

export const CrmMultilocationInjectionToken = new InjectionToken<CrmMultiLocationDependencies>(
  'dependencies for multilocation crm',
  {
    factory: () => {
      const locationsService = inject(LocationsService);
      const accountGroupService = inject(AccountGroupService);
      const router = inject(Router);

      const currentLocation$ = locationsService.currentLocation$.pipe(
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const groupId$ = currentLocation$.pipe(
        map((location) => {
          return isBrand(location) ? (location as Brand).groupNodes[0] : '';
        }),
      );

      const multiLocationContext$ = combineLatest([groupId$, locationsService.currentAccountGroups$]).pipe(
        map(([groupId, accountGroups]) => {
          if (!groupId) {
            return {} as MultiLocationContext;
          }

          return {
            groupId: groupId,
            accountGroupsIds: Object.keys(accountGroups).map((accountGroupId) => accountGroupId),
          } as MultiLocationContext;
        }),
      );

      const isMultiLocation$ = multiLocationContext$.pipe(map((context) => !!context.groupId));

      const routePrefix$ = groupId$.pipe(
        map((groupID) => {
          return `/account/brands/${groupID}`;
        }),
      );

      const accountGroupIdFromRoute$: Observable<string> = router.events.pipe(
        filter((event) => event instanceof GuardsCheckStart),
        map((event: GuardsCheckStart) => extractAccountGroupIdFromRoute(event.url)),
      );

      const namespace$: Observable<string> = locationsService.isCurrentLocationABrand$.pipe(
        switchMap((isBrand) => {
          if (isBrand) {
            return accountGroupIdFromRoute$;
          } else {
            return of('');
          }
        }),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const currentLocationName$ = namespace$.pipe(
        switchMap((namespace) => accountGroupService.getAccountGroup(namespace)),
        map((accountGroup) => accountGroup?.companyName || ''),
      );

      return {
        multiLocationContext$,
        routePrefix$,
        currentLocationName$,
        isMultiLocation$,
      };
    },
  },
);

function objectViewPresets$(
  translationService: TranslateService,
  translateByObjectService: TranslateForCrmObjectService,
  ownerId$: Observable<string>,
  objectType: ObjectType,
  lastEngagedFieldId: string,
  ownerFieldId: string,
): Observable<PresetFilter[]> {
  const myFilterName$ = translateByObjectService.getTranslationForCrmObject(objectType, 'MY_OBJECTS');
  const myFilter$ = combineLatest([ownerId$, myFilterName$]).pipe(
    map(([userId, filterName]) => {
      if (userId) {
        const filter: GalaxyFilterInterface = {
          fieldId: ownerFieldId,
          operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
          values: [{ string: userId }],
        };
        return {
          name: filterName,
          filters: [filter],
          id: 'owned-objects',
        } as PresetFilter;
      }
      return undefined;
    }),
  );

  const allFilter$ = translateByObjectService.getTranslationForCrmObject(objectType, 'ALL_OBJECTS').pipe(
    map((filterName) => {
      return {
        name: filterName,
        filters: [],
        id: 'all-objects',
      } as PresetFilter;
    }),
  );

  return combineLatest([myFilter$, allFilter$]).pipe(
    map(([myFilter, allFilter]) => {
      const filters: PresetFilter[] = [];
      if (myFilter) {
        filters.push(myFilter);
      }
      if (allFilter) {
        filters.push(allFilter);
      }

      // recently engaged
      const lastEngagedFilter = {
        name: translationService.instant('CRM.PRESET_FILTERS.RECENTLY_ENGAGED'),
        filters: [
          {
            fieldId: lastEngagedFieldId,
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_AFTER,
            values: [{ dateDefault: GalaxyDateDefault.DATE_DEFAULT_LAST_WEEK }],
          },
        ],
        id: 'recently-engaged',
      } as PresetFilter;
      filters.push(lastEngagedFilter);

      return filters;
    }),
  );
}

function filterActivityInputOverrides$(translationService: TranslateService): Observable<CRMFilterOverride[]> {
  return of([
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      fieldId: SystemFieldIds.ActivityOwnerID,
      filterInput: TeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
  ] as CRMFilterOverride[]);
}

function filterContactInputOverrides$(translationService: TranslateService): Observable<CRMFilterOverride[]> {
  return of([
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.PRIMARY_COMPANY'),
      fieldId: StandardIds.ContactPrimaryCompanyID,
      filterInput: CompanyCustomFilterInputComponent,
      filterChip: CompanyCustomFilterChipComponent,
    },
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      fieldId: SystemFieldIds.ContactOwnerID,
      filterInput: TeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.TEAM_MEMBERS'),
      fieldId: StandardIds.ContactAdditionalTeamMemberIDs,
      filterInput: AdditionalTeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
  ] as CRMFilterOverride[]);
}

function filterCompanyInputOverride$(translationService: TranslateService): Observable<CRMFilterOverride[]> {
  return of([
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.PARENT_COMPANY'),
      fieldId: StandardIds.CompanyParentCompanyID,
      filterInput: CompanyCustomFilterInputComponent,
      filterChip: CompanyCustomFilterChipComponent,
    },
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      fieldId: SystemFieldIds.CompanyOwnerID,
      filterInput: TeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
    {
      title: translationService.instant('CRM.CUSTOM_FILTERS.TEAM_MEMBERS'),
      fieldId: StandardIds.CompanyAdditionalTeamMemberIDs,
      filterInput: AdditionalTeamMemberFilterInputComponent,
      filterChip: TeamMemberFilterChipComponent,
    },
  ] as CRMFilterOverride[]);
}

function contactTableCustomCells(translationService: TranslateService): TableCustomCell[] {
  return [
    {
      fieldIds: [SystemFieldIds.ContactOwnerID],
      columnDefinition: {
        id: SystemFieldIds.ContactOwnerID,
        title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      },
      customCellComponent: OwnerCellComponent,
    },
    {
      fieldIds: [StandardIds.ContactAdditionalTeamMemberIDs],
      columnDefinition: {
        id: StandardIds.ContactAdditionalTeamMemberIDs,
        title: translationService.instant('CRM.CUSTOM_FILTERS.TEAM_MEMBERS'),
      },
      customCellComponent: TeamMemberCellComponent,
    },
  ] as TableCustomCell[];
}

function companyTableCustomCells(translationService: TranslateService): TableCustomCell[] {
  return [
    {
      fieldIds: [SystemFieldIds.CompanyOwnerID],
      columnDefinition: {
        id: SystemFieldIds.CompanyOwnerID,
        title: translationService.instant('CRM.CUSTOM_FILTERS.OWNER'),
      },
      customCellComponent: OwnerCellComponent,
    },
    {
      fieldIds: [StandardIds.CompanyAdditionalTeamMemberIDs],
      columnDefinition: {
        id: StandardIds.CompanyAdditionalTeamMemberIDs,
        title: translationService.instant('CRM.CUSTOM_FILTERS.TEAM_MEMBERS'),
      },
      customCellComponent: TeamMemberCellComponent,
    },
  ] as TableCustomCell[];
}

function hasAccessToFeature$(
  feature: string,
  partnerMarketConfigService: PartnerMarketConfigService,
): Observable<boolean> {
  return feature == 'task-queue'
    ? of(true)
    : partnerMarketConfigService.hasAccessToFeature(feature as Feature).pipe(startWith(false));
}

function buildContactHeaders$(partnerMarketConfigService: PartnerMarketConfigService): Observable<ProfileHeader[]> {
  const leadScoringFeature$ = hasAccessToFeature$('crm-lead-scoring', partnerMarketConfigService);
  const leadScoringConfig$ = partnerMarketConfigService.businessAppConfig$.pipe(
    map((config) => config.showLeadScoring),
  );
  return combineLatest([leadScoringFeature$, leadScoringConfig$]).pipe(
    map(([leadScoringFeature, leadScoringConfig]) => {
      const headers: ProfileHeader[] = [];
      headers.push({ component: NpsBadgeComponent });
      if (leadScoringFeature && leadScoringConfig) {
        headers.push({ component: LeadScoreBadgeComponent });
      }
      return headers;
    }),
  );
}

// Search for the accountGroupId in the given route and extract it if it exists
function extractAccountGroupIdFromRoute(route: string): string {
  const accountGroupIdPattern = /\/account\/brands\/[A-Z0-9-]*\/[a-z]*\/(AG-[A-Z0-9-]*)\/[A-Za-z0-9-/]*/;
  const agCapture = accountGroupIdPattern.exec(route);
  return agCapture && agCapture.length > 1 ? agCapture[1] : '';
}
