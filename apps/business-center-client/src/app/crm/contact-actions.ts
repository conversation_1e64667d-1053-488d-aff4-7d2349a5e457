import { AddContactsToCampaignRedirectService } from '@galaxy/campaign/shared';
import { AccountGroupService } from '../account-group';
import { TranslateService } from '@ngx-translate/core';
import { ProductService } from '../core/product.service';
import { FeatureFlagService } from '../core/feature-flag.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { combineLatest, map, Observable, of, switchMap, firstValueFrom } from 'rxjs';
import { CRMSelectAllOptions, MultiRowAction, SelectAllAction, SingleRowAction } from '@galaxy/crm/static';
import { Row } from '@vendasta/galaxy/table';
import {
  AddToListMultiRowAction,
  AddToListSingleRowAction,
  AddToListSelectAllAction,
  ListActionsService,
} from '@galaxy/crm/integrations/dynamic-lists-actions';
import { ConversationChannel } from '@vendasta/conversation';
import { CampaignsProDemo, CampaignsProProd, DynamicListsFeature } from './constants';
import { InboxService } from './inbox.service';
import { ExportActionsService, ExportSelectAllRowAction } from '@galaxy/crm/dynamic';
import { CRMAccessService } from './access.service';
import {
  AutomationActionsService,
  AutomationMultiRowAction,
  AutomationSelectAllRowAction,
  AutomationSingleRowAction,
} from '@galaxy/crm/integrations/automation';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ReputationService } from '../reputation/reputation.service';
import { BusinessCenterApiService } from '@vendasta/business-center';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  AddToCampaignMultiRowAction,
  AddToCampaignSingleRowAction,
  CampaignActionsService,
} from '@galaxy/crm/integrations/yesware';

export function buildContactSingleRowTableActions(
  campaignService: AddContactsToCampaignRedirectService,
  productAnalyticsService: ProductAnalyticsService,
  accountGroupService: AccountGroupService,
  featureFlagService: FeatureFlagService,
  listActionsService: ListActionsService,
  crmInboxService: InboxService,
  accessService: CRMAccessService,
  automationActionService: AutomationActionsService,
  translationSvc: TranslateService,
  confirmationModalService: OpenConfirmationModalService,
  reputationService: ReputationService,
  snackbarService: SnackbarService,
  rmPremiumActive$: Observable<boolean>,
  yeswareCampaignActionsService: CampaignActionsService,
  yeswareActive$: Observable<boolean>,
  activeAppService: ProductService,
): Observable<SingleRowAction[]> {
  const canAccessCampaigns$ = activeAppService.activeProducts$.pipe(
    map((apps) => {
      return apps.filter((app) => app.productId === CampaignsProProd || app.productId === CampaignsProDemo).length > 0;
    }),
  );

  return accountGroupService.currentAccountGroup$.pipe(
    switchMap((currentAccountGroup) => {
      return combineLatest([
        accountGroupService.currentAccountGroupId$,
        of(currentAccountGroup.partnerId),
        featureFlagService.checkFeatureFlag(currentAccountGroup.partnerId, '', DynamicListsFeature),
        accessService.canRunManualAutomations$,
        rmPremiumActive$,
        yeswareActive$,
        canAccessCampaigns$,
      ]);
    }),
    map(
      ([
        accountGroupId,
        partnerId,
        canAddToCRMLists,
        canRunAutomations,
        rmPremiumActive,
        yeswareActive,
        canAccessCampaigns,
      ]) => {
        const actions: SingleRowAction[] = [];
        actions.push(goToConversationSingleRowAction(crmInboxService, accountGroupId, partnerId));
        if (rmPremiumActive) {
          actions.push(
            sendReviewRequestSingleRowAction(
              translationSvc,
              confirmationModalService,
              reputationService,
              snackbarService,
              accountGroupService,
            ),
          );
        }
        if (canAddToCRMLists) {
          actions.push(
            AddToListSingleRowAction(accountGroupId, listActionsService, 'Contact', 'CRM.ADD_TO_STATIC_LIST'),
          );
        }
        if (canRunAutomations) {
          actions.push(
            AutomationSingleRowAction(
              accountGroupId,
              automationActionService,
              'Contact',
              'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION',
            ),
          );
        }
        if (canAccessCampaigns) {
          actions.push({
            label: translationSvc.instant('CONTACT_TABLE_ADD_TO_CAMPAIGN'),
            callback: (row: Row) => {
              productAnalyticsService.trackEvent(
                'user-clicked-crm-table-add-multi-contacts',
                'send-campaign-workflow',
                'click',
                null,
              );
              campaignService.navigateToSendCampaign([row.id]);
            },
          });
        }
        if (yeswareActive) {
          actions.push(
            AddToCampaignSingleRowAction(
              { accountGroupId: accountGroupId },
              yeswareCampaignActionsService,
              'YESWARE.ACTIONS.ADD_TO_CAMPAIGN',
            ),
          );
        }
        return actions;
      },
    ),
  );
}

export function buildContactMultiRowTableActions(
  campaignService: AddContactsToCampaignRedirectService,
  accountGroupService: AccountGroupService,
  translationSvc: TranslateService,
  activeAppService: ProductService,
  productAnalyticsService: ProductAnalyticsService,
  featureFlagService: FeatureFlagService,
  listActionsService: ListActionsService,
  accessService: CRMAccessService,
  automationActionService: AutomationActionsService,
  confirmationModalService: OpenConfirmationModalService,
  reputationService: ReputationService,
  snackbarService: SnackbarService,
  rmPremiumActive$: Observable<boolean>,
  yeswareCampaignActionsService: CampaignActionsService,
  yeswareActive$: Observable<boolean>,
): Observable<MultiRowAction[]> {
  const canAccessCampaigns$ = activeAppService.activeProducts$.pipe(
    map((apps) => {
      return apps.filter((app) => app.productId === CampaignsProProd || app.productId === CampaignsProDemo).length > 0;
    }),
  );

  return accountGroupService.currentAccountGroup$.pipe(
    switchMap((currentAccountGroup) => {
      return combineLatest([
        accountGroupService.currentAccountGroupId$,
        featureFlagService.checkFeatureFlag(currentAccountGroup.partnerId, '', DynamicListsFeature),
        canAccessCampaigns$,
        accessService.canRunManualAutomations$,
        rmPremiumActive$,
        yeswareActive$,
      ]);
    }),
    map(([accountGroupId, canAddToList, canAccessCampaigns, canRunAutomations, rmPremiumActive, yeswareActive]) => {
      const actions: MultiRowAction[] = [];
      if (canAddToList) {
        actions.push(
          AddToListMultiRowAction(
            accountGroupId,
            listActionsService,
            'Contact',
            translationSvc.instant('CRM.ADD_TO_STATIC_LIST'),
          ),
        );
      }
      if (rmPremiumActive) {
        actions.push(
          sendReviewRequestMultiRowAction(
            translationSvc,
            confirmationModalService,
            reputationService,
            snackbarService,
            accountGroupService,
          ),
        );
      }
      if (canRunAutomations) {
        actions.push(
          AutomationMultiRowAction(
            accountGroupId,
            automationActionService,
            'Contact',
            'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION',
          ),
        );
      }
      if (canAccessCampaigns) {
        actions.push({
          label: translationSvc.instant('CONTACT_TABLE_ADD_TO_CAMPAIGN'),
          callback: (rows: Row[]) => {
            productAnalyticsService.trackEvent(
              'user-clicked-crm-table-add-multi-contacts',
              'send-campaign-workflow',
              'click',
              null,
            );
            campaignService.navigateToSendCampaign(rows.map((c) => c.id));
          },
        });
      }
      if (yeswareActive) {
        actions.push(
          AddToCampaignMultiRowAction(
            { accountGroupId: accountGroupId },
            yeswareCampaignActionsService,
            'YESWARE.ACTIONS.ADD_TO_CAMPAIGN',
          ),
        );
      }
      return actions;
    }),
  );
}

export function buildContactSelectAllTableActions(
  campaignService: AddContactsToCampaignRedirectService,
  translationSvc: TranslateService,
  activeAppService: ProductService,
  productAnalyticsService: ProductAnalyticsService,
  accountGroupService: AccountGroupService,
  exportActionsService: ExportActionsService,
  featureFlagService: FeatureFlagService,
  listActionsService: ListActionsService,
  accessService: CRMAccessService,
  automationActionService: AutomationActionsService,
  confirmationModalService: OpenConfirmationModalService,
  businessCenterApiService: BusinessCenterApiService,
  snackbarService: SnackbarService,
  rmPremiumActive$: Observable<boolean>,
): Observable<SelectAllAction[]> {
  return accountGroupService.currentAccountGroup$.pipe(
    switchMap((currentAccountGroup) => {
      return combineLatest([
        accountGroupService.currentAccountGroupId$,
        featureFlagService.checkFeatureFlag(currentAccountGroup.partnerId, '', DynamicListsFeature),
        activeAppService.activeProducts$,
        accessService.canRunManualAutomations$,
        rmPremiumActive$,
      ]);
    }),
    map(([accountGroupId, canAddToList, apps, canRunAutomations, rmPremiumActive]): SelectAllAction[] => {
      const actions: MultiRowAction[] = [];
      if (canAddToList) {
        actions.push(
          AddToListSelectAllAction(
            accountGroupId,
            listActionsService,
            'Contact',
            translationSvc.instant('CRM.ADD_TO_STATIC_LIST'),
          ),
        );
      }
      if (rmPremiumActive) {
        actions.push(
          sendReviewRequestAllContactsAction(
            translationSvc,
            confirmationModalService,
            accountGroupService,
            businessCenterApiService,
            snackbarService,
          ),
        );
      }
      if (canRunAutomations) {
        actions.push(
          AutomationSelectAllRowAction(
            accountGroupId,
            automationActionService,
            'Contact',
            'COMPANY.LIST_OBJECTS_TABLE.ACTIONS.START_AUTOMATION',
          ),
        );
      }
      if (apps.filter((app) => app.productId === CampaignsProProd || app.productId === CampaignsProDemo).length !== 0) {
        actions.push({
          label: translationSvc.instant('CONTACT_TABLE_ADD_TO_CAMPAIGN'),
          callback: (rows: Row[], selectOptions?: CRMSelectAllOptions) => {
            productAnalyticsService.trackEvent(
              'user-clicked-crm-table-select-all-contacts',
              'crciweate-campaign-workflow',
              'click',
              null,
            );
            campaignService.navigateToSendCampaign(
              rows.map((c) => c.id),
              selectOptions.filters,
              selectOptions.search,
              selectOptions.useSelectAll,
              selectOptions.totalObjects,
            );
          },
        });
      }
      actions.push(
        ExportSelectAllRowAction(accountGroupId, exportActionsService, 'Contact', 'ACTIONS.CRM.EXPORT.LABEL'),
      );
      return actions;
    }),
  );
}

function goToConversationSingleRowAction(
  inbox: InboxService,
  namespace: string,
  parentNamespace: string,
): SingleRowAction {
  return {
    label: 'CRM.SEND_MESSAGE',
    callback: (row: Row) => {
      const phone = inbox.getContactPhoneFromRow(row);
      const channel = phone
        ? ConversationChannel.CONVERSATION_CHANNEL_SMS
        : ConversationChannel.CONVERSATION_CHANNEL_EMAIL;
      inbox.goToContactConversation(namespace, parentNamespace, row.id, channel);
    },
  };
}

function sendReviewRequestSingleRowAction(
  translationSvc: TranslateService,
  confirmationModalService: OpenConfirmationModalService,
  reputationService: ReputationService,
  snackbarService: SnackbarService,
  accountGroupService: AccountGroupService,
) {
  return {
    label: translationSvc.instant('CRM.REQUEST_REVIEW'),
    callback: (row: Row) => {
      const contactId = row.id;
      if (contactId) {
        const contactName = row.data.fullName.value;
        let message = translationSvc.instant(
          'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_CONFIRMATION_MESSAGE_NO_NAME',
        );
        if (contactName) {
          message = translationSvc.instant('PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_CONFIRMATION_MESSAGE', {
            contactName,
          });
        }
        confirmationModalService
          .openModal({
            title: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUEST_QUESTION',
            message,
            confirmButtonText: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUEST',
          })
          .subscribe(async (confirmed) => {
            if (confirmed) {
              try {
                await reputationService.scheduleReviewRequest(accountGroupService.currentAccountGroupId(), [contactId]);
                snackbarService.openSuccessSnack(
                  'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_CONFIRMATION_MESSAGE_SUCCESS',
                );
              } catch (error) {
                snackbarService.openErrorSnack(
                  'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_CONFIRMATION_MESSAGE_ERROR',
                );
              }
            }
          });
      }
    },
  };
}

function sendReviewRequestMultiRowAction(
  translationSvc: TranslateService,
  confirmationModalService: OpenConfirmationModalService,
  reputationService: ReputationService,
  snackbarService: SnackbarService,
  accountGroupService: AccountGroupService,
) {
  return {
    label: translationSvc.instant('CRM.REQUEST_REVIEWS'),
    callback: (rows: Row[]) => {
      const contactIds = rows.map((row) => row.id); // TODO: this just takes the first page of contacts. Will have to make API call to get all contacts
      // and page through them
      if (contactIds.length > 0) {
        confirmationModalService
          .openModal({
            title: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUEST_QUESTION',
            message: translationSvc.instant(
              'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE',
              { contactCount: contactIds.length },
            ),
            confirmButtonText: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUESTS',
          })
          .subscribe(async (confirmed) => {
            if (confirmed) {
              try {
                await reputationService.scheduleReviewRequest(accountGroupService.currentAccountGroupId(), contactIds);
                snackbarService.openSuccessSnack(
                  'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_SUCCESS',
                );
              } catch (error) {
                snackbarService.openErrorSnack(
                  'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_ERROR',
                );
              }
            }
          });
      }
    },
  };
}

function sendReviewRequestAllContactsAction(
  translationSvc: TranslateService,
  confirmationModalService: OpenConfirmationModalService,
  accountGroupService: AccountGroupService,
  businessCenterApiService: BusinessCenterApiService,
  snackbarService: SnackbarService,
) {
  return {
    label: translationSvc.instant('CRM.REQUEST_REVIEWS'),
    callback: (_: Row[]) => {
      confirmationModalService
        .openModal({
          title: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUEST_QUESTION',
          message: translationSvc.instant('PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_ALL_CONFIRMATION_MESSAGE'),
          confirmButtonText: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUESTS',
        })
        .subscribe(async (confirmed) => {
          if (confirmed) {
            try {
              await firstValueFrom(
                businessCenterApiService.sendReviewRequestToContacts({
                  filters: {
                    businessId: accountGroupService.currentAccountGroupId(),
                  },
                }),
              );
              snackbarService.openSuccessSnack(
                'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_SUCCESS',
              );
            } catch (error) {
              snackbarService.openErrorSnack(
                'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE_ERROR',
              );
            }
          }
        });
    },
  };
}
