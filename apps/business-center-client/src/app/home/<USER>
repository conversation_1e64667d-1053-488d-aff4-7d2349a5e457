import { animate, style, transition, trigger } from '@angular/animations';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { SessionService } from '@galaxy/core';
import { PartnerUserAgreementTermsOfApiService } from '@galaxy/partner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { CookieService } from 'ngx-cookie-service';
import { combineLatest, firstValueFrom, Observable } from 'rxjs';
import { delay, filter, map, switchMap, tap } from 'rxjs/operators';
import { partnerId } from '../../globals';
import { AccountGroupService } from '../account-group';
import { AuthWallService } from '../auth-wall';
import { showAuthDialog } from '../auth-wall/auth-wall-dialog.component';
import { AuthService } from '../auth.service';
import { FeatureFlagService } from '../core/feature-flag.service';
import { GetStartedService } from '../get-started/get-started.service';
import { InviteUserSidebarComponent } from '../my-team/invite-user-sidebar/invite-user-sidebar.component';
import { Mode, SidepanelService, Size } from '../navigation/sidepanel.service';
import { OrderCompleteDialogComponent } from '../orders/order-complete-dialog/order-complete-dialog.component';
import {
  orderDeclinedQueryParam,
  orderSubmittedQueryParam,
  trialOrderSubmittedQueryParam,
} from '../orders/order-navigator.service';
import { PageAccessService, PageId } from '../page-access';
import { PartnerMarketConfigService } from '../partner-market-config';
import { SocialConnectionsService } from '../social-connections/social-connections.service';
import { TermsOfServiceData } from '../terms-of-service/interface/terms-of-service-data.interface';
import { TermsOfServiceService } from '../terms-of-service/providers/terms-of-service.service';
import { TermsOfServiceDialogComponent } from '../terms-of-service/terms-of-service.component';
import { WIDGET_TYPE } from './dashboard-widget/dashboard-widget.component';
import { Capacitor } from '@capacitor/core';

const getStartedCookie = 'routeToGetStarted';

@Component({
  selector: 'bc-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [style({ height: 0 }), animate('0.4s 0s ease-in', style({ height: '*' }))]),
    ]),
  ],
  standalone: false,
})
export class HomeComponent implements OnInit {
  protected readonly accountGroup$ = inject(AccountGroupService).currentAccountGroup$.pipe(takeUntilDestroyed());
  private readonly authService = inject(AuthService);
  private readonly bpObserver = inject(BreakpointObserver);

  widgetType = WIDGET_TYPE;

  isRecentActivityVisible$ = this.partnerMarketConfigService.businessAppConfig$.pipe(
    map((c) => c.showRecentActivity && !Capacitor.isNativePlatform()),
  );
  isMyProductsVisible$ = this.pageVisibilityService.isPageAccessible$(PageId.my_products);
  private readonly bizappConfig$ = this.partnerMarketConfigService.businessAppConfig$;
  protected readonly inviteTeamEnabled$ = this.bizappConfig$.pipe(map((c) => c.showInviteTeam));
  protected readonly accessToMarketingFunnel$ = this.bizappConfig$.pipe(map((c) => c.showMarketingFunnel));

  showAuthDialog = showAuthDialog();

  protected readonly isMobile$: Observable<boolean>;
  protected readonly showGetStarted$: Observable<boolean>;

  constructor(
    private route: ActivatedRoute,
    public pageVisibilityService: PageAccessService,
    private featureFlagService: FeatureFlagService,
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private authWallService: AuthWallService,
    private cookieService: CookieService,
    private termsOfServiceService: TermsOfServiceService,
    private partnerUserAgreementTermsOfApiService: PartnerUserAgreementTermsOfApiService,
    public router: Router,
    private snackBar: MatSnackBar,
    private sessionService: SessionService,
    private socialConnectionsService: SocialConnectionsService,
    private partnerMarketConfigService: PartnerMarketConfigService,
    private readonly sidepanelService: SidepanelService,
    private readonly destroyRef: DestroyRef,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly getStartedService: GetStartedService,
  ) {
    const canAccessGetStarted$ = this.partnerMarketConfigService.businessAppConfig$.pipe(
      map((config) => config.showGetStarted),
    );
    this.showGetStarted$ = combineLatest([canAccessGetStarted$, this.getStartedService.setupCompleted$]).pipe(
      map(([canAccess, completed]) => canAccess && !completed),
    );

    this.isMobile$ = this.breakpointObserver
      .observe([Breakpoints.HandsetPortrait])
      .pipe(map((result) => result.matches));

    this.setGetStartedCookie();
    this.accountGroup$.pipe(
      switchMap((ag) => this.featureFlagService.checkFeatureFlag(ag.partnerId, ag.marketId, 'terms_of_service_pop_up')),
      filter((flag) => flag === true),
      tap(() => this.checkTermsOfService()),
    );
  }

  ngOnInit(): void {
    this.subscribeToQueryParams(this.route);
    this.checkAuthWall();
    this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.checkAuthWall();
    });
  }

  private setGetStartedCookie(): void {
    if (window.localStorage.getItem(getStartedCookie) === 'true') return;

    this.socialConnectionsService.socialProfileConnections$
      .pipe(
        map((connections) => {
          Object.entries(connections).forEach((connection) => {
            if (connection[1]?.length > 0) {
              window.localStorage.setItem(getStartedCookie, 'true');
            }
          });
          window.localStorage.setItem(getStartedCookie, 'true');
          this.router.navigateByUrl(this.router.url.replace('dashboard', 'get-started'));
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  subscribeToQueryParams(route: ActivatedRoute): void {
    route.queryParamMap
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        delay(0),
        map((params) => {
          const error = params.get('error');
          if (error && error === 'google-auth-failed') {
            this.snackbarService.openErrorSnack('ERRORS.NO_ACCESS.NO_ACCESS');
          }

          const nextUrl = params.get('nextUrl');
          const orderSubmitted = params.get(orderSubmittedQueryParam);
          if (orderSubmitted) {
            const dialogRef = this.dialog.open(OrderCompleteDialogComponent, {
              data: {
                orderSuccessful: true,
                nextUrl: nextUrl,
              },
            });
            if (this.authWallService.isInBuyItNowWorkflow()) {
              firstValueFrom(dialogRef.afterClosed()).then(() => this.showAuthDialog());
            }
          }

          const trialOrderSubmitted = params.get(trialOrderSubmittedQueryParam);
          if (trialOrderSubmitted) {
            const dialogRef = this.dialog.open(OrderCompleteDialogComponent, {
              data: {
                orderSuccessful: true,
                trialOrder: true,
                nextUrl: nextUrl,
              },
            });
            if (this.authWallService.isInBuyItNowWorkflow()) {
              firstValueFrom(dialogRef.afterClosed()).then(() => this.showAuthDialog());
            }
          }

          const orderDeclined = params.get(orderDeclinedQueryParam);
          if (orderDeclined) {
            this.dialog.open(OrderCompleteDialogComponent, {
              data: {
                orderSuccessful: false,
                orderDeclined: true,
                nextUrl: nextUrl,
              },
            });
          }
        }),
      )
      .subscribe();
  }

  checkAuthWall(): void {
    const showAuthWall = this.cookieService.get('showAuthWall');
    if (showAuthWall === 'true') {
      this.cookieService.delete('showAuthWall', '/');
      if (this.authService.isInPreviewMode()) {
        this.showAuthDialog();
      }
    }
  }

  async checkTermsOfService(): Promise<void> {
    const userAgreementTerms$ = this.termsOfServiceService.getUserAgreement({
      feature: 'NULL',
    });

    const termsOfService$ = this.termsOfServiceService
      .getTermsOfService({
        feature: '',
      })
      .pipe(map((tos) => tos?.termsOfService));

    const showTOS = await firstValueFrom(this.termsOfServiceService.showTermsOfService$);
    if (!showTOS) return;

    const tos = await firstValueFrom(termsOfService$);
    const userAgreementRespose = await firstValueFrom(userAgreementTerms$);

    const hasTermsOfService = this.termsOfServiceService.hasTermsOfService({
      terms: tos,
    });

    // Check that the date of the last acceptance of terms is less than the date
    // of the terms of use, if not, the term of use is new and needs to be accepted again
    const isNewTos = this.termsOfServiceService.isNewTermsOfService({
      userAgreement: userAgreementRespose?.userAgreementTermsOfService,
      terms: tos,
    });
    const isAgreementAccepted = this.termsOfServiceService.userAgreementIsAccepted({
      userAgreement: userAgreementRespose.userAgreementTermsOfService,
    });

    if (hasTermsOfService && (isNewTos || !isAgreementAccepted)) {
      this.showTermsOfService({
        terms: tos?.termsOfService,
      });
    }
  }

  showTermsOfService(termsOfService: { terms: string }): void {
    const conf: MatDialogConfig = {
      width: '600px',
      disableClose: true,
      data: {
        termsOfService: termsOfService.terms,
        accepted: false,
      } as TermsOfServiceData,
    };
    const isSmallScreen = this.bpObserver.isMatched(Breakpoints.XSmall);
    if (isSmallScreen) {
      conf.maxWidth = '100vw';
      conf.maxHeight = '100vh';
    }
    const dialogRef = this.dialog.open(TermsOfServiceDialogComponent, conf);
    firstValueFrom(dialogRef.afterClosed()).then((result) => {
      if (result === undefined || !result?.accepted) {
        this.redirectToLoginPage();
        return false;
      }
      this.saveAcceptsTermsOfService();
    });
  }

  saveAcceptsTermsOfService(): void {
    this.partnerUserAgreementTermsOfApiService
      .setUserAgreementTermsOfService({
        userAgreementTermsOfService: {
          partnerId: partnerId,
          userId: this.authService.userId(),
          accepted: true,
          acceptedAt: new Date(),
          feature: 'NULL',
        },
      })
      .subscribe({
        next: () => {
          this.snackBar.open('Terms of Service was accepted', 'OK', {
            duration: 2000,
          });
        },
        error: (error) => {
          console.warn('setUserAgreementTermsOfService warn', error);
        },
      });
  }

  redirectToLoginPage(): void {
    this.sessionService.clearSessionId();
    window.location.href = '/logout/';
  }

  openInviteUserSidebar(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, InviteUserSidebarComponent);
    this.sidepanelService.open();
  }
}
