import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { ChartComponent, NgApexchartsModule } from 'ng-apexcharts';
import { ChartOptions } from '../../chart-interfaces';
import { BaseChartComponent } from '@galaxy/shared/utils/charts';

export type Ratings = {
  fiveStarRating: number;
  fourStarRating: number;
  threeStarRating: number;
  twoStarRating: number;
  oneStarRating: number;
};

@Component({
  selector: 'app-rating-card',
  imports: [
    CommonModule,
    MatCardModule,
    NgApexchartsModule,
    MatIconModule,
    TranslateModule,
    GalaxyEmptyStateModule,
    BaseChartComponent,
  ],
  templateUrl: './rating-card.component.html',
  styleUrls: ['./rating-card.component.scss'],
})
export class RatingCardComponent implements OnInit, OnChanges {
  @Input() cardTitle: string;
  @Input() ratings: Ratings = {
    fiveStarRating: 0,
    fourStarRating: 0,
    threeStarRating: 0,
    twoStarRating: 0,
    oneStarRating: 0,
  };

  @ViewChild('chart') chart: ChartComponent;

  public chartOptions: Partial<ChartOptions>;
  protected math = Math;
  public averageRating = 0;
  ratingCount = 0;

  ngOnInit(): void {
    this.setRatingCount();
    this.setAverageRating();
    this.setChartConfig();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.ratings) {
      this.setRatingCount();
      this.setChartConfig();
      this.setAverageRating();
    }
  }

  setRatingCount() {
    this.ratingCount =
      this.ratings.fiveStarRating +
      this.ratings.fourStarRating +
      this.ratings.threeStarRating +
      this.ratings.twoStarRating +
      this.ratings.oneStarRating;
  }

  setAverageRating() {
    const fiveStartotals = this.ratings.fiveStarRating * 5;
    const fourStartotals = this.ratings.fourStarRating * 4;
    const threeStartotals = this.ratings.threeStarRating * 3;
    const twoStartotals = this.ratings.twoStarRating * 2;
    const oneStartotals = this.ratings.oneStarRating;
    const totalRatings = fiveStartotals + fourStartotals + threeStartotals + twoStartotals + oneStartotals;

    if (this.ratingCount === 0) {
      this.averageRating = 0;
      return;
    }
    this.averageRating = totalRatings / this.ratingCount;
  }

  getDataFromRatings(ratings: Ratings): number[] {
    if (!ratings) {
      return [0, 0, 0, 0, 0];
    }
    const data = [];
    data.push(ratings.fiveStarRating);
    data.push(ratings.fourStarRating);
    data.push(ratings.threeStarRating);
    data.push(ratings.twoStarRating);
    data.push(ratings.oneStarRating);
    return data;
  }

  setChartConfig() {
    this.chartOptions = {
      series: [
        {
          name: 'Ratings',
          data: this.getDataFromRatings(this.ratings),
        },
      ],
      chart: {
        height: 120,
        width: 150,
        type: 'bar',
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      legend: {
        show: false,
        floating: true,
      },
      plotOptions: {
        bar: {
          horizontal: false,
          borderRadius: 4,
          columnWidth: 16,
          borderRadiusApplication: 'end',
        },
      },
      tooltip: {
        enabled: false,
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth',
      },
      grid: {
        padding: {
          top: -10,
          bottom: -10,
          // left: 0,
        },
        yaxis: {
          lines: {
            show: false,
          },
        },
        xaxis: {
          lines: {
            show: false,
          },
        },
      },
      yaxis: {
        labels: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
        show: false,
      },
      xaxis: {
        tickAmount: 5,
        categories: ['5★', '4★', '3★', '2★', '1★'],
        labels: {
          show: true,
        },
        axisTicks: {
          show: false,
        },
        axisBorder: {
          show: false,
        },
      },
    };
  }
}
