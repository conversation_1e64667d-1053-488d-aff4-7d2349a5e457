import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';

@Component({
  selector: 'app-responding-card',
  imports: [CommonModule, MatCardModule, MatIconModule, TranslateModule, GalaxyTooltipModule],
  templateUrl: './responding-card.component.html',
  styleUrls: ['./responding-card.component.scss'],
})
export class RespondingCardComponent implements OnInit, OnChanges {
  @Input() title = '';
  /** @property {number} currentAverageResponseTime - The current average response time in hours */
  @Input() currentAverageResponseTime = 0;
  @Input() total: number;
  @Input() respondedToTotal: number;
  percentageResponded: string;

  ngOnInit(): void {
    this.calculatePercentageResponded();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.total || changes.respondedToTotal) {
      this.calculatePercentageResponded();
    }
  }

  calculatePercentageResponded() {
    this.percentageResponded = this.total === 0 ? '0%' : ((this.respondedToTotal / this.total) * 100).toFixed(0) + '%';
  }
}
