import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { TranslateTestingModule } from 'ngx-translate-testing';
import * as i18n from '../../assets/i18n/en_devel.json';
import { RatingCardComponent } from './metric-cards/rating-card/rating-card.component';
import { Sentiment } from './metric-cards/trending-keywords/trending-keywords.component';
import { ReviewMetricsComponent } from './review-metrics.component';
import { RespondingCardComponent } from './metric-cards/responding-card/responding-card.component';

const meta: Meta<ReviewMetricsComponent> = {
  //👇 Creates a Story for the component
  title: 'Review Metrics/Metrics Container',
  component: ReviewMetricsComponent,
  decorators: [
    moduleMetadata({
      providers: [],
      imports: [RespondingCardComponent, RatingCardComponent, TranslateTestingModule.withTranslations('en', i18n)],
    }),
  ],
  argTypes: {
    currentOverall: {
      description: 'The current overall review rating',
      control: {
        type: 'object',
      },
    },
  },
};

export default meta;

type Story = StoryObj<ReviewMetricsComponent>;

//👇 We create a “template” of how args map to rendering
export const Default: Story = {
  args: {
    currentOverall: {
      oneStarCount: 1,
      twoStarCount: 2,
      threeStarCount: 3,
      fourStarCount: 4,
      fiveStarCount: 5,
      numberResponded: 6,
      numberUnresponded: 7,
    },
    currentAverageResponseTime: 8.32444642,

    trendingKeywords: [
      {
        keyword: 'Delicious',
        totalCount: 10,
        sentiment: Sentiment.Positive,
      },
      {
        keyword: 'Gross',
        totalCount: 10,
        sentiment: Sentiment.Negative,
      },
      {
        keyword: 'Okay',
        totalCount: 10,
        sentiment: Sentiment.Neutral,
      },
      {
        keyword: 'Tasty',
        totalCount: 10,
        sentiment: Sentiment.Positive,
      },
    ],
  },
};
