import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { ReviewRating } from '../metrics/review-classes';
import { RatingCardComponent, Ratings } from './metric-cards/rating-card/rating-card.component';
import {
  TrendingKeyword,
  TrendingKeywordsComponent,
} from './metric-cards/trending-keywords/trending-keywords.component';
import { RespondingCardComponent } from './metric-cards/responding-card/responding-card.component';

export type ReviewRatingInput = {
  oneStarCount: number;
  twoStarCount: number;
  threeStarCount: number;
  fourStarCount: number;
  fiveStarCount: number;
  numberResponded: number;
  numberUnresponded: number;
};

const EMPTY_REVIEW_RATING: ReviewRatingInput = {
  oneStarCount: 0,
  twoStarCount: 0,
  threeStarCount: 0,
  fourStarCount: 0,
  fiveStarCount: 0,
  numberResponded: 0,
  numberUnresponded: 0,
};

@Component({
  selector: 'app-review-metrics',
  imports: [RatingCardComponent, TranslateModule, TrendingKeywordsComponent, RespondingCardComponent],
  templateUrl: './review-metrics.component.html',
  styleUrls: ['./review-metrics.component.scss'],
})
export class ReviewMetricsComponent implements OnChanges {
  @Input() currentOverall: ReviewRatingInput = EMPTY_REVIEW_RATING;

  @Input() currentAverageResponseTime = 0;

  @Input() trendingKeywords: TrendingKeyword[] = [];

  ratings: Ratings;
  averageRating: number;
  totalRatings: number;
  respondedToTotal: number;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.currentOverall || changes.previousOverall) {
      this.setValues(this.currentOverall);
    }
  }

  setValues(currentOverall: ReviewRatingInput) {
    if (!currentOverall) currentOverall = EMPTY_REVIEW_RATING;

    const current = new ReviewRating(
      currentOverall.oneStarCount,
      currentOverall.twoStarCount,
      currentOverall.threeStarCount,
      currentOverall.fourStarCount,
      currentOverall.fiveStarCount,
      null,
      currentOverall.numberResponded,
      currentOverall.numberUnresponded,
    );

    this.respondedToTotal = current?.numberResponded ?? 0;
    this.averageRating = current?.averageReviewScore() ?? 0;
    this.totalRatings = current?.totalReviews() ?? 0;

    this.ratings = {
      fiveStarRating: current?.numberFiveStars ?? 0,
      fourStarRating: current?.numberFourStars ?? 0,
      threeStarRating: current?.numberThreeStars ?? 0,
      twoStarRating: current?.numberTwoStars ?? 0,
      oneStarRating: current?.numberOneStars ?? 0,
    };
  }
}
