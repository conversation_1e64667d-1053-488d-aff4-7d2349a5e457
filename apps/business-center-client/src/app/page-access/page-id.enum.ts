export enum PageId {
  home = 'home',
  get_started = 'get_started',
  inbox = 'new_inbox',
  tasks = 'tasks',
  contacts = 'contacts',
  recommendations = 'recommendations',
  old_recommendations = 'lmi_dashboard',
  my_products = 'my_products',
  store = 'store',
  customer_list = 'customer_list',
  executive_report = 'executive_report',
  guides = 'guides',
  business_profile = 'business_profile',
  billing_settings = 'billing_settings',
  files = 'files',
  brands = 'brands',
  user_profile = 'user_profile',
  old_notification_settings = 'old_notification_settings',
  new_notification_settings = 'new_notification_settings',
  social_connections = 'social_connections',
  email_history = 'email_history',
  email_configuration = 'email_configuration',
  sms_configuration = 'sms_configuration',
  google_question_answer = 'google_question_answer',
  multi_location_dashboard = 'multi_location_dashboard',
  multi_location_reviews = 'multi_location_overview',
  ml_reviews = 'multi_location_reviews',
  multi_location_net_promoter_score = 'multi_location_net_promoter_score',
  multi_location_nps_overview = 'multi_location_net_promoter_score_overview',
  multi_location_nps_team = 'multi_location_nps_team',
  multi_location_overview = 'multi_location_ml_overview',
  multi_location_requests = 'multi_location_requests',
  multi_location_requests_overview = 'multi_location_requests_overview',
  multi_location_listings = 'multi_location_listings',
  keyword_tracking = 'nav-brand-listings-keyword-tracking',
  listing_sync = 'nav-brand-listing-sync',
  manage_multi_location_reviews = 'manage_multi_location_reviews',
  manage_multi_location_listings = 'manage_multi_location_listings',
  multi_location_reviews_sentiment = 'multi_location_reviews_sentiment',
  multi_location_google_my_business = 'multi_location_google_my_business',
  embedded_gmb = 'embedded-google-my-business',
  multi_location_google_question_answer = 'multi_location_google_question_answer',
  multi_location_advertising = 'multi_location_advertising',
  multi_location_social = 'multi_location_social',
  manage_multi_location_social_posts = 'manage_multi_location_social_posts',
  multi_location_view_report = 'multi_location_view_report',
  multi_location_data_export = 'multi_location_data_export',
  orders = 'orders',
  edition_upgrades = 'edition_upgrades',
  request_multi_location_reviews = 'request_multi_location_reviews',
  multi_location_report = 'multi_location_report',
  single_location_report = 'single_location_report',
  website = 'website',
  invoices = 'invoices',
  meeting_scheduler = 'meeting_scheduler',
  review_cart = 'review_cart',
  orders_table = 'orders_table',
  referral = 'referral',
  reputation = 'reputation',
  brand_reputation = 'brand-reputation',
  manage_response_templates = 'manage_response_templates',
  edit_response_template = 'edit_response_template',
  fulfillment = 'fulfillment',
  work_order = 'work_order',
  crm = 'crm',
  crm_companies = 'nav-crm-companies',
  leaderboard = 'nav-leaderboard',
  forms = 'forms',
  embedded_listings = 'embedded_listings',
  multi_location_embedded_listings = 'nav-embedded-listings',
  multi_location_embedded_social = 'multi-location-embedded-social',
  embedded_social = 'embedded_social',
  embedded_advertising = 'embedded_advertising',
  campaigns = 'campaigns',
  emails = 'emails',
  platform_integrations = 'platform_integrations',
  inbox_brand = 'nav-brand-inbox',
  settings_page = 'settings_page',
  profile_settings = 'profile_settings',
  multi_location_litings_keyword = 'nav-brand-listings-rank-tracking',
  multi_location_keyword_tracking = 'nav-embedded-keyword-tracking',
  multi_location_keyword = 'nav-brand-keyword-tracking-overview',
  ai = 'ai',
  payments = 'nav-payment-settings',
  nav_brand_reputation_nps = 'nav-brand-reputation-nps',
  payments_invoices = 'nav-payments-invoices',
  payments_payments = 'nav-payments-payments',
  payments_payouts = 'nav-payments-payouts',
}

// This maps the business app page IDs to it's Atlas sidebar navigation link IDs, OR to the navigation ID of a parent
// page. Not all pages are directly linked from the sidebar. If a page is accessed through a parent page that has a
// relevant access check, you can use the parent page's nav ID here to protect the child. A page ID of empty is assumed
// to be accessible, since no relevant access check can be performed for it.
//
// For example, if edit review responses templates should only be accessible if you have access to the manage reviews page:
//
// [PageId.manage-reviews]: 'nav-manage-reviews',
// [PageId.manage_response_templates]: 'nav-manage-reviews',
//
// But if there is no relevant access check, and/or no sidebar link, you can leave the nav ID blank:
//
// [PageId.dashboard]: '',
//
export const PageIdToNavIdMap: Record<PageId, string> = {
  [PageId.home]: 'nav-home',
  [PageId.get_started]: 'nav-home',
  [PageId.inbox]: 'nav-inbox',
  [PageId.tasks]: 'nav-tasks',
  [PageId.contacts]: 'nav-contacts',
  [PageId.recommendations]: 'nav-recommendations',
  [PageId.old_recommendations]: '',
  [PageId.my_products]: 'nav-products',
  [PageId.store]: 'nav-store',
  [PageId.customer_list]: 'nav-customer-list',
  [PageId.executive_report]: 'nav-executive-report',
  [PageId.guides]: 'nav-guides',
  [PageId.business_profile]: 'nav-settings',
  [PageId.billing_settings]: 'nav-billing-settings',
  [PageId.files]: 'nav-files',
  [PageId.brands]: '',
  [PageId.user_profile]: '',
  [PageId.multi_location_keyword]: 'nav-brand-keyword-tracking-overview',
  [PageId.old_notification_settings]: '',
  [PageId.new_notification_settings]: 'nav-notification-settings',
  [PageId.social_connections]: 'nav-social-connections',
  [PageId.platform_integrations]: '',
  [PageId.email_history]: 'nav-emails',
  [PageId.email_configuration]: '',
  [PageId.sms_configuration]: '',
  [PageId.google_question_answer]: 'nav-google-my-business-google-q-and-a',
  [PageId.multi_location_dashboard]: 'nav-brand-home',
  [PageId.multi_location_reviews]: 'nav-brand-reputation-overview',
  [PageId.ml_reviews]: 'nav-brand-reputation-reviews',
  [PageId.multi_location_overview]: 'nav-brand-reputation-overview',
  [PageId.multi_location_net_promoter_score]: 'nav-brand-reputation-net_promoter_score',
  [PageId.multi_location_nps_overview]: 'nav-brand-reputation-nps-overview',
  [PageId.multi_location_nps_team]: 'nav-brand-reputation-nps-team',
  [PageId.multi_location_requests]: 'nav-brand-reputation-requests',
  [PageId.multi_location_requests_overview]: 'nav-brand-reputation-requests-overview',
  [PageId.multi_location_listings]: 'nav-brand-listings-overview',
  [PageId.keyword_tracking]: 'nav-brand-listings-keyword-tracking',
  [PageId.listing_sync]: 'nav-brand-listing-sync',
  [PageId.manage_multi_location_reviews]: 'nav-brand-reputation-manage',
  [PageId.manage_multi_location_listings]: 'nav-brand-manage-listings',
  [PageId.multi_location_reviews_sentiment]: 'nav-brand-reputation-insights',
  [PageId.multi_location_google_my_business]: 'nav-brand-google-my-business',
  [PageId.multi_location_google_question_answer]: 'nav-brand-google-my-business-google-q-and-a',
  [PageId.multi_location_advertising]: 'nav-brand-advertising',
  [PageId.multi_location_social]: 'nav-brand-social',
  [PageId.manage_multi_location_social_posts]: 'nav-brand-social-manage-posts',
  [PageId.multi_location_view_report]: '',
  [PageId.multi_location_data_export]: 'nav-brand-data-export',
  [PageId.orders]: 'nav-orders',
  [PageId.edition_upgrades]: '',
  [PageId.request_multi_location_reviews]: 'nav-brand-reputation-manage',
  [PageId.multi_location_report]: 'nav-brand-report',
  [PageId.single_location_report]: '',
  [PageId.website]: '',
  [PageId.invoices]: 'nav-invoices',
  [PageId.meeting_scheduler]: 'nav-meeting-scheduler',
  [PageId.review_cart]: '',
  [PageId.orders_table]: '',
  [PageId.referral]: '',
  [PageId.reputation]: 'product-RM',
  [PageId.brand_reputation]: 'nav-reputation',
  [PageId.manage_response_templates]: '',
  [PageId.edit_response_template]: '',
  [PageId.fulfillment]: 'nav-fulfillment',
  [PageId.work_order]: '',
  [PageId.crm]: 'nav-crm',
  [PageId.ai]: 'nav-ai-assistants',
  [PageId.crm_companies]: 'nav-crm-companies',
  [PageId.leaderboard]: 'nav-leaderboard',
  [PageId.forms]: 'nav-custom-forms',
  [PageId.embedded_listings]: 'product-MS',
  [PageId.multi_location_embedded_listings]: 'nav-embedded-listings',
  [PageId.embedded_social]: 'product-SM',
  [PageId.multi_location_embedded_social]: 'nav-embedded-social',
  [PageId.embedded_advertising]: 'product-MP-94072e44d5364872b672d7ab4fc7a7e8',
  [PageId.campaigns]: '',
  [PageId.embedded_gmb]: 'nav-embedded-google-my-business',
  [PageId.emails]: '',
  [PageId.inbox_brand]: 'nav-brand-inbox',
  [PageId.settings_page]: 'nav-settings-link',
  [PageId.profile_settings]: 'nav-profile-settings',
  [PageId.multi_location_litings_keyword]: 'nav-brand-listings-rank-tracking',
  [PageId.multi_location_keyword_tracking]: 'nav-embedded-keyword-tracking',
  [PageId.payments]: 'nav-payment-settings',
  [PageId.nav_brand_reputation_nps]: 'nav-brand-reputation-nps',
  [PageId.payments_invoices]: 'nav-payments-invoices',
  [PageId.payments_payments]: 'nav-payments-payments',
  [PageId.payments_payouts]: 'nav-payments-payouts',
};
