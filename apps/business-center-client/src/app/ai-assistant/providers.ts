import { inject, InjectionToken } from '@angular/core';
import { combineLatest, of } from 'rxjs';
import { catchError, distinctUntilChanged, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { AccountGroup, AccountGroupService } from '../account-group';
import { editionIds, partnerId, productIds } from '../../globals';
import {
  Action,
  ActionWithPath,
  ActionWithUrl,
  AiAssistant,
  AiAssistantConfig,
  AiConnection,
  ASSISTANT_ID_CHAT_RECEPTIONIST,
  ASSISTANT_ID_CONTENT_CREATOR,
  ASSISTANT_ID_REVIEW_MANAGER,
  ASSISTANT_ID_VOICE_RECEPTIONIST,
  AssistantInfo,
  ConnectionType,
  disabled_card_colour,
  MY_LISTING_CONNECTION_NAME,
  WEBCHAT_CONNECTION_NAME,
} from '@galaxy/ai-assistant';
import { AppPartnerService } from '../core/app_partner.service';
import { AppKey } from '@galaxy/marketplace-apps';
import { ProductService } from '../core/product.service';
import { PartnerService, Product } from '../core';
import {
  AI_DEFAULT_BUSINESS_ASSISTANT_CONNECTIONS,
  AI_DEFAULT_BUSINESS_ASSISTANT_WORKFORCE,
  aiAssistantInfoMap,
  productIdToAssistantIdMap,
} from './ai-assistant-constants';
import { BusinessNavDataService } from '@vendasta/business-nav';
import { SideNavigationLink } from '@vendasta/atlas';
import { PageId } from '../page-access';
import { PageIdToNavIdMap } from '../page-access/page-id.enum';
import { Configuration, PartnerSettingsApiService } from '@vendasta/listing-products';
import { isBrand, LocationsService } from '../locations';
import { Connection, Namespace } from '@vendasta/ai-assistants';
import { toObservable } from '@angular/core/rxjs-interop';
import { AuthService } from '../auth.service';
import { FeatureFlagService } from '../core/feature-flag.service';

function handleProduct(
  product: Product,
  assistantId: string,
  isPro: boolean,
  useProductIcon = true,
  navLink?: SideNavigationLink,
): AssistantInfo {
  const assistantInfo = aiAssistantInfoMap[assistantId];

  if (!product.productId) {
    assistantInfo.hideCard = true;
  }

  if (navLink?.serviceProviderId === 'VBC') {
    assistantInfo.cta ??= { pathCommands: [] } satisfies ActionWithPath;
    assistantInfo.cta.pathCommands = [navLink.path];
  } else if (navLink?.serviceProviderId === productIds.socialMarketing) {
    assistantInfo.cta ??= { pathCommands: [] } satisfies ActionWithPath;
    assistantInfo.cta.pathCommands[0] = { outlets: { action: 'compose-social-post' } };
  } else if (navLink?.url) {
    assistantInfo.cta ??= { url: '' } satisfies ActionWithUrl;
    assistantInfo.cta.url = navLink?.url || '';
    assistantInfo.cta.newTab = navLink?.openInNewTab ?? false;
  }

  if (useProductIcon) {
    assistantInfo.iconUrl = product.iconUrl;
  }
  assistantInfo.cardDisabled = !isPro;
  if (assistantInfo.cta) {
    assistantInfo.cta.showButton = isPro;
  }

  return assistantInfo;
}

function hydrateAssistantFromInfo(assistant: AiAssistant, assistantsInfo: AssistantInfo[]): AiAssistant {
  const info = assistantsInfo.find((info) => info.assistantId === assistant.assistant.id);
  if (!info) {
    return assistant;
  }
  if (info.cta) {
    //default cta label if not provided in the default assistant list
    assistant.cta ??= {
      label: 'AI_ASSISTANT.WORKFORCE.TRY_IT',
    };

    if (info.cta?.url || info.cta?.pathCommands || info.cta?.callback) {
      assistant.cta.action = info.cta;
    }
  }

  if (info.iconUrl) {
    assistant.decoration ??= {};
    assistant.decoration.avatarIconUrl = info.iconUrl;
  }

  assistant.isDisabled = info.cardDisabled;
  if (info.cardDisabled) {
    assistant.decoration ??= {};
    assistant.decoration.gradientColor = disabled_card_colour;
  }

  return assistant;
}

function filterNavItemByProduct(navigationLinks: SideNavigationLink[], productId: string): SideNavigationLink {
  let navigationId = '';
  switch (productId) {
    case productIds.socialMarketing:
      navigationId = PageIdToNavIdMap[PageId.embedded_social];
      break;

    case productIds.reputationManagement:
      navigationId = PageIdToNavIdMap[PageId.reputation];
      break;
  }

  return navigationLinks.find((navItem) => navItem.navigationId === navigationId);
}

export const AI_ASSISTANTS_CONFIG = new InjectionToken<AiAssistantConfig>(
  '[Business Center Client]: Token for AI assistants config',
  {
    providedIn: 'root',
    factory: function (): AiAssistantConfig {
      const accountGroupService = inject(AccountGroupService);
      const appPartnerService = inject(AppPartnerService);
      const productService = inject(ProductService);
      const navData = inject(BusinessNavDataService);
      const partnerService = inject(PartnerService);
      const partnerSettingsApiService = inject(PartnerSettingsApiService);
      const locationService = inject(LocationsService);
      const authService = inject(AuthService);
      const featureFlagService = inject(FeatureFlagService);
      // BREW-1431: NBLY Voice Receptionist POC
      const voiceAINBLYAccountGroups = [
        'AG-6R772G8ZJ6',
        'AG-R6VN65LK8H',
        'AG-JQB5ZTP4X8',
        'AG-2BPQCT27GH',
        'AG-4T2TR48LM2',
        'AG-N42V33GCVZ',
        'AG-G4PHF8MR2F',
        'AG-8PFGWWLM6Z',
        'AG-DKL4G3STWT',
        'AG-H4V5C6NWX3',
        'AG-K5BM4K87TC',
        'AG-4TW6XV2NGS',
        'AG-8B7PMHHMXB',
        'AG-G3X6QF3RQK',
        'AG-S8FGCZHRHK',
        'AG-KT6B555PK6',
        'AG-GLH3JJKCDB',
      ];

      const currentLocation$ = locationService.currentLocation$.pipe(
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const accountGroupId$ = currentLocation$.pipe(
        filter((loc) => loc !== null),
        distinctUntilChanged(),
        map((location) => {
          return isBrand(location) ? '' : (<AccountGroup>location).accountGroupId;
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const marketId$ = accountGroupService.currentAccountGroup$.pipe(
        map((ag) => ag?.marketId),
        filter<string>(Boolean),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const partnerId$ = of(partnerId);

      const msChatWidgetEnabled$ = partnerService.wlConfig$.pipe(map((config) => config.msChatWidgetEnabled));

      const config$ = combineLatest([msChatWidgetEnabled$, partnerId$, accountGroupId$]).pipe(
        switchMap(([enabled, partnerId, accountGroupId]) => {
          if (enabled) {
            return partnerSettingsApiService
              .getConfiguration({ partnerId: partnerId, businessId: accountGroupId })
              .pipe(
                map((resp) => resp?.configuration),
                catchError(() => {
                  console.error('Error getting partner configuration');
                  return of({} as Configuration);
                }),
              );
          }
          return of({} as Configuration);
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const voiceFeatureFlagEnabled$ = combineLatest([partnerId$, marketId$]).pipe(
        distinctUntilChanged(),
        switchMap(([partnerId, marketId]) => featureFlagService.checkFeatureFlag(partnerId, marketId, 'voice_ai')),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );
      const phoneSupportingRegion$ = accountGroupService.currentAccountGroup$.pipe(
        distinctUntilChanged(),
        map((accountGroup) => ['US', 'CA'].includes(accountGroup?.country)),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );
      const excludedFromVoice$ = combineLatest([partnerId$, accountGroupId$]).pipe(
        map(([partnerId, accountGroupId]) => {
          return partnerId === 'NBLY' && !voiceAINBLYAccountGroups.includes(accountGroupId);
        }),
        distinctUntilChanged(),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );
      const voiceAIAvailable$ = combineLatest([
        voiceFeatureFlagEnabled$,
        phoneSupportingRegion$,
        excludedFromVoice$,
      ]).pipe(
        distinctUntilChanged(),
        map(([ffEnabled, supportingRegion, excludedFromVoice]) => ffEnabled && supportingRegion && !excludedFromVoice),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const assistantsProductInfo$ = combineLatest([
        productService.activeProducts$,
        marketId$,
        navData.navigationLinks$,
        config$,
      ]).pipe(
        switchMap(([products, marketId, navigationLinks, config]) => {
          const activeProdAssistants = products
            .map((product) => {
              if (product.productId === productIds.socialMarketing) {
                return handleProduct(
                  product,
                  ASSISTANT_ID_CONTENT_CREATOR,
                  !product.editionId,
                  true,
                  filterNavItemByProduct(navigationLinks, product.productId),
                );
              }
              if (product.productId === productIds.reputationManagement) {
                return handleProduct(
                  product,
                  ASSISTANT_ID_REVIEW_MANAGER,
                  product.editionId === editionIds.reputationManagementPremium,
                  true,
                  filterNavItemByProduct(navigationLinks, product.productId),
                );
              }
              if (product.productId === productIds.inboxPro) {
                return handleProduct(product, ASSISTANT_ID_VOICE_RECEPTIONIST, !product.editionId, false);
              }
              const localSEOUrl = config?.publicMyListingUrl || '';

              if (product.productId === productIds.listingBuilder && !!localSEOUrl) {
                const assistantInfo = aiAssistantInfoMap[ASSISTANT_ID_CHAT_RECEPTIONIST];
                assistantInfo.cta ??= {} as Action;
                assistantInfo.cta.showButton = true;
                assistantInfo.cta.newTab = true;
                assistantInfo.cta.url = localSEOUrl;
                return assistantInfo;
              }
              return {} as AssistantInfo;
            })
            .filter((assistantInfo) => assistantInfo);

          const inactiveProducts = [
            productIds.socialMarketing,
            productIds.reputationManagement,
            productIds.inboxPro,
          ].filter((handledProductId) => !products.some((product) => product.productId === handledProductId));

          // If all products are active, return the active infos
          if (!inactiveProducts.length) {
            return of(activeProdAssistants);
          }

          // If there are inactive products, fetch them either way because icons are still needed
          return appPartnerService
            .getMulti(
              inactiveProducts.map((productId) => <AppKey>{ appId: productId }),
              marketId,
            )
            .pipe(
              map((res) => {
                return [
                  ...activeProdAssistants,
                  ...res.map((appData, index) => {
                    const originalProductId = inactiveProducts[index];
                    const product = appData ? Product.fromV2App(appData) : undefined;
                    const useProductIcon = !(product?.productId === productIds.inboxPro);
                    return handleProduct(product, productIdToAssistantIdMap[originalProductId], false, useProductIcon);
                  }),
                ].filter((assistantInfo) => assistantInfo);
              }),
            );
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );

      const defaultAIWorkforce$ = assistantsProductInfo$.pipe(
        map((assistantsProductInfo) => {
          return AI_DEFAULT_BUSINESS_ASSISTANT_WORKFORCE.filter((defaultAssistant) => {
            const associatedProductInfo = assistantsProductInfo.find(
              (productInfo) => productInfo.assistantId === defaultAssistant.assistant.id,
            );

            if (associatedProductInfo) {
              return !associatedProductInfo.hideCard;
            }

            return true;
          }).map((defaultAssistant) => {
            const aiAssistant = structuredClone(defaultAssistant);
            return hydrateAssistantFromInfo(aiAssistant, assistantsProductInfo);
          });
        }),
      );

      const defaultConnections$ = combineLatest([accountGroupId$, productService.activeProducts$, config$]).pipe(
        map(([accountGroupId, activeProducts, config]) =>
          buildAIDefaultConnections(accountGroupId, activeProducts, config),
        ),
      );

      const currentUserId$ = toObservable(authService.effectiveUserId);

      return {
        accountGroupId$,
        marketId$,
        partnerId$,
        defaultConnections$,
        defaultAIWorkforce$,
        currentUserId$,
        voiceAIAvailable$,
      };
    },
  },
);

function buildAIDefaultConnections(
  accountGroupId: string,
  activeProducts: Product[],
  config: Configuration,
): AiConnection[] {
  const defaultConnections = AI_DEFAULT_BUSINESS_ASSISTANT_CONNECTIONS.map((aiConnection) => {
    return {
      ...aiConnection,
      connection: new Connection({
        ...aiConnection.connection,
        namespace: new Namespace({ accountGroupNamespace: { accountGroupId: accountGroupId } }),
      }),
    };
  });

  const aiDefaultConnections: AiConnection[] = [];
  defaultConnections.forEach((defaultAiConnection) => {
    const aiConnection = structuredClone(defaultAiConnection);
    const connection = aiConnection.connection;
    if (connection.connectionType === ConnectionType.WebchatWidget) {
      if (connection.name === MY_LISTING_CONNECTION_NAME && config?.publicMyListingUrl && !config?.chatWidgetId) {
        aiConnection.cta = {
          label: 'AI_ASSISTANT.SETTINGS.CONNECTIONS.TRY_MY_LISTING',
          action: {
            showButton: true,
            url: config.publicMyListingUrl,
            newTab: true,
          },
        };
        aiDefaultConnections.push(aiConnection);
      }
      // If inbox Pro is active and the webchat is not created, show default message
      if (
        connection.name === WEBCHAT_CONNECTION_NAME &&
        activeProducts?.some(
          (product) => product.productId === productIds.inboxPro || product.productId === productIds.aiWebchat,
        )
      ) {
        aiConnection.connection.name = 'AI_ASSISTANT.SETTINGS.CONNECTIONS.WEBCHAT_CREATING';
        aiConnection.cta = {
          action: {
            showButton: false,
            url: '',
          },
        };
        aiDefaultConnections.push(aiConnection);
      }
    }
  });

  return aiDefaultConnections;
}
