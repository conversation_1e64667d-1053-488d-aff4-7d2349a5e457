import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Params, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { combineLatest, Observable, of } from 'rxjs';
import { EditionUpgradesSettingsService } from './edition-upgrades-settings.service';
import { AppPartnerService, EditionUpgradeAction } from '@galaxy/marketplace-apps';
import { map, mergeMap } from 'rxjs/operators';
import { AccountGroupService } from '../account-group';
import { FeatureFlagService } from '@galaxy/partner';
import { FieldMask, MarketplacePackagesApiService } from '@vendasta/marketplace-packages';
import { ShoppingCartManagementService } from '../shopping-cart/shopping-cart-management.service';
import { ShoppingCartOverlayService } from '../shopping-cart/shopping-cart-overlay/shopping-cart-overlay.service';

@Injectable()
export class EditionUpgradesPageGuard {
  constructor(
    private readonly router: Router,
    private readonly editionUpgradesSettingsService: EditionUpgradesSettingsService,
    private readonly accountGroupService: AccountGroupService,
    private readonly appPartnerService: AppPartnerService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly shoppingCartManagementService: ShoppingCartManagementService,
    private readonly marketplacePackagesService: MarketplacePackagesApiService,
    private readonly shoppingCartOverlayService: ShoppingCartOverlayService,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const queryParams = route.queryParams;
    // TODO do a real check for whether to go to create orders page.
    if (queryParams.items && route.params.agid) {
      const items = JSON.parse(queryParams['items']);
      if (items.length === 1) {
        return this.editionUpgradesSettingsService.loadEditionUpgradeAction(items[0].appId).pipe(
          mergeMap((action) => {
            if (
              action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_CREATE_ORDER ||
              action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_UNSET
            ) {
              const dashboard = 'account/location/' + route.params.agid + '/orders/create';
              return of(
                this.router.createUrlTree([dashboard], {
                  queryParams: queryParams,
                }),
              );
            } else if (action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_CUSTOM_ORDER) {
              return this.getUrlTreeForCustomeOrderAction(items[0].appId, route.params.agid);
            }
            return of(true);
          }),
        );
      } else if (items.length > 1) {
        return this.editionUpgradesSettingsService.loadEditionUpgradeAction(items[0].appId).pipe(
          mergeMap((action) => {
            if (
              action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_CREATE_ORDER ||
              action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_UNSET
            ) {
              const storePage = 'account/location/' + route.params.agid + '/store/app/' + items[0].appId;
              return of(
                this.router.createUrlTree([storePage], {
                  queryParams: { tab: 2 },
                }),
              );
            } else if (action === EditionUpgradeAction.EDITION_UPGRADE_ACTION_CUSTOM_ORDER) {
              return this.getUrlTreeForCustomeOrderAction(items[0].appId, route.params.agid);
            }
            return of(true);
          }),
        );
      }
    }
    return true;
  }

  private getUrlTreeForCustomeOrderAction(appId: string, accountGroupId: string): Observable<UrlTree> {
    return this.accountGroupService.currentAccountGroup$.pipe(
      mergeMap((ag) => {
        return combineLatest([
          this.featureFlagService.batchGetStatus(ag.partnerId, ag.marketId, ['business_center_shopping_cart']),
          this.appPartnerService.getAppSettings(appId, ag.partnerId, ag.marketId),
        ]).pipe(
          mergeMap(([flags, settings]) => {
            const packageIds = settings.editionChange.customUpgradePackageIds;
            if (!!packageIds && packageIds.length > 1) {
              return this.getProductName(ag.partnerId, appId).pipe(
                map((appName) => {
                  const storePage = 'account/location/' + accountGroupId + '/store';
                  const packageIdsValue = packageIds.join(',');
                  const params: Params = {
                    packageId: packageIdsValue,
                    appName: appName,
                  };
                  return this.router.createUrlTree([storePage], {
                    queryParams: params,
                  });
                }),
              );
            } else if (!!packageIds && packageIds.length > 0) {
              const customPackageId: string = settings.editionChange.customUpgradePackageIds[0];
              if (flags['business_center_shopping_cart']) {
                this.shoppingCartManagementService
                  .addPackage(customPackageId)
                  .subscribe(() => this.shoppingCartOverlayService.open(document.getElementById('shoppingCartButton')));
                const viewCart = 'account/location/' + accountGroupId + '/review-cart';
                return of(this.router.createUrlTree([viewCart]));
              }
              const viewOrderForm = 'account/location/' + accountGroupId + '/orders/create';
              const queryParams: Params = {
                items: `[{"packageId":"${customPackageId}"}]`,
              };
              return of(this.router.createUrlTree([viewOrderForm], { queryParams }));
            }
          }),
        );
      }),
    );
  }

  private getProductName(partnerId: string, productId: string): Observable<string> {
    const appsFieldMask = new FieldMask({ paths: ['productId', 'name'] });
    return this.marketplacePackagesService
      .getMultiProducts({
        partnerId: partnerId,
        productIds: [productId],
        fieldMask: appsFieldMask,
      })
      .pipe(
        map((resp) => {
          const products = resp.products.map((item) => item.product);
          return !!products && products.length > 0 ? products[0].name : null;
        }),
      );
  }
}
