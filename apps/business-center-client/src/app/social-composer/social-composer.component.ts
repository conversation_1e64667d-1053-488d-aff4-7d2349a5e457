import { Component, Injector, OnDestroy, OnInit } from '@angular/core';

import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ActivatedRoute, ParamMap, Router, RouterModule } from '@angular/router';
import { ComposerComponent, DATA_TOKEN } from './composer';

import { Subject } from 'rxjs';
import { map, take, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'bc-social-composer',
  imports: [RouterModule],
  template: ``,
})
export class SocialComposerComponent implements OnInit, OnDestroy {
  private overlayRef: OverlayRef;
  private destroy$$ = new Subject<void>();
  private readonly postId$ = this.route.paramMap.pipe(map((params) => params.get('postId')));
  workflow = '';

  constructor(
    private readonly overlay: Overlay,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
  ) {}

  ngOnInit() {
    this.route.queryParamMap.pipe(map((x: ParamMap) => x.get('workflow'))).subscribe((wf) => {
      this.workflow = wf;
    });
    this.postId$.pipe(take(1)).subscribe((postId) => this.open(postId));
  }

  ngOnDestroy() {
    this.destroy$$.next();
    this.destroy$$.complete();
    this.workflow = '';
    this.close();
  }

  private open(postId: string | null): void {
    this.overlayRef = this.overlay.create({
      scrollStrategy: this.overlay.scrollStrategies.block(),
      height: `100vh`,
      width: `100vw`,
    });
    const portalInjector = Injector.create({
      providers: [{ provide: DATA_TOKEN, useValue: { postId } }],
    });
    const composerPortal = new ComponentPortal(ComposerComponent, null, portalInjector);
    const ref = this.overlayRef.attach(composerPortal);
    ref.instance.workflow = this.workflow;
    ref.instance.composerClose.pipe(take(1), takeUntil(this.destroy$$)).subscribe(() => this.close());
  }

  private close(): void {
    this.overlayRef.dispose();

    this.router.navigate(['', { outlets: { action: null } }], {
      queryParamsHandling: 'merge',
    });
  }
}
