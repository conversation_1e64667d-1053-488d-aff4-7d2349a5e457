import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { ActivatedRoute, Router, TitleStrategy } from '@angular/router';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';
import {
  ResponseTemplateRequest,
  ResponseTemplatesService,
  ReviewResponseTemplateManagementModule,
  ReviewResponseTemplateServiceInterfaceToken,
} from '@vendasta/review-response-template-management';
import { combineLatest, Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { partnerId } from '../../../../globals';
import { isAccountGroup, isBrand, LocationsService } from '../../../locations';
import { NavigationService } from '../../../navigation/navigation.service';
import { buildBrandFullPath } from '../../brand-helper';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { KeywordTrackingModule } from '@vendasta/local-seo';
import { LaunchAppButtonComponent } from '../../../launch-app-button/launch-app-button.component';
import { NavTabsComponent } from '@vendasta/business-nav';
import { TranslateModule } from '@ngx-translate/core';
import { previousRoute } from '../../route-utils';
import { FeatureFlagService } from '../../../core/feature-flag.service';

export interface ManagerInput {
  templateRequest: ResponseTemplateRequest;
  templateEditorUrl: string;
}

const REPMAN_EMBEDDED = 'repman_reviews_in_sl_bizapp';

@Component({
  selector: 'bc-manage-response-templates',
  imports: [
    GalaxySnackbarModule,
    CommonModule,
    ReviewResponseTemplateManagementModule,
    GalaxyPageModule,
    KeywordTrackingModule,
    LaunchAppButtonComponent,
    NavTabsComponent,
    TranslateModule,
  ],
  providers: [
    {
      provide: ReviewResponseTemplateServiceInterfaceToken,
      useClass: ResponseTemplatesService,
    },
  ],
  templateUrl: './manage-response-templates.component.html',
  styleUrls: ['./manage-response-templates.component.scss'],
})
export default class ManageResponseTemplatesComponent {
  private readonly featureFlagService = inject(FeatureFlagService);

  protected readonly managerInput$: Observable<ManagerInput>;

  protected readonly title: string = inject(TitleStrategy).getResolvedTitleForRoute(inject(ActivatedRoute).snapshot);
  protected readonly previousPageUrl = previousRoute(this.router.url);

  readonly isRepmanEmbedded$ = this.featureFlagService
    .checkFeatureFlag(partnerId, '', REPMAN_EMBEDDED)
    .pipe(shareReplay({ bufferSize: 1, refCount: true }));

  constructor(
    readonly navigationService: NavigationService,
    readonly locationService: LocationsService,
    private readonly router: Router,
  ) {
    this.managerInput$ = combineLatest([locationService.currentLocation$, this.isRepmanEmbedded$]).pipe(
      map(([location, isRepmanEmbedded]) => {
        if (isBrand(location)) {
          const brandId = buildBrandFullPath(location.groupNodes);
          return {
            templateRequest: {
              partnerId,
              brandId,
            },
            templateEditorUrl: isRepmanEmbedded
              ? `/account/brands/${brandId}/reputation/manage-reviews/response-templates/edit-template`
              : `/account/brands/${brandId}/manage-reviews/response-templates/edit-template`,
          };
        }
        if (isAccountGroup(location)) {
          return {
            templateRequest: {
              partnerId,
              businessId: location.accountGroupId,
            },
            templateEditorUrl: `/account/location/${location.accountGroupId}/reputation/manage-reviews/response-templates/edit-template`,
          };
        }
        return null;
      }),
    );
  }
}
