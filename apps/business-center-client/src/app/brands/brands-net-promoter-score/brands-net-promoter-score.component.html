<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ 'NPS.TITLE' | translate }}
    </glxy-page-title>
    <glxy-page-actions class="nps-filter-container">
      <bc-time-range-picker></bc-time-range-picker>
      @if (isCurrentLocationABrand$ | async) {
        <bc-brand-filter-container [displaySourceFilter]="false"></bc-brand-filter-container>
      }
      <button class="nav-btn" mat-stroked-button (click)="toggleSidebar()">
        <mat-icon class="icon">tune</mat-icon>
        <span class="message">
          {{ 'PERFORMANCE.MULTI_LOCATION.FILTERS.PAGE_FILTERS' | translate }}
        </span>
      </button>
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-below-toolbar>
    <bc-nav-tabs></bc-nav-tabs>
  </glxy-page-below-toolbar>
  <glxy-page-wrapper widthPreset="full">
    <div class="page-wrapper">
      @if (hasNPSData()) {
        <div class="nps-graph-chart-wrapper">
          <reviews-nps-chart-overall-score
            [ChartData$]="ChartData$"
            class="chart-item"
          ></reviews-nps-chart-overall-score>
          <reviews-nps-rolling-average
            [npsRollingAvgData$]="npsRollingAvgData$"
            class="chart-item"
          ></reviews-nps-rolling-average>
        </div>
      }
      @if (npsData$ | async; as npsData) {
        @if (npsData.length > 0) {
          @for (review of npsData; track review) {
            <reviews-nps-card
              [review]="review"
              [enableResendNPSRequest]="enableResendNPSRequest$ | async"
              [redirectMlToContact]="true"
            >
              <reviews-nps-provider [providerNames$]="review.Providers"></reviews-nps-provider>
              <reviews-nps-address [address]="review.Address"></reviews-nps-address>
            </reviews-nps-card>
          }
          @if (hasMoreNPSData$ | async) {
            <glxy-infinite-scroll-trigger
              [visiblilityMargin]="20"
              [minHeight]="0"
              (isVisible)="loadMore()"
            ></glxy-infinite-scroll-trigger>
          }
        }
        @if (npsData.length === 0 && (loadingNPSFeed$ | async) === false) {
          <glxy-empty-state>
            <glxy-empty-state-hero>
              <img src="{{ noNPSReviewFoundImageURL }}" />
            </glxy-empty-state-hero>
            <glxy-empty-state-title>
              {{ 'NPS.EMPTY_STATE' | translate: { filterType: filterType } }}
            </glxy-empty-state-title>
          </glxy-empty-state>
        }
      }
      @if (loadingNPSFeed$ | async) {
        <glxy-loading-spinner
          [size]="'default'"
          [fullWidth]="true"
          [fullHeight]="false"
          [inline]="false"
        ></glxy-loading-spinner>
      }
    </div>
  </glxy-page-wrapper>
</glxy-page>
