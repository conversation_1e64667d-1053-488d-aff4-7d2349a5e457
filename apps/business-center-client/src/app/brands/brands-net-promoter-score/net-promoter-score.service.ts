import { inject, Injectable, signal } from '@angular/core';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, debounceTime, map, switchMap, tap, delay } from 'rxjs/operators';
import {
  CompositeFilterOperator,
  DateRange as SDKDateRange,
  FieldFilter,
  FieldFilterOperator,
  FieldFilterOperatorFunction,
  Filter,
  GroupBy,
  GroupByDimension,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MultiLocationAnalyticsService,
  Order,
  OrderBy,
  OrderByOrderColumn,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
  TypedValue,
} from '@vendasta/multi-location-analytics';
import {
  GetProviderRequest,
  NetPromoterScoreApiService,
  ProviderApiService,
  ProviderDetails,
} from '@vendasta/reputation';
import { NPSData, NPSRollingAverageDataPoints, NPSScoreOverallData } from '@vendasta/reviews';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BrandContext, QueryService } from '../../metrics/query.service';
import { partnerId } from '../../../globals';
import { CRMApiService } from '@vendasta/crm';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { CheckboxFilterField, Filters, FilterSection } from '@vendasta/uikit';
import { NpsScoreService } from '@vendasta/reviews';
import { SidepanelService } from '../../navigation/sidepanel.service';

interface TeamNPSOverviewData {
  ProviderID: string;
  NPSVolume: number;
  Promoters: number;
  Passives: number;
  Detractors: number;
  NPSScore: number;
}

export interface CRMData {
  Email: string;
  Name: string;
  PhoneNumber: string;
  ContactId: string;
}

interface NetPromoterScoreData {
  netPromoterScoreId: string;
  crmContactId: string | null;
}

interface NPSOverviewData {
  AccountGroupID: string;
  NPSVolume: number;
  Promoters: number;
  Passives: number;
  Detractors: number;
  NPSScore: number;
}

interface State {
  npsData: NPSData[];
  loadingNPSFeed: boolean;
  npsDataCursor: string | null;
}

const InitialState: State = {
  npsData: [],
  loadingNPSFeed: false,
  npsDataCursor: null,
};

@Injectable({ providedIn: 'root' })
export class NetPromoterScoreService {
  private multiLocationAnalyticsService = inject(MultiLocationAnalyticsService);
  private crmAPIService = inject(CRMApiService);
  private queryService = inject(QueryService);
  private netPromoterScoreApiService = inject(NetPromoterScoreApiService);
  private snackbarService = inject(SnackbarService);
  private npsScoreService = inject(NpsScoreService);
  private sidepanelService = inject(SidepanelService);
  private providerApiService = inject(ProviderApiService);

  private accountGroupService = inject(AccountGroupMetricService);
  private loadMoreNPSData$$: BehaviorSubject<null> = new BehaviorSubject(null);
  private store$$: BehaviorSubject<State> = new BehaviorSubject<State>({ ...InitialState });
  readonly npsData$: Observable<NPSData[]> = this.store$$.pipe(map((state) => state.npsData));
  private npsScoreFilters$$ = new BehaviorSubject<string[]>([]);
  private toggleSidebarChanged$$: BehaviorSubject<null> = new BehaviorSubject(null);
  public selectedProviders = new BehaviorSubject<ProviderDetails[]>([]);
  public providerIds$$ = new BehaviorSubject<string[]>([]);

  readonly hasMoreNPSData$: Observable<boolean> = this.store$$.pipe(map((state) => !!state.npsDataCursor));
  readonly loadingNPSFeed$: Observable<boolean> = this.store$$.pipe(map((state) => state.loadingNPSFeed));
  currentByLocation$: Observable<NPSOverviewData[]> = this.getNPSOverviewData('current');
  previousByLocation$: Observable<NPSOverviewData[]> = this.getNPSOverviewData('previous');

  currentByProvider$: Observable<TeamNPSOverviewData[]> = this.getTeamNPSOverviewData('current');
  previousByProvider$: Observable<TeamNPSOverviewData[]> = this.getTeamNPSOverviewData('previous');

  overallScoreChartLoading = signal<boolean>(false);
  rollingAvgChartLoading = signal<boolean>(false);

  initializeNPSData(): Observable<null> {
    return combineLatest([
      this.queryService.brandsContext$.pipe(
        tap(() => {
          this.state = { ...this.state, npsData: [], npsDataCursor: null, loadingNPSFeed: true };
        }),
      ),
      this.loadMoreNPSData$$.pipe(
        tap(() => {
          this.state = { ...this.state, loadingNPSFeed: true };
        }),
      ),
      this.accountGroupService.filteredLocationsForPath$,
      this.npsScoreFilters$$.pipe(
        tap(() => {
          this.state = { ...this.state, npsData: [], npsDataCursor: null, loadingNPSFeed: true };
        }),
      ),
      this.providerIds$$.asObservable().pipe(
        tap(() => {
          this.state = { ...this.state, npsData: [], npsDataCursor: null, loadingNPSFeed: true };
        }),
      ),
    ]).pipe(
      debounceTime(200),
      switchMap(([brandsContext, _, accountGroupData, npsScoreFilters, providerIds]) => {
        const request = this.createQueryMetricsRequest(
          partnerId,
          brandsContext,
          null,
          providerIds?.length ? providerIds : null,
        );
        if (npsScoreFilters.length > 0) {
          if (request?.filter?.compositeFilter) {
            request.filter.compositeFilter.filters.push(this.createQueryMetricFilters(npsScoreFilters));
          } else {
            request.filter = this.createQueryMetricFilters(npsScoreFilters);
          }
        }
        request.measures = [
          new Measure({
            measure: 'net_promoter_score_id',
          }),
          new Measure({
            measure: 'score_left_time',
          }),
        ];
        request.limit = 10;
        if (this.state.npsDataCursor) {
          request.cursor = this.state.npsDataCursor;
        }

        return this.multiLocationAnalyticsService.queryMetrics(request).pipe(
          map((response) => {
            const extractedIds = this.extractNPSIdsFromResponse(response);
            this.state = {
              ...this.state,
              npsDataCursor: response.metricResults[0].cursor || null,
            };
            return { newNPSIds: extractedIds, accountGroupData };
          }),
        );
      }),
      switchMap(({ newNPSIds, accountGroupData }) => {
        if (newNPSIds.length === 0) {
          this.state = { ...this.state, loadingNPSFeed: false };
          return of([]);
        }

        const request = { netPromoterScoreIds: newNPSIds };
        return this.netPromoterScoreApiService.getMulti(request).pipe(
          switchMap((response) => {
            const newNPSData = response.netPromoterScore || [];
            // Use groupedData here
            const groupedData: Record<string, NetPromoterScoreData[]> = newNPSData.reduce(
              (acc, nps) => {
                const accountGroupId = nps.accountGroupId;
                const netPromoterScoreId = nps.netPromoterScoreId;
                const crmContactId = nps.attributes?.find((attr) => attr.key === 'CRMContactID')?.value || null;
                if (!acc[accountGroupId]) {
                  acc[accountGroupId] = [];
                }

                acc[accountGroupId].push({
                  netPromoterScoreId,
                  crmContactId,
                });
                return acc;
              },
              {} as Record<string, NetPromoterScoreData[]>,
            );

            // Pass groupedData to GetCRMContactsData
            return this.GetCRMContactsData(groupedData).pipe(
              map((crmData) => {
                const enrichedNPSData: NPSData[] = newNPSData.map((npsItem) => {
                  let enrichedItem: NPSData = {
                    netPromoterScoreId: npsItem?.netPromoterScoreId,
                    accountGroupId: npsItem?.accountGroupId,
                    score: npsItem?.score?.toString(),
                    comment: npsItem?.comment,
                    scoreLeftTime: npsItem?.scoreLeftTime,
                    attributes: npsItem?.attributes,
                  } as NPSData;

                  const crmContactID = npsItem.attributes?.find((attr) => attr.key === 'CRMContactID')?.value;
                  const matchingCRMData = crmData.find((crmItem) => crmItem.ContactId === crmContactID);
                  if (matchingCRMData) {
                    enrichedItem = {
                      ...enrichedItem,
                      Name: matchingCRMData.Name || '',
                      Email: matchingCRMData.Email || '',
                      PhoneNumber: matchingCRMData.PhoneNumber || '',
                    };
                  }

                  const matchingAccountGroup = accountGroupData[npsItem.accountGroupId];
                  if (matchingAccountGroup) {
                    enrichedItem = {
                      ...enrichedItem,
                      Address: `${matchingAccountGroup.address || ''}, ${matchingAccountGroup.city || ''}`,
                    };
                  }

                  const providerIds = npsItem?.providerIds ?? [];
                  if (providerIds.length === 0) {
                    return enrichedItem;
                  }
                  const uniqueProviderIds = [...new Set(providerIds)];
                  const request = new GetProviderRequest({
                    providerIds: uniqueProviderIds,
                  });

                  const providerNames$ = this.providerApiService.getProviderDetailsById(request).pipe(
                    map((response) => {
                      const providerDetails = response?.providerDetails || [];
                      return providerDetails.map((provider) => {
                        const firstName = provider.firstName ?? '';
                        const lastName = provider.lastName ?? '';
                        return `${firstName} ${lastName}`.trim();
                      });
                    }),
                  );

                  enrichedItem = {
                    ...enrichedItem,
                    Providers: providerNames$,
                  };

                  return enrichedItem;
                });

                return enrichedNPSData;
              }),
              map((enrichedNPSData) => {
                this.state = {
                  ...this.state,
                  npsData: this.mergeAndDeduplicateReviews(this.state.npsData, enrichedNPSData),
                  loadingNPSFeed: false,
                };
                return null;
              }),
              catchError((error) => {
                this.state = { ...this.state, loadingNPSFeed: false };
                this.snackbarService.openErrorSnack(error.error.message);
                return of(null);
              }),
            );
          }),
        );
      }),
    );
  }

  loadMore(): void {
    this.loadMoreNPSData$$.next(null);
  }

  toggleSidebar() {
    this.toggleSidebarChanged$$.next(null);
  }

  private mergeAndDeduplicateReviews(existingReviews: NPSData[], newReviews: NPSData[]): NPSData[] {
    const allReviews = [...existingReviews, ...newReviews];
    return allReviews.filter(
      (value, index, self) => index === self.findIndex((t) => t.netPromoterScoreId === value.netPromoterScoreId),
    );
  }

  private createQueryMetricsRequest(
    partnerID: string,
    brandsContext: BrandContext,
    accountGroupId?: string,
    providerIds?: string[],
  ): QueryMetricsRequest {
    const request: QueryMetricsRequest = this.getBaseQuery(
      'multi_location_net_promoter_score',
      partnerID,
      brandsContext.resourceIds,
    );
    request.dateRange = new SDKDateRange({
      start: brandsContext.startDate,
      end: brandsContext.endDate,
    });
    if (accountGroupId) {
      request.filter = new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            {
              fieldFilter: {
                dimension: 'account_group_id',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: accountGroupId,
                },
              },
            },
          ],
        },
      });
    } else if (providerIds?.length) {
      request.filter = new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            {
              fieldFilter: {
                dimension: 'provider_ids',
                operator: FieldFilterOperator.OVERLAP,
                value: {
                  value: providerIds,
                  valueType: PropertyType.PROPERTY_TYPE_STRING,
                },
              },
            },
          ],
        },
      });
    } else {
      request.filter = brandsContext.buildFilter();
    }
    request.orderBy = new OrderBy({
      orderBy: [
        new OrderByOrderColumn({
          column: 'score_left_time',
          order: Order.ORDER_DESC,
        }),
      ],
    });
    return request;
  }

  private createQueryMetricsRequestForScoreCard(
    partnerID: string,
    brandsContext: BrandContext,
    providerID?: string,
  ): QueryMetricsRequest {
    const request: QueryMetricsRequest = this.getBaseQuery(
      'multi_location_team_score_details',
      partnerID,
      brandsContext.resourceIds,
    );
    request.dateRange = new SDKDateRange({
      start: brandsContext.startDate,
      end: brandsContext.endDate,
    });
    if (providerID) {
      request.filter = new Filter({
        compositeFilter: {
          op: CompositeFilterOperator.AND,
          filters: [
            {
              fieldFilter: {
                dimension: 'provider_id',
                operator: FieldFilterOperator.EQUAL,
                value: {
                  value: providerID,
                },
              },
            },
          ],
        },
      });
    } else {
      request.filter = brandsContext.buildFilter();
    }
    return request;
  }

  private getBaseQuery(metricName: string, partnerID?: string, resourceIds?: ResourceId[]): QueryMetricsRequest {
    return new QueryMetricsRequest({
      partnerId: partnerID,
      metricName: metricName,
      resourceIds: resourceIds,
    });
  }

  // Helper function to extract NPS IDs from the response
  private extractNPSIdsFromResponse(response: QueryMetricsResponse): string[] {
    return response.metricResults?.[0]?.metrics?.metrics?.flatMap((metric) => metric.measures) || [];
  }

  private get state(): State {
    return this.store$$.getValue();
  }

  private set state(newState: State) {
    this.store$$.next(newState);
  }

  private GetCRMContactsData(groupedData: Record<string, NetPromoterScoreData[]>): Observable<CRMData[]> {
    const crmRequests = Object.entries(groupedData).flatMap(([accountGroupId, group]) => {
      const contactIDs = group.filter((item) => item.crmContactId).map((item) => item.crmContactId);

      if (contactIDs.length === 0) return [];

      return this.crmAPIService
        .getMultiContact({
          namespace: accountGroupId,
          crmObjectIds: contactIDs,
        })
        .pipe(
          map((crmResponse) =>
            crmResponse.crmObjects.map((crmObj) => {
              const fields = crmObj.fields || [];
              const firstNameField = fields.find((field) => field.externalId === 'standard__first_name');
              const lastNameField = fields.find((field) => field.externalId === 'standard__last_name');
              const phoneNumberField = fields.find((field) => field.externalId === 'standard__phone_number');
              const emailField = fields.find((field) => field.externalId === 'standard__email');

              return {
                ContactId: crmObj.crmObjectId,
                Name: `${firstNameField?.stringValue || ''} ${lastNameField?.stringValue || ''}`.trim(),
                PhoneNumber: phoneNumberField?.stringValue.replace('tel:', '') || '',
                Email: emailField?.stringValue || '',
              } as CRMData;
            }),
          ),
          catchError((error) => {
            this.snackbarService.openErrorSnack(error.error.message);
            return of(null);
          }),
        );
    });

    return combineLatest(crmRequests).pipe(map((results) => results.flat()));
  }

  public GetTeamScoreCardOverallScoreChartData(providerId: string): Observable<NPSScoreOverallData> {
    return combineLatest([this.queryService.brandsContext$]).pipe(
      debounceTime(50),
      tap(() => {
        this.overallScoreChartLoading.set(true);
      }),
      map(([brandsContext]) => {
        const { previousStartDate, previousEndDate } = this.calculatePreviousDates(
          brandsContext.startDate,
          brandsContext.endDate,
        );

        const currentRequest: QueryMetricsRequest = this.createQueryMetricsRequestForScoreCard(
          partnerId,
          brandsContext,
          providerId,
        );

        currentRequest.dateRange = new SDKDateRange({
          start: brandsContext.startDate,
          end: brandsContext.endDate,
        });

        currentRequest.filter = new Filter({
          compositeFilter: {
            op: CompositeFilterOperator.AND,
            filters: [
              {
                fieldFilter: {
                  dimension: 'provider_id',
                  operator: FieldFilterOperator.EQUAL,
                  value: {
                    value: providerId,
                  },
                },
              },
            ],
          },
        });

        currentRequest.measures = [
          this.createScoreMeasure([9, 10], 'promoter_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([7, 8], 'passive_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([6], 'detractor_score', FieldFilterOperator.LESS_THAN_OR_EQUAL),
        ];

        const previousRequest: QueryMetricsRequest = this.createQueryMetricsRequestForScoreCard(
          partnerId,
          brandsContext,
          providerId,
        );

        previousRequest.dateRange = new SDKDateRange({
          start: previousStartDate,
          end: previousEndDate,
        });

        previousRequest.filter = currentRequest.filter;
        previousRequest.measures = currentRequest.measures;

        return { currentRequest, previousRequest };
      }),
      switchMap(({ currentRequest, previousRequest }) => {
        return combineLatest([
          this.multiLocationAnalyticsService.queryMetrics(currentRequest),
          this.multiLocationAnalyticsService.queryMetrics(previousRequest),
        ]);
      }),
      map(([currentResponse, previousResponse]) => {
        const extractScores = (response) => {
          const [promoters = 0, passives = 0, detractors = 0] =
            response?.metricResults?.[0]?.metrics?.metrics?.[0]?.measures?.map(Number) || [];
          return { promoters, passives, detractors };
        };
        this.overallScoreChartLoading.set(false);
        return {
          current: extractScores(currentResponse),
          previous: extractScores(previousResponse),
        };
      }),
      catchError((error) => {
        this.overallScoreChartLoading.set(false);
        this.snackbarService.openErrorSnack(error.error.message);
        return of({
          current: {
            detractors: 0,
            passives: 0,
            promoters: 0,
          },
          previous: {
            detractors: 0,
            passives: 0,
            promoters: 0,
          },
        });
      }),
    );
  }

  public GetTeamScoreCardMetricsDataRollingAvg(providerId?: string): Observable<NPSRollingAverageDataPoints[]> {
    return combineLatest([
      this.queryService.brandsContext$,
      this.toggleSidebarChanged$$.asObservable(),
      this.sidepanelService.visible$,
      this.sidepanelService.size$$.asObservable(),
    ]).pipe(
      debounceTime(50),
      tap(() => {
        this.rollingAvgChartLoading.set(true);
      }),
      map(([brandsContext, _, __, ___]) => {
        const request: QueryMetricsRequest = this.createQueryMetricsRequestForScoreCard(
          partnerId,
          brandsContext,
          providerId,
        );

        request.measures = [
          new Measure({
            aggregate: new MeasureAggregate({
              measure: 'score_date',
              aggOp: MeasureAggregateOperator.MAX,
            }),
          }),
        ];

        const rollingAvgFilter: Filter = new Filter({
          compositeFilter: {
            op: CompositeFilterOperator.AND,
            filters: [
              {
                fieldFilter: {
                  dimension: 'provider_id',
                  operator: FieldFilterOperator.EQUAL,
                  value: {
                    value: providerId,
                    valueType: PropertyType.PROPERTY_TYPE_STRING,
                  },
                },
              },
              {
                fieldFilter: {
                  dimension: 'row_number',
                  operator: FieldFilterOperator.EQUAL,
                  value: {
                    value: '1',
                    valueType: PropertyType.PROPERTY_TYPE_INT64,
                  },
                },
              },
            ],
          },
        });

        if (request?.filter?.compositeFilter) {
          request.filter.compositeFilter.filters.push(rollingAvgFilter);
        } else {
          request.filter = rollingAvgFilter;
        }

        request.groupBy = new GroupBy({
          dimension: [
            new GroupByDimension({
              dimension: 'avg_score',
            }),
          ],
        });
        return request;
      }),
      switchMap((request) => {
        return this.multiLocationAnalyticsService.queryMetrics(request);
      }),
      map((response) => {
        this.rollingAvgChartLoading.set(false);
        if (response.metricResults[0]?.metrics?.metrics) {
          return response.metricResults[0].metrics.metrics.map((metric) => {
            return {
              netPromoterScoreDate: new Date(metric.measures[0]),
              rollingAverage: metric.dimension,
            };
          });
        } else {
          return [];
        }
      }),
      catchError((error) => {
        this.rollingAvgChartLoading.set(false);
        this.snackbarService.openErrorSnack(error.error.message);
        return [];
      }),
    );
  }

  getTeamNPSOverviewData(duration: 'current' | 'previous' | 'alltime'): Observable<TeamNPSOverviewData[]> {
    return this.queryService.brandsContext$.pipe(
      debounceTime(50),
      map((brandsContext) => this.buildTeamScoreRequest(duration, brandsContext)),
      switchMap((request) => this.multiLocationAnalyticsService.queryMetrics(request)),
      map((response) => {
        const currentMetrics: TeamNPSOverviewData[] = [];
        if (response.metricResults[0]?.metrics?.metrics) {
          response.metricResults[0].metrics.metrics.map((metric) => {
            currentMetrics.push({
              NPSScore: this.npsScoreService.calculateCategoryAndOverallScore({
                promoters: Number(metric.measures[1]),
                passives: Number(metric.measures[2]),
                detractors: Number(metric.measures[3]),
              }),
              NPSVolume: Number(metric.measures[0]),
              ProviderID: metric.dimension,
              Promoters: Number(metric.measures[1]),
              Passives: Number(metric.measures[2]),
              Detractors: Number(metric.measures[3]),
            });
          });
          return currentMetrics;
        } else {
          return [];
        }
      }),
      catchError((error) => {
        this.snackbarService.openErrorSnack(error.error.message);
        return [];
      }),
    );
  }

  getNPSOverviewData(duration: 'current' | 'previous'): Observable<NPSOverviewData[]> {
    return combineLatest([this.queryService.brandsContext$]).pipe(
      debounceTime(50),
      map(([brandsContext]) => {
        const request: QueryMetricsRequest = this.createQueryMetricsRequest(partnerId, brandsContext);
        if (duration === 'previous') {
          const { previousStartDate, previousEndDate } = this.calculatePreviousDates(
            brandsContext.startDate,
            brandsContext.endDate,
          );
          request.dateRange = new SDKDateRange({
            start: previousStartDate,
            end: previousEndDate,
          });
        }
        request.measures = [
          this.createCountMeasure('score', 'nps_volume'),
          this.createScoreMeasure([9, 10], 'promoter_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([7, 8], 'passive_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([6], 'detractor_score', FieldFilterOperator.LESS_THAN_OR_EQUAL),
        ];

        request.groupBy = new GroupBy({
          dimension: [
            new GroupByDimension({
              dimension: 'account_group_id',
            }),
          ],
        });
        return request;
      }),
      switchMap((request) => {
        return this.multiLocationAnalyticsService.queryMetrics(request);
      }),
      map((response) => {
        const currentMetrics: NPSOverviewData[] = [];
        if (response.metricResults[0]?.metrics?.metrics) {
          response.metricResults[0].metrics.metrics.map((metric) => {
            currentMetrics.push({
              NPSScore: this.npsScoreService.calculateCategoryAndOverallScore({
                promoters: Number(metric.measures[1]),
                passives: Number(metric.measures[2]),
                detractors: Number(metric.measures[3]),
              }),
              NPSVolume: Number(metric.measures[0]),
              AccountGroupID: metric.dimension,
              Promoters: Number(metric.measures[1]),
              Passives: Number(metric.measures[2]),
              Detractors: Number(metric.measures[3]),
            });
          });
          return currentMetrics;
        } else {
          return [];
        }
      }),
      catchError((error) => {
        this.snackbarService.openErrorSnack(error.error.message);
        return [];
      }),
    );
  }

  getMetricsDataRollingAvg(accountGroupId?: string): Observable<NPSRollingAverageDataPoints[]> {
    return combineLatest([
      this.queryService.brandsContext$,
      this.toggleSidebarChanged$$.asObservable(),
      this.sidepanelService.visible$,
      this.sidepanelService.size$$.asObservable(),
    ]).pipe(
      debounceTime(50),
      delay(300),
      map(([brandsContext, _, __, ___]) => {
        const request: QueryMetricsRequest = this.createQueryMetricsRequest(partnerId, brandsContext);

        request.measures = [
          new Measure({
            aggregate: new MeasureAggregate({
              measure: 'score_date',
              aggOp: MeasureAggregateOperator.MAX,
            }),
          }),
        ];

        const rollingAvgFilter: Filter = new Filter({
          compositeFilter: {
            op: CompositeFilterOperator.AND,
            filters: [
              ...(accountGroupId
                ? [
                    {
                      fieldFilter: {
                        dimension: 'account_group_id',
                        operator: FieldFilterOperator.EQUAL,
                        value: {
                          value: accountGroupId,
                          valueType: PropertyType.PROPERTY_TYPE_STRING,
                        },
                      },
                    },
                  ]
                : []),
              {
                fieldFilter: {
                  dimension: 'row_number',
                  operator: FieldFilterOperator.EQUAL,
                  value: {
                    value: '1',
                    valueType: PropertyType.PROPERTY_TYPE_INT64,
                  },
                },
              },
            ],
          },
        });

        if (request?.filter?.compositeFilter) {
          request.filter.compositeFilter.filters.push(rollingAvgFilter);
        } else {
          request.filter = rollingAvgFilter;
        }

        request.groupBy = new GroupBy({
          dimension: [
            new GroupByDimension({
              dimension: 'avg_score',
            }),
          ],
        });
        return request;
      }),
      switchMap((request) => {
        return this.multiLocationAnalyticsService.queryMetrics(request);
      }),
      map((response) => {
        if (response.metricResults[0]?.metrics?.metrics) {
          return response.metricResults[0].metrics.metrics.map((metric) => {
            return {
              netPromoterScoreDate: new Date(metric.measures[0]),
              rollingAverage: metric.dimension,
            };
          });
        } else {
          return [];
        }
      }),
      catchError((error) => {
        this.snackbarService.openErrorSnack(error.error.message);
        return [];
      }),
    );
  }

  getMetricsData(accountGroupId?: string): Observable<NPSScoreOverallData> {
    return combineLatest([
      this.queryService.brandsContext$,
      this.toggleSidebarChanged$$.asObservable(),
      this.sidepanelService.visible$,
      this.sidepanelService.size$$.asObservable(),
    ]).pipe(
      debounceTime(50),
      delay(300),
      map(([brandsContext, _, __, ___]) => {
        const { previousStartDate, previousEndDate } = this.calculatePreviousDates(
          brandsContext.startDate,
          brandsContext.endDate,
        );
        // Create the request for the current date range
        const request: QueryMetricsRequest = this.createQueryMetricsRequest(partnerId, brandsContext, accountGroupId);

        request.measures = [
          this.createScoreMeasure([9, 10], 'promoter_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([7, 8], 'passive_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([6], 'detractor_score', FieldFilterOperator.LESS_THAN_OR_EQUAL),
        ];

        // Create a new request for the previous date range
        const previousRequest: QueryMetricsRequest = this.createQueryMetricsRequest(partnerId, brandsContext);

        previousRequest.measures = [
          this.createScoreMeasure([9, 10], 'promoter_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([7, 8], 'passive_score', FieldFilterOperator.EQUAL),
          this.createScoreMeasure([6], 'detractor_score', FieldFilterOperator.LESS_THAN_OR_EQUAL),
        ];
        previousRequest.dateRange = new SDKDateRange({
          start: previousStartDate,
          end: previousEndDate,
        });

        return { request, previousRequest };
      }),
      switchMap(({ request, previousRequest }) => {
        // Fetch data for both the current and previous date ranges
        return combineLatest([
          this.multiLocationAnalyticsService.queryMetrics(request),
          this.multiLocationAnalyticsService.queryMetrics(previousRequest),
        ]);
      }),
      map(([currentResponse, previousResponse]) => {
        const currentMetrics = currentResponse.metricResults?.[0]?.metrics?.metrics?.[0]?.measures || [];
        const previousMetrics = previousResponse.metricResults?.[0]?.metrics?.metrics?.[0]?.measures || [];

        // Get current scores
        const currentPromoterScore = Number(currentMetrics[0]) || 0;
        const currentPassiveScore = Number(currentMetrics[1]) || 0;
        const currentDetractorScore = Number(currentMetrics[2]) || 0;

        // Get previous scores
        const previousPromoterScore = Number(previousMetrics[0]) || 0;
        const previousPassiveScore = Number(previousMetrics[1]) || 0;
        const previousDetractorScore = Number(previousMetrics[2]) || 0;

        // Return both current and previous data
        return {
          current: {
            detractors: currentDetractorScore,
            passives: currentPassiveScore,
            promoters: currentPromoterScore,
          },
          previous: {
            detractors: previousDetractorScore,
            passives: previousPassiveScore,
            promoters: previousPromoterScore,
          },
        };
      }),
      catchError((error) => {
        this.snackbarService.openErrorSnack(error.error.message);
        return of({
          current: {
            detractors: 0,
            passives: 0,
            promoters: 0,
          },
          previous: {
            detractors: 0,
            passives: 0,
            promoters: 0,
          },
        });
      }),
    );
  }

  public calculatePreviousDates(start: Date, end: Date) {
    // Calculate the previous date range (e.g., 30 days before or 1 year before)
    const previousStartDate = new Date(start);
    const previousEndDate = new Date(end);

    // Adjust the previous date range based on selected range (last 30 days, last year, etc.)
    const timeDifference = previousEndDate.getTime() - previousStartDate.getTime();
    previousStartDate.setTime(previousStartDate.getTime() - timeDifference);
    previousEndDate.setTime(previousEndDate.getTime() - timeDifference);
    return { previousStartDate, previousEndDate };
  }

  // Helper function to create score measures
  private createScoreMeasure(scoreRange: number[], alias: string, filterOperator: FieldFilterOperator): Measure {
    return this.createCountMeasure(
      'score',
      alias,
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'score',
          operator: filterOperator,
          value: new TypedValue({
            value: scoreRange,
            valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
          }),
          operatorFunction: FieldFilterOperatorFunction.ANY,
        }),
      }),
    );
  }

  private createCountMeasure(measure: string, alias: string, filter?: Filter): Measure {
    return new Measure({
      aggregate: new MeasureAggregate({
        measure: measure,
        aggOp: MeasureAggregateOperator.COUNT,
        alias: alias,
        filter: filter,
      }),
    });
  }

  updateFiltersChecked(filters: CheckboxFilterField): void {
    const currentFilters = this.npsScoreFilters$$.getValue();
    let updatedFilters;
    if (filters.value) {
      updatedFilters = [...currentFilters, filters.id];
    } else {
      updatedFilters = currentFilters.filter((filter) => filter !== filters.id);
    }
    this.npsScoreFilters$$.next(updatedFilters);
  }

  getPageFiltersToBeShown(): Observable<Filters> {
    const sections = [
      new FilterSection({
        title: 'NPS.FILTERS.SCORES',
        type: 'or',
        fields: [
          new CheckboxFilterField({
            id: 'PROMOTERS',
            name: 'NPS.FILTERS.PROMOTERS',
            value: this.npsScoreFilters$$.value.includes('PROMOTERS') ?? null,
          }),
          new CheckboxFilterField({
            id: 'PASSIVES',
            name: 'NPS.FILTERS.PASSIVES',
            value: this.npsScoreFilters$$.value.includes('PASSIVES') ?? null,
          }),
          new CheckboxFilterField({
            id: 'DETRACTORS',
            name: 'NPS.FILTERS.DETRACTORS',
            value: this.npsScoreFilters$$.value.includes('DETRACTORS') ?? null,
          }),
        ],
      }),
    ];
    const filters = new Filters('Filters', sections);
    return of(filters);
  }

  private createQueryMetricFilters(npsScoreFilters: string[]): Filter {
    // Create individual FieldFilter objects for each NPS score type
    const fieldFilters = npsScoreFilters
      .filter((filter) => ['PROMOTERS', 'PASSIVES', 'DETRACTORS'].includes(filter)) // Filter out non-matching values
      .map((filter) => {
        if (filter === 'PROMOTERS') {
          return new FieldFilter({
            dimension: 'score',
            operator: FieldFilterOperator.EQUAL,
            operatorFunction: FieldFilterOperatorFunction.ANY,
            value: new TypedValue({
              value: [9, 10],
              valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
            }),
          });
        } else if (filter === 'PASSIVES') {
          return new FieldFilter({
            dimension: 'score',
            operator: FieldFilterOperator.EQUAL,
            operatorFunction: FieldFilterOperatorFunction.ANY,
            value: new TypedValue({
              value: [7, 8],
              valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
            }),
          });
        } else if (filter === 'DETRACTORS') {
          return new FieldFilter({
            dimension: 'score',
            operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
            operatorFunction: FieldFilterOperatorFunction.ANY,
            value: new TypedValue({
              value: [6],
              valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
            }),
          });
        }
      });

    return new Filter({
      compositeFilter: {
        filters: fieldFilters.map((fieldFilter) => new Filter({ fieldFilter })),
        op: CompositeFilterOperator.OR,
      },
    });
  }

  private buildTeamScoreRequest(
    duration: 'current' | 'previous' | 'alltime',
    brandsContext: BrandContext,
  ): QueryMetricsRequest {
    let request: QueryMetricsRequest;

    if (duration === 'alltime') {
      request = this.getBaseQuery('multi_location_team_score_details', partnerId, brandsContext.resourceIds);
      request.filter = brandsContext.buildFilter();
    } else {
      request = this.createQueryMetricsRequestForScoreCard(partnerId, brandsContext);
    }

    if (duration === 'previous') {
      const { previousStartDate, previousEndDate } = this.calculatePreviousDates(
        brandsContext.startDate,
        brandsContext.endDate,
      );
      request.dateRange = new SDKDateRange({
        start: previousStartDate,
        end: previousEndDate,
      });
    }

    request.measures = [
      this.createCountMeasure('score', 'nps_volume'),
      this.createScoreMeasure([9, 10], 'promoter_score', FieldFilterOperator.EQUAL),
      this.createScoreMeasure([7, 8], 'passive_score', FieldFilterOperator.EQUAL),
      this.createScoreMeasure([6], 'detractor_score', FieldFilterOperator.LESS_THAN_OR_EQUAL),
    ];

    request.groupBy = new GroupBy({
      dimension: [new GroupByDimension({ dimension: 'provider_id' })],
    });

    return request;
  }
}
