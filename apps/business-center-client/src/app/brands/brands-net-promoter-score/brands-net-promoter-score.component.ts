import { Component, computed, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsComponent } from '@vendasta/business-nav';
import { TranslateModule } from '@ngx-translate/core';
import {
  NPSAddressComponent,
  NPSProviderComponent,
  NPSCardComponent,
  NpsChartOverallScoreComponent,
  NpsRollingAverageComponent,
  NPSRollingAverageDataPoints,
  NPSScoreOverallData,
} from '@vendasta/reviews';
import { LocationsService } from '../../locations';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { NetPromoterScoreService } from './net-promoter-score.service';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { map, startWith } from 'rxjs/operators';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { NetPromoterScoreFilterComponent } from './net-promoter-score-filter/net-promoter-score-filter.component';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { isMobile } from '../../core/mobile';
import { Observable } from 'rxjs';
import { ImageService } from '../../core/image.service';
import { FeatureFlagService } from '@vendasta/businesses';
import { partnerId } from '../../../globals';

@Component({
  selector: 'app-brands-net-promoter-score',
  imports: [
    CommonModule,
    GalaxyPageModule,
    NavTabsComponent,
    TranslateModule,
    NPSCardComponent,
    NPSAddressComponent,
    NPSProviderComponent,
    NpsRollingAverageComponent,
    NpsChartOverallScoreComponent,
    BrandFilterContainerModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatIconModule,
    MatButtonModule,
    TimeRangePickerComponent,
  ],
  templateUrl: './brands-net-promoter-score.component.html',
  styleUrl: './brands-net-promoter-score.component.scss',
})
export default class BrandsNetPromoterScoreComponent implements OnInit, OnDestroy {
  private readonly locationsService = inject(LocationsService);
  readonly sidePanelService = inject(SidepanelService);
  private npsService = inject(NetPromoterScoreService);
  private destroyRef = inject(DestroyRef);
  private snackbarService = inject(SnackbarService);
  private imageService = inject(ImageService);

  enableResendNPSRequest$: Observable<boolean>;
  hasMoreNPSData$ = this.npsService.hasMoreNPSData$;
  loadingNPSFeed$ = this.npsService.loadingNPSFeed$.pipe(startWith(true));
  npsData$ = this.npsService.npsData$;
  isCurrentLocationABrand$ = this.locationsService.isCurrentLocationABrand$;
  npsRollingAvgData$: Observable<NPSRollingAverageDataPoints[]>;
  ChartData$: Observable<NPSScoreOverallData>;
  noNPSReviewFoundImageURL = this.imageService.getImageSrc('nps_empty_state.svg');
  readonly chartData = toSignal(this.npsService.getMetricsData(), { initialValue: null });
  readonly hasNPSData = computed(() => {
    const data = this.chartData();
    return !!data && Object.values(data.current || {}).some((value) => value > 0);
  });

  constructor(private featureFlagService: FeatureFlagService) {
    this.enableResendNPSRequest$ = this.featureFlagService.checkFeatureFlag(partnerId, '', 'nps_resend_request');
  }

  ngOnInit() {
    this.npsService
      .initializeNPSData()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        error: (err) => {
          this.snackbarService.openErrorSnack(err.error.message);
        },
      });

    this.npsRollingAvgData$ = this.npsService
      .getMetricsDataRollingAvg()
      .pipe(
        map((dataPoints) =>
          dataPoints.sort((a, b) => a.netPromoterScoreDate.getTime() - b.netPromoterScoreDate.getTime()),
        ),
      );
    this.ChartData$ = this.npsService.getMetricsData();
    this.sidePanelService.setView(Size.REGULAR, Mode.SIDE, NetPromoterScoreFilterComponent, false);
    if (!isMobile()) {
      this.sidePanelService.open();
    }
  }

  get filterType(): string {
    return this.hasNPSData() ? 'filters' : 'date range';
  }

  loadMore(): void {
    this.npsService.loadMore();
  }

  ngOnDestroy(): void {
    this.sidePanelService.close();
  }

  toggleSidebar(): void {
    this.sidePanelService.toggle();
    this.npsService.toggleSidebar();
  }
}
