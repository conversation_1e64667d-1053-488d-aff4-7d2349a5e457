import { Component, inject, OnInit, AfterViewInit, On<PERSON><PERSON>roy, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { TranslateModule } from '@ngx-translate/core';
import { NavTabsComponent } from '@vendasta/business-nav';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { BrandChartHeaderModule } from '../brand-chart-header/brand-chart-header.module';
import { TableModule } from '../table/table.module';
import { BrandSidebarHeaderModule } from '../brand-sidebar-header/brand-sidebar-header.module';
import { BrandRow, MetricColumn } from '../table/table.service';
import { MultiLocationService } from '../multi-location.service';
import { catchError, combineLatest, filter, map, Observable, of, shareReplay, switchMap } from 'rxjs';
import { startWith, take, tap } from 'rxjs/operators';
import { GetProviderRequest, GetProviderResponse, ProviderApiService, ProviderDetails } from '@vendasta/reputation';
import { NetPromoterScoreService } from '../brands-net-promoter-score/net-promoter-score.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { AccountGroup } from '../../account-group';
import { BrandTeamSidebarHeaderService } from '../brand-team-sidebar-header/brand-team-sidebar-header.service';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { BrandTeamSidebarHeaderComponent } from '../brand-team-sidebar-header/brand-team-sidebar-header.component';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { ImageService } from '../../core/image.service';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';

const NPS_SCORE = 'nps_score';
const NPS_BREAKDOWN = 'nps_breakdown';
const NPS_VOLUME = 'nps_volume';
const NPS_PROMOTERS = 'nps_promoters';
const NPS_PASSIVES = 'nps_passives';
const NPS_DETRACTORS = 'nps_detractors';

@Component({
  selector: 'app-brand-nps-team-overview',
  imports: [
    BrandChartHeaderModule,
    CommonModule,
    TranslateModule,
    TableModule,
    BrandSidebarHeaderModule,
    GalaxyPageModule,
    NavTabsComponent,
    ReactiveFormsModule,
    BrandFilterContainerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule,
    TimeRangePickerComponent,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
  ],
  templateUrl: './brand-nps-team-overview.component.html',
  styleUrls: ['./../brands-common.component.scss', 'brand-nps-team-overview.component.scss'],
})
export class BrandNpsTeamOverviewComponent implements OnInit, AfterViewInit, OnDestroy {
  protected multiLocationService = inject(MultiLocationService);
  private npsService = inject(NetPromoterScoreService);
  private fb = inject(FormBuilder);
  private providerApiService = inject(ProviderApiService);
  private snackbarService = inject(SnackbarService);
  private sidepanelService = inject(SidepanelService);
  private imageService = inject(ImageService);
  private brandTeamSidebarHeaderService = inject(BrandTeamSidebarHeaderService);

  protected readonly loadingNPSFeed = signal(true);

  protected tableData$: Observable<BrandRow[]>;
  private search$: Observable<string>;
  private teamMembers$: Observable<BrandRow[]>;

  private providerIds$: Observable<string[]> = this.npsService.getTeamNPSOverviewData('alltime').pipe(
    map((teamScoreData) => {
      const providerIds = [];
      teamScoreData.forEach((info) => {
        if (!providerIds.includes(info.ProviderID)) {
          providerIds.push(info.ProviderID);
        }
      });
      return providerIds;
    }),
    catchError((error) => {
      this.snackbarService.openErrorSnack(error?.error?.message ?? 'Failed to fetch providers');
      this.loadingNPSFeed.set(false);
      return of([]);
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  private providerDetails$: Observable<ProviderDetails[]> = this.providerIds$.pipe(
    filter((providerIds) => !!providerIds && providerIds.length > 0),
    switchMap((providers) => {
      const request = new GetProviderRequest({
        providerIds: providers,
      });
      return this.providerApiService.getProviderDetailsById(request).pipe(
        catchError((error) => {
          this.snackbarService.openErrorSnack(error.error.message);
          // return an empty array or some default value in case of error
          return of({ providerDetails: [] }); // return empty array in case of error
        }),
      );
    }),
    takeUntilDestroyed(),
    map((response: GetProviderResponse) => {
      return response.providerDetails || []; // empty array if no details
    }),
  );

  protected gradesMeasure = NPS_SCORE;

  protected form: FormGroup;

  protected tableColumns: MetricColumn[] = [
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_SCORE',
      measureKey: NPS_SCORE,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_SCORE',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_BREAKDOWN',
      thermometerMetadata: {
        seriesKey: NPS_BREAKDOWN,
        colors: ['#D33030', '#FFC108', '#2F7E33'],
        tooltips: ['Detractors', 'Passives', 'Promoters'],
      },
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_VOLUME',
      measureKey: NPS_VOLUME,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.NPS_VOLUME',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PROMOTERS',
      measureKey: NPS_PROMOTERS,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PROMOTERS',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PASSIVES',
      measureKey: NPS_PASSIVES,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.PASSIVES',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.DETRACTORS',
      measureKey: NPS_DETRACTORS,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.DETRACTORS',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'mean',
    },
  ];
  noNPSReviewFoundImageURL = this.imageService.getImageSrc('nps_empty_state.svg');

  readonly providerIds = toSignal(this.providerIds$, { initialValue: [] });
  readonly hasProviders$ = this.providerIds$.pipe(
    map((ids) => Array.isArray(ids) && ids.length > 0),
    tap((has) => {
      if (!has) {
        this.loadingNPSFeed.set(false); // stop loading if no providers
      }
    }),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  ngOnInit(): void {
    this.mountForm();
    this.hasProviders$.pipe(take(1)).subscribe();
    this.initializeTeamMembers();
    this.prepareTableData();
  }

  ngAfterViewInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandTeamSidebarHeaderComponent);
  }

  ngOnDestroy(): void {
    this.sidepanelService.clearView();
    this.sidepanelService.close();
  }

  openNPSDetailsDrawer(event: any): void {
    this.brandTeamSidebarHeaderService.setResource(event.accountGroupId, event.providerId);
    this.sidepanelService.open();
  }
  mountForm(): void {
    this.form = this.fb.group({
      comparison: new FormControl<string>(
        'PERFORMANCE.MULTI_LOCATION.NET_PROMOTER_SCORE.TEAM_MEMBER',
        Validators.required,
      ),
      search: new FormControl<string>(''),
    });

    this.search$ = this.form.valueChanges.pipe(
      map((changes) => changes['search']),
      startWith(''),
    );
  }

  private initializeTeamMembers(): void {
    const teamMembersMeasureMap$ = combineLatest([
      this.providerIds$,
      this.npsService.currentByProvider$,
      this.npsService.previousByProvider$,
    ]).pipe(
      map(([providerIds, currentNpsData, previousNpsData]) => {
        const measureMap = {};

        providerIds.map((providerId) => {
          measureMap[providerId] = createEmptyMetricMap();
        });

        // Set data for current NPS data
        currentNpsData.forEach((npsData) => {
          const providerId = npsData.ProviderID;
          if (measureMap[providerId]) {
            measureMap[providerId].nps_score.value = npsData.NPSScore;
            measureMap[providerId].nps_volume.value = npsData.NPSVolume;
            measureMap[providerId].nps_promoters.value = npsData.Promoters;
            measureMap[providerId].nps_passives.value = npsData.Passives;
            measureMap[providerId].nps_detractors.value = npsData.Detractors;
          }
        });

        // Set data for previous NPS data
        if (previousNpsData) {
          previousNpsData.forEach((pNpsData) => {
            const providerId = pNpsData.ProviderID;
            if (measureMap[providerId]) {
              measureMap[providerId][NPS_SCORE].deltaAbs =
                (measureMap[providerId][NPS_SCORE].value || 0) - (pNpsData.NPSScore || 0);
              measureMap[providerId][NPS_VOLUME].deltaAbs =
                (measureMap[providerId][NPS_VOLUME].value || 0) - (pNpsData.NPSVolume || 0);
              measureMap[providerId][NPS_PROMOTERS].deltaAbs =
                (measureMap[providerId][NPS_PROMOTERS].value || 0) - (pNpsData.Promoters || 0);
              measureMap[providerId][NPS_PASSIVES].deltaAbs =
                (measureMap[providerId][NPS_PASSIVES].value || 0) - (pNpsData.Passives || 0);
              measureMap[providerId][NPS_DETRACTORS].deltaAbs =
                (measureMap[providerId][NPS_DETRACTORS].value || 0) - (pNpsData.Detractors || 0);
            }
          });
        }

        return measureMap;
      }),
    );

    // Combine provider details and team member measures into one observable
    this.teamMembers$ = combineLatest([this.providerDetails$, teamMembersMeasureMap$]).pipe(
      map(([providerDetails, measureMap]) => {
        return providerDetails
          .filter((provider) => {
            // Filter out providers that are missing or have invalid details
            return provider && provider.providerId && provider.businessId && measureMap[provider.providerId];
          })
          .map((provider) => {
            const providerId = provider.providerId;
            return {
              accountGroup: new AccountGroup({ accountGroupId: provider.businessId }),
              title: `${provider.firstName ?? ''} ${provider.lastName ?? ''}`.trim(),
              subtitle: '',
              measureMap: measureMap[providerId] || createEmptyMetricMap(),
              providerId: providerId,
            };
          });
      }),
    );
  }
  readonly tableData = signal<BrandRow[]>([]);

  private prepareTableData(): void {
    combineLatest([this.teamMembers$, this.search$])
      .pipe(
        map(([teamMembers, search]) => {
          return teamMembers
            .filter((row) => {
              if (row == null) return true;
              return row.title.toUpperCase().includes(search.toUpperCase());
            })
            .map((row) => {
              const npsClassifiedRating = [
                row.measureMap[NPS_DETRACTORS].value, // Negative
                row.measureMap[NPS_PASSIVES].value, // Neutral
                row.measureMap[NPS_PROMOTERS].value, // Positive
              ];

              const npsClassifiedRatingSum = npsClassifiedRating.reduce((a, b) => a + b, 0);
              const npsNormalizedRating = npsClassifiedRating.map((value) => (value / npsClassifiedRatingSum) * 100);

              row.seriesMap = {
                nps_breakdown: {
                  values: npsClassifiedRating,
                  normalized: npsNormalizedRating,
                },
              };
              return row;
            });
        }),
        tap(() => this.loadingNPSFeed.set(false)),
        catchError((err) => {
          this.snackbarService.openErrorSnack(err?.error?.message ?? 'Table load failed');
          this.loadingNPSFeed.set(false);
          return of([]);
        }),
      )
      .subscribe((result) => {
        this.tableData.set(result);
      });
  }
}

function createEmptyMetricMap() {
  return {
    nps_score: { value: null, deltaAbs: null },
    nps_volume: { value: 0, deltaAbs: null },
    nps_promoters: { value: 0, deltaAbs: null },
    nps_passives: { value: 0, deltaAbs: null },
    nps_detractors: { value: 0, deltaAbs: null },
  };
}
