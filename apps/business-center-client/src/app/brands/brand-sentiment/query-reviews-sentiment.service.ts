import { DestroyRef, Injectable } from '@angular/core';
import {
  AnalyticsApiService,
  CompositeFilter,
  CompositeFilterOperator,
  DateRange,
  FieldFilter,
  FieldFilterOperator,
  FieldFilterOperatorFunction,
  Filter,
  GroupBy,
  GroupByDimension,
  GroupByOperator,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  Order,
  OrderBy,
  OrderByOrderColumn,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  TypedValue,
} from '@vendasta/multi-location-analytics';
import { GetReviewsRequest, Review } from '@vendasta/reputation';
import { HighlightOptions } from '@vendasta/reviews';
import dayjs from 'dayjs';
import { BehaviorSubject, Observable, combineLatest, of } from 'rxjs';
import {
  distinctUntilChanged,
  map,
  shareReplay,
  skipWhile,
  startWith,
  switchMap,
  tap,
  withLatestFrom,
} from 'rxjs/operators';
import { partnerId } from '../../../globals';
import { AccountGroup } from '../../account-group';
import { LocationsService } from '../../locations';
import { BrandContext, QueryService } from '../../metrics/query.service';
import { getFormattingOption } from '../../shared/time-range-shared';
import { QueryReviewsService, ReviewIdentifier } from '../brand-manage-reviews/query-reviews.service';

export enum SentimentLevel {
  SENTIMENT_LEVEL_NEUTRAL = 0,
  SENTIMENT_LEVEL_POSITIVE = 1,
  SENTIMENT_LEVEL_NEGATIVE = 2,
}

export interface ReviewSentimentEntity {
  phrase: string;
  totalCount: number;
  averageScore: number;
  sentimentLevel: SentimentLevel;
  delta?: number;
  scoreDelta?: number;
}

export interface PhraseSentimentByDay {
  phrase: string;
  averageScore: number;
  sentimentLevel: SentimentLevel;
  date: Date;
  numMentions: number;
}

export interface DateRangeFormat {
  numberOfUnits: number;
  unit: dayjs.UnitType;
}

export interface PhraseSentimentOverTime {
  sentimentByDay: PhraseSentimentByDay[];
  numPositive: number;
  numNegative: number;
  numNeutral: number;
  numMentions: number;
  numReviews: number;
  dateRangeFormat: DateRangeFormat;
}

export interface ReviewWordPair {
  phrase: string;
  modifier: string;
  curModifierCount: number;
  averageScore: number;
  sentimentLevel: SentimentLevel;
  percentage?: number;
}

export interface EntityMentionsDelta {
  delta: number;
  absDelta: number;
}

export interface TopSentimentLocations {
  accountGroup: AccountGroup;
  averageScore: number;
}

export enum SentimentSortOption {
  SORT_OPTION_ALL_KEYWORDS = 0,
  SORT_OPTION_MOST_POSITIVE = 1,
  SORT_OPTION_MOST_NEGATIVE = 2,
}

export class ReviewWithHighlightOptions {
  review: Review;
  highlightOptions: HighlightOptions;
}

const NEUTRAL_SENTIMENT_LOWER_BOUND = -0.15;
const NEUTRAL_SENTIMENT_UPPER_BOUND = 0.15;

@Injectable()
export class QueryReviewsSentimentService {
  sentimentContext$ = this.queryService
    .buildBrandContext$(['partnerId', 'path', 'dateRange', 'filters', 'activeSources', 'selectedSourceIds'])
    .pipe(shareReplay(1));

  private readonly loadingSentimentOverTime$$ = new BehaviorSubject<boolean>(false);
  readonly loadingSentimentOverTime$ = this.loadingSentimentOverTime$$.asObservable().pipe(distinctUntilChanged());

  private readonly selectedPhrase$$: BehaviorSubject<string> = new BehaviorSubject(null);
  private readonly selectedPhrase$ = this.selectedPhrase$$.asObservable().pipe(
    skipWhile((selectedPhrase) => selectedPhrase === null),
    tap(() => this.loadingSentimentOverTime$$.next(true)),
    shareReplay(1),
  );

  private readonly sentimentDetails$ = combineLatest([this.selectedPhrase$, this.sentimentContext$]).pipe(
    switchMap(([selectedPhrase, brandsContext]) =>
      this.analyticsService.queryMetrics(this.buildPhraseSentimentDetailsRequest(brandsContext, selectedPhrase)),
    ),
  );

  private readonly sentimentByDay$ = combineLatest([this.selectedPhrase$, this.sentimentContext$]).pipe(
    switchMap(([selectedPhrase, brandsContext]) =>
      this.analyticsService.queryMetrics(this.buildPhraseSentimentByDayRequest(brandsContext, selectedPhrase)),
    ),
  );

  readonly phraseSentimentOverTime$ = combineLatest([this.sentimentDetails$, this.sentimentByDay$]).pipe(
    withLatestFrom(this.sentimentContext$),
    map(([[sentimentDetails, sentimentByDay], brandsContext]) => {
      const phraseSentimentByDay = this.extractPhraseSentimentByDayData(sentimentByDay);
      const [startDate, endDate] = brandsContext.dateRange(true);
      return this.buildPhraseSentimentOverTime(phraseSentimentByDay, sentimentDetails, startDate, endDate);
    }),
    tap(() => this.loadingSentimentOverTime$$.next(false)),
    shareReplay(1),
  );

  private readonly loadingPositiveWordPairs$$ = new BehaviorSubject(false);
  readonly loadingPositiveWordPairs$ = this.loadingPositiveWordPairs$$.asObservable();

  private readonly loadingNegativeWordPairs$$ = new BehaviorSubject(false);
  readonly loadingNegativeWordPairs$ = this.loadingNegativeWordPairs$$.asObservable();

  readonly positiveWordPairs$ = this.wordPairsQuery(true).pipe(shareReplay(1));
  readonly negativeWordPairs$ = this.wordPairsQuery(false).pipe(shareReplay(1));

  private readonly loadingTopPositiveSentimentLocations$$ = new BehaviorSubject<boolean>(true);
  readonly topPositiveLocationLoading$ = this.loadingTopPositiveSentimentLocations$$.asObservable();
  private readonly loadingTopNegativeSentimentLocations$$ = new BehaviorSubject<boolean>(true);
  readonly topNegativeLocationLoading$ = this.loadingTopNegativeSentimentLocations$$.asObservable();

  readonly topPositiveSentimentLocations$ = this.queryTopSentimentLocations(true);
  readonly topNegativeSentimentLocations$ = this.queryTopSentimentLocations(false);

  private readonly selectedModifier$$: BehaviorSubject<ReviewWordPair> = new BehaviorSubject(null);
  readonly selectedModifier$ = this.selectedModifier$$.asObservable();

  private readonly loadingReviews$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  readonly loadingReviews$ = this.loadingReviews$$.asObservable();

  private readonly reviewIdentifiers$ = combineLatest([
    this.selectedPhrase$,
    this.selectedModifier$,
    this.sentimentContext$,
  ]).pipe(
    skipWhile(([selectedPhrase, selectedModifier]) => selectedPhrase === null && selectedModifier === null),
    tap(() => this.loadingReviews$$.next(true)),
    switchMap(([selectedPhrase, selectedModifier, brandContext]) => {
      const request = this.buildReviewsByModifierRequest(
        brandContext,
        selectedPhrase,
        selectedModifier ? selectedModifier.modifier : undefined,
        selectedModifier ? selectedModifier.sentimentLevel === SentimentLevel.SENTIMENT_LEVEL_POSITIVE : undefined,
      );
      return this.analyticsService.queryMetrics(request);
    }),
    map((rows) => {
      if (rows.metricResults[0].metrics.metrics) {
        return rows.metricResults[0].metrics.metrics.map((value: MetricResult) => {
          return new ReviewIdentifier(
            value.results.metrics[0].results.metrics[0].measures[0],
            value.results.metrics[0].results.metrics[0].dimension,
          );
        });
      } else {
        return [];
      }
    }),
  );

  private readonly beginOffsetsToHighlight$ = this.reviewIdentifiers$.pipe(
    withLatestFrom(this.sentimentContext$, this.selectedPhrase$, this.selectedModifier$),
    switchMap(([reviewIds, brandContext, selectedPhrase, selectedModifier]) => {
      const beginOffsetRequest = this.buildReviewBeginOffsetsRequest(
        reviewIds.map((reviewIdentifier) => reviewIdentifier.reviewId),
        selectedPhrase,
        brandContext,
        true,
        selectedModifier ? selectedModifier.modifier : undefined,
        selectedModifier ? selectedModifier.sentimentLevel === SentimentLevel.SENTIMENT_LEVEL_POSITIVE : undefined,
      );

      return this.analyticsService.queryMetrics(beginOffsetRequest);
    }),
    map((rows) => {
      if (rows.metricResults[0].metrics.metrics) {
        return rows.metricResults[0].metrics.metrics.map((value: MetricResult) => {
          return {
            reviewId: value.measures[0],
            beginOffset: parseInt(value.measures[1], 10),
          };
        });
      } else {
        return [];
      }
    }),
  );

  private readonly allReviews$ = this.reviewIdentifiers$.pipe(
    switchMap((reviewIds) =>
      this.qrs.reviewService.getReviews(
        new GetReviewsRequest({
          reviewIds: reviewIds,
          includeComments: false,
        }),
      ),
    ),
    withLatestFrom(this.selectedPhrase$, this.selectedModifier$),
    map(([reviewResponse, selectedPhrase, selectedModifier]) => {
      if (reviewResponse && reviewResponse.reviews) {
        return reviewResponse.reviews.map((review) => {
          return {
            review: review,
            highlightOptions: {
              phrase: selectedModifier ? selectedModifier.modifier : selectedPhrase,
              beginOffsets: [],
            },
          };
        });
      }
      return [];
    }),
  );

  readonly reviews$ = combineLatest([this.allReviews$, this.beginOffsetsToHighlight$]).pipe(
    map(([reviewsWithHighlightOptions, beginOffsetData]) => {
      if (reviewsWithHighlightOptions.length <= 0) {
        return [];
      }
      beginOffsetData.forEach((beginOffset) => {
        const index = reviewsWithHighlightOptions.findIndex(
          (review) => review.review.reviewId.reviewId === beginOffset.reviewId,
        );
        if (index === -1) {
          return;
        }
        reviewsWithHighlightOptions[index].highlightOptions.beginOffsets.push(beginOffset.beginOffset);
      });
      return reviewsWithHighlightOptions as ReviewWithHighlightOptions[];
    }),
    tap(() => this.loadingReviews$$.next(false)),
  );

  private readonly loading$$ = new BehaviorSubject(true);
  readonly loadingEntities$ = this.loading$$.asObservable();

  private readonly reviewSentimentEntities$ = this.sentimentContext$.pipe(
    tap(() => this.loading$$.next(true)),
    switchMap((brandContext) => {
      const request = this.buildSentimentRequest(brandContext, true);
      return this.analyticsService.queryMetrics(request);
    }),
    map((rows) => this.rowsToReviewSentimentEntity(rows)),
  );

  private readonly allTimeReviewSentimentEntities$ = this.sentimentContext$.pipe(
    tap(() => this.loading$$.next(true)),
    switchMap((brandContext) => {
      const request = this.buildSentimentRequest(brandContext, true, true);
      return this.analyticsService.queryMetrics(request);
    }),
    map((rows) => this.rowsToReviewSentimentEntity(rows)),
  );

  private readonly allEntities$ = this.reviewSentimentEntities$.pipe(
    withLatestFrom(this.sentimentContext$),
    switchMap(([sentiments, brandContext]) => {
      const phrases = sentiments.map((sentiment) => sentiment.phrase);
      return this.getPreviousPeriodSentiments(brandContext, phrases).pipe(
        map((previousSentiments) => {
          const sentimentsWithDelta = sentiments.map((sentiment) => {
            const previousSentiment = previousSentiments.find((prev) => prev.phrase === sentiment.phrase);
            const previousScore = previousSentiment ? previousSentiment.averageScore : 0;
            const previousCount = previousSentiment ? previousSentiment.totalCount : 0;
            sentiment.scoreDelta = sentiment.averageScore - previousScore;
            sentiment.delta = sentiment.totalCount - previousCount;
            return sentiment;
          });
          return sentimentsWithDelta;
        }),
      );
    }),
  );

  readonly selectedPhraseDeltaData$ = combineLatest([this.allEntities$, this.selectedPhrase$]).pipe(
    skipWhile(([_, selectedPhrase]) => selectedPhrase === null),
    map(([entities, selectedPhrase]) => {
      const selectedEntity = entities.find((entity) => entity.phrase === selectedPhrase);
      return {
        delta: selectedEntity.delta,
        absDelta: Math.abs(selectedEntity.delta),
      };
    }),
  );

  private readonly entitySearchTerm$$ = new BehaviorSubject<string>('');
  private readonly sentimentSortOrder$$ = new BehaviorSubject<SentimentSortOption>(
    SentimentSortOption.SORT_OPTION_ALL_KEYWORDS,
  );

  readonly allTimeEntities$ = combineLatest([
    this.allTimeReviewSentimentEntities$,
    this.entitySearchTerm$$,
    this.sentimentSortOrder$$,
  ]).pipe(
    map(([entities, entitySearchTerm, sortOrder]) => {
      if (entitySearchTerm !== '') {
        entities = entities.filter((entity) => entity.phrase.startsWith(entitySearchTerm));
      }
      const sortFn = this.getSortFunction(sortOrder);
      return entities.sort(sortFn);
    }),
    tap(() => this.loading$$.next(false)),
    startWith([] as ReviewSentimentEntity[]),
  );

  readonly entities$ = combineLatest([this.allEntities$, this.entitySearchTerm$$, this.sentimentSortOrder$$]).pipe(
    map(([entities, entitySearchTerm, sortOrder]) => {
      if (entitySearchTerm !== '') {
        entities = entities.filter((entity) => entity.phrase.startsWith(entitySearchTerm));
      }
      const sortFn = this.getSortFunction(sortOrder);
      return entities.sort(sortFn);
    }),
    tap(() => this.loading$$.next(false)),
    startWith([] as ReviewSentimentEntity[]),
  );

  private readonly wordPairLimit = 30;

  constructor(
    public analyticsService: AnalyticsApiService,
    public queryService: QueryService,
    private qrs: QueryReviewsService,
    private locationsService: LocationsService,
    private readonly destroyRef: DestroyRef,
  ) {}

  set selectedEntity(entity: ReviewSentimentEntity) {
    this.selectedPhrase$$.next(entity.phrase);
    this.selectedModifier$$.next(null);
  }

  set selectedModifier(modifier: ReviewWordPair) {
    this.selectedModifier$$.next(modifier);
  }

  private sentimentLevelFromAverageScore(score: number): SentimentLevel {
    if (score > NEUTRAL_SENTIMENT_UPPER_BOUND) {
      return SentimentLevel.SENTIMENT_LEVEL_POSITIVE;
    } else if (score < NEUTRAL_SENTIMENT_LOWER_BOUND) {
      return SentimentLevel.SENTIMENT_LEVEL_NEGATIVE;
    } else {
      return SentimentLevel.SENTIMENT_LEVEL_NEUTRAL;
    }
  }

  updateEntitySearch(searchTerm: string): void {
    this.entitySearchTerm$$.next(searchTerm);
  }

  updateSentimentRequestSort(opt: SentimentSortOption): void {
    this.sentimentSortOrder$$.next(opt);
  }

  private getSortFunction(opt: SentimentSortOption): (a: ReviewSentimentEntity, b: ReviewSentimentEntity) => number {
    const sortByTotalCount = (a: ReviewSentimentEntity, b: ReviewSentimentEntity) => {
      if (a.totalCount > b.totalCount) {
        return -1;
      } else if (a.totalCount < b.totalCount) {
        return 1;
      }
      return a.phrase < b.phrase ? -1 : 1;
    };

    switch (opt) {
      case SentimentSortOption.SORT_OPTION_ALL_KEYWORDS:
        return sortByTotalCount;
      case SentimentSortOption.SORT_OPTION_MOST_POSITIVE:
        return (a, b) => {
          if (a.averageScore > b.averageScore) {
            return -1;
          } else if (a.averageScore < b.averageScore) {
            return 1;
          }
          return sortByTotalCount(a, b);
        };
      case SentimentSortOption.SORT_OPTION_MOST_NEGATIVE:
        return (a, b) => {
          if (a.averageScore > b.averageScore) {
            return 1;
          } else if (a.averageScore < b.averageScore) {
            return -1;
          }
          return sortByTotalCount(a, b);
        };
    }
  }

  buildSentimentRequest(brandsContext: BrandContext, curPeriod: boolean, isAllTime?: boolean): QueryMetricsRequest {
    const req = new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
            alias: 'average_score',
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'review_count',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(phrase)',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([brandsContext.buildActiveSourcesFilter()]),
      orderBy: {
        orderBy: [
          {
            column: 'review_count',
            order: Order.ORDER_DESC,
          },
        ],
      },
      limit: 1000,
    });

    if (!isAllTime) {
      const [startDate, endDate] = brandsContext.dateRange(curPeriod);
      req.dateRange = new DateRange({ start: startDate, end: endDate });
    } else {
      const start = new Date();
      start.setFullYear(start.getFullYear() - 1);
      req.dateRange = new DateRange({
        start: start,
        end: new Date(),
      });
    }

    return req;
  }

  buildPreviousSentimentRequest(brandsContext: BrandContext, curPeriod: boolean, filter: Filter): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
            alias: 'average_score',
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'review_count',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(phrase)',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([filter, brandsContext.buildActiveSourcesFilter()]),
    });
  }

  private getPreviousPeriodSentiments(
    brandsContext: BrandContext,
    sentimentPhrases: string[],
  ): Observable<ReviewSentimentEntity[]> {
    if (sentimentPhrases.length === 0) {
      return of([]);
    }

    const phraseFilter = this.buildSentimentPhraseFilter(sentimentPhrases);
    const request = this.buildPreviousSentimentRequest(brandsContext, false, phraseFilter);
    return this.analyticsService.queryMetrics(request).pipe(
      map((rows) => {
        if (rows && rows.metricResults && rows.metricResults[0].metrics != null) {
          if (!rows.metricResults[0].metrics.metrics) {
            return [];
          }
          return rows.metricResults[0].metrics.metrics.map((metric) => ({
            phrase: metric.dimension,
            averageScore: metric.measures[0],
            totalCount: parseInt(metric.measures[1], 10),
            sentimentLevel: this.sentimentLevelFromAverageScore(metric.measures[0]),
          }));
        } else {
          return [];
        }
      }),
    );
  }

  private wordPairsQuery(positive: boolean): Observable<ReviewWordPair[]> {
    return combineLatest([this.selectedPhrase$, this.sentimentContext$]).pipe(
      tap(() => this.setWordPairLoadingState(positive, true)),
      switchMap(([selectedPhrase, brandContext]) => {
        const request = this.buildWordPairRequest(brandContext, true, selectedPhrase, positive);
        return this.analyticsService.queryMetrics(request).pipe(
          map((rows) => {
            const metrics = rows?.metricResults?.[0]?.metrics?.metrics;
            if (!metrics) {
              return [];
            }

            return metrics.map((metric) => ({
              phrase: selectedPhrase,
              modifier: metric.dimension,
              curModifierCount: parseInt(metric.measures[1], 10),
              averageScore: metric.measures[0],
              sentimentLevel: positive
                ? SentimentLevel.SENTIMENT_LEVEL_POSITIVE
                : SentimentLevel.SENTIMENT_LEVEL_NEGATIVE,
            }));
          }),
        );
      }),
      tap(() => this.setWordPairLoadingState(positive, false)),
    );
  }

  private setWordPairLoadingState(positive: boolean, loading: boolean): void {
    positive ? this.loadingPositiveWordPairs$$.next(loading) : this.loadingNegativeWordPairs$$.next(loading);
  }

  private buildSentimentPhraseFilter(phrases: string[]): Filter {
    return new Filter({
      fieldFilter: {
        dimension: 'LOWER(phrase)',
        operator: FieldFilterOperator.EQUAL,
        operatorFunction: FieldFilterOperatorFunction.ANY,
        value: {
          value: phrases,
          valueType: PropertyType.PROPERTY_TYPE_STRING,
        },
      },
    });
  }

  private buildWordPairFilter(positive: boolean): Filter {
    return new Filter({
      fieldFilter: {
        dimension: 'score',
        operator: positive ? FieldFilterOperator.GREATER_THAN_OR_EQUAL : FieldFilterOperator.LESS_THAN,
        value: {
          value: 0,
          valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
        },
      },
    });
  }

  private buildPhraseSentimentByDayRequest(brandsContext: BrandContext, lowerPhrase: string): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(true);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
            alias: 'sentiment',
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_mentions',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(phrase)',
          }),
          new GroupByDimension({
            dimension: 'published::date',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter(),
        this.buildPhraseOverTimeFilter(lowerPhrase),
      ]),
    });
  }

  private buildPhraseSentimentDetailsRequest(brandsContext: BrandContext, lowerPhrase: string): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(true);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_positive',
            filter: new Filter({
              fieldFilter: {
                dimension: 'score',
                value: new TypedValue({
                  value: NEUTRAL_SENTIMENT_UPPER_BOUND,
                  valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
                }),
                operator: FieldFilterOperator.GREATER_THAN,
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_negative',
            filter: new Filter({
              fieldFilter: {
                dimension: 'score',
                value: new TypedValue({
                  value: NEUTRAL_SENTIMENT_LOWER_BOUND,
                  valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
                }),
                operator: FieldFilterOperator.LESS_THAN,
              },
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_neutral',
            filter: new Filter({
              compositeFilter: new CompositeFilter({
                op: CompositeFilterOperator.AND,
                filters: [
                  new Filter({
                    fieldFilter: {
                      dimension: 'score',
                      value: new TypedValue({
                        value: NEUTRAL_SENTIMENT_LOWER_BOUND,
                        valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
                      }),
                      operator: FieldFilterOperator.GREATER_THAN_OR_EQUAL,
                    },
                  }),
                  new Filter({
                    fieldFilter: {
                      dimension: 'score',
                      value: new TypedValue({
                        value: NEUTRAL_SENTIMENT_UPPER_BOUND,
                        valueType: PropertyType.PROPERTY_TYPE_DOUBLE,
                      }),
                      operator: FieldFilterOperator.LESS_THAN_OR_EQUAL,
                    },
                  }),
                ],
              }),
            }),
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_mentions',
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'unique_review_id',
            aggOp: MeasureAggregateOperator.COUNT_DISTINCT,
            alias: 'num_reviews',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(phrase)',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter(),
        this.buildPhraseOverTimeFilter(lowerPhrase),
      ]),
    });
  }

  private buildReviewBeginOffsetsRequest(
    reviewIds: string[],
    phrase: string,
    brandsContext: BrandContext,
    curPeriod: boolean,
    modifier?: string,
    positive?: boolean,
  ): QueryMetricsRequest {
    let metricName = 'review_sentiment_by_phrase_v2';

    let filters = [
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'LOWER(phrase)',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: phrase,
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'review_id',
          operator: FieldFilterOperator.EQUAL,
          operatorFunction: FieldFilterOperatorFunction.ANY,
          value: new TypedValue({
            value: reviewIds,
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    ];

    if (modifier && positive !== undefined) {
      metricName = 'review_word_pair';
      filters = filters.concat(
        this.buildWordPairFilter(positive),
        new Filter({
          fieldFilter: new FieldFilter({
            dimension: 'LOWER(modifier)',
            operator: FieldFilterOperator.EQUAL,
            value: new TypedValue({
              value: modifier,
              valueType: PropertyType.PROPERTY_TYPE_STRING,
            }),
          }),
        }),
      );
    }
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: metricName,
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          measure: 'review_id',
        }),
        new Measure({
          measure: 'begin_offset',
        }),
      ],
      filter: brandsContext.buildFilter(filters),
    });
  }

  private buildReviewsByModifierRequest(
    brandsContext: BrandContext,
    phrase: string,
    modifier?: string,
    positive?: boolean,
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(true);
    let metricName = 'review_sentiment_by_phrase_v2';

    let filters = [
      brandsContext.buildActiveSourcesFilter(),
      new Filter({
        fieldFilter: new FieldFilter({
          dimension: 'LOWER(phrase)',
          operator: FieldFilterOperator.EQUAL,
          value: new TypedValue({
            value: phrase,
            valueType: PropertyType.PROPERTY_TYPE_STRING,
          }),
        }),
      }),
    ];

    if (modifier && positive !== undefined) {
      metricName = 'review_word_pair';
      filters = filters.concat(
        this.buildWordPairFilter(positive),
        new Filter({
          fieldFilter: new FieldFilter({
            dimension: 'LOWER(modifier)',
            operator: FieldFilterOperator.EQUAL,
            value: new TypedValue({
              value: modifier,
              valueType: PropertyType.PROPERTY_TYPE_STRING,
            }),
          }),
        }),
      );
    }

    return new QueryMetricsRequest({
      partnerId,
      metricName: metricName,
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: {
            measure: 'review_id',
            aggOp: MeasureAggregateOperator.MAX,
            alias: 'review_id',
          },
        }),
      ],
      groupBy: {
        dimension: [
          {
            dimension: 'unique_review_id',
          },
          {
            limitDimension: {
              dimension: 'published',
              order: Order.ORDER_DESC,
              limit: 1,
            },
          },
          {
            limitDimension: {
              dimension: 'account_group_id',
              limit: 1,
            },
          },
        ],
      },
      orderBy: new OrderBy({
        orderBy: [
          new OrderByOrderColumn({
            column: 'published',
            order: Order.ORDER_DESC,
          }),
        ],
      }),
      filter: brandsContext.buildFilter(filters),
      limit: 15,
    });
  }

  private buildPreviousPhraseMentionsRequest(brandsContext: BrandContext, lowerPhrase: string): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.previousPeriod();
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'num_mentions',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(phrase)',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter(),
        this.buildPhraseOverTimeFilter(lowerPhrase),
      ]),
    });
  }

  private buildPhraseOverTimeFilter(lowerPhrase: string): Filter {
    return new Filter({
      fieldFilter: new FieldFilter({
        dimension: 'LOWER(phrase)',
        value: new TypedValue({
          value: lowerPhrase,
          valueType: PropertyType.PROPERTY_TYPE_STRING,
        }),
        operator: FieldFilterOperator.EQUAL,
      }),
    });
  }

  private extractPhraseSentimentByDayData(rows: QueryMetricsResponse): PhraseSentimentByDay[] {
    if (rows && rows.metricResults && rows.metricResults[0].metrics != null && rows.metricResults[0].metrics.metrics) {
      return rows.metricResults[0].metrics.metrics.map((metric) => ({
        phrase: metric.dimension,
        averageScore: metric.results.metrics[0].measures[0],
        sentimentLevel:
          metric.results.metrics[0].measures[0] > 0
            ? SentimentLevel.SENTIMENT_LEVEL_POSITIVE
            : SentimentLevel.SENTIMENT_LEVEL_NEGATIVE,
        date: new Date(metric.results.metrics[0].dimension),
        numMentions: parseInt(metric.results.metrics[0].measures[1], 10),
      }));
    }
    return [];
  }

  private buildPhraseSentimentOverTime(
    entitySentimentByDay: PhraseSentimentByDay[],
    entitySentimentDetails: QueryMetricsResponse,
    startDate: Date,
    endDate: Date,
  ): PhraseSentimentOverTime {
    const timeUnits = getFormattingOption(startDate, endDate) as dayjs.UnitType;
    const phraseSentimentOverTime = {
      sentimentByDay: entitySentimentByDay,
      numPositive: 0,
      numNegative: 0,
      numNeutral: 0,
      numMentions: 0,
      numReviews: 0,
      dateRangeFormat: {
        numberOfUnits: dayjs(endDate).diff(dayjs(startDate), timeUnits),
        unit: timeUnits,
      },
    };

    if (
      entitySentimentDetails &&
      entitySentimentDetails.metricResults &&
      entitySentimentDetails.metricResults[0].metrics != null &&
      entitySentimentDetails.metricResults[0].metrics.metrics
    ) {
      phraseSentimentOverTime.numPositive = entitySentimentDetails.metricResults[0].metrics.metrics[0].measures[0];
      phraseSentimentOverTime.numNegative = entitySentimentDetails.metricResults[0].metrics.metrics[0].measures[1];
      phraseSentimentOverTime.numNeutral = entitySentimentDetails.metricResults[0].metrics.metrics[0].measures[2];
      phraseSentimentOverTime.numMentions = entitySentimentDetails.metricResults[0].metrics.metrics[0].measures[3];
      phraseSentimentOverTime.numReviews = entitySentimentDetails.metricResults[0].metrics.metrics[0].measures[4];
    }

    return phraseSentimentOverTime;
  }

  buildWordPairRequest(
    brandsContext: BrandContext,
    curPeriod: boolean,
    sentimentPhrase: string,
    positive: boolean,
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(curPeriod);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_word_pair',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
            alias: 'average_score',
          }),
        }),
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'DISTINCT(unique_review_id, begin_offset)',
            aggOp: MeasureAggregateOperator.COUNT,
            alias: 'word_count',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          new GroupByDimension({
            dimension: 'LOWER(modifier)',
          }),
        ],
        groupByOperator: GroupByOperator.OPERATOR_GROUP_BY,
      }),
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter(),
        this.buildSentimentPhraseFilter([sentimentPhrase]),
        this.buildWordPairFilter(positive),
      ]),
      orderBy: {
        orderBy: [
          {
            column: 'word_count',
            order: 1,
          },
          {
            column: 'LOWER(modifier)',
            order: 0,
          },
        ],
      },
      limit: this.wordPairLimit,
    });
  }

  buildTopLocationsSentimentQuery(
    brandsContext: BrandContext,
    selectedPhrase: string,
    positive: boolean,
  ): QueryMetricsRequest {
    const [startDate, endDate] = brandsContext.dateRange(true);
    return new QueryMetricsRequest({
      partnerId,
      metricName: 'review_sentiment_by_phrase_v2',
      resourceIds: brandsContext.resourceIds,
      dateRange: new DateRange({ start: startDate, end: endDate }),
      measures: [
        new Measure({
          aggregate: new MeasureAggregate({
            measure: 'score',
            aggOp: MeasureAggregateOperator.AVG,
            alias: 'average_score',
          }),
        }),
      ],
      groupBy: new GroupBy({
        dimension: [
          {
            dimension: 'account_group_id',
          },
        ],
      }),
      limit: 5,
      filter: brandsContext.buildFilter([
        brandsContext.buildActiveSourcesFilter(),
        this.buildSentimentPhraseFilter([selectedPhrase]),
      ]),
      orderBy: {
        orderBy: [
          {
            column: 'average_score',
            order: positive ? Order.ORDER_DESC : Order.ORDER_ASC,
          },
        ],
      },
    });
  }

  private queryTopSentimentLocations(positive: boolean): Observable<TopSentimentLocations[]> {
    return combineLatest([this.selectedPhrase$, this.sentimentContext$]).pipe(
      tap(() => this.setTopSentimentLoading(positive, true)),
      switchMap(([selectedPhrase, brandsContext]) =>
        this.analyticsService.queryMetrics(
          this.buildTopLocationsSentimentQuery(brandsContext, selectedPhrase, positive),
        ),
      ),
      map((rows) => {
        if (
          rows &&
          rows.metricResults &&
          rows.metricResults[0].metrics != null &&
          rows.metricResults[0].metrics.metrics
        ) {
          return rows.metricResults[0].metrics.metrics.map((row) => {
            return {
              accountGroupId: row.dimension,
              averageSentiment: row.measures[0],
            };
          });
        }
        return [];
      }),
      withLatestFrom(this.locationsService.currentAccountGroups$),
      map(([averageScores, accountGroups]) => {
        return averageScores.map((score) => {
          return {
            accountGroup: accountGroups[score.accountGroupId],
            averageScore: score.averageSentiment,
          };
        });
      }),
      tap(() => this.setTopSentimentLoading(positive, false)),
    );
  }

  private setTopSentimentLoading(positive: boolean, loading: boolean): void {
    positive
      ? this.loadingTopPositiveSentimentLocations$$.next(loading)
      : this.loadingTopNegativeSentimentLocations$$.next(loading);
  }

  private rowsToReviewSentimentEntity(rows: QueryMetricsResponse): ReviewSentimentEntity[] {
    if (
      rows &&
      rows.metricResults &&
      rows.metricResults[0].metrics != null &&
      rows.metricResults[0].metrics.metrics != null
    ) {
      return rows.metricResults[0].metrics.metrics.map(
        (metric) =>
          ({
            phrase: metric.dimension,
            averageScore: metric.measures[0],
            totalCount: parseInt(metric.measures[1], 10),
            sentimentLevel: this.sentimentLevelFromAverageScore(metric.measures[0]),
            delta: null,
            scoreDelta: null,
          }) as ReviewSentimentEntity,
      );
    } else {
      return [] as ReviewSentimentEntity[];
    }
  }
}
