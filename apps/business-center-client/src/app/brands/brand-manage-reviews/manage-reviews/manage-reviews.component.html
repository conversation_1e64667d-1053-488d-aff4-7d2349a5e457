<glxy-page [disableShadow]="true">
  <glxy-page-toolbar>
    <glxy-page-title>
      {{ title }}
    </glxy-page-title>
    <glxy-page-title-actions>
      <app-launch-app-button></app-launch-app-button>
    </glxy-page-title-actions>
    <glxy-page-actions>
      @for (button of dataActionButtons$ | async; track button) {
        <app-nav-action-button [config]="button" (click)="logPosthogEvent(button)"></app-nav-action-button>
      }
      @if (isCurrentLocationABrand$ | async) {
        <bc-brand-filter-container></bc-brand-filter-container>
      }
      <button class="nav-btn desktop" mat-stroked-button (click)="sidepanelService.toggle()">
        <mat-icon class="icon">tune</mat-icon>
        <span class="message">
          {{ 'PERFORMANCE.MULTI_LOCATION.FILTERS.PAGE_FILTERS' | translate }}
        </span>
      </button>
      <button class="nav-btn mobile" mat-icon-button (click)="sidepanelService.toggle()">
        <mat-icon class="icon">tune</mat-icon>
      </button>
      @if (rmPremiumActive$ | async) {
        <a mat-flat-button color="primary" class="nav-btn" (click)="openSelectRecipientsModal()">
          <span class="desktop">{{ 'COMMON.ACTION_LABELS.REQUEST_REVIEWS' | translate }}</span>
          <span class="mobile">{{ 'COMMON.ACTION_LABELS.REQUEST' | translate }}</span>
        </a>
      }
    </glxy-page-actions>
  </glxy-page-toolbar>
  <glxy-page-below-toolbar>
    <bc-nav-tabs></bc-nav-tabs>
  </glxy-page-below-toolbar>
  <glxy-page-wrapper widthPreset="wide">
    <app-review-metrics
      class="review-metrics"
      [currentOverall]="currentReviewRatingInput$ | async"
      [currentAverageResponseTime]="
        (reviewService.allTimeCurrentAverageResponseTime$ | async)?.averageResponseTime ?? 0
      "
      [trendingKeywords]="trendingKeywords$ | async"
    ></app-review-metrics>
    @if (reviewsAndSettings$ | async; as data) {
      <bc-shared-review-feed
        [reviewPublishSettingsMap]="data.publishSettings"
        [reviews]="data.reviews"
        [gmbConnections]="accountsWithGmb$ | async"
        [facebookConnections]="accountsWithFacebook$ | async"
      ></bc-shared-review-feed>
    }
    <div class="loading-button">
      @if (loading$ | async) {
        <mat-spinner diameter="40" strokeWidth="4"></mat-spinner>
      }
      @if ((loading$ | async) === false) {
        @if (hasMore$ | async) {
          <button mat-raised-button color="primary" (click)="nextPage()">
            {{ 'COMMON.ACTION_LABELS.LOAD_MORE' | translate }}
          </button>
        }
      }
    </div>
    <div class="mobile-filter">
      <button mat-mini-fab (click)="sidepanelService.open()">
        <mat-icon>filter_list</mat-icon>
      </button>
    </div>

    <div class="disclaimer">
      {{ 'COMMON.DISCLAIMER' | translate }}
    </div>
  </glxy-page-wrapper>
</glxy-page>
