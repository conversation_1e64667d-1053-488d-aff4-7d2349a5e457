import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn, createUrlTreeFromSnapshot } from '@angular/router';
import { NavigationOption } from '@vendasta/uikit';
import { combineLatest, firstValueFrom, map } from 'rxjs';
import { partnerId } from '../../../../globals';
import { FeatureFlagService } from '../../../core/feature-flag.service';
import { ContentConfig, NavActionButton } from '../../../navigation/navigation.interface';
import { BrandsService } from '../../brands.service';

export const actionButtonMlResolver: ResolveFn<ContentConfig> = async (route: ActivatedRouteSnapshot) => {
  const brands = inject(BrandsService);
  const ff = inject(FeatureFlagService);

  const actionButtons: NavActionButton[] = [
    {
      buttonType: 'mat-icon-button',
      icon: 'settings',
      tooltipTranslationKey: 'BRANDS.REVIEWS.RESPONSE_TEMPLATES.MANAGE_TEMPLATES',
      link: createUrlTreeFromSnapshot(route, ['response-templates']).toString(),
      navigationOption: NavigationOption.ROUTERLINK,
    },
  ];

  const multiLocationFf$ = ff.checkFeatureFlagsMulti(partnerId, '', ['multilocation_customer_voice']);

  const multiLocationCustomerVoiceEnabled$ = combineLatest([multiLocationFf$, brands.currentBrand$]).pipe(
    map(([featureFlags, brand]) =>
      brand.requestReviewsPageVisible ? featureFlags['multilocation_customer_voice'] : false,
    ),
  );

  if (await firstValueFrom(multiLocationCustomerVoiceEnabled$)) {
    actionButtons.push({
      buttonType: 'mat-raised-button',
      labelTranslationKey: 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REQUEST_REVIEWS',
      link: createUrlTreeFromSnapshot(route, ['request-reviews']).toString(),
    });
  }

  return { actionButtons };
};

export const actionButtonSlResolver: ResolveFn<ContentConfig> = (route: ActivatedRouteSnapshot) => {
  return {
    actionButtons: [
      {
        buttonType: 'mat-icon-button',
        icon: 'settings',
        tooltipTranslationKey: 'BRANDS.REVIEWS.RESPONSE_TEMPLATES.MANAGE_TEMPLATES',
        link: createUrlTreeFromSnapshot(route, ['response-templates']).toString(),
        navigationOption: NavigationOption.ROUTERLINK,
      },
    ],
  };
};
