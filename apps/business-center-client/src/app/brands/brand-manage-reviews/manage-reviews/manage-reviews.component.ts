import { Component, inject, Injector, On<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, TitleStrategy } from '@angular/router';
import { map, Observable, of, OperatorFunction, scan, shareReplay, startWith, Subject, switchMap, zip } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { editionIds, partnerId, productIds } from '../../../../globals';
import { isMobile } from '../../../core/mobile';
import { GmbService } from '../../../metrics/gmb.service';
import { ReviewsService } from '../../../metrics/reviews.service';
import { MLSocialService } from '../../../metrics/social.service';
import { NavigationService } from '../../../navigation/navigation.service';
import { TranslateService } from '@ngx-translate/core';
import { GalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import { Mode, SidepanelService, Size } from '../../../navigation/sidepanel.service';
import {
  Sentiment,
  TrendingKeyword,
} from '../../../review-metrics/metric-cards/trending-keywords/trending-keywords.component';
import { ReviewRatingInput } from '../../../review-metrics/review-metrics.component';
import { BrandFilterContainerService } from '../../brand-filter-container/brand-filter-container.service';
import {
  QueryReviewsSentimentService,
  ReviewSentimentEntity,
  ReviewWithHighlightOptions,
} from '../../brand-sentiment/query-reviews-sentiment.service';
import { QueryReviewsService, ReviewsWithPublishSettings, ThirdPartyPublishSettings } from '../query-reviews.service';
import { ManageReviewsFiltersComponent } from './manage-reviews-filters/manage-reviews-filters.component';
import { ManageReviewsFiltersService } from './manage-reviews-filters/manage-reviews-filters.service';
import { LocationsService } from '../../../locations';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { AccountGroupService } from '../../../account-group/account-group.service';
import { AccountsService } from '@vendasta/accounts/legacy';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  SelectObjectTypeModalComponent,
  tableFiltersInjectionTokenGenerator,
  ListObjectTypeDialogData,
  ListObjectTypeDialogResult,
  PageAnalyticsInjectionToken,
  NonStandardExternalIDs,
} from '@galaxy/crm/static';
import { ContactCustomColumns, CrmFieldService, StandardExternalIds } from '@galaxy/crm/static';
import { ReputationService } from '../../../reputation/reputation.service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { NavActionButton } from '../../../navigation/navigation.interface';

@Component({
  selector: 'bc-manage-reviews',
  templateUrl: './manage-reviews.component.html',
  styleUrls: ['./manage-reviews.component.scss'],
  standalone: false,
})
export class ManageReviewsComponent implements OnInit, OnDestroy {
  private readonly loadMore = new Subject<void>();

  private readonly iter$ = this.queryReviewsService.iter$.pipe(shareReplay({ refCount: true, bufferSize: 1 }));

  protected readonly reviewsAndSettings$ = this.iter$.pipe(
    switchMap((iter) =>
      this.loadMore.pipe(
        startWith(null),
        switchMap(() => iter.next()),
      ),
    ),
    map((next) =>
      next === null ? { reviews: [], publishSettings: new Map<string, ThirdPartyPublishSettings>() } : next,
    ),
    shareReplay({ refCount: true, bufferSize: 1 }),
    switchMap((newNext) =>
      zip([
        of(newNext).pipe(ManageReviewsComponent.toReviews()),
        of(newNext).pipe(ManageReviewsComponent.toPublishSettings()),
      ]),
    ),
    map(([reviews, publishSettings]) => ({ reviews: reviews || [], publishSettings: publishSettings })),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  protected readonly loading$ = this.iter$.pipe(switchMap((iter) => iter.loading$));
  protected readonly hasMore$ = this.iter$.pipe(switchMap((iter) => iter.hasMore$));

  protected accountsWithGmb$: Observable<Map<string, number>>;
  protected accountsWithFacebook$: Observable<Map<string, number>>;
  protected currentReviewRatingInput$: Observable<ReviewRatingInput>;
  protected trendingKeywords$: Observable<TrendingKeyword[]>;

  protected readonly dataActionButtons$ = this.navigationService.routeData$.pipe(
    map((data) => data.injectedPageContent?.actionButtons),
  );
  protected readonly rmPremiumActive$: Observable<boolean> = this.accountGroupService.currentAccountGroupId$.pipe(
    switchMap((accountGroupId) => this.accountsService.list(accountGroupId, partnerId)),
    map((res) => res.accounts || []),
    map((accounts) => accounts.filter((a) => !a.trial && !a.deactivation)),
    map((accounts) =>
      accounts.some(
        (account) =>
          account.productId === productIds.reputationManagement &&
          account.editionId === editionIds.reputationManagementPremium,
      ),
    ),
  );

  protected readonly title: string = inject(TitleStrategy).getResolvedTitleForRoute(inject(ActivatedRoute).snapshot);
  private readonly injector = inject(Injector);

  private readonly locationsService = inject(LocationsService);
  protected readonly isCurrentLocationABrand$ = this.locationsService.isCurrentLocationABrand$;

  private readonly crmFieldService = inject(CrmFieldService);
  private readonly productAnalyticsService = inject(ProductAnalyticsService);

  constructor(
    private readonly queryReviewsService: QueryReviewsService,
    private readonly manageReviewsFiltersService: ManageReviewsFiltersService,
    private readonly navigationService: NavigationService,
    private readonly route: ActivatedRoute,
    private readonly gmbService: GmbService,
    private readonly socialService: MLSocialService,
    public readonly sidepanelService: SidepanelService,
    private brandFilterContainerService: BrandFilterContainerService,
    readonly reviewService: ReviewsService,
    readonly queryReviewsSentimentService: QueryReviewsSentimentService,
    private readonly translationService: TranslateService,
    private readonly confirmationModalService: OpenConfirmationModalService,
    private readonly accountGroupService: AccountGroupService,
    private readonly accountsService: AccountsService,
    private readonly snackbarService: SnackbarService,
    private readonly dialog: MatDialog,
    private readonly reputationService: ReputationService,
  ) {
    this.trendingKeywords$ = this.queryReviewsSentimentService.allTimeEntities$.pipe(
      map((entities) => this.entitiesToKeywords(entities)),
    );

    this.currentReviewRatingInput$ = this.reviewService.allTimeOverall$.pipe(
      map((overall) => {
        return <ReviewRatingInput>{
          oneStarCount: overall.numberOneStars,
          twoStarCount: overall.numberTwoStars,
          threeStarCount: overall.numberThreeStars,
          fourStarCount: overall.numberFourStars,
          fiveStarCount: overall.numberFiveStars,
          numberResponded: overall.numberResponded,
          numberUnresponded: overall.numberUnresponded,
        };
      }),
    );
  }

  entitiesToKeywords(entities: ReviewSentimentEntity[]): TrendingKeyword[] {
    return entities.slice(0, 10).map(
      (entity) =>
        <TrendingKeyword>{
          keyword: entity.phrase,
          sentiment:
            entity.sentimentLevel === 1
              ? Sentiment.Positive
              : entity.sentimentLevel === 0
                ? Sentiment.Neutral
                : Sentiment.Negative,
          totalCount: entity.totalCount,
        },
    );
  }

  nextPage(): void {
    this.loadMore.next();
  }

  ngOnInit(): void {
    this.sidepanelService.setView(Size.REGULAR, Mode.SIDE, ManageReviewsFiltersComponent, false);
    this.queryReviewsService.setIsAllTimeRange(true);
    if (!isMobile()) {
      this.sidepanelService.open();
    }

    this.brandFilterContainerService.setMinimizedStatus(false);

    // Filter query params are applied
    const ratingParam: string = this.route.snapshot.queryParamMap.get('rating');
    if (ratingParam) {
      this.manageReviewsFiltersService.setRatingFilter(ratingParam);
    }
    const statusParam: string = this.route.snapshot.queryParamMap.get('status');
    if (statusParam) {
      this.manageReviewsFiltersService.setStatusFilter(statusParam);
    }
    const changesParam: string = this.route.snapshot.queryParamMap.get('changes');
    if (changesParam) {
      this.manageReviewsFiltersService.setChangesFilter(changesParam);
    }

    this.accountsWithGmb$ = this.gmbService.connectedLocations$.pipe(shareReplay({ refCount: true, bufferSize: 1 }));
    this.accountsWithFacebook$ = this.socialService.facebookConnections$.pipe(
      shareReplay({ refCount: true, bufferSize: 1 }),
    );
  }

  private static toPublishSettings(): OperatorFunction<
    ReviewsWithPublishSettings,
    Map<string, ThirdPartyPublishSettings>
  > {
    return (source: Observable<ReviewsWithPublishSettings>) =>
      source.pipe(
        map((settings) => settings.publishSettings),
        scan((acc, curr) => new Map([...acc, ...curr]), new Map<string, ThirdPartyPublishSettings>()),
      );
  }

  private static toReviews(): OperatorFunction<ReviewsWithPublishSettings, ReviewWithHighlightOptions[]> {
    return (source: Observable<ReviewsWithPublishSettings>) =>
      source.pipe(
        map((settings): ReviewWithHighlightOptions[] =>
          settings.reviews.map((review) => ({ review }) as ReviewWithHighlightOptions),
        ),
        scan((acc, curr) => acc.concat(curr), []),
      );
  }

  openSelectRecipientsModal(): void {
    const dialogRef = this.dialog.open(SelectObjectTypeModalComponent, {
      data: {
        baseColumnIds: this.buildReviewRequestCrmColumns(),
        modalTitle: this.translationService.instant('COMMON.ACTION_LABELS.SELECT_RECIPIENTS'),
        objectType: 'Contact',
        associations: [],
        hideCreateButton: true,
        enableMultiplePreSelectedRows: true,
        newSelectionIDs: [],
        unlockPreSelectedRows: true,
      } as ListObjectTypeDialogData,
      injector: Injector.create({
        providers: [
          {
            provide: PageAnalyticsInjectionToken,
            useFactory: () => {
              const deps = {
                trackEvent(): void {
                  return;
                },
              };
              return deps;
            },
          },
          {
            provide: GalaxyFilterChipInjectionToken,
            useFactory: tableFiltersInjectionTokenGenerator('Contact'),
          },
        ],
        parent: this.injector,
      }),
    });

    dialogRef.afterClosed().subscribe((result: ListObjectTypeDialogResult) => {
      const contactIds = result?.selectedRows.map((row) => row.id);
      if (contactIds.length > 0) {
        this.confirmationModalService
          .openModal({
            title: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUEST_QUESTION',
            message: this.translationService.instant(
              'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_BULK_CONFIRMATION_MESSAGE',
              { contactCount: contactIds.length },
            ),
            confirmButtonText: 'COMMON.ACTION_LABELS.SEND_REVIEW_REQUESTS',
          })
          .subscribe(async (confirmed) => {
            if (confirmed) {
              try {
                await this.reputationService.scheduleReviewRequest(
                  this.accountGroupService.currentAccountGroupId(),
                  contactIds,
                );
              } catch (error) {
                this.snackbarService.openErrorSnack(
                  'PERFORMANCE.MULTI_LOCATION.REVIEWS.SEND_REVIEWS_CONFIRMATION_MESSAGE_ERROR',
                );
              }
            }
          });
      }
    });
  }

  ngOnDestroy(): void {
    this.sidepanelService.close();
  }

  private buildReviewRequestCrmColumns(): string[] {
    return [
      ContactCustomColumns.FullName,
      this.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber),
      this.crmFieldService.getFieldId(StandardExternalIds.Email),
      this.crmFieldService.getFieldId(NonStandardExternalIDs.LastEmailRequest),
      this.crmFieldService.getFieldId(NonStandardExternalIDs.LastSmsRequest),
    ];
  }

  logPosthogEvent(button: NavActionButton) {
    if (button?.labelTranslationKey === 'PERFORMANCE.MULTI_LOCATION.CUSTOMER_VOICE.REQUEST_REVIEWS') {
      this.productAnalyticsService.trackEvent(
        'request-reviews-button-clicked',
        'manage-reviews-request-reviews-activity',
        'click',
      );
    }
  }
}
