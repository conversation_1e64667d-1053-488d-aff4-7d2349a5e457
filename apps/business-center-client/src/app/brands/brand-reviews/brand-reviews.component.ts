import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { DecimalPipe } from '@angular/common';
import { AfterViewInit, Component, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  filter,
  map,
  Observable,
  of,
  shareReplay,
  skip,
  startWith,
  tap,
} from 'rxjs';
import { AccountGroup, getLocation } from '../../account-group/account-group';
import { LanguageService } from '../../core/language-service.service';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { round } from '../../metrics/helpers';
import { ReviewRating } from '../../metrics/review-classes';
import { ReviewsService } from '../../metrics/reviews.service';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import {
  CardChartConfig,
  CardDataContainer,
  CardMultiSourceDataConfig,
  SingleSeriesChartType,
} from '../../performance-dashboard/cards/interface';
import { ConnectedCardsService } from '../../performance-dashboard/connected-cards/connected-cards.service';
import { ReputationService } from '../../reputation';
import { TimeRangeService } from '../../shared/time-range.service';
import { BrandSidebarHeaderService } from '../brand-sidebar-header/brand-sidebar-header.service';
import { BrandsService } from '../brands.service';
import { computeGradesFromMeasures } from '../grades';
import { GradeFilter } from '../interface';
import { MultiLocationService } from '../multi-location.service';
import { BrandRow, MeasureValueMap, MetricColumn } from '../table/table.service';
import { BrandReviewsSidebarComponent } from './brand-reviews-sidebar/brand-reviews-sidebar.component';
import { ActivatedRoute, TitleStrategy } from '@angular/router';

// Values copied over from grade colors
const F_COLOR = '#C62828';
const D_COLOR = '#e95d5f';
const FILTERED_OUT_COLOR = '#D3D3D3';
const B_COLOR = '#87c387';
const A_COLOR = '#45AA49';

const FIVE_COLOR = A_COLOR;
const FOUR_COLOR = B_COLOR;
const THREE_COLOR = FILTERED_OUT_COLOR;
const TWO_COLOR = D_COLOR;
const ONE_COLOR = F_COLOR;

interface ResponseTotals {
  responded: number;
  unresponded: number;
  total: number;
}

enum Cache {
  reviewsMapShowing = 'multi-location_reviews-map-showing',
  reviewsComparison = 'multi-location_reviews-comparison',
}

@Component({
  selector: 'bc-brand-reviews',
  templateUrl: './brand-reviews.component.html',
  styleUrls: ['./../brands-common.component.scss'],
  standalone: false,
})
export class BrandReviewsComponent implements OnInit, OnDestroy, AfterViewInit {
  mapLocations$: Observable<BrandRow[]>;
  loadedMapLocations$: Observable<BrandRow[]>;
  loadedCompareData$: Observable<BrandRow[]>;
  tableData$: Observable<BrandRow[]>;

  form: FormGroup;

  protected readonly title: string = inject(TitleStrategy).getResolvedTitleForRoute(inject(ActivatedRoute).snapshot);

  public comparison$$: BehaviorSubject<string> = new BehaviorSubject<string>('TABLE.NODE_LOCATION');
  public comparison$: Observable<string> = this.comparison$$.asObservable();

  search$: Observable<string>;
  public mapShowing$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  public mapShowing$: Observable<boolean> = this.mapShowing$$.asObservable();
  readonly mapShowingText$ = this.mapShowing$.pipe(
    map((showing) =>
      showing === false
        ? 'PERFORMANCE.MULTI_LOCATION.LISTINGS.SHOW_MAP'
        : 'PERFORMANCE.MULTI_LOCATION.LISTINGS.HIDE_MAP',
    ),
  );

  gradeFilter$$ = new BehaviorSubject<GradeFilter>(GradeFilter.All);
  gradeFilter$ = this.gradeFilter$$.asObservable();

  public reviewResponseTotals$: Observable<ResponseTotals>;

  gradesMeasure = 'average_rating';

  dataSource$: Observable<CardDataContainer[]>;
  cardConfigs$: Observable<(CardChartConfig | CardMultiSourceDataConfig)[]>;

  tableColumns: MetricColumn[] = [
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.TOTAL_REVIEWS_TAB',
      measureKey: 'total_reviews',
      metricMetadata: {
        name: 'Total',
      },
      useManageRouterLink: true,
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.SENTIMENT',
      thermometerMetadata: {
        seriesKey: 'sentiment_thermometer',
        colors: [TWO_COLOR, THREE_COLOR, FOUR_COLOR],
        tooltips: ['Negative', 'Neutral', 'Positive'],
      },
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.AVERAGE_REVIEW_RATING_TAB',
      measureKey: 'average_rating',
      metricMetadata: {
        name: 'Rating',
        numberPipeArgs: '1.2-2',
        noDefault: true,
      },
      metaValueList: ['mean', 'median'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONDED_TO',
      measureKey: 'responded',
      metricMetadata: {
        name: 'Responses',
        displayValueType: 'number',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONSE_RATE',
      measureKey: 'response_rate',
      metricMetadata: {
        name: 'Response Rate',
        displayValueType: 'percentage',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONSE_TIME_HOURS',
      measureKey: 'response_time_hour',
      metricMetadata: {
        name: 'Total',
        displayValueType: 'number',
        numberPipeArgs: '1.0-0',
        increaseBad: true,
      },
      metaValueList: ['mean', 'median'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.5_STARS',
      measureKey: '5_star_reviews',
      metricMetadata: {
        name: 'Total',
      },
      useManageRouterLink: true,
      manageRouterParams: { rating: '5' },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.4_STARS',
      measureKey: '4_star_reviews',
      metricMetadata: {
        name: 'Total',
      },
      useManageRouterLink: true,
      manageRouterParams: { rating: '4' },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.3_STARS',
      measureKey: '3_star_reviews',
      metricMetadata: {
        name: 'Total',
      },
      useManageRouterLink: true,
      manageRouterParams: { rating: '3' },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.2_STARS',
      measureKey: '2_star_reviews',
      metricMetadata: {
        name: 'Total',
        increaseBad: true,
      },
      useManageRouterLink: true,
      manageRouterParams: { rating: '2' },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.1_STAR',
      measureKey: '1_star_reviews',
      metricMetadata: {
        name: 'Total',
        increaseBad: true,
      },
      useManageRouterLink: true,
      manageRouterParams: { rating: '1' },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.NO_RATING_REVIEW_SERIES',
      measureKey: 'no_star_reviews',
      metricMetadata: {
        name: 'Total',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
  ];

  readonly isMobile$ = this.breakpointObserver.observe(Breakpoints.Handset).pipe(
    map((result) => result.matches),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    public multiLocationService: MultiLocationService,
    private brandsService: BrandsService,
    public sidepanelService: SidepanelService,
    readonly reviewService: ReviewsService,
    private timeRangeService: TimeRangeService,
    private accountGroupService: AccountGroupMetricService,
    private connectedCardsService: ConnectedCardsService,
    private readonly brandSidebarHeaderService: BrandSidebarHeaderService,
    private languageService: LanguageService,
    private fb: FormBuilder,
    private reputationService: ReputationService,
    private locationsService: LocationsService,
    private readonly breakpointObserver: BreakpointObserver,
    private readonly destroyRef: DestroyRef,
  ) {
    this.connectedCardsService.forceMobile(true);

    const sourceMeasureMap$ = combineLatest([
      this.reviewService.currentBySource$,
      this.reviewService.previousBySource$,
      this.reviewService.responseTimesBySource$,
      this.reviewService.previousResponseTimesBySource$,
    ]).pipe(
      map(([reviewStats, pReviewStats, responseTimes, pResponseTimes]) => {
        const measureMap = {};
        if (reviewStats == null || responseTimes == null) {
          return measureMap;
        }
        reviewStats.forEach((reviewStat) => {
          // Set value for all locations in stats
          measureMap[reviewStat.dimension] = measureMapFromStat(reviewStat.stat);
        });
        responseTimes.forEach((responseTime) => {
          if (measureMap[responseTime.dimension]) {
            if (measureMap[responseTime.dimension].responded.value) {
              measureMap[responseTime.dimension].response_time_hour.value = responseTime.stat.averageResponseTime || 0;
            } else {
              measureMap[responseTime.dimension].response_time_hour.value = null;
            }
          }
        });
        if (pReviewStats) {
          pReviewStats.forEach((pReviewStat) => {
            if (!measureMap[pReviewStat.dimension]) {
              measureMap[pReviewStat.dimension] = createEmptyMetricMap();
            }
            measureMap[pReviewStat.dimension]['average_rating'].deltaAbs =
              (measureMap[pReviewStat.dimension]['average_rating'].value || 0) -
              (pReviewStat.stat.averageReviewScore() || 0);
            // Round the difference again, since oddities happen when doing floating point math
            measureMap[pReviewStat.dimension]['average_rating'].deltaAbs = round(
              measureMap[pReviewStat.dimension]['average_rating'].deltaAbs,
              '1.2-2',
            );
            measureMap[pReviewStat.dimension]['total_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['total_reviews'].value || 0) - (pReviewStat.stat.totalReviews() || 0);
            measureMap[pReviewStat.dimension]['5_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['5_star_reviews'].value || 0) -
              (pReviewStat.stat.numberFiveStars || 0);
            measureMap[pReviewStat.dimension]['4_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['4_star_reviews'].value || 0) -
              (pReviewStat.stat.numberFourStars || 0);
            measureMap[pReviewStat.dimension]['3_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['3_star_reviews'].value || 0) -
              (pReviewStat.stat.numberThreeStars || 0);
            measureMap[pReviewStat.dimension]['2_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['2_star_reviews'].value || 0) - (pReviewStat.stat.numberTwoStars || 0);
            measureMap[pReviewStat.dimension]['1_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['1_star_reviews'].value || 0) - (pReviewStat.stat.numberOneStars || 0);
            measureMap[pReviewStat.dimension]['no_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['no_star_reviews'].value || 0) -
              (pReviewStat.stat.noRatingReviews() || 0);
            measureMap[pReviewStat.dimension]['responded'].deltaAbs =
              (measureMap[pReviewStat.dimension]['responded'].value || 0) - (pReviewStat.stat.numberResponded || 0);
            measureMap[pReviewStat.dimension]['unresponded'].deltaAbs =
              (measureMap[pReviewStat.dimension]['unresponded'].value || 0) - (pReviewStat.stat.numberUnresponded || 0);
            measureMap[pReviewStat.dimension]['response_rate'].deltaAbs =
              (measureMap[pReviewStat.dimension]['response_rate'].value || 0) -
              (pReviewStat.stat.numberResponded || 0) / (pReviewStat.stat.totalReviews() || 0);
          });
        }

        if (pResponseTimes) {
          pResponseTimes.forEach((pResponseTime) => {
            if (!measureMap[pResponseTime.dimension]) {
              measureMap[pResponseTime.dimension] = createEmptyMetricMap();
            }
            if (measureMap[pResponseTime.dimension].responded.value) {
              measureMap[pResponseTime.dimension].response_time_hour.value = measureMap[pResponseTime.dimension]
                .response_time_hour.value
                ? measureMap[pResponseTime.dimension].response_time_hour.value
                : 0;
              measureMap[pResponseTime.dimension].response_time_hour.deltaAbs =
                (measureMap[pResponseTime.dimension].response_time_hour.value || 0) -
                (pResponseTime.stat.averageResponseTime || 0);
            }
          });
        }
        return measureMap;
      }),
    );

    // Build measure value map for reviews
    const locationMeasureMap$ = combineLatest([
      this.locationsService.currentAccountGroupIds$,
      this.reviewService.currentByLocation$,
      this.reviewService.previousByLocation$,
      this.reviewService.responseTimesByLocation$,
      this.reviewService.previousResponseTimesByLocation$,
    ]).pipe(
      debounceTime(10),
      map(([accountGroupIds, reviewStats, pReviewStats, responseTimes, pResponseTimes]) => {
        const measureMap = {};
        if (accountGroupIds == null || reviewStats == null || responseTimes == null) {
          return measureMap;
        }
        accountGroupIds.forEach((accountGroupId) => {
          // Default value all locations
          measureMap[accountGroupId] = createEmptyMetricMap();
        });
        reviewStats.forEach((reviewStat) => {
          // Set value for all locations in stats
          if (!measureMap[reviewStat.dimension]) {
            return;
          }
          measureMap[reviewStat.dimension].average_rating.value = reviewStat.stat.averageReviewScore();
          measureMap[reviewStat.dimension].total_reviews.value = reviewStat.stat.totalReviews();
          measureMap[reviewStat.dimension]['5_star_reviews'].value = reviewStat.stat.numberFiveStars;
          measureMap[reviewStat.dimension]['4_star_reviews'].value = reviewStat.stat.numberFourStars;
          measureMap[reviewStat.dimension]['3_star_reviews'].value = reviewStat.stat.numberThreeStars;
          measureMap[reviewStat.dimension]['2_star_reviews'].value = reviewStat.stat.numberTwoStars;
          measureMap[reviewStat.dimension]['1_star_reviews'].value = reviewStat.stat.numberOneStars;
          measureMap[reviewStat.dimension].no_star_reviews.value = reviewStat.stat.noRatingReviews();
          measureMap[reviewStat.dimension].responded.value = reviewStat.stat.numberResponded;
          measureMap[reviewStat.dimension].unresponded.value = reviewStat.stat.numberUnresponded;
          measureMap[reviewStat.dimension].response_rate.value =
            reviewStat.stat.numberResponded / reviewStat.stat.totalReviews();
        });
        responseTimes.forEach((responseTime) => {
          if (!measureMap[responseTime.dimension]) {
            return;
          }
          if (measureMap[responseTime.dimension]) {
            if (measureMap[responseTime.dimension].responded.value) {
              measureMap[responseTime.dimension].response_time_hour.value = responseTime.stat.averageResponseTime || 0;
            } else {
              measureMap[responseTime.dimension].response_time_hour.value = null;
            }
          }
        });
        if (pReviewStats) {
          pReviewStats.forEach((pReviewStat) => {
            if (!measureMap[pReviewStat.dimension]) {
              return;
            }
            measureMap[pReviewStat.dimension]['average_rating'].deltaAbs = round(
              (measureMap[pReviewStat.dimension]['average_rating'].value || 0) -
                (pReviewStat.stat.averageReviewScore() || 0),
              '1.2-2',
            );
            measureMap[pReviewStat.dimension]['total_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['total_reviews'].value || 0) - (pReviewStat.stat.totalReviews() || 0);
            measureMap[pReviewStat.dimension]['5_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['5_star_reviews'].value || 0) -
              (pReviewStat.stat.numberFiveStars || 0);
            measureMap[pReviewStat.dimension]['4_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['4_star_reviews'].value || 0) -
              (pReviewStat.stat.numberFourStars || 0);
            measureMap[pReviewStat.dimension]['3_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['3_star_reviews'].value || 0) -
              (pReviewStat.stat.numberThreeStars || 0);
            measureMap[pReviewStat.dimension]['2_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['2_star_reviews'].value || 0) - (pReviewStat.stat.numberTwoStars || 0);
            measureMap[pReviewStat.dimension]['1_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['1_star_reviews'].value || 0) - (pReviewStat.stat.numberOneStars || 0);
            measureMap[pReviewStat.dimension]['no_star_reviews'].deltaAbs =
              (measureMap[pReviewStat.dimension]['no_star_reviews'].value || 0) -
              (pReviewStat.stat.noRatingReviews() || 0);
            measureMap[pReviewStat.dimension]['responded'].deltaAbs =
              (measureMap[pReviewStat.dimension]['responded'].value || 0) - (pReviewStat.stat.numberResponded || 0);
            measureMap[pReviewStat.dimension]['unresponded'].deltaAbs =
              (measureMap[pReviewStat.dimension]['unresponded'].value || 0) - (pReviewStat.stat.numberUnresponded || 0);
            measureMap[pReviewStat.dimension]['response_rate'].deltaAbs =
              (measureMap[pReviewStat.dimension]['response_rate'].value || 0) -
              (pReviewStat.stat.numberResponded || 0) / (pReviewStat.stat.totalReviews() || 0);
          });
        }
        if (pResponseTimes) {
          pResponseTimes.forEach((pResponseTime) => {
            if (!measureMap[pResponseTime.dimension]) {
              return;
            }
            if (measureMap[pResponseTime.dimension].responded.value) {
              measureMap[pResponseTime.dimension].response_time_hour.value =
                measureMap[pResponseTime.dimension].response_time_hour.value || 0;
              measureMap[pResponseTime.dimension].response_time_hour.deltaAbs =
                (measureMap[pResponseTime.dimension].response_time_hour.value || 0) -
                (pResponseTime.stat.averageResponseTime || 0);
            }
          });
        }
        return measureMap;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const compareGroupSwitchMap: Record<string, Observable<BrandRow[]>> = {
      account_group_id: this.accountGroupService.filteredLocationsForPath$.pipe(
        map((locations) => {
          if (locations == null) {
            return null;
          }
          return Object.keys(locations)
            .map((k) => locations[k])
            .map((ag: AccountGroup) => {
              return {
                accountGroup: ag,
                title: ag.companyName,
                subtitle: getLocation(ag),
              };
            });
        }),
      ),
      source_id: this.reputationService.brandSourcesFull$.pipe(
        map((sources) => {
          if (sources == null) {
            return null;
          }
          return Array.from(sources.values()).map((source) => {
            return {
              source: source,
              title: source.name,
              icon: `//www.cdnstyles.com/static/images/icon50/sourceId-${source.sourceId}.png`,
            };
          });
        }),
      ),
      region: this.brandsService.childGroups$.pipe(
        map((regions) => {
          if (regions == null) {
            return null;
          }
          return regions.map((region) => {
            return {
              group: region,
              title: region.name,
            };
          });
        }),
      ),
    };
    const getExpectedGroupCopy$ = (groupBy: string) => {
      return compareGroupSwitchMap[groupBy].pipe(map((brandRows) => (brandRows ? [...brandRows] : brandRows)));
    };

    // Progressively loaded data for map/table. Location data, then measures/grades
    this.mapLocations$ = combineLatest([getExpectedGroupCopy$('account_group_id'), locationMeasureMap$]).pipe(
      map(([locationBrandRows, measures]) => {
        if (locationBrandRows == null) {
          return null;
        }
        let gradeMap;
        if (measures !== null) {
          [gradeMap] = computeGradesFromMeasures(measures, this.gradesMeasure);
        }
        return locationBrandRows.map((brandRow: BrandRow) => {
          brandRow.grade = (gradeMap || {})[brandRow.accountGroup.accountGroupId] || 'Loading';
          brandRow.measureMap = (measures || {})[brandRow.accountGroup.accountGroupId] || createEmptyMetricMap();
          return brandRow;
        });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.loadedMapLocations$ = this.mapLocations$.pipe(filter((locations) => !!locations));

    this.loadedCompareData$ = combineLatest([
      of('source_id'),
      getExpectedGroupCopy$('source_id'),
      sourceMeasureMap$,
    ]).pipe(
      map(([groupBy, locationBrandRows, measures]) => {
        if (locationBrandRows == null) {
          return null;
        }
        let measureMapKeyFn = null;
        if (groupBy === 'account_group_id') {
          measureMapKeyFn = (r: BrandRow) => r.accountGroup.accountGroupId;
        } else if (groupBy === 'source' || groupBy === 'source_id') {
          measureMapKeyFn = (r: BrandRow) => r.source.sourceId + '';
        } else if (groupBy === 'region') {
          // measureMapKeyFn = (r: BrandRow) => r.accountGroup.accountGroupId;
        } else if (groupBy === 'state') {
          // measureMapKeyFn = (r: BrandRow) => r.accountGroup.accountGroupId;
        }
        let gradeMap;
        if (measures !== null) {
          [gradeMap] = computeGradesFromMeasures(measures, this.gradesMeasure);
        }
        return locationBrandRows
          .map((brandRow: BrandRow) => {
            const measureMapKey = measureMapKeyFn(brandRow);
            brandRow.grade = (gradeMap || {})[measureMapKey] || 'Loading';
            brandRow.measureMap = (measures || {})[measureMapKey];
            return brandRow;
          })
          .filter((brandRow) => !!brandRow.measureMap);
      }),
      filter((rows) => !!rows),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const ratingConfig: CardMultiSourceDataConfig = {
      dataDisplayType: SingleSeriesChartType.HorizontalBar,
      chartConfig: {
        chartDataType: 'string',
        colorStepOverride: 0,
        showXAxis: false,
      },
    };
    const countConfig: CardChartConfig = {
      chartType: 'thermometer',
      chartPoints: [
        {
          dataPointSourceKey: 'respondedPosition',
          dataPointBackgroundHexColour: A_COLOR,
        },
        {
          dataPointSourceKey: 'notRespondedPosition',
          dataPointBackgroundHexColour: THREE_COLOR,
        },
      ],
    };
    const sentimentConfig: CardMultiSourceDataConfig = {
      dataDisplayType: SingleSeriesChartType.Doughnut,
      chartConfig: {
        chartDataType: 'string',
        showLegend: false,
      },
    };
    this.cardConfigs$ = this.timeRangeService.formattingOption$.pipe(
      map((formatting) => {
        ratingConfig.chartConfig.formatting = formatting;
        sentimentConfig.chartConfig.formatting = formatting;
        return [ratingConfig, countConfig, sentimentConfig];
      }),
      startWith([ratingConfig, countConfig, sentimentConfig]),
    );

    this.reviewResponseTotals$ = this.reviewService.currentOverall$.pipe(
      map((responseTotals) => {
        if (!responseTotals) {
          return;
        }
        return {
          total: responseTotals.totalReviews(),
          responded: responseTotals.numberResponded,
          unresponded: responseTotals.numberUnresponded,
        };
      }),
    );

    this.dataSource$ = combineLatest([
      this.reviewService.currentOverall$,
      this.reviewService.previousOverall$,
      this.reviewResponseTotals$,
    ]).pipe(
      map(([overall, pOverall, responseTotals]) => {
        if (!overall || !pOverall || !responseTotals) {
          return [{}, {}, {}];
        }
        const decimalPipe = new DecimalPipe(this.languageService.currentLocale || 'en');
        const averageDelta = overall.averageReviewScore() - pOverall.averageReviewScore();
        const responded = responseTotals.responded || 0;
        const unresponded = responseTotals.unresponded || 0;
        const total = responseTotals.total || 0;
        return [
          {
            chartData: [
              {
                name: '5 \u2605',
                value: overall.numberFiveStars,
                color: FIVE_COLOR,
              },
              {
                name: '4 \u2605',
                value: overall.numberFourStars,
                color: FOUR_COLOR,
              },
              {
                name: '3 \u2605',
                value: overall.numberThreeStars,
                color: THREE_COLOR,
              },
              {
                name: '2 \u2605',
                value: overall.numberTwoStars,
                color: TWO_COLOR,
              },
              {
                name: '1 \u2605',
                value: overall.numberOneStars,
                color: ONE_COLOR,
              },
            ],
            data: {
              cardTitle: {
                key: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.AVERAGE_REVIEW_RATING_TAB',
              },
              extraInfo: {
                value: decimalPipe.transform(overall.averageReviewScore(), '1.1-1'),
                type: 'review',
                secondaryValue: decimalPipe.transform(Math.abs(averageDelta), '1.1-1'),
                secondaryValueColor: averageDelta >= 0 ? A_COLOR : F_COLOR,
                statusIcon: averageDelta >= 0 ? 'arrow_drop_up' : 'arrow_drop_down',
              },
            },
          },
          {
            data: {
              cardTitle: {
                key: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.TOTAL_REVIEWS_TAB',
              },
              chart: {
                respondedCount: responded,
                respondedPosition: (responded / total) * 100,
                notRespondedCount: unresponded,
                notRespondedPosition: 100,
              },
              extraInfo: [
                {
                  title: {
                    key: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.RESPONDED_TO',
                    params: {},
                  },
                  value: responded,
                  secondaryValue: `(${round((total ? responded / total : 0) * 100, '1.0-0')}%)`,
                  valueColor: A_COLOR,
                },
                {
                  title: {
                    key: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.TOTAL_REVIEWS_TAB',
                    params: {},
                  },
                  value: total,
                },
              ],
            },
          },
          {
            chartData: [
              {
                name: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.POSITIVE_REVIEWS_SERIES',
                value: overall.numberFiveStars + overall.numberFourStars,
                color: A_COLOR,
              },
              {
                name: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.NEUTRAL_REVIEWS_SERIES',
                value: overall.numberThreeStars,
                color: THREE_COLOR,
              },
              {
                name: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.NEGATIVE_REVIEWS_SERIES',
                value: overall.numberTwoStars + overall.numberOneStars,
                color: F_COLOR,
              },
            ],
            data: {
              cardTitle: {
                key: 'PERFORMANCE.MULTI_LOCATION.REVIEWS.SENTIMENT',
              },
            },
          },
        ];
      }),
      startWith([{}, {}, {}]),
    );

    this.mountForm();
  }

  ngOnInit(): void {
    this.sidepanelService.close();
    this.multiLocationService.setMetricCategory('REVIEWS');

    this.listenPropertiesStoreInCache();

    this.getDefinitionsInCache();
  }

  ngAfterViewInit(): void {
    this.sidepanelService.setView(Size.LARGE, Mode.SIDE, BrandReviewsSidebarComponent);
  }

  ngOnDestroy(): void {
    this.sidepanelService.clearView();
    this.sidepanelService.close();
    this.connectedCardsService.forceMobile(false);
  }

  listenPropertiesStoreInCache(): void {
    this.mapShowing$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        skip(1), // skip the first value when loading the component
      )
      .subscribe((value) => {
        localStorage.setItem(Cache.reviewsMapShowing, String(value));
      });

    this.comparison$
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        skip(1), // skip the first value when loading the component
      )
      .subscribe((value) => {
        localStorage.setItem(Cache.reviewsComparison, String(value));
      });
  }

  mountForm(): void {
    this.form = this.fb.group({
      comparison: new FormControl<string>('TABLE.NODE_LOCATION', Validators.required),
      search: new FormControl<string>(''),
    });

    this.form.valueChanges.pipe(
      map((changes) => changes['comparison']),
      tap((value) => {
        this.handleComparison(value);
        this.comparison$$.next(value);
      }),
    );
    this.search$ = this.form.valueChanges.pipe(
      map((changes) => changes['search']),
      startWith(''),
    );
  }

  getDefinitionsInCache(): void {
    const reviewsMapShowing = localStorage.getItem(Cache.reviewsMapShowing);
    this.mapShowing$$.next(reviewsMapShowing === 'true');

    const comparison = localStorage.getItem(Cache.reviewsComparison);
    if (comparison) {
      this.form.controls['comparison'].setValue(comparison);
      this.comparison$$.next(comparison);
    }

    this.prepareTableData();
  }

  prepareTableData(): void {
    const selectedTableData$ = combineLatest([
      this.loadedCompareData$,
      this.loadedMapLocations$,
      this.comparison$,
    ]).pipe(
      map(([sourceData, locationData, comparison]) => {
        return comparison === 'TABLE.NODE_LOCATION' ? locationData : sourceData;
      }),
    );

    this.tableData$ = combineLatest([selectedTableData$, this.search$]).pipe(
      map(([tableData, search]) => {
        return tableData.filter((row) => {
          if (row === null || row === undefined) {
            return true;
          }
          if (row.subtitle) {
            return (
              row.title.toUpperCase().includes(search.toUpperCase()) ||
              row.subtitle.toUpperCase().includes(search.toUpperCase())
            );
          }
          return row.title.toUpperCase().includes(search.toUpperCase());
        });
      }),
      map((tableData) => {
        return tableData.map((row) => {
          if (!row) {
            return;
          }
          const sentiment = [
            row.measureMap['1_star_reviews'].value + row.measureMap['2_star_reviews'].value, // Negative
            row.measureMap['3_star_reviews'].value, // Neutral
            row.measureMap['5_star_reviews'].value + row.measureMap['4_star_reviews'].value, // Positive
          ];
          const sentimentSum = sentiment.reduce((a, b) => a + b, 0);
          const sentimentNormalized = sentiment.map((value) => (value / sentimentSum) * 100);
          row.seriesMap = {
            sentiment_thermometer: {
              values: sentiment,
              normalized: sentimentNormalized,
            },
          };
          return row;
        });
      }),
    );
  }

  toggleMapShowing(value: boolean): void {
    this.mapShowing$$.next(!value);
  }

  toggleComparison(value: string): void {
    this.handleComparison(value);
    this.comparison$$.next(value);
  }

  handleComparison(value: string): void {
    if (value === 'TABLE.NODE_SOURCE') {
      this.disableMapAndResetFilter();
    }
  }

  disableMapAndResetFilter(): void {
    this.mapShowing$$.next(false);
    this.gradeFilter$$.next(GradeFilter.All);
  }

  openDetailsDrawer(accountGroupId: string): void {
    this.brandSidebarHeaderService.setResource(accountGroupId);
    this.sidepanelService.open();
  }

  public onGradeSelectedChange(gradeFilter: GradeFilter): void {
    this.gradeFilter$$.next(gradeFilter);
  }
}

function createEmptyMetricMap(): MeasureValueMap {
  return {
    average_rating: { value: null, deltaAbs: 0 },
    total_reviews: { value: 0, deltaAbs: 0 },
    '5_star_reviews': { value: 0, deltaAbs: 0 },
    '4_star_reviews': { value: 0, deltaAbs: 0 },
    '3_star_reviews': { value: 0, deltaAbs: 0 },
    '2_star_reviews': { value: 0, deltaAbs: 0 },
    '1_star_reviews': { value: 0, deltaAbs: 0 },
    no_star_reviews: { value: 0, deltaAbs: 0 },
    response_time_hour: { value: null, deltaAbs: null },
    responded: { value: 0, deltaAbs: 0 },
    unresponded: { value: 0, deltaAbs: 0 },
    response_rate: { value: 0, deltaAbs: 0 },
  };
}

function measureMapFromStat(stat: ReviewRating): MeasureValueMap {
  return {
    average_rating: { value: stat.averageReviewScore(), deltaAbs: 0 },
    total_reviews: { value: stat.totalReviews(), deltaAbs: 0 },
    '5_star_reviews': { value: stat.numberFiveStars, deltaAbs: 0 },
    '4_star_reviews': { value: stat.numberFourStars, deltaAbs: 0 },
    '3_star_reviews': { value: stat.numberThreeStars, deltaAbs: 0 },
    '2_star_reviews': { value: stat.numberTwoStars, deltaAbs: 0 },
    '1_star_reviews': { value: stat.numberOneStars, deltaAbs: 0 },
    no_star_reviews: { value: stat.noRatingReviews(), deltaAbs: 0 },
    response_time_hour: { value: null, deltaAbs: null },
    responded: { value: stat.numberResponded, deltaAbs: 0 },
    unresponded: { value: stat.numberUnresponded, deltaAbs: 0 },
    response_rate: {
      value: stat.numberResponded / stat.totalReviews(),
      deltaAbs: 0,
    },
  };
}
