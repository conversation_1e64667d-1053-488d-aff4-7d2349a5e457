import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsComponent } from '@vendasta/business-nav';
import { TranslateModule } from '@ngx-translate/core';
import { combineLatest, filter, map, Observable, shareReplay } from 'rxjs';
import { BrandRow, MeasureValueMap, MetricColumn } from '../table/table.service';
import { MultiLocationService } from '../multi-location.service';
import { BrandChartHeaderModule } from '../brand-chart-header/brand-chart-header.module';
import { BrandFilterContainerModule } from '../brand-filter-container/brand-filter-container.module';
import { TableModule } from '../table/table.module';
import { BrandSidebarHeaderModule } from '../brand-sidebar-header/brand-sidebar-header.module';
import { <PERSON><PERSON><PERSON><PERSON>, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { startWith } from 'rxjs/operators';
import { LocationsService } from '../../locations';
import { AccountGroupMetricService } from '../../metrics/account-group.service';
import { AccountGroup, getLocation } from '../../account-group/account-group';
import { BrandsService } from '../brands.service';
import { BrandsRequestsOverviewService, RequestReviewMetric } from './brands-requests-overview.service';
import { TimeRangePickerComponent } from '../../shared/time-range-picker.component';
import { Mode, SidepanelService, Size } from '../../navigation/sidepanel.service';
import { isMobile } from '../../core/mobile';
import { RequestsOverviewFilterComponent } from './requests-overview-filter/requests-overview-filter.component';
import { GalaxyProgressBarModule } from '@vendasta/galaxy/progress-bar';
import { StatisticDeltaComponent } from '@vendasta/reviews';

const CTR = 'ctr';
const REQUESTS_SENT = 'requests_sent';
const LINKS_CLICKED = 'links_clicked';

interface requestMetrics {
  requestSent: number;
  requestClicked: number;
  ctr: number;
}

@Component({
  selector: 'app-brands-requests-overview',
  imports: [
    BrandChartHeaderModule,
    CommonModule,
    TranslateModule,
    TableModule,
    BrandSidebarHeaderModule,
    GalaxyPageModule,
    NavTabsComponent,
    ReactiveFormsModule,
    BrandFilterContainerModule,
    MatButtonModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatTabsModule,
    TimeRangePickerComponent,
    GalaxyProgressBarModule,
    StatisticDeltaComponent,
  ],
  templateUrl: './brands-requests-overview.component.html',
  styleUrls: ['./../brands-common.component.scss', 'brands-requests-overview.component.scss'],
})
export class BrandsRequestsOverviewComponent implements OnInit, OnDestroy {
  multiLocationService = inject(MultiLocationService);
  private accountGroupService = inject(AccountGroupMetricService);
  private locationsService = inject(LocationsService);
  private brandsService = inject(BrandsService);
  private fb = inject(FormBuilder);
  requestMetricsChartData$: Observable<requestMetrics>;
  requestPreviousAnalysis$: Observable<number>;

  readonly sidePanelService = inject(SidepanelService);

  mapLocations$: Observable<BrandRow[]>;
  loadedMapLocations$: Observable<BrandRow[]>;
  tableData$: Observable<BrandRow[]>;
  search$: Observable<string>;

  gradesMeasure = CTR;

  tableColumns: MetricColumn[] = [
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.CTR',
      measureKey: CTR,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.CTR',
        displayValueType: 'percentage',
      },
      metaValueList: ['mean', 'median'],
      metaValueDefault: 'mean',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.REQUESTS_SENT',
      measureKey: REQUESTS_SENT,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.REQUESTS_SENT',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
    {
      headerTranslateKey: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.LINKS_CLICKED',
      measureKey: LINKS_CLICKED,
      metricMetadata: {
        name: 'PERFORMANCE.MULTI_LOCATION.REQUESTS.LINKS_CLICKED',
        numberPipeArgs: '1.0-0',
      },
      metaValueList: ['mean', 'median', 'sum'],
      metaValueDefault: 'sum',
    },
  ];

  form: FormGroup;

  constructor(private brandsRequestsOverviewService: BrandsRequestsOverviewService) {
    // Build measure value map for reviews
    const locationMeasureMap$ = combineLatest([
      this.locationsService.currentAccountGroupIds$,
      this.brandsRequestsOverviewService.currentByLocation$,
      this.brandsRequestsOverviewService.previousByLocation$,
    ]).pipe(
      map(([accountGroupIds, metricsCurrentData, metricsPreviousData]) => {
        const measureMap = {};
        if (accountGroupIds == null) {
          return measureMap;
        }
        accountGroupIds.forEach((accountGroupId) => {
          // Default value all locations
          measureMap[accountGroupId] = createEmptyMetricMap();
        });
        metricsCurrentData.forEach((metricData) => {
          measureMap[metricData.accountGroupId].requests_sent.value = Number(metricData.requestsSent);
          measureMap[metricData.accountGroupId].links_clicked.value = Number(metricData.linksClicked);
          measureMap[metricData.accountGroupId].ctr.value = metricData.ctr;
        });
        metricsPreviousData.forEach((previousNpsData) => {
          measureMap[previousNpsData.accountGroupId][CTR].deltaAbs =
            (measureMap[previousNpsData.accountGroupId][CTR].value || 0) - (previousNpsData.ctr || 0);
          measureMap[previousNpsData.accountGroupId][REQUESTS_SENT].deltaAbs =
            (measureMap[previousNpsData.accountGroupId][REQUESTS_SENT].value || 0) -
            (previousNpsData.requestsSent || 0);
          measureMap[previousNpsData.accountGroupId][LINKS_CLICKED].deltaAbs =
            (measureMap[previousNpsData.accountGroupId][LINKS_CLICKED].value || 0) -
            (previousNpsData.linksClicked || 0);
        });
        return measureMap;
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    const compareGroupSwitchMap: Record<string, Observable<BrandRow[]>> = {
      account_group_id: this.accountGroupService.filteredLocationsForPath$.pipe(
        map((locations) => {
          if (locations == null) {
            return null;
          }
          return Object.values(locations).map((ag: AccountGroup) => {
            return {
              accountGroup: ag,
              title: ag.companyName,
              subtitle: getLocation(ag),
            };
          });
        }),
      ),
      region: this.brandsService.childGroups$.pipe(
        map((regions) => {
          if (regions == null) {
            return null;
          }
          return regions.map((region) => {
            return {
              group: region,
              title: region.name,
            };
          });
        }),
      ),
    };

    const getExpectedGroupCopy$ = (groupBy: string) => {
      return compareGroupSwitchMap[groupBy].pipe(map((brandRows) => (brandRows ? [...brandRows] : brandRows)));
    };

    // Progressively loaded data for map/table. Location data, then measures/grades
    this.mapLocations$ = combineLatest([getExpectedGroupCopy$('account_group_id'), locationMeasureMap$]).pipe(
      map(([locationBrandRows, measures]) => {
        if (locationBrandRows == null) {
          return null;
        }
        return locationBrandRows.map((brandRow: BrandRow) => {
          brandRow.measureMap = (measures || {})[brandRow.accountGroup.accountGroupId] || createEmptyMetricMap();
          return brandRow;
        });
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.loadedMapLocations$ = this.mapLocations$.pipe(filter((locations) => !!locations));

    this.mountForm();
  }

  mountForm(): void {
    this.form = this.fb.group({
      comparison: new FormControl<string>('TABLE.NODE_LOCATION', Validators.required),
      search: new FormControl<string>(''),
    });

    this.search$ = this.form.valueChanges.pipe(
      map((changes) => changes['search']),
      startWith(''),
    );
  }

  ngOnInit() {
    this.prepareTableData();
    this.sidePanelService.setView(Size.REGULAR, Mode.SIDE, RequestsOverviewFilterComponent, false);
    if (!isMobile()) {
      this.sidePanelService.open();
    }

    this.requestMetricsChartData$ = this.brandsRequestsOverviewService.getMetricsData('').pipe(
      map((metrics: RequestReviewMetric[]) => {
        const totalRequestsSent = metrics.reduce((sum, metric) => sum + Number(metric.requestsSent), 0);
        const totalLinksClicked = metrics.reduce((sum, metric) => sum + Number(metric.linksClicked), 0);

        const percentage = totalRequestsSent > 0 ? (totalLinksClicked / totalRequestsSent) * 100 : 0;

        return {
          requestSent: totalRequestsSent,
          requestClicked: totalLinksClicked,
          ctr: percentage,
        };
      }),
    );

    this.requestPreviousAnalysis$ = this.brandsRequestsOverviewService.getMetricsData('previous').pipe(
      map((metrics: RequestReviewMetric[]) => {
        const totalRequestsSent = metrics.reduce((sum, metric) => sum + Number(metric.requestsSent), 0);
        const totalLinksClicked = metrics.reduce((sum, metric) => sum + Number(metric.linksClicked), 0);

        return totalRequestsSent > 0 ? (totalLinksClicked / totalRequestsSent) * 100 : 0;
      }),
    );
  }

  ngOnDestroy(): void {
    this.sidePanelService.close();
  }

  prepareTableData(): void {
    this.tableData$ = combineLatest([this.loadedMapLocations$, this.search$]).pipe(
      map(([tableData, search]) => {
        return tableData.filter((row) => {
          if (!row) {
            return true;
          }
          if (row.subtitle) {
            return (
              row.title.toUpperCase().includes(search.toUpperCase()) ||
              row.subtitle.toUpperCase().includes(search.toUpperCase())
            );
          }
          return row.title.toUpperCase().includes(search.toUpperCase());
        });
      }),
    );
  }

  toggleSidebar(): void {
    this.sidePanelService.toggle();
  }
}

function createEmptyMetricMap(): MeasureValueMap {
  return {
    ctr: {
      value: 0,
    },
    requests_sent: {
      value: null,
    },
    links_clicked: {
      value: null,
    },
  };
}
