@use 'design-tokens' as *;

.footer-text {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.54);
  display: flex;
  align-items: flex-start;
  margin-top: 5px;

  .error-icon {
    margin-right: 10px;
    color: $yellow;
    font-size: 20px;
  }
}

.loading-spinner {
  margin: 1em;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 7.5em;
}

.location-title-container {
  display: flex;
  flex-direction: column;
  min-width: 0;
  max-width: 100%;
}

.location-title {
  font-weight: 500;
  font-size: 16px;
}

.location-subtitle {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.54);
}

.social-icons-container {
  display: flex;
  gap: $spacing-2;
}

.social-icon {
  height: 20px;
  width: 20px;
  border-radius: 100%;
}

.social-icon.gray {
  filter: grayscale(100%);
}

.mat-column-socialServices {
  flex: 0 20%;
}

.clickable-row {
  cursor: pointer;
}
