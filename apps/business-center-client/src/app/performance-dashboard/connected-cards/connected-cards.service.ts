import { Injectable } from '@angular/core';
import { ConfigurationService } from './configuration.service';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { AccountGroupService } from '../../account-group';
import { productIds } from '../../../globals';

export interface CardMapping {
  V2CardType?: string;
  skip?: boolean;
}

const ExecCardMapping_All: Record<string, string> = {
  // GOOGLE MY BUSINESS
  queries_direct: 'bc-exec-v2-gmb',
  queries_indirect: 'skip',
  views_search: 'skip',
  views_maps: 'skip',
  actions_driving_directions: 'skip',
  actions_website: 'skip',
  actions_phone: 'skip',

  // Constant Contact
  'MP-745V5GBKVRZ3DHP6D8P8RT58KM7SHDZS': 'bc-exec-v2-ctct',

  // Reputation Management
  RM: 'bc-exec-v2-rep-reviews',
  new_review_count: 'skip',
  new_review_ratings: 'skip',
  'Average Rating of New Reviews': 'skip',
  average_sentiment: 'skip',
  new_review_content_card: 'skip',

  // Listings
  'Accurate Listings': 'skip',
  'Inaccurate Listings': 'skip',
  'Missing Listings': 'skip',
  listings_nap_accuracy: 'skip',
};
const ExecCardMapping_Private: Record<string, string> = {};

const ExecCardPopInCards_All: Record<string, string[]> = {
  customer_relations: ['crm-and-inbox-metrics'],
  reputation: ['bc-exec-v2-rep-reviews', 'reputation-management-cards'],
  listings: [
    'bc-listing-score-v2',
    'bc-listing-accuracy-and-acceptance',
    'bc-exec-v2-gmb',
    'iyp-cards',
    'citation-cards',
    'bc-exec-bing-card',
  ],
  social: ['bc-exec-v2-social-stats'],
  advertising: ['bc-exec-v2-advertising-intelligence'],
  website: ['website-pro-cards'],
  seo: ['bc-local-seo-cards', 'bc-keyword-metrics-cards', 'bc-gsc-cards'],
};
const ExecCardPopInCards_Private: Record<string, string[]> = {
  accounting: ['quickbooks'],
};

// A dict containing marketplace ids that should be hidden when data is found in Tesseract
const CardsToHideWhenDataIsFound = {
  'filter-website-pro': [productIds.websitePro],
};

@Injectable({
  providedIn: 'root',
})
export class ConnectedCardsService {
  constructor(
    private configurationService: ConfigurationService,
    private accountGroupService: AccountGroupService,
  ) {}

  readonly activeMarketplaceAppMetadata$ = this.accountGroupService.currentAccountGroup$.pipe(
    switchMap((ag) => (ag ? this.configurationService.listActiveAppsMetadata(ag.accountGroupId) : of(null))),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  readonly activeMarketplaceAppIds$ = this.activeMarketplaceAppMetadata$.pipe(
    map((appMetas) => (appMetas ? appMetas.map((am) => am.appId) : [])),
  );

  private forceMobile$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  get forceMobile$(): Observable<boolean> {
    return this.forceMobile$$.asObservable();
  }

  forceMobile(force: boolean): void {
    this.forceMobile$$.next(force);
  }

  ProcessV1CardId(v1CheckingId: string, cardType: string): string {
    if (cardType !== 'CALL_TO_ACTION') {
      if (v1CheckingId in ExecCardMapping_All) {
        return ExecCardMapping_All[v1CheckingId];
      }
      if (v1CheckingId in ExecCardMapping_Private) {
        return ExecCardMapping_Private[v1CheckingId];
      }
    }
    return 'v1-style';
  }

  GetPopInCards(category: string): string[] {
    if (category in ExecCardPopInCards_All) {
      return ExecCardPopInCards_All[category];
    }
    if (category in ExecCardPopInCards_Private) {
      return ExecCardPopInCards_Private[category];
    }
    return [];
  }

  GetCardsToHideForApp(appId: string): string[] {
    const cardsToHide = CardsToHideWhenDataIsFound[appId];
    if (cardsToHide) {
      return cardsToHide;
    }
    return [];
  }
}
