import { Component, DestroyRef, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { Observable, combineLatest, switchMap } from 'rxjs';
import { filter, map, shareReplay, take } from 'rxjs/operators';
import { partnerId, productIds } from '../../../../globals';
import { AccountGroupService } from '../../../account-group';
import { ProductService } from '../../../core/product.service';
import { CardConfig, CardDataContainer, MultiSeriesChartType } from '../../cards/interface';
import { BingInsightsService, DateBoundedData, DateBoundedDataTrend } from './bing-insights.service';

import {
  IsLocalSEOProActiveForAccountRequest,
  IsLocalSEOProActiveForAccountResponse,
  ListingProductsApiService,
} from '@vendasta/listing-products';
import { FeatureFlagService } from '@vendasta/businesses';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'bc-exec-bing-card',
  templateUrl: './bing-insights.component.html',
  styleUrls: ['./bing-insights.component.scss'],
  standalone: false,
})
export class BingInsightsComponent implements OnInit {
  public buildLink = (link: string, appId: string): string => {
    return this._buildLink(link, this.accountGroupId, appId);
  };

  _buildLink(link: string, accountGroupId: string, appId: string): string {
    return (
      `/redirect/external/?accountGroupId=${accountGroupId}&partnerId=${partnerId}` +
      `&productId=${appId || productIds.listingBuilder}&nextUrl=${encodeURIComponent(link)}`
    );
  }

  @Input()
  startDate: Date;
  @Input()
  endDate: Date;
  @Output()
  cardDataLoaded: EventEmitter<string> = new EventEmitter();

  destroyRef = inject(DestroyRef);

  isLocalSEOActive = false;

  lbSourceConfig = {
    sourceTooltip: 'Local SEO',
    sourceUrl: '',
    sourceIconUrl: '',
  };

  viewsMetricsConfig: CardConfig = {
    cardSize: 4,
    cardLayout: 'vertical',
    trackingConfig: {
      linkTrackingTag: 'bingInsights/views',
    },
    headingConfig: {
      headingTranslationKey: 'PERFORMANCE.LISTINGS.BING_INSIGHTS.VIEWS.TITLE',
      subHeadingTranslationKey: 'PERFORMANCE.LISTINGS.BING_INSIGHTS.VIEWS.SUBTITLE',
      sourceConfig: this.lbSourceConfig,
    },
    dataSourceConfig: {
      mainValue: {
        dataSourceKey: 'views',
        displayValueType: 'number',
      },
      mainValueChange: {
        dataSourceKey: 'viewsChange',
        displayValueType: 'number',
        displayChangeTrendSourceKey: 'viewsTrend',
        displayChangeTypeSourceKey: 'viewsTrendDirection',
      },
    },
    multiSourceDataConfig: [
      {
        dataDisplayType: MultiSeriesChartType.LineWithFill,
        dataTitleTranslationKey: 'COMMON.TREND.12_MONTHS',
        chartConfig: {
          chartDataType: 'date',
          showLegend: true,
          colorStepOverride: 14,
          formatting: 'month',
        },
      },
    ],
  };
  viewsMetricsData$: Observable<CardDataContainer>;

  actionsMetricsConfig: CardConfig = {
    cardSize: 4,
    cardLayout: 'vertical',
    trackingConfig: {
      linkTrackingTag: 'bingInsights/actions',
    },
    headingConfig: {
      headingTranslationKey: 'PERFORMANCE.LISTINGS.BING_INSIGHTS.ACTIONS.TITLE',
      subHeadingTranslationKey: 'PERFORMANCE.LISTINGS.BING_INSIGHTS.ACTIONS.SUBTITLE',
      sourceConfig: this.lbSourceConfig,
    },
    dataSourceConfig: {
      mainValue: {
        dataSourceKey: 'actions',
        displayValueType: 'number',
      },
      mainValueChange: {
        dataSourceKey: 'actionsChange',
        displayValueType: 'number',
        displayChangeTrendSourceKey: 'actionsTrend',
        displayChangeTypeSourceKey: 'actionsTrendDirection',
      },
    },
    multiSourceDataConfig: [
      {
        dataDisplayType: MultiSeriesChartType.LineWithFill,
        dataTitleTranslationKey: 'COMMON.TREND.12_MONTHS',
        chartConfig: {
          chartDataType: 'date',
          showLegend: true,
          colorStepOverride: 14,
          formatting: 'month',
        },
      },
    ],
  };
  actionsMetricsData$: Observable<CardDataContainer>;

  noDataFound = false;

  public accountGroupId: string;

  constructor(
    private bingInsightsService: BingInsightsService,
    private productService: ProductService,
    private accountGroupService: AccountGroupService,
    private listingProducts: ListingProductsApiService,
    private featureFlagService: FeatureFlagService,
  ) {}

  ngOnInit(): void {
    this.accountGroupService.currentAccountGroup$
      .pipe(
        take(1),
        switchMap((ag) => {
          this.accountGroupId = ag?.accountGroupId;
          return combineLatest([
            this.listingProducts.isLocalSeoProActiveForAccount(
              new IsLocalSEOProActiveForAccountRequest({ accountGroupId: ag.accountGroupId }),
            ),
            this.featureFlagService.checkFeatureFlag(partnerId, '', 'bing_insights_single_location_busines_app'),
          ]);
        }),
        map(([editionResponse, isFeatureEnabled]: [IsLocalSEOProActiveForAccountResponse, boolean]) => ({
          isFree: !editionResponse.isActive,
          isFeatureEnabled,
        })),
        take(1),
      )
      .subscribe(({ isFree, isFeatureEnabled }) => {
        if (isFree || !isFeatureEnabled) {
          this.noDataFound = true;
          return;
        }
        this.initializeInsightsData();
      });
  }

  private initializeInsightsData(): void {
    combineLatest([this.productService.activeAccessibleProducts$, this.accountGroupService.currentAccountGroup$])
      .pipe(
        filter(([products]) => !!products),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([products, accountGroup]) => {
        const listingBuilderProduct = products.filter((product) => product.productId === 'MS');
        if (listingBuilderProduct.length > 0) {
          this.isLocalSEOActive = true;
          this.lbSourceConfig.sourceIconUrl = listingBuilderProduct[0].iconUrl;
          this.lbSourceConfig.sourceTooltip = listingBuilderProduct[0].name;
          this.lbSourceConfig.sourceUrl =
            `/redirect/external/?accountGroupId=${accountGroup.accountGroupId}&partnerId=${partnerId}&` +
            `productId=MS&nextUrl=%2Fedit%2Faccount%2F${accountGroup.accountGroupId}%2Fapp%2Fanalytics%2F`;
        }
      });

    const insightsData$ = combineLatest([
      this.bingInsightsService.getCurrentAndPreviousInsights(),
      this.bingInsightsService.getHistoricalInsights(),
    ]).pipe(
      map(([currentData, allTimeData]: [DateBoundedData, DateBoundedDataTrend]) => {
        this.noDataFound = false;
        if (typeof allTimeData === 'undefined') return;

        if (allTimeData.actions[0].data.length < 3 && !this.bingInsightsService.isLimitingTo30Days()) {
          this.bingInsightsService.limitGMBInsightsTo30Days();
          this.actionsMetricsConfig.multiSourceDataConfig[0].chartConfig.formatting = undefined;
          this.viewsMetricsConfig.multiSourceDataConfig[0].chartConfig.formatting = undefined;
          this.actionsMetricsConfig.multiSourceDataConfig[0].dataTitleTranslationKey = 'COMMON.TREND.30_DAYS';
          this.viewsMetricsConfig.multiSourceDataConfig[0].dataTitleTranslationKey = 'COMMON.TREND.30_DAYS';
        } else if (allTimeData.actions[0].data.length >= 2) {
          currentData.views.chartData = allTimeData.views;
          currentData.actions.chartData = allTimeData.actions;
          this.cardDataLoadedIn();
          return currentData;
        } else {
          this.noDataFound = true;
        }
      }),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.viewsMetricsData$ = insightsData$.pipe(map((currentData) => currentData?.views));
    this.actionsMetricsData$ = insightsData$.pipe(map((currentData) => currentData?.actions));
  }

  cardDataLoadedIn(): void {
    this.cardDataLoaded.emit('data-loaded');
  }
}
