import { TestBed } from '@angular/core/testing';
import { Router, UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { Product } from '../core/product';
import { ProductService } from '../core/product.service';
import { productIds } from '../../globals';
import { EmailGuard } from './email.guard';

describe('EmailGuard', () => {
  let guard: EmailGuard;
  let productServiceMock;

  beforeEach(() => {
    productServiceMock = {
      activeProducts$: of([{ productId: productIds.customerVoice } as Product]),
    };

    TestBed.configureTestingModule({
      imports: [RouterTestingModule.withRoutes([])],
    });
    guard = new EmailGuard(productServiceMock as ProductService, TestBed.inject(Router));
  });

  describe('can activate', () => {
    it('should return true if the customer voice product is activated', () => {
      guard.canActivate().subscribe((result) => {
        expect(result).toBe(true);
      });
    });

    it('should return true if the website pro product is activated', () => {
      productServiceMock.activeProducts$ = of([{ productId: productIds.websitePro } as Product]);
      guard.canActivate().subscribe((result) => {
        expect(result).toBeInstanceOf(UrlTree);
      });
    });

    it('should return a url tree if the customer voice product is not activated', () => {
      productServiceMock.activeProducts$ = of(null);
      guard.canActivate().subscribe((result) => {
        expect(result).toBeInstanceOf(UrlTree);
      });
    });
  });
});
