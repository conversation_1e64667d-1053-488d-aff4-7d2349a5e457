import { ActivatedRoute } from '@angular/router';
import { StoryObj, moduleMetadata } from '@storybook/angular';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { of } from 'rxjs';
import { Product } from '../core';
import { ProductService } from '../core/product.service';
import { NavigationComponent } from '../navigation/navigation.component';
import { NavigationService } from '../navigation/navigation.service';
import { CitationCardsService } from '../reports/executive-report/report-section/cards/citation-cards/citation-cards.service';
import { CloudVoiceService } from '../reports/executive-report/report-section/cards/cloud-voice-cards/cloud-voice.service';
import { LocalAdsService } from '../reports/executive-report/report-section/cards/local-ads-cards/local-ads.service';
import { ExecutiveReportComponent } from './executive-report.component';
import { ExecutiveReportModule } from './executive-report.module';
import { ExecReportService } from './report-data.service';

export default {
  component: ExecutiveReportComponent,
  decorators: [
    moduleMetadata({
      imports: [ExecutiveReportModule, TranslateTestingModule.withTranslations({})],
      providers: [
        {
          provide: CitationCardsService,
          useValue: {},
        },
        {
          provide: ProductService,
          useValue: {
            marketplaceProducts$: of([
              {
                productId: 'MS',
                name: 'Listings',
              },
            ] as Product[]),
            activeAccessibleProducts$: of([]),
          },
        },
        {
          provide: ExecReportService,
          useValue: {
            getExecReportData: (accountGroupId: string, reportDate: string, category: string) =>
              category === 'other'
                ? of([
                    {
                      data: [
                        {
                          templateType: 'BASIC_NUMBER',
                          uniqueId: 'e24b6121-84c1-4408-875e-43af31ee95c5',
                          title: 'OTHER',
                          description: 'This is a test of the OTHER category',
                          value: '0',
                          change: '0',
                          createdOn: '0001-01-01T00:00:00Z',
                          initialValue: '0',
                        },
                      ],
                      marketplaceAppId: 'MP-DTRNC65FT5RJG3PHD6R4G67VDB8QGKK2',
                    },
                  ])
                : of([
                    {
                      data: [
                        {
                          templateType: 'CALL_TO_ACTION',
                          uniqueId: 'gmbInsightsCallToAction',
                          title: 'Unlock Google My Business Insights',
                          description:
                            "Activate Google My Business Insights to discover how people interact with Winston's English Pub & Grill online. Understand how people are finding you, where they're coming from, and how many of them are clicking on your phone number! Once your GMB account is connected, you will be able to view online business insights on your next report.",
                          value: '0',
                          change: '0',
                          width: 'FULL',
                          nextUrl: '/edit/account/AG-H2CGZTQ3VG/google-insights/',
                          nextUrlLabel: 'Connect Account',
                          createdOn: '0001-01-01T00:00:00Z',
                          initialValue: '0',
                        },
                        {
                          templateType: 'CALL_TO_ACTION',
                          uniqueId: 'bingInsightsCallToAction',
                          title: 'Unlock Bing Places Insights',
                          description:
                            "Activate Bing Places Insights to discover how people interact with your business online. Understand how people are finding you, where they're coming from, and how many of them are clicking on your phone number! Once your Bing Places account is connected, you will be able to view online business insights on your next report.",
                          value: '0',
                          change: '0',
                          width: 'FULL',
                          nextUrl: '/edit/account/AG-H2CGZTQ3VG/bing-insights/',
                          nextUrlLabel: 'Connect Account',
                          createdOn: '0001-01-01T00:00:00Z',
                          initialValue: '0',
                        },
                      ],
                      marketplaceAppId: 'MS',
                    },
                  ]),
          },
        },
        {
          provide: NavigationService,
          useValue: {
            setBreadcrumbs: () => {
              return;
            },
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({}),
          },
        },
        {
          provide: NavigationComponent,
          useValue: {
            pageActionsPortalContent: {
              set: () => {
                return;
              },
            },
            breadcrumbPortalContent: {
              set: () => {
                return;
              },
            },
          },
        },
        {
          provide: CloudVoiceService,
          useValue: {},
        },
        {
          provide: LocalAdsService,
          useValue: {},
        },
      ],
    }),
  ],
  args: {
    partnerId: 'BYAT',
    accountGroupId: 'AG-H2CGZTQ3VG',
    category: {
      category: 'listings',
      content: {
        title: 'Listings',
        titleTranslation: 'EXECUTIVE_REPORT.CATEGORY_TITLE.LISTINGS',
        description: 'EXECUTIVE_REPORT.CATEGORY_QUESTION.LISTINGS',
      },
    },
    frequency: 'monthly',
    reportDate: '2019-04-01T00:00:00Z',
  },
};

type Story = StoryObj<ExecutiveReportComponent>;

export const Preview: Story = {};
