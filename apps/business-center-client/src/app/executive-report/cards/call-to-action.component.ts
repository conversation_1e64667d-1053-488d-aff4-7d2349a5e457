import { Component, Signal, input, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { NavigateLinkDirective } from '../../core/mobile/navigate-link.directive';
import { CallToActionData } from '../interfaces';

@Component({
  selector: 'bc-call-to-action-card',
  template: `
    <mat-card appearance="outlined" color="primary" class="cta cta-background">
      <div class="title">{{ cardData().title | translate }}</div>
      <mat-card-content>
        {{ cardData().description | translate }}
      </mat-card-content>
      <div class="cta-footer">
        <a [id]="cardData().uniqueId" mat-flat-button [bcNavigateLink]="redirectLink()">
          {{ cardData().nextUrlLabel | translate }}
        </a>
      </div>
    </mat-card>
  `,
  styleUrls: ['./call-to-action.component.scss', '../basic/basic.scss'],
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, TranslateModule, NavigateLinkDirective],
})
export class CallToActionCardComponent {
  readonly cardData = input.required<CallToActionData>();
  readonly appId = input.required<string>();
  readonly buildLink = input.required<(link: string, appId: string) => string>();

  redirectLink: Signal<string> = computed(() => this.buildLink()(this.cardData().nextUrl, this.appId()));
}
