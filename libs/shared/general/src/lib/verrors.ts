import { HttpErrorResponse } from '@angular/common/http';

const googleErrorDetailsBadRequestType = 'type.googleapis.com/google.rpc.BadRequest';

export interface FieldViolation {
  field: string;
  description: string;
}
export function getFieldViolationsFromError(err: HttpErrorResponse): FieldViolation[] {
  if (!err?.error?.details || !Array.isArray(err.error.details)) {
    return [];
  }
  return err.error.details
    .filter((detail: Record<string, unknown>) => detail['@type'] === googleErrorDetailsBadRequestType)
    .reduce((acc: FieldViolation[], curr: { fieldViolations: FieldViolation[] }) => {
      const fieldViolations = curr?.fieldViolations.map((fv: FieldViolation) => ({
        field: fv.field,
        description: fv.description,
      }));
      acc.push(...fieldViolations);
      return acc;
    }, []);
}
