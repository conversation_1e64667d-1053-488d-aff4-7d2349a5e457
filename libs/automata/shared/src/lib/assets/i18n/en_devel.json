{"COMMON": {"USER": "User", "REQUIRED": "Required ", "NAME": "Name", "ERROR_TRY_AGAIN": "An error occurred. Please try again.", "COPIED_TO_CLIPBOARD": "Copied to Clipboard", "TOTAL": "Total", "ADD_ON": "Add-on", "PRODUCT": "Product", "UNKNOWN": "Unknown", "THIS_FIELD_REQUIRED": "This field is required", "TITLE": "Title", "BUSINESS_APP": "Business App", "AN_ACCOUNT": "an account", "AN_ORDER": "an order", "ACCOUNT": "Account", "ACCOUNTS": "Accounts", "ORDER": "Order", "ORDERS": "Orders", "USERS": "Users", "CATEGORY": "Category", "TAGS": "Tags", "EMAIL": "Email", "KEY": "Key", "VALUE": "Value", "FIELD": "Field", "NO_RESULTS_FOUND": "No results found", "BLANK": "Blank", "ACTION_LABELS": {"END": "End", "START": "Start", "CANCEL": "Cancel", "SAVE": "Save", "NEXT": "Next", "EDIT": "Edit", "UPGRADE": "Upgrade", "DELETE": "Delete", "LEARN_MORE": "Learn more", "SHOW_ALL": "Show all", "NONE_LABEL": "None", "SEARCH": "Search", "CLOSE": "Close", "UPDATE": "Update", "CONFIRM": "Confirm", "ADD": "Add", "COPY": "Copy", "GROUP": "Group", "SUBMIT": "Submit"}, "TABLE": {"COLUMN_SELECTOR": {"BUTTON": "Columns"}, "SORT": {"SELECT_COLUMN_PLACEHOLDER": "Add another column to sort by", "SELECT_COLUMN_PLACEHOLDER_EMPTY_STATE": "Choose a column to sort by", "SELECT_COLUMN_NONE": "None", "SORT_COLUMN_ORDER_DESCRIPTION_TEXT": {"SORT_BY": "Sort by", "THEN_SORT_BY": "then by"}, "SORT_TOGGLE_LABELS": {"A_TO_Z": "A to Z", "Z_TO_A": "Z to A", "_1_TO_9": "1 to 9", "_9_TO_1": "9 to 1", "FIRST_TO_LAST": "First to last", "LAST_TO_FIRST": "Last to first", "EARLIEST_TO_LATEST": "Oldest first", "LATEST_TO_EARLIEST": "Newest first"}}}, "VALIDATORS": {"INVALID_URL": "Please enter a valid URL"}, "LIFE_CYCLE_STAGES": {"LEAD": "Lead", "PROSPECT": "Prospect", "CUSTOMER": "Customer", "UNSET": "Unset"}, "MANUAL_AUTOMATIONS": "Manual automations"}, "AUTOMATIONS": {"CANCEL": "Cancel", "START": "Start", "VIEW_MORE": "View more", "SELECT": "Select", "USAGE": {"BANNER_TITLE": "Automations started this billing period", "BANNER_CTA": "Upgrade", "BANNER_OF": "{{usage}} of {{quota}}"}, "DELETE_STEP_MODAL": {"TITLE": "Delete step", "DESCRIPTION": "Are you sure you want to delete this step?", "GOAL_TITLE": "Delete goal", "GOAL_DESCRIPTION": "Are you sure you want to delete this goal?"}, "COMMON": {"INTERNAL": "Internal", "VIEW_MORE": "View More", "AUTOMATIONS": "Automations", "MANAGE_AUTOMATIONS": "Manage automations", "SYSTEM_AUTOMATIONS": "System automations", "SYSTEM_AUTOMATION": "System automation", "AUTOMATION_ACTIVITIES": "Automation activities", "AUTOMATION_HISTORY": "Automation history", "AUTOMATION_TEMPLATES": "Automation templates", "MANAGE_CLIENT_TEMPLATES": "Manage client templates", "BUSINESS_APP_AUTOMATION_TEMPLATES": "Business App automation templates", "AUTOMATION_NOT_FOUND": {"TITLE_403": "Access denied", "TITLE_OTHER": "Automation not available", "BODY_404": "The automation you requested could not be found. The link you followed may be broken, or the automation may no longer exist.", "BODY_403": "You do not have permission to view the requested automation. If you think you're seeing this page in error, contact your partner administrator or the person who gave you this link.", "BODY_OTHER": "There was a problem retrieving the automation you requested. The link you followed may be broken, or a temporary error may have occurred. If this problem persists, please contact support.", "BACK": "Back to automations"}, "YOUR_EXPERT": "Your Expert", "TITLE": "Title", "MY_AUTOMATIONS": "My automations", "TEMPLATES": "Templates", "MANAGE_TEMPLATES": "Manage templates", "BUILT_IN_AUTOMATIONS": "Built-in automations", "MANAGE_BUILT_IN_AUTOMATIONS": "Manage built-in automations", "BUILT_IN": "Built-in", "PARTNER_CREATED": "Partner created", "GLOBAL_ACTIONS": "Global actions", "ACTIONS": "Actions", "ACTION": "Action", "BUILDING_BLOCKS": "Building blocks", "INTERNAL_ACTIONS": "Internal Actions", "START": "Start", "STOP": "Stop", "DELETE": "Delete", "DELETED": "Deleted", "DETAILS": "Details", "AUTOMATION": "Automation", "ENTITY_AUTOMATION": "{{ entity_type }} automation", "ACTIVITY": "Activity", "STEP": "Step", "TIME": "Time", "STARTED_TIME": "Started Time", "ACCOUNT": "Account", "ACCOUNT_STEP": "Account Step", "ON": "On", "OFF": "Off", "TURN_ON": "Turn on", "TURN_OFF": "Turn off", "STOP_DRAINING": "Stop draining", "DUPLICATE": "Duplicate", "DISABLED_DELETE": "To delete, turn off the automation", "DISABLED_DELETE_TEMPLATE": "To delete, unpublish the template", "DISABLED_DELETE_BUILT_IN_AUTOMATION": "To delete, unpublish the built-in automation", "FORM_INVALID": "Form is invalid", "WORKFLOW": "Workflow", "ADVANCED": "Advanced", "SETTINGS": "Settings", "WHEN": "When", "IF": "If", "SAVE": "Save", "CANCEL": "Cancel", "UNKNOWN": "unknown", "UNKNOWN_USER": "An unknown user", "ALL_MARKETS": "All markets", "DRAFT": "Draft", "EARLY_ACCESS": "Early access", "EARLY_ACCESS_DISCLAIMER": "This step is currently in early access. It functions correctly but some of the visual graphics might not be displayed properly.", "LIST": "List", "DUPLICATION_WARNING_FOUND": {"TITLE": "Some steps have been altered as a result of duplicating this automation. ", "BODY": "Please confirm the automation below before continuing."}, "WARNINGS_FOUND": "Before turning on this automation, customize the template steps to better fit your business.", "ERRORS_FOUND": "There are errors on this automation. Please fix them or delete the steps for the workflow to work again.", "TOKEN_BROKEN": "Automation requires valid permissions to run. Transfer permissions to your user profile in the settings tab to authorize this automation.", "TOKEN_BROKEN_BUTTON": "Go to settings", "ASSIGNED_SALESPERSON": "Assigned salesperson", "ADDITIONAL_SALESPEOPLE": "Additional assignees", "START_ELLIPSES": "Start...", "START_AUTOMATION": "Start automation", "START_MANUAL_AUTOMATION": "Start manual automation", "GO_TO_SETTINGS": "Go to settings", "LEARN_MORE": "Learn more", "USE_THIS_TEMPLATE": "Use this template", "DUPLICATE_OF_NAME": "[DUPLICATE] {{name}}", "DESCRIPTION_OF": "Description of <b>{{name}}</b>", "AN_ENTITY": "an entity", "ENTITY": "Entity", "ENTITY_TYPE": "Entity type", "ENTITIES": "Entities", "ENTITYLESS_RUN": "Entityless run", "NUMBER": "Number", "BOOLEAN": "Boolean", "MORE_TAGS": "+{{count}}", "TYPE": "Type", "TEXT": "Text", "LAST_MODIFIED_BY": "Last updated by", "LAST_MODIFIED": "Last updated", "SUBSCRIBE": "Subscribe", "UNSUBSCRIBE": "Unsubscribe", "ORDER_ID": "Order ID", "USER_ID": "User ID", "ACCOUNT_ID": "Account ID", "PARTNER_ID": "Partner ID", "CONTACT_ID": "Contact ID", "COMPANY_ID": "Company ID", "RUN": "Run", "SALES_ORDERS": "Sales orders", "TRIGGER": "<PERSON><PERSON>", "AUTOMATION_ID": "Automation ID", "AUTOMATION_TURNED_ON": "Automation turned on", "AUTOMATION_TURNED_OFF": "Automation turned off", "AUTOMATION_NOW_DRAINING": "Automation now draining", "UNKNOWN_STEP": "Unknown step", "ERROR_LOADING": "There was an error loading the data", "ERROR_GENERIC": "Something went wrong, please try again.", "SEARCH": "Search", "EDIT_DETAILS": "Edit details", "ADD_GOAL": "Add goal", "EDIT_GOAL": "Edit goal", "VIEW_GOAL": "View goal", "GOAL": "Goal", "REFRESH": "Refresh", "REFRESH_TABLE": "Refresh the table", "CONTACT": "Contact", "CONTACTS": "Contacts", "A_CONTACT": "a contact", "COMPANY": "Company", "COMPANIES": "Companies", "A_COMPANY": "a company", "DISABLED_DUE_TO_TEMPLATE": "This field will be enabled for your customers when using the template", "BLANK": "Blank", "PLATFORM": "Platform", "PUBLISHED": "Published", "STARTING_USER": "Starting user", "SYSTEM_INITIATED": "System initiated"}, "ZOOM_CONTROL": {"CENTER": "Center yourself to the start"}, "APP_REQUIREMENTS_ALERT": {"SINGLE_APP": "This action requires {{ appName }}.", "MULTIPLE_APPS": "This action requires {{ appNames }} or {{ lastAppName }}.", "BACKUP_MESSAGE": "This action requires particular products active to function."}, "SYSTEM_AUTOMATION_DESCRIPTION": "Customize how the platform works by toggling these system automations on or off. To edit a workflow, you can duplicate it.", "STAGES": {"TITLE": "Stages", "RUNNING": "Running", "COMPLETED": "Completed", "COMPLETED_FROM_GOAL": "Completed from goal", "CANCELLED_DUE_TO_ERROR": "Cancelled due to error", "DID_NOT_RUN": "Did not run", "CANCELED": "Canceled", "CONTINUED_FROM_ERROR": "Continued on new step due to an error", "CONTINUED": "Continued running on new step"}, "MANUAL_AUTOMATIONS": {"LIST_WARNINGS": "Accounts deleted from the platform may still be on the list and will not trigger an automation.", "EMPTY_STATE_DESCRIPTION": "Start an automation manually for this account", "SELECT_AUTOMATION": "Search for a specific automation", "TITLE": "Start automation", "SELECT_TITLE": "Select manual automation", "START_TITLE": "Start manual automation", "LIST_DIALOG_TITLE": "<PERSON>gger automation for \"{{name}}\"", "NO_AUTOMATION_DESCRIPTION": "No description provided", "NO_MATCHING_AUTOMATIONS": "No results found", "EMPTY_STATE_MESSAGE": "Automations run for this account will show up here", "SELECT_TITLE_V2": "Select automation", "MODAL_INFO_V3": "Only automations with a manual trigger and are turned on will appear here.", "GO_TO_PAGE": "Go to automations page", "AUTOMATION_STARTED": "Automation {{name}} started", "EMPTY": "There are no manual automations to display. Make sure your automation uses a manual trigger and is turned on.", "START_SPECIFIC_AUTOMATION": "Select the '{{name}}' automation"}, "CONTINUE_ON_NEW_STEP": {"CONTINUE_ON_NEW_STEP": "Continue on new step", "CONTINUE_ON_SELECTED_STEP": "Continue on selected step", "SELECT_STEP": "Select a step to continue on", "SELECT_STEP_DROPDOWN": "Select workflow step", "SELECT_STEP_HINT": "Only steps that are named will show up here", "ERROR_GENERIC": "Something went wrong, please try again.", "SUCCESS": "Continuing automation from selected step", "SHOW_MORE_DETAILS": "Show more details"}, "PREVIEW_TEMPLATE_DIALOG": {"TAGS": "Tags", "USE_TEMPLATE": "Use template"}, "END_RUN": {"END_RUN": "End run", "END_AUTOMATION_RUN": "End automation run", "END_RUN_CONFIRMATION": "End the automation run before it completes the delay step?", "END_RUN_INFO": "Ending an automation run may take a few minutes to process", "PROCESSING_RUN_CANCELLATION": "Processing run cancellation...", "SUCCESS": "Processing run cancellation", "ERROR_GENERIC": "Something went wrong, please try again.", "ERROR_NO_LONGER_DELAYED": "Unable to end run - The automation is no longer in a delay step", "REFRESH_PAGE": "Refresh page"}, "CREATE": {"CREATE_AUTOMATION": "Create automation", "CREATE_AUTOMATION_FROM_TEMPLATE": "Use template", "CREATE_BLANK_AUTOMATION": "Create new automation", "PREVIEW": "Preview", "UNTITLED": "Untitled", "BLANK_TEMPLATE": "Blank template", "CHOOSE_TEMPLATE": "Choose a template", "CREATE_TEMPLATE": "Create template", "NEW_TEMPLATE": "New template", "CREATE_BUILT_IN_AUTOMATION": "Create built-in automation", "MARKET_HINT_V2": "Certain market-specific options will not be available", "NEW_BUILT_IN_AUTOMATION": "New built-in automation", "START_FROM_SCRATCH": "Start a new automation from scratch", "USE_A_TEMPLATE": "or use a template", "SELECT_A_MARKET": "Select a market", "CHOOSE_AN_AUTOMATION_TYPE": "Choose an automation type", "CHOOSE_AN_AUTOMATION_TYPE_DESCRIPTION": "This determines what the automation will be based on. You won’t be able to change this later.", "SELECT_A_MARKET_DESCRIPTION": "Select which market the automation should be created in. You won’t be able to change the market later.", "SELECT_A_MARKET_DUPLICATION_DESCRIPTION": "Select which market the automation should be duplicated into. You won't be able to change the market later."}, "PERMISSIONS": {"WARNING_TITLE": "Must have the appropriate permissions to turn on the automation", "WARNING_MESSAGE": "An admin must grant you the permissions on your user profile, or have a different user with the required permissions turn on the automation.", "TRANSFER_TITLE": "Appropriate permissions required to transfer automation to your profile", "TRANSFER_MESSAGE": "An admin must grant you the permissions on your user profile"}, "TABLE": {"BANNER": {"TITLE": "A faster, smarter way to work", "DESCRIPTION": "Automate your sales and marketing processes in a few simple clicks.", "GET_STARTED": "Get started with templates"}, "NO_RESULTS": "No automations found for filters", "FILTERS": {"ADMIN": "Admin", "FILTERS": "Filters", "MARKET": "Market", "SEARCH": "Search...", "SHOW_DISABLED": "Show disabled options", "STATUS": "Status", "ON": "On", "OFF": "Off", "DRAINING": "Draining", "LAST_UPDATED": "Last updated", "LAST_UPDATED_BY": "Last updated by", "VIEW_DETAILS": "View details", "ALL_MARKETS": "All markets"}, "ERROR_CONTINUE_DISCLAIMER": "When a step errors within an automation run, the step will be skipped and the run will continue.", "ERROR_STOP_DISCLAIMER": "When a step errors within an automation run, the run will stop. These runs can be restarted.", "VIEW_ERROR_SETTINGS": "View error settings", "VIEW_ACTIVITY_OF_RUN": "View all activities of this run"}, "NO_AUTOMATIONS": {"TITLE": "A faster, smarter way to work", "DESCRIPTION": "Create automated, personalized workflows for your business. Attract more customers, sell more effectively, and watch your business grow.", "GET_STARTED": "Get started", "LEARN_MORE": "Learn more"}, "TEMPLATES_TABLE": {"BANNER": "Templates that are published here are available for all partners to use.", "NO_RESULTS": "No templates found for filters", "FILTERS": {"FILTERS": "Filters", "SEARCH": "Search...", "STATUS": "Status", "PUBLISHED": "Published", "UNPUBLISHED": "Unpublished", "DRAFT": "Draft", "LAST_UPDATED": "Last updated", "LAST_UPDATED_BY": "Last updated by"}, "COLUMNS": {"ACTIONS": "Actions"}}, "MANAGE_BUILT_IN_AUTOMATIONS_TABLE": {"BANNER": "Automations that are published here are On for all partners.", "NO_RESULTS": "No built-in automations found for filters", "FILTERS": {"FILTERS": "Filters", "STATUS": "Status", "CONTEXT": "Context", "PUBLISHED": "Published", "DRAFT": "Draft", "LAST_UPDATED": "Last updated", "LAST_UPDATED_BY": "Last updated by"}, "ALL_MARKETS": "All markets", "SOME_MARKETS": "{{numerator}} of {{denominator}} markets", "MARKET_DIALOG_TITLE": "Markets for <b>{{name}}</b>", "MARKET_IS_ON": "This automation is ON for the following markets:", "MARKET_IS_OFF": "This automation is OFF for the following markets:"}, "BUILT_IN_AUTOMATIONS_TABLE": {"NO_RESULTS": "No built-in automations found for filters", "FILTERS": {"FILTERS": "Filters", "STATUS": "Status", "ON": "On", "OFF": "Off", "LAST_UPDATED": "Last updated"}}, "COMBINED_PAGE": {"DESCRIPTION": "Create automated, personalized workflows for your business.", "NO_RESULTS_FOUND": "No results found", "STARTING_REGULAR_AUTOMATION_ERROR": "Could not turn on automation, ensure automation is valid and try again."}, "EDITOR": {"NOTE_POPOVER": {"TITLE": "Notes", "EDIT": "Edit"}, "ADD_STEP": {"PASTE_HERE": "Paste here"}, "ADD_STEPS_LIST": {"ACTIONS": "Actions", "ACTION_SETS": "Action sets", "MISSING_INFO": "One or more actions are missing required information", "LOADING_SNIPPETS_ERROR": "Error loading Action Sets, please refresh and try again.", "EMPTY_STATE_TITLE": "Create your first action set", "EMPTY_STATE_BODY": "Save time with reusable action sets. Create an action set from any group you have created."}, "RETURN_VALUES": {"RETURN_VALUES": "Payload data", "RESPONSE_BODY": "Response body", "RETURN_VALUES_DESCRIPTION": "Specify the data from the webhook response that should be passed to other steps", "RETURN_VALUES_TRIGGER_DESCRIPTION": "Define the structure of the data that will be passed in via the webhook. The data type is required to help make the automation safer.", "KEY_NAME": "Key name", "KEY_NAME_REQUIRED": "A key name is required", "TYPE": "Type", "TYPE_REQUIRED": "A type is required", "ADD_VALUE": "+ Add data value", "DATA_TYPES": {"BOOLEAN": "Boolean", "STRING": "String", "ACCOUNT_ID": "Account Id", "ORDER_ID": "Order Id", "UNKNOWN": "Unknown", "USER_ID": "User Id", "OPPORTUNITY_ID": "Opportunity Id", "COMPANY_ID": "Company Id", "CONTACT_ID": "Contact Id", "JSON": "JSON", "PRODUCT": "Product Id", "TASK": "Task Id", "NOTE": "Note Id", "CRM_EMAIL": "Email activity Id", "CRM_CALL": "Call activity Id", "CRM_MEETING": "Meeting activity Id", "CRM_SMS": "SMS activity Id", "CRM_INBOX": "Inbox activity Id", "CRM_LINKEDIN": "Linkedin activity Id", "INTEGRATION_CONNECTION": "Integration Connection Id", "NUMBER": "Number", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "DATETIME": "Datetime", "INVOICE": "Invoice", "CRM_CUSTOM_OBJECT_ID": "Custom object Id"}, "EXPECTED_PAYLOAD": "Expected payload", "KEY": "key", "VALUE": "value", "INVALID_DATATYPE": "Payload should include a valid entity, e.g., Company Id, Contact Id, Account Id, etc."}, "CLEAR_FILTERS": {"HEADER": "Change automation trigger?", "BODY": "Changing the trigger will clear the current trigger conditions (if any). This can't be undone.", "CONTINUE": "Change trigger"}, "AUTOMATION": {"START_EDITING": "To edit this workflow, turn off the automation", "DESCRIPTION": {"EXAMPLE": "Describe the automation"}, "BUILT_IN_ORIGIN": {"TURNED_ON_BANNER": "This automation was duplicated from <b>{{built_in_automation_name}}</b>, which is currently ON.", "TURNED_OFF_BANNER": "This automation was duplicated from <b>{{built_in_automation_name}}</b>, which is currently OFF.", "VIEW": "View original automation"}, "MISSING_WORKFLOW": "To turn on this automation, add steps to the workflow"}, "TEMPLATE": {"START_EDITING": "To edit this workflow, unpublish the template", "DESCRIPTION": {"EXAMPLE": "Describe the template"}}, "BUILT_IN_AUTOMATION": {"BANNER": "This is a built-in automation. To edit the workflow, make a duplicate.", "START_EDITING": "To edit this workflow, unpublish the built-in automation", "DESCRIPTION": {"EXAMPLE": "Describe the built-in automation"}}, "NO_RESULTS": "No results found", "NOT_ENOUGH_INFO": "There are actions in this automation that require more information than this trigger provides.", "MORE_INFO_NEEDED": "This requires more information from previous automation steps.", "MARKET_RELIANT": "This step can't be used for all markets. To use this step, duplicate the automation into a specific market.", "MORE_INFO_NEEDED_SHORT": "Requires more information.", "WRONG_AUTOMATION_TYPE_TRIGGER": "This trigger is not supported for the selected automation/data type", "NO_ACCESS_TO_RUN": "You do not have access to run this step.", "NO_ACCESS_TO_RUN_SHORT": "Requires access to run.", "TITLE": "Edit automation", "STEP": {"EDIT": "Edit step", "ADD": "Add a step"}, "STEP_ID": "Step name", "GOAL_NAME": "Goal name", "AUTOMATION_GOAL": "Automation goal", "CANNOT_ADD_GOAL_WHEN_RUNNING": "You cannot add or edit a goal while the automation is running, you will need to turn it off first", "STEP_ID_MAX_LENGTH": "Step name must be less than 50 characters", "DATA_PASSING": "Data passing", "DATA_PASSING_DESCRIPTION": "Data from this step is available to use in following steps. <a target=\"_blank\" href='https://support.vendasta.com/hc/en-us/articles/4460858604695'>Learn more</a>", "DATA_PASSING_DESCRIPTION_WHITE_LABELED": "Data from this step is available to use in following steps.", "DATA_PASSING_SELECTOR": "Allow data passing", "DATA_PASSING_REQUIRED": "Step must have an identifier to pass data", "DEFAULT_UPGRADE_SUBSCRIPTION_TITLE": "A faster, smarter way to work", "DEFAULT_UPGRADE_SUBSCRIPTION_DESCRIPTION": "This step is unavailable on your current subscription plan. Upgrade today to unlock this step and other premium features.", "NO_EDITING_WHILE_RUNNING": "To edit this step, turn off the automation", "NO_EDITING_WHILE_DRAINING": "Stop draining or create a duplicate to edit this automation", "NO_EDITING_WHILE_TEMPLATE_PUBLISHED": "To edit this step, unpublish the template", "NO_EDITING_WHILE_BUILT_IN_AUTOMATION_PUBLISHED": "To edit this step, unpublish the built-in automation", "NO_EDITING_BUILT_IN_AUTOMATIONS": "To edit this step, duplicate the built-in automation.", "DESCRIPTION": {"EXAMPLE": "Describe the automation", "PROMPT": "Click to add a description.", "LABEL": "Description", "HINT_LABEL": "Maximum 500 characters.", "GENERATE": "Generate description", "REGENERATE": "Regenerate description"}, "NOTES": "Notes", "EDIT_NAME_DIALOG": {"NO_DUPLICATE_IDS_ERROR_V2": "You cannot have duplicate IDs, please edit the ID and try again.", "ERROR_V2": "Could not save step ID, please refresh and try again.", "TITLE_V3": "Update step name", "INPUT_LABEL_V3": "Step name"}, "TAGS": "Tags", "ADD_TAGS": "Add tags...", "ERRORS": {"CANT_DELETE_BRANCH": "Cannot delete decision points that have steps in one or more branches. Delete these steps first.", "CANT_DELETE_BRANCH_V2": "This step can't be deleted while there are steps in one or more branches. Delete the branch steps first.", "TRIGGER_MISSING_DATA_FOR_STEP": "<PERSON>gger doesn't provide data for highlighted step", "TRIGGER_MISSING_DATA_FOR_FILTER": "Trigger doesn't provide data for highlighted filter", "STEP_MISSING_DATA": "Previous steps don't provide data for highlighted step", "ERROR_STARTING_AUTOMATION": "There was an error starting the automation, please try again.", "PERMISSION_DENIED_STARTING_AUTOMATION": "You do not have permission to run this automation.", "UNABLE_TO_LOAD_FILTER": "Unable to load filter", "CANT_ADD_END_NODE_WITHIN_AUTOMATION": "Cannot end automation with subsequent steps", "CANT_ADD_TERMINATING_NODE_WITHIN_AUTOMATION": "This step must be placed at the end of a workflow path", "PREVIOUS_STEP_REFERENCE_ERROR": "Action sets cannot contain if/else branches or actions that use data from previous steps.", "REQUIRED": "This is required.", "CANT_DELETE_BRANCH_REFERENCE": "This step can't be deleted while there are other steps that reference this one. Change or delete those steps first", "CANT_DELETE_BRANCH_BECAUSE_OF_GOAL_REFERENCE": "This step can't be deleted because the goal is referencing it. Change or delete the goal then try again.", "CANT_CHANGE_TRIGGER_BECAUSE_OF_GOAL_REFERENCE": "The trigger can't be changed because the goal is referencing it. Change or delete the goal then try again.", "TOO_MANY_JUMPS": "A maximum of 10 jump steps are allowed in an automation", "CANNOT_ALTER_PASSED_PARAMETERS": "Data that is passed by this step cannot be altered or removed while there are other steps that reference it. Change or delete those steps first"}, "CURRENTLY_WAITING": {"DIALOG_TITLE": "Entities currently waiting", "NODE_LABEL": "Currently waiting: <b>{{ numWaiting }}</b>", "STARTED_WAITING": "Started Waiting"}, "BRANCH": {"YES": "Yes", "NO": "No", "A": "A", "B": "B", "EVENT_HAPPENED": "Event happened", "EVENT_DID_NOT_HAPPEN": "Event did not happen", "DID_NOT_PASS_FILTER": "Did not pass filter", "ADD_ANOTHER": "Add another branch", "PLUS_ADD_ANOTHER": "+ Add another branch", "NO_CONDITIONS_MET": "No conditions met", "BRANCH_NUMBER": "Branch {{ number }}", "FIRST_CHECK_BRANCH": "First, check <b>{{ branchName }}</b>", "ELSE_CHECK_BRANCH": "<PERSON><PERSON>, check <b>{{ branchName }}</b>"}, "TRIGGERS": {"ENTERED_AUTOMATION": "Entered automation", "INTERNAL_TRIGGERS": "Internal Triggers", "COMMON": {"ADD": "Add a trigger", "EDIT": "Edit trigger", "ANY": "Any"}, "ADD": "Add a trigger to start building this automation", "START_WHEN": "Start the automation when:", "ADDED_TO_LIST": {"ADDED_TO_LIST": "An account is added to a list", "DESCRIPTION": "Triggered when an account is added to a list", "CONFIG_ERROR": "Choose new list", "WHEN_ACTION": "When an account is added to the <a target=\"_blank\" href=\"/action-lists/{{ actionListId }}/accounts\" style=\"font-weight: 500;\">{{ actionListName }}</a> list", "WHEN_EMPTY_ACTION": "When an account is added to a list", "WAIT_UNTIL_ACTION": "Wait until the account is added to the <a target=\"_blank\" href=\"/action-lists/{{ actionListId }}/accounts\" style=\"font-weight: 500;\">{{ actionListName }}</a> list", "WAIT_UNTIL_EMPTY_ACTION": "Wait until the account is added to a list", "ACTION": "An account is added to the <a target=\"_blank\" href=\"/action-lists/{{ actionListId }}/accounts\" style=\"font-weight: 500;\">{{ actionListName }}</a> list", "ACTIVITY": "Account was added to the <a target=\"_blank\" href=\"/action-lists/{{ actionListId }}/accounts\" style=\"font-weight: 500;\">{{ actionListName }}</a> list"}, "USER_ASSOCIATION": {"NAME": "A user is added to an account", "WHEN_ACTION": "When a user is added to an account", "WAIT_UNTIL_ACTION": "Wait until a user is added to the account", "ACTIVITY_SUBTITLE": "<b>{{ userName }}</b> was added to <b>{{ businessName }}</b>", "ACTIVITY_SUBTITLE_GENERIC": "A user was added to the account</b>"}, "USER_ONLINE": {"USER_ONLINE": "A user is active in {{businessAppName}}", "WHEN_ACTION": "When a user is active in  {{businessAppName}}", "ACTIVITY": "User was active in {{businessAppName}}", "WARN_TITLE": "This is a high-frequency trigger.", "MORE_INFO_V2": "This automation runs approximately once per minute while the user is online in Business App. This won't be triggered by admins, salespeople, or digital agents visiting Business App.\nWe recommend also using a Rate Filter to limit how often an automation step will run."}, "SNAPSHOT_CREATED": {"SNAPSHOT_CREATED": "A Snapshot Report is created/refreshed", "WHEN_ACTION": "When a Snapshot Report is created/refreshed", "WAIT_UNTIL_ACTION": "Wait until a Snapshot Report is created/refreshed", "ACTIVITY": "A Snapshot Report was created/refreshed for <b>{{ businessName }}</b>", "A_BUSINESS": "a business", "RATE_LIMITED": "Snapshot was not created as the step was rate limited. Ending the automation"}, "SNAPSHOT_OPENED": {"SNAPSHOT_OPENED": "A Snapshot Report is opened", "WHEN_ACTION": "When a Snapshot Report is opened", "WAIT_UNTIL_ACTION": "Wait until a Snapshot Report is opened", "ACTIVITY": "A Snapshot Report was opened for <b>{{ businessName }}</b>", "A_BUSINESS": "a business"}, "BILLING_PURCHASE": {"PURCHASE_SUCCESSFUL": "You are billed wholesale for a product", "CONFIG_ERROR": "There is an issue with your step, please delete it or configure it.", "APPROVAL_STATES": {"APPROVED": "Approved", "NOT_APPROVED": "Not Approved"}, "ACTION": {"WHEN_BILLING_DESCRIPTION": "When you are billed the wholesale cost for a product that an account has purchased, and the approval state of the charge is:", "WHEN_BILLING_SUCCESS": "When you <b>are</b> successfully billed wholesale for a product that an account has purchased", "WHEN_BILLING_FAIL": "When you <b>are not</b> successfully billed wholesale for a product that an account has purchased", "WHEN_BILLING": "When you <b>are</b> or <b>are not</b> successfully billed wholesale for a product that an account has purchased", "EXCLUDED_PRODUCTS_DESCRIPTION": "The following products will not activate this trigger", "PRODUCTS_YOU_HAVE_CREATED": "Products you've created", "SNAPSHOT_CREATE": "Snapshot Report Create", "SNAPSHOT_REFRESH": "Snapshot Report Refresh", "REP_MAN_TRIAL": "Reputation Management (Trial)", "LISTING_BUILDER_BASE": "Listing Builder", "REP_MAN_EXPRESS": "Reputation Management Express", "SOCIAL_MRKT_EXPRESS": "Social Marketing Express", "WEBSITE_PRO_EXPRESS": "Website Express", "CUSTOMER_VOICE": "Customer Voice Express", "AD_INTEL_EXPRESS": "Advertising Intelligence", "CALENDAR_HERO": "CalendarHero"}, "ACTIVITY": {"ACCOUNT_BILLED": "You were successfully billed wholesale for <b>{{ productName }}</b> purchased by <b>{{ accountName }}</b>", "ACCOUNT_NOT_BILLED": "You were <b>not</b> successfully billed wholesale for <b>{{ productName }}</b> purchased by <b>{{ accountName }}</b>", "ITEM_NOT_FOUND": "The purchase record could not be found", "UNKNOWN_ERROR": "An unknown error occurred"}}, "CLAIM_ACCOUNT": {"CLAIM_ACCOUNT": "An account group user is claimed", "WHEN_ACTION": "When an account group user is claimed", "WAIT_UNTIL_ACTION": "Wait until an account group user is claimed", "ACTIVITY": "An account group user was claimed for <b>{{ businessName }}</b>", "A_BUSINESS": "a business"}, "VENDASTA_PAYMENTS_START_SETUP": {"VENDASTA_PAYMENTS_START_SETUP": "A partner starts Vendasta Payments setup", "WHEN_ACTION": "When a partner starts Vendasta Payments setup", "WAIT_UNTIL_ACTION": "Wait until the partner starts Vendasta Payments setup", "ACTIVITY": "Vendasta Payments setup was started by <b>{{ userName }}</b> for <b>{{ businessName }}</b>", "A_BUSINESS": "a business", "A_USER": "a user"}, "PRODUCT_ACTIVATION": {"PRODUCT_ACTIVATED": {"PRODUCT_ACTIVATED": "A product is activated", "WHEN_ACTION": "When <b>{{ productName }}</b> is activated", "WAIT_UNTIL_ACTION": "Wait until <b>{{ productName }}</b> is activated", "ACTION": "<b>{{ productName }}</b> is activated", "ACTIVITY": "<b>{{ productName }}</b> was activated for <b>{{ businessName }}</b>", "A_BUSINESS": "a business", "A_PRODUCT": "A product"}, "PRODUCT_DEACTIVATED": {"PRODUCT_DEACTIVATED": "A product is deactivated", "WHEN_ACTION": "When <b>{{ productName }}</b> is deactivated", "WAIT_UNTIL_ACTION": "Wait until <b>{{ productName }}</b> is deactivated", "ACTION": "<b>{{ productName }}</b> is deactivated", "ACTIVITY": "<b>{{ productName }}</b> deactivated"}, "SELECT_PRODUCT": "Select product", "ANY_PRODUCT": "Any product", "PRODUCT_OR_ADDON": "a product or add-on", "PRODUCT_NOT_FOUND": "product ({{ appId }}) not found", "SELECT_PRODUCT_TYPE": "Select product type", "EDITION_OR_ADDON": "Any edition or add-on", "SELECT_ADDON": "Select add-on", "ANY_ADDON": "Any add-on", "ALL_ADDONS": "All add-ons", "TRIGGER": "Trigger on...", "PRODUCT": "Product", "NO_ADDONS": "Product add-on (no add-ons)", "PRODUCT_ADDON": "Product add-on", "PRODUCT_EDITION": "Product edition", "SELECT_EDITION": "Select edition", "ANY_EDITION": "Any edition", "SELECT_TRIAL_OPTION": "Select trial option", "ANY_OPTION": "Any option", "TRIAL": "Trial", "NO_TRIAL": "No Trial", "CONFIG_ERROR_NOT_ENABLED": "The selected product is not enabled.<br>Choose a new product or change trigger", "CONFIG_ERROR_TEXT": "Choose a new product or change trigger", "ACTION": "When <b>{{ productName }}</b> is activated for an account"}, "INBOX_MESSAGE_SENT": {"INBOX_MESSAGE_SENT": "An Inbox message is sent to (or received from) an account", "DESCRIPTION": "In a conversation with an account:", "NOTE": "Note: This trigger will only fire for conversations that include one of your accounts; it will not fire for conversations with no accounts – such as a conversation with <PERSON><PERSON><PERSON><PERSON> or a <PERSON><PERSON>or.", "WHEN_ACTION": "When an <strong>{{type}}</strong> in a conversation with an account", "ACTIVITY": "An Inbox message was sent to (or received from) an account", "TRIGGER_OPTIONS": "Trigger options", "TRIGGER_OPTION": {"BOTH_WAYS": "a new message is sent or received", "OUTBOUND_ONLY": "a new message is sent by someone from your organization", "INBOUND_ONLY": "a new message is received from someone in another organization"}}, "INBOX_MESSAGE_SENT_TO_VMF": {"INBOX_MESSAGE_SENT_TO_VMF": "An inbox message is sent to (or received from) a partner", "DESCRIPTION": "In a VMF conversation with a partner:", "NOTE": "Note: This trigger will only fire for conversations that include both VMF and a partner associated with an account.", "WHEN_ACTION": "When an <strong>{{type}}</strong> in a VMF conversation with a partner", "ACTIVITY": "An inbox message was sent to (or received from) a partner", "TRIGGER_OPTIONS": "Trigger options", "TRIGGER_OPTION": {"BOTH_WAYS": "a new message is sent or received", "OUTBOUND_ONLY": "a new message is sent by someone from your organization", "INBOUND_ONLY": "a new message is received from someone in another organization"}}, "ACCOUNT_GROUP_CREATED": {"ACCOUNT_GROUP_CREATED": "An account is created", "WHEN_ACTION": "When an account is created", "ACTIVITY": "Account was created"}, "SALES_ACTIVITY_CREATED": {"SALES_ACTIVITY_CREATED": "A sales activity is created", "WHEN_ACTION": "When a sales activity is created", "ACTIVITY": "Sales activity was created", "ACTION": "Action", "ACTIVITY_TITLE": "Activity", "ANY": "Any", "ACTIONS": {"EMAIL_SENT": "Email sent", "EMAIL_RECEIVED": "<PERSON><PERSON> received", "INBOUND_CALL": "Inbound call", "OUTBOUND_CALL": "Outbound call", "MEETING": "Meeting", "OTHER": "Other"}, "CONNECTED": "Connected", "CONNECTED_STATUS": {"CONNECTED": "Connected", "NOT_CONNECTED": "Not connected"}}, "CRM_CONTACT_CREATED": {"NAME": "A contact is created", "ACTION": "When a contact is created", "ACTIVITY": "A contact was created", "DESCRIPTION": "When a contact is created"}, "CRM_CONTACT_CREATED_OR_MODIFIED": {"NAME": "A contact is created or modified", "ACTION": "When a contact is {{ action }}", "ACTIVITY": "A contact was created or modified", "DESCRIPTION": "When a contact is created or modified", "WAIT_UNTIL": "Wait until the contact is {{ action }}"}, "CONTACT_CREATED_VIA_WEB_CHAT": {"NAME": "<PERSON> captures a lead", "ACTION": "When <PERSON> captures a lead", "ACTIVITY": "<PERSON> captured a lead", "DESCRIPTION": "This trigger runs when a Web Chat summary is generated for a lead. This could be for a new Contact that Web Chat created, or for an existing Contact."}, "CONTACT_FOLLOW_UP_REQUESTED": {"NAME": "A contact requests a follow up (AI conversation event)", "ACTION": "When AI analysis of a conversation determines that the contact requested a follow up", "ACTIVITY": "AI determines that a contact explicitly or implicitly requested a follow-up", "DESCRIPTION": "This trigger runs when AI determines that a contact explicitly or implicitly requested a follow-up"}, "CRM_CONTACT_ADDED_TO_LIST": {"NAME": "A contact is added to a list", "ACTION": "When a contact is added to a list", "ACTION_WITH_DATA": "When a contact is added to <strong>{{listName}}</strong>", "ACTIVITY": "A contact was added to a list", "DESCRIPTION": "When a contact is added to a list", "CONFIG_ERROR": "There is an issue with your step, please delete it or configure it.", "WAIT_UNTIL": "Wait until the contact is added to a list"}, "CRM_CONTACT_REMOVED_FROM_LIST": {"NAME": "A contact is removed from a list", "ACTION": "When a contact is removed from a list", "ACTION_WITH_DATA": "When a contact is removed from <strong>{{listName}}</strong>", "ACTIVITY": "A contact was removed from a list", "DESCRIPTION": "When a contact is removed from a list", "WAIT_UNTIL": "Wait until the contact is removed from a list"}, "CRM_COMPANY_ADDED_TO_LIST": {"NAME": "A company is added to a list", "ACTION": "When a company is added to a list", "ACTION_WITH_DATA": "When a company is added to <strong>{{listName}}</strong>", "ACTIVITY": "A company was added to a list", "DESCRIPTION": "When a company is added to a list", "WAIT_UNTIL": "Wait until the company is added to a list"}, "CRM_COMPANY_REMOVED_FROM_LIST": {"NAME": "A company is removed from a list", "ACTION": "When a company is removed from a list", "ACTION_WITH_DATA": "When a company is removed from <strong>{{listName}}</strong>", "ACTIVITY": "A company was removed from a list", "DESCRIPTION": "When a company is removed from a list", "WAIT_UNTIL": "Wait until the company is removed from a list"}, "CRM_COMPANY_CREATED_OR_MODIFIED": {"NAME": "A company is created or modified", "ACTION": "When a company is {{ action }}", "ACTIVITY": "A company was created or modified", "DESCRIPTION": "When a company is created or modified", "WAIT_UNTIL": "Wait until the company is {{ action }}"}, "CRM_CUSTOM_OBJECT_CREATED_OR_MODIFIED": {"NAME": "A custom object is created or modified", "ACTION": "When a custom object is {{ action }}", "ACTIVITY": "A custom object was created or modified", "DESCRIPTION": "When a custom object is created or modified", "WAIT_UNTIL": "Wait until the custom object is {{ action }}"}, "CRM_OPPORTUNITY_CREATED_OR_MODIFIED": {"NAME": "An opportunity is created or modified", "ACTION": "When an opportunity is {{ action }}", "ACTIVITY": "An opportunity was created or modified", "DESCRIPTION": "When an opportunity is created or modified", "WAIT_UNTIL": "Wait until the opportunity is {{ action }}"}, "CREATED_MODIFIED": {"DESCRIPTION": "If multiple fields are selected, the automation will trigger if at least one of them is modified."}, "CRM_COMPANY_CREATED": {"NAME": "A company is created", "ACTION": "When a company is created", "ACTIVITY": "A company was created", "DESCRIPTION": "When a company is created"}, "CRM_TASK_CREATED_CONTACT": {"NAME_V2": "A CRM sales task is created or modified for a contact", "DESCRIPTION": "When a task has the following action for a contact", "ACTION_V2": "When a CRM sales task is <strong>{{action}}</strong> for a contact", "ACTIVITY_V2": "A CRM sales task was <strong>{{action}}</strong> for a contact", "SELECT_LABEL": "Action", "CREATED": "Created", "MODIFIED": "Modified", "CREATED_OR_MODIFIED": "Created or modified", "WAIT_UNTIL": "Wait until a sales task is {{ action }} on the contact"}, "CRM_TASK_OVERDUE_CONTACT": {"NAME": "A CRM sales task is overdue for a contact", "ACTION": "When a CRM sales task is overdue for a contact", "ACTIVITY": "A CRM sales task is overdue for a contact", "WAIT_UNTIL": "Wait until a sales task is overdue on the contact"}, "CRM_NOTE_CREATED_CONTACT": {"NAME": "A note activity is created or modified for a contact", "DESCRIPTION": "When a note has the following action for a contact", "ACTION": "When a note is <strong>{{action}}</strong> for a contact", "ACTIVITY": "A note was <strong>{{action}}</strong> for a contact", "WAIT_UNTIL": "Wait until a note is {{action}} on the contact"}, "CRM_NOTE_CREATED_COMPANY": {"NAME": "A note activity is created or modified for a company", "ACTION": "When a note is <strong>{{action}}</strong> for a company", "ACTIVITY": "A note was <strong>{{action}}</strong> for a company", "WAIT_UNTIL": "Wait until a note is {{action}} on the company"}, "CRM_CALL_CREATED_CONTACT": {"NAME": "A call activity is created or modified for a contact", "ACTION": "When a call activity is <strong>{{action}}</strong> for a contact", "ACTIVITY": "A call activity was <strong>{{action}}</strong> for a contact", "WAIT_UNTIL": "Wait until a call is {{action}} on the contact"}, "CRM_CALL_CREATED_COMPANY": {"NAME": "A call activity is created or modified for a company", "ACTION": "When a call activity is <strong>{{action}}</strong> for a company", "ACTIVITY": "A call activity was <strong>{{action}}</strong> for a company", "WAIT_UNTIL": "Wait until a call is {{action}} on the company"}, "CRM_MEETING_CREATED_CONTACT": {"NAME": "A meeting activity is created or modified for a contact", "ACTION": "When a meeting activity is <strong>{{action}}</strong> for a contact", "ACTIVITY": "A meeting activity was <strong>{{action}}</strong> for a contact", "WAIT_UNTIL": "Wait until a meeting is {{action}} on the contact"}, "CRM_MEETING_CREATED_COMPANY": {"NAME": "A meeting activity is created or modified for a company", "ACTION": "When a meeting activity is <strong>{{action}}</strong> for a company", "ACTIVITY": "A meeting activity was <strong>{{action}}</strong> for a company", "WAIT_UNTIL": "Wait until a meeting is {{action}} on the company"}, "CRM_EMAIL_CREATED_CONTACT": {"NAME": "An email activity is created or modified for a contact", "ACTION": "When an email activity is <strong>{{action}}</strong> for a contact", "ACTIVITY": "An email activity was <strong>{{action}}</strong> for a contact", "WAIT_UNTIL": "Wait until an email is {{action}} on the contact"}, "CRM_EMAIL_CREATED_COMPANY": {"NAME": "An email activity is created or modified for a company", "ACTION": "When an email activity is <strong>{{action}}</strong> for a company", "ACTIVITY": "An email activity was <strong>{{action}}</strong> for a company", "WAIT_UNTIL": "Wait until an email is {{action}} on the company"}, "CRM_TASK_CREATED_COMPANY": {"NAME_V2": "A CRM sales task is created or modified for a company", "DESCRIPTION": "When a task has the following action for a company", "ACTION_V2": "When a CRM sales task is <strong>{{action}}</strong> for a company", "ACTIVITY_V2": "A CRM sales task was <strong>{{action}}</strong> for a company", "SELECT_LABEL": "Action", "CREATED": "Created", "MODIFIED": "Modified", "CREATED_OR_MODIFIED": "Created or modified", "WAIT_UNTIL": "Wait until a task is {{action}} on the company"}, "CRM_TASK_OVERDUE_COMPANY": {"NAME": "A CRM sales task is overdue for a company", "ACTION": "When a CRM sales task is overdue for a company", "ACTIVITY": "A CRM sales task is overdue for a company", "WAIT_UNTIL": "Wait until a task is overdue on the company"}, "CONNECTION_CHANGED": {"NAME": "A connection is changed", "ACTION": "When a connection is changed", "ACTIVITY": "A connection was changed", "DESCRIPTION": "When a connection is changed"}, "ACCOUNT_GROUP_UPDATED": {"ACCOUNT_GROUP_UPDATED": "An account is updated", "WAIT_UNTIL": "Wait until an account is updated", "WHEN_ACTION": "When an account is updated", "ACTIVITY": "Account was updated", "DISCLAIMER": "Updates to the following fields will trigger this automation: Name, Address, City, State, Country, Postal code, Website, Phone number, Call tracking, External identifiers, Salesperson, Business category, Lifecycle stage"}, "SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP": {"SALES_PERSON_ASSIGNED_TO_ACCOUNT_GROUP": "A salesperson is assigned to an account", "WHEN_ACTION": "When a salesperson is assigned", "WAIT_UNTIL": "Wait until a salesperson is assigned", "ACTIVITY": "A salesperson was assigned to an account"}, "PARTNER_SUBSCRIPTION_TIER_UPDATE": {"LOAD_ERROR": "Error loading trigger options, please try again", "PARTNER_SUBSCRIPTION_TIER_CHANGE": "Partner subscription tier changed", "WHEN_ACTION": "When a Partner's subscription tier changes", "ACTIVITY": "Partner's subscription tier changed", "CHANGE_TYPE_FORM": {"TITLE": "Change type", "ANY": "Any", "FREE_TO_PAID": "Free to paid tier", "PAID_TO_PAID": "Paid to paid", "SWITCH_FREE_TRIAL": "Switch free trial tier", "CANCEL_FREE_TRIAL": "Cancel free trial", "CANCEL_FREE_TRIAL_FAILED_PAYMENT": "Cancel free trial due to failed payment", "FREE_TO_FREE_TRIAL": "Free to free trial"}, "FROM_TIER_FORM": {"TITLE": "From subscription tier", "ANY": "Any"}, "TO_TIER_FORM": {"TITLE": "To subscription tier", "ANY": "Any"}, "TO_BILLING_FREQUENCY_FORM": {"TITLE": "To billing frequency", "MONTHLY": "Monthly", "YEARLY": "Yearly", "ANY": "Any"}, "FROM_BILLING_FREQUENCY_FORM": {"TITLE": "From billing frequency"}}, "INIT_SUBSCRIPTION_TIER_UPDATE": {"LOAD_ERROR": "Error loading trigger options, please try again", "PARTNER_SUBSCRIPTION_TIER_CHANGE": "A partner initiates an upgrade", "WHEN_ACTION": "When a partner initiates an upgrade", "ACTIVITY": "A partner initiates an upgrade"}, "PARTNER_FREE_TRIAL_CANCELLATION_REASON": {"PARTNER_FREE_TRIAL_CANCELLATION_REASON": "Partner free trial cancellation reason", "WHEN_ACTION": "When a Partner provides a reason for cancelling their free trial", "ACTIVITY": "Partner provided cancellation reason for their free trial", "CANCEL_REASON_FORM": {"TITLE": "Cancel reason", "ANY": "Any"}}, "PARTNER_START_TRIAL": {"PARTNER_START_TRIAL": "A free trial has been started", "WHEN_ACTION": "When a free trial has been started", "ACTIVITY": "A free trial is started"}, "SHOPPING_CART_UPDATED": {"SHOPPING_CART_UPDATED": "A shopping cart is updated", "WHEN_ACTION": "When a user adds, removes, or updates an item in their <b>Shopping Cart</b>", "WAIT_UNTIL_ACTION": "Wait until a user adds, removes, or updates an item in their <b>Shopping Cart</b>", "ACTIVITY": "Shopping cart was updated"}, "CUSTOMER_SURVEY_RESPONSE": {"CUSTOMER_SURVEY_RESPONSE": "Someone leaves a survey response", "DESCRIPTION": "Someone leaves a survey response", "COMPARISON_LABEL": "With a rating", "WHEN_ACTION": "When a user leaves a customer survey response with <b>{{ratingString}}</b> rating", "WAIT_UNTIL_ACTION": "Wait until a user leaves a customer survey response with <b>{{ratingString}}</b> rating", "ACTIVITY": "User left customer survey response with rating of <b>{{rating}}</b>", "LESS_THAN_EQUAL": "Less than or equal to", "GREATER_THAN": "Greater than", "ANY": "Any", "RATING_LABEL": "Rating"}, "RETAIL_PAYMENT": {"RETAIL_PAYMENT": "A user on an account makes a payment", "DESCRIPTION": "A user on an account makes a payment and it...", "WHEN_ACTION": "When a user on an account makes a payment and it <b>{{ statusName }}</b>", "WAIT_UNTIL_ACTION": "Wait until a user on the account makes a payment and it <b>{{ statusName }}</b>", "SUCCESSFUL": "Successful", "FAILED": "Failed", "SUCCEEDS": "Succeeds", "FAILS": "Fails", "SUCCEEDS_OR_FAILS": "Succeeds or fails", "ACTIVITY": "A user on the account made a <b>{{ statusName }}</b> payment", "ACTIVITY_INTERNAL": "<b>{{ customerName }}</b> made a <b>{{ statusName }}</b> payment", "DISCLAIMER": "This trigger provides an <b>Order ID</b> <i>or</i> an <b>Invoice ID</b> based on how the payment originated. To reduce errors in your automation, please use a workflow data filter to check the value's existence before using a step that relies on that data."}, "MANUAL_ACCOUNT_TRIGGER": {"TITLE": "Triggered manually for an account", "DESCRIPTION": "This automation will only run when you start it.\nAfter turning on the automation, go to an account details page to start it.", "DESCRIPTION_WITH_LINK": "This automation will only run when you start it.\nAfter turning on the automation, go to an account details page to start it. <a href='https://support.vendasta.com/hc/en-us/articles/*************' target='_blank'>Learn more</a>", "SALES_RUNNABLE": "Allow Salespeople to run this automation", "WHEN_ACTION": "When this automation is triggered manually for an account", "WHEN_ACTION_GENERIC": "When this automation is triggered manually", "WHEN_ACTION_WITH_USERS": "When this automation is triggered manually by <b>{{ userNames }}</b>", "ACTIVITY": "Manually triggered this automation", "ACTIVITY_SUBTITLE": "Started manually by <b>{{ userName }}</b>", "UNKNOWN_USER": "Unknown", "ONLY_ADMINS": "Only admins", "ADMINS_AND_SALES": "Admins and salespeople", "SPECIFIC_ADMINS": "Specific admins", "WHO_CAN_RUN": "Who can run this automation?", "SPECIFIC_USERS_DESCRIPTION": "Only allow these specific people to run the automation", "SMB_INSTRUCTIONS": "By selecting this trigger, the automation will be available to be triggered through CRM table or list."}, "MANUAL_ORDER_TRIGGER": {"TITLE": "Triggered manually for an order", "DESCRIPTION": "This trigger is currently only fired via customizing the order workflow.\nAfter turning on the automation, You can <a target='_blank' href='https://partners.vendasta.com/customize-design/customize-sales-orders'>customize the order workflow</a> to trigger this automation.", "SALES_RUNNABLE": "Allow Salespeople to run this automation", "SALES_RUNNABLE_DESCRIPTION": "This is required if you want to run this automation via the order workflow", "WHEN_ACTION": "When this automation is triggered manually for an order", "ACTIVITY": "Manually triggered this automation", "ACTIVITY_SUBTITLE": "Started manually by <b>{{ userName }}</b>", "UNKNOWN_USER": "Unknown", "WHEN_ACTION_WITH_USERS": "When this automation is triggered manually by <b>{{ userNames }}</b>"}, "MANUAL_COMPANY_TRIGGER": {"TITLE": "Triggered manually for a company"}, "MANUAL_CONTACT_TRIGGER": {"TITLE": "Triggered manually for a contact"}, "API_ACCOUNT_TRIGGER": {"ACCOUNT_TITLE": "Triggered via API for an account", "STEP": "An API triggered this automation", "ACTIVITY": "An API triggered this automation", "WHEN_ACTION": "When this automation is triggered via API", "API_DOCS": "To trigger this automation via an API, view the <a href='https://developers.vendasta.com/platform/ZG9jOjIwNDk1NjU4-trigger-automation' target='_blank', rel='noopener noreferrer'>API documentation here</a><br /><br />Automation ID: <b>{{automationId}}</b>"}, "API_ORDER_TRIGGER": {"TITLE": "Triggered via API for an order", "STEP": "An API triggered this automation for an order", "ACTIVITY": "An API triggered this automation for an order", "WHEN_ACTION": "When this automation is triggered via API for an order", "API_DOCS_TODO": "To trigger this automation via an API, view the <a href='https://developers.vendasta.com/platform/ZG9jOjIwNDk1NjU4-trigger-automation' target='_blank', rel='noopener noreferrer'>API documentation here</a><br /><br />Automation ID: <b>{{automationId}}</b>"}, "API_NO_ENTITY_TRIGGER": {"TITLE": "A webhook is received", "STEP": "A received webhook triggered this automation", "ACTIVITY": "A received webhook triggered this automation", "WHEN_ACTION": "When this automation is triggered via a received webhook", "API_DOCS_NOT_WHITELABELLED": "Send requests to this custom webhook URL. <a href='https://developers.vendasta.com/platform/a563fe90063fc-trigger-a-prebuilt-automation ' target='_blank', rel='noopener noreferrer'>Learn more about using webhooks</a>", "API_DOCS_WHITELABELLED": "Send requests to this custom webhook URL.", "WEBHOOK_URL": "Webhook URL"}, "ZAPIER_TRIGGER": {"TITLE": "Triggered via Zapier", "STEP": "Zapier triggered this automation", "ACTIVITY": "Zapier triggered this automation", "WHEN_ACTION": "When this automation is triggered via Zapier", "HOW_DOES_IT_WORK": "How does it work?", "INSTRUCTIONS_1": "In Zapier, setup a new Zap and select the desired trigger to initiate this workflow.", "INSTRUCTIONS_2_SMB_CONTEXT": "Click on 'Add a Step' and select Business App as the app that will carry out your desired action.", "INSTRUCTIONS_2_PARTNER_CONTEXT": "Click on 'Add a Step' and select Vendasta as the app that will carry out your desired action.", "INSTRUCTIONS_3": "Select 'Run automation' as the event.", "INSTRUCTIONS_4_SMB_CONTEXT": "Click on 'Sign in' to be directed to the app's login page. Here, <PERSON><PERSON>ier will prompt you to enter your Account ID and grant the necessary permissions. You can find your Account ID below:", "INSTRUCTIONS_4_PARTNER_CONTEXT": "Click on 'Sign in' to be directed to the app's login page. Here, Zapier will prompt you to enter your Partner ID and grant the necessary permissions. You can find your Partner ID below:", "INSTRUCTIONS_5": "The Automation ID can be populated by searching for the name of this automation. Alternatively, select 'Custom' and the field will allow you to paste the Automation ID directly, which you can find below:"}, "OPPORTUNITY": {"OPPORTUNITY_UPDATED": "An opportunity is created or changed", "CONFIG_ERROR": "Choose a new pipeline or change trigger", "DESCRIPTION": "An opportunity is...", "ACTION": {"ERROR_LOADING_ACTION": "There was an error loading the step", "WHEN_OPPORTUNITY_CREATED_OR_UPDATED_ANY_STAGE_ANY_PIPELINE": "When an opportunity is <b>created</b> in or <b>changed</b> to <b>any stage</b> in <b>any pipeline</b>", "WHEN_OPPORTUNITY_UPDATED_ANY_STAGE_ANY_PIPELINE": "When an opportunity is <b>changed</b> to <b>any stage</b> in <b>any pipeline</b>", "WHEN_OPPORTUNITY_CREATED_ANY_STAGE_ANY_PIPELINE": "When an opportunity is <b>created</b> in <b>any stage</b> in <b>any pipeline</b>", "WAIT_UNTIL_OPPORTUNITY_CREATED_OR_UPDATED_ANY_STAGE_ANY_PIPELINE": "Wait until an opportunity is <b>created</b> in or <b>changed</b> to <b>any stage</b> in <b>any pipeline</b>", "WAIT_UNTIL_OPPORTUNITY_CREATED_ANY_STAGE_ANY_PIPELINE": "Wait until an opportunity is <b>created</b> in <b>any stage</b> in <b>any pipeline</b>", "WAIT_UNTIL_OPPORTUNITY_UPDATED_ANY_STAGE_ANY_PIPELINE": "Wait until an opportunity is changed to <b>any stage</b> in <b>any pipeline</b>", "WHEN_CLOSED_ACTION_IN_PIPELINE": "When an <b>opportunity status</b> is changed to <b>{{ status }}</b> in <b>{{ pipelineName }}</b> pipeline", "WAIT_UNTIL_CLOSED_ACTION_IN_PIPELINE": "Wait until an <b>opportunity status</b> is changed to <b>{{ status }}</b> in <b>{{ pipelineName }}</b> pipeline", "WHEN_CLOSED_ACTION_IN_ANY_PIPELINE": "When an <b>opportunity status</b> is changed to <b>{{ status }}</b> in <b>any pipeline</>", "WAIT_UNTIL_CLOSED_ACTION_IN_ANY_PIPELINE": "Wait until an <b>opportunity status</b> is changed to <b>{{ status }}</b> in <b>any pipeline</b>", "WHEN_CLOSED_ACTION_WITH_LOST_REASON_IN_PIPELINE": "When an <b>opportunity status</b> is changed to <b>Closed lost</b> in <b>{{ pipelineName }}</b> pipeline, with the close reason <b>{{ closedLostReason }}</b>", "WAIT_UNTIL_CLOSED_ACTION_WITH_LOST_REASON_IN_PIPELINE": "Wait until an <b>opportunity status</b> is changed to <b>Closed lost</b> in <b>{{ pipelineName }}</b> pipeline, with the close reason <b>{{ closedLostReason }}</b>", "WHEN_CLOSED_ACTION_WITH_LOST_REASON_IN_ANY_PIPELINE": "When an <b>opportunity status</b> is changed to <b>Closed lost</b> in any pipeline, reason: <b>{{ closedLostReason }}</b>", "WAIT_UNTIL_CLOSED_ACTION_WITH_LOST_REASON_IN_ANY_PIPELINE": "Wait until an <b>opportunity status</b> is changed to <b>Closed lost</b> in any pipeline, reason: <b>{{ closedLostReason }}</b>", "WHEN_CREATED_OR_CHANGED_IN_PIPELINE_WITH_STAGE": "When an opportunity is <b>created</b> in or <b>changed</b> to stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "WAIT_UNTIL_CREATED_OR_CHANGED_IN_PIPELINE_WITH_STAGE": "Wait until an opportunity is <b>created</b> in or <b>changed</b> to stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "WHEN_CREATED_IN_PIPELINE_WITH_STAGE": "When an opportunity is <b>created</b> in stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "WAIT_UNTIL_CREATED_IN_PIPELINE_WITH_STAGE": "Wait until an opportunity is <b>created</b> in stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "WHEN_UPDATED_IN_PIPELINE_WITH_STAGE": "When an opportunity is <b>changed</b> to stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "WAIT_UNTIL_UPDATED_IN_PIPELINE_WITH_STAGE": "Wait until an opportunity is <b>changed</b> to stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline"}, "ACTIVITY": {"ERROR_LOADING_ACTIVITY": "There was an error loading the activity", "CHANGED_TO_CLOSED_STATUS": "The <b>{{ opportunityName }}</b> opportunity status was changed to <b>{{ status }}</b> in <b>{{ pipelineName }}</b> pipeline", "CHANGED_TO_CLOSED_STATUS_WITH_LOST_REASON": "The <b>{{ opportunityName }}</b> opportunity status was changed to <b>Closed lost</b> in <b>{{ pipelineName }}</b> pipeline, reason: <b>{{ closedLostReason }}</b>", "CHANGED_PIPELINE_STAGE": "The <b>{{ opportunityName }}</b> opportunity was changed to stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "CREATED_PIPELINE_STAGE": "The <b>{{ opportunityName }}</b> opportunity was created at stage <b>{{ stageName }}</b> in <b>{{ pipelineName }}</b> pipeline", "EMPTY_ACTIVITY_CLOSED_LOST_WITH_LOST_REASON": "The <b>{{ opportunityName }}</b> opportunity status was changed to <b>Closed lost</b> in a pipeline, reason: <b>{{ closedLostReason }}</b>", "EMPTY_ACTIVITY_CLOSED_WON": "The <b>{{ opportunityName }}</b> opportunity status was changed to <b>Closed won</b> in a pipeline", "EMPTY_ACTIVITY_CREATED": "The <b>{{ opportunityName }}</b> opportunity was created in a pipeline", "EMPTY_ACTIVITY_UPDATED": "The <b>{{ opportunityName }}</b> opportunity was updated in a pipeline"}, "LAST_EDIT_ACTION_OPTIONS": {"CREATED": "Created", "CREATED_OR_CHANGED_STAGE": "Created or changed stage", "CHANGED_STAGE": "Changed stage", "PIPELINE_STAGE": "Created in or changed to a pipeline stage", "PIPELINE_STAGE_UPDATED": "Changed to a pipeline stage", "PIPELINE_STAGE_CREATED": "Created in a pipeline stage", "CLOSED": "Closed"}, "FILTER_DISCLAIMER": "To specify pipelines and stages, select the \"Opportunity data\" condition type in the \"Add conditions for starting this automation\" checkbox below", "STATUS": "Opportunity status", "CONDITION": "Condition", "AND_IS_IN_PIPELINE_AT_STAGE": "and is in the pipeline at stage...", "AND_IS_IN_PIPELINE": "and is in pipeline...", "AND_HAS_STATUS": "with a status of...", "AND_HAS_CLOSED_LOST_REASON": "and close lost reason is...", "STATUS_OPTIONS": {"OPEN": "Open", "CLOSED_ANY": "Closed won or Closed lost", "CLOSED_LOST": "Closed lost", "CLOSED_WON": "Closed won"}, "CLOSED_LOST_REASON": "Reason", "CLOSED_LOST_REASON_OPTIONS": {"ANY": "Any", "PRICE": "Price", "NO_BUDGET": "No budget", "LOST_TO_COMPETITOR": "Lost to competitor", "NOT_READY": "Not ready", "OTHER": "Other"}, "PIPELINE": "Pipeline", "DEFAULT_PIPELINE": "De<PERSON>ult <PERSON>", "STAGE": "Stage"}, "SALES_ORDER_EXPIRING": {"SALES_ORDER_EXPIRING": "A sales order is expiring", "ORIGIN_DESCRIPTION": "is changed to...", "STATUS_DESCRIPTION": "a sales order is expiring in...", "EXPIRY": "Expiry", "EXPIRY_OPTIONS": {"NINETY_DAYS": "90 Days", "THIRTY_DAYS": "30 Days", "ONE_DAY": "1 Day"}, "WHEN_ACTION": "When an order is expiring", "ACTIVITY": "Order is expiring", "ORIGIN": "Origin", "ORIGIN_OPTIONS": {"ANY": "Any origin", "PC": "Partner Center", "SSC": "Sales & Success Center", "BUSINESS_APP": "Business App"}, "ACCOUNT_SCOPED_ORIGIN_DESCRIPTION": "A sales order on the account from…", "ACTION": {"WHEN_STATUS_CHANGES": "When a sales order expires in <b>{{expiry}}</b>"}}, "ORDER_STATUS_CHANGED": {"ORDER_STATUS_CHANGED": "A sales order status is changed", "ORIGIN_DESCRIPTION": "A sales order from...", "ACCOUNT_SCOPED_ORIGIN_DESCRIPTION": "A sales order on the account from…", "STATUS_DESCRIPTION": "is changed to...", "ACTION": {"WHEN_STATUS_CHANGES": "When a sales order from <b>{{origin}}</b> is changed to <b>{{status}}</b>", "WAIT_UNTIL_STATUS_CHANGES": "Wait until a sales order on the account from <b>{{origin}}</b> is changed to <b>{{status}}</b>"}, "ACTIVITY": {"STATUS_CHANGED": "The <b>{{orderLink}}</b> sales order from <b>{{origin}}</b> changed to <b>{{status}}</b>"}, "STATUS": "Status", "STATUS_OPTIONS": {"RESUBMITTED": "Resubmitted", "SUBMITTED": "Pending", "SUBMITTED_FOR_CUSTOMER_APPROVAL": "Awaiting customer approval", "APPROVED": "Approved", "PROCESSING": "Processing", "ACTIVATION_ERRORS": "Activation errors", "FULFILLED": "Activated", "ARCHIVED": "Archived", "DECLINED": "Declined", "SCHEDULED_ACTIVATION": "Scheduled activation", "DRAFTED": "Drafted", "CANCELLATION_REQUESTED": "Cancellation requested", "CANCELLED": "Cancelled"}, "ORIGIN": "Origin", "ORIGIN_OPTIONS": {"ANY": "Any origin", "PC": "Partner Center", "SSC": "Sales & Success Center", "BUSINESS_APP": "Business App"}}, "ORDER_LEASED_TO_AUTOMATIONS": {"ORDER_LEASED_TO_AUTOMATIONS": "A sales order is sent to automations for processing", "ORDER_ON_ACCOUNT_LEASED_TO_AUTOMATIONS": "A sales order on the account is sent to automations for processing", "ACTION": {"WHEN_LEASED": "When a sales order is sent to automations for processing", "WAIT_UNTIL_LEASED": "Wait until a sales order on the account is sent to automations for processing"}, "ACTIVITY": {"LEASED": "The <b>{{orderLink}}</b> sales order was sent to automations for processing"}, "FILTERS": {"ACTION": "Order action", "APPROVED": "Approved", "DECLINED": "Declined"}}, "CAMPAIGN_EMAIL": {"CAMPAIGN_EMAIL": "There's activity on a campaign email", "CAMPAIGN_EMAIL_NAME": "There's activity on a campaign email", "CONFIG_ERROR": {"DEFAULT": "Choose a new campaign or change trigger", "DRAFT_ERROR": "The current selected campaign is in draft.<br>Choose a new campaign or change trigger", "ARCHIVED_ERROR": "The current selected campaign was archived.<br>Choose a new campaign or change trigger"}, "DESCRIPTION": "An email within a campaign is...", "ADDITIONAL_DESCRIPTION": "for an account", "HINT": "This trigger will ignore activities that are flagged as suspicious.", "ACTION": {"ERROR_LOADING_ACTION": "There was an error loading the step", "CONFIG_ERROR": "Choose a new campaign or change trigger", "LINK": "the <a target=\"_blank\" href=\"/marketing/campaign/details/{{campaignId}}\" style=\"font-weight: 500;\">{{campaignName}}</a> campaign", "ANY_CAMPAIGN_NODE_TEXT": "<b>Any campaign</b>", "A_CAMPAIGN": "a campaign", "WHEN_CAMPAIGN_EMAIL": "When an email within {{campaignLink}} is {{eventType}} for an account", "WHEN_CAMPAIGN_EMAIL_OPENED": "When an email within {{campaignLink}} is opened for an account", "WHEN_CAMPAIGN_EMAIL_CLICKED": "When a link within {{campaignLink}} is clicked for an account", "WHEN_CAMPAIGN_EMAIL_BOUNCED": "When an email within {{campaignLink}} is bounced for an account", "WHEN_CAMPAIGN_EMAIL_UNSUBSCRIBED": "When an email within {{campaignLink}} is unsubscribed from for an account", "WAIT_UNTIL_CAMPAIGN_EMAIL": "Wait until an email within {{campaignLink}} is {{eventType}} for an account", "WAIT_UNTIL_CAMPAIGN_EMAIL_OPENED": "Wait until an email within {{campaignLink}} is opened for an account", "WAIT_UNTIL_CAMPAIGN_EMAIL_CLICKED": "Wait until a link within {{campaignLink}} is clicked for an account", "WAIT_UNTIL_CAMPAIGN_EMAIL_BOUNCED": "Wait until email within {{campaignLink}} is bounced for an account", "WAIT_UNTIL_CAMPAIGN_EMAIL_UNSUBSCRIBED": "Wait until email within {{campaignLink}} is unsubscribed from for an account"}, "ACTIVITY": {"ERROR_LOADING_ACTIVITY": "There was an error loading the activity", "LINK": "<a target=\"_blank\" href=\"/marketing/campaign/details/{{campaignId}}\" style=\"font-weight: 500;\">{{emailTitle}}</a> email", "CAMPAIGN_EMAIL": "{{emailLink}} was {{eventType}}", "CAMPAIGN_EMAIL_OPENED": "{{emailLink}} was opened", "CAMPAIGN_EMAIL_CLICKED": "A link within {{emailLink}} was clicked", "CAMPAIGN_EMAIL_BOUNCED": "{{emailLink}} was bounced", "CAMPAIGN_EMAIL_UNSUBSCRIBED": "{{emailLink}} was unsubscribed"}, "EVENT_OPTIONS": {"OPENED": "opened", "CLICKED": "clicked", "BOUNCED": "bounced", "UNSUBSCRIBED": "unsubscribed from", "SENT": "sent", "DROPPED": "dropped", "REPLIED": "replied to", "ANY": "any"}, "CAMPAIGN": "Campaign", "ANY_CAMPAIGN": "Any campaign", "EMAIL_STATUS": "Email status"}, "CUSTOMER_ASKED_QUESTION": {"ASKED_QUESTION_ACTION_TITLE": "Question asked", "CUSTOMER_ASKED_QUESTION": "A user on an account asked a question", "WHEN_ACTION": "When a user on an account asked a question", "WAIT_UNTIL_ACTION": "Wait until a user on the account asks a question", "ASKED_QUESTION_DESCRIPTION": "A user clicked a help button and asked a question"}, "CUSTOMER_VIEWS_PACKAGE": {"ANY_PACKAGE": "Any Package", "PACKAGE": "Package", "CUSTOMER_INTERESTED_IN_A_PACKAGE": "A user shows interest in a package", "ACTION": {"CONFIG_ERROR": "Choose a new package or change trigger", "WHEN_CUSTOMER_INTERESTED_IN_A_PACKAGE": "When a user shows interest in a package", "WAIT_UNTIL_CUSTOMER_INTERESTED_IN_A_PACKAGE": "Wait until a user on the account shows interest in a package", "WHEN_CUSTOMER_INTERESTED_IN_SPECIFIC_PACKAGE": "When a user shows interest in the package {{ packageName }}", "WAIT_UNTIL_CUSTOMER_INTERESTED_IN_SPECIFIC_PACKAGE": "Wait until a user on the account views the package {{ packageName }}"}, "CUSTOMER_INTERESTED_IN_PACKAGE_ACTION_TITLE": "Package viewed", "CUSTOMER_INTERESTED_IN_PACKAGE_DESCRIPTION": "A user on the account shows interest in a package", "CUSTOMER_INTERESTED_IN_PACKAGE_HINT": "Triggered when a user fills out the 'Contact sales' modal for the selected package", "CUSTOMER_INTERESTED_IN_PACKAGE_ORIGIN": "A user on the account shows interest in a package via", "CUSTOMER_INTERESTED_IN_PACKAGE_SELECTION": "And selected this package", "ORIGIN": {"PUBLIC_STORE": "Public Store"}}, "TASK_STATUS_CHANGED": {"TASK_STATUS_TITLE": "A fulfillment task status is changed", "TASK_STATUS_SUBTITLE": "A fulfillment task status is changed to...", "ASSIGNEE": "the task's assignee", "FILTERS": {"STATUS": "Status", "OTHER_FILTERS": "Specify a task name and/or assignee (optional). If these fields are left blank, the trigger will apply for all fulfillment tasks.", "OTHER_FILTERS_FOR_DELAY_EVENT": "Specify a task name and/or assignee (optional). If these fields are left blank, the event will apply for all fulfillment tasks.", "TASK_NAME": "Search for a task name (optional)", "TASK_ASSIGNEE": "Assignee (optional)"}, "ACTION": {"CONFIG_ERROR": "Specify different task information or change trigger", "SPECIFIED_STATUS": "When a fulfillment task status is changed to <b>{{status}}</b>", "SPECIFIED_STATUS_AND_NAME": "When a fulfillment task named <b>{{task_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_STATUS_AND_USER": "When a fulfillment task assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_ALL": "When a fulfillment task named <b>{{task_name}}</b> and assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status"}, "ACTIVITY": {"FAILURE": "Failed to get task information", "STEP_TITLE": "A fulfillment task status is changed", "ACTIVITY_SUBTITLE": "The fulfillment task named <b>{{task_name}}</b> was changed to <b>{{status}}</b> status\n"}}, "SALES_TASK_STATUS_CHANGED": {"TASK_STATUS_TITLE_V2": "A sales task status is changed (legacy)", "TASK_STATUS_SUBTITLE_V2": "Start the automation when: a sales task status in Sales & Success Center is changed to...", "ASSIGNEE": "the task's assignee", "FILTERS": {"STATUS": "Status", "OTHER_FILTERS": "Specify a task name and/or assignee (optional). If these fields are left blank, the trigger will apply for all sales tasks.", "OTHER_FILTERS_FOR_DELAY_EVENT": "Specify a task name and/or assignee (optional). If these fields are left blank, the event will apply for all sales tasks.", "TASK_NAME": "Search for a task name (optional)", "TASK_ASSIGNEE": "Assignee (optional)"}, "ACTION": {"CONFIG_ERROR": "Specify different task information or change trigger", "SPECIFIED_STATUS": "When a sales task status is changed to <b>{{status}}</b>", "SPECIFIED_STATUS_AND_NAME": "When a sales task named <b>{{task_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_STATUS_AND_USER": "When a sales task assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_ALL": "When a sales task named <b>{{task_name}}</b> and assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status"}, "ACTIVITY": {"FAILURE": "Failed to get task information", "STEP_TITLE": "A sales task status is changed", "ACTIVITY_SUBTITLE": "The sales task named <b>{{task_name}}</b> was changed to <b>{{status}}</b> status\n"}}, "FULFILLMENT_PROJECT_STATUS_CHANGED": {"PROJECT_STATUS_ACCOUNT_TITLE": "A fulfillment project status is changed", "PROJECT_STATUS_ORDER_TITLE": "A fulfillment project for an order changes status", "TASK_STATUS_TITLE": "A fulfillment project status is changed", "TASK_STATUS_SUBTITLE": "A fulfillment project status is changed to...", "ASSIGNEE": "the project's assignee", "FILTERS": {"OTHER_FILTERS": "Specify a project name and/or assignee (optional). If these fields are left blank, the trigger will apply for all fulfillment projects.", "OTHER_FILTERS_FOR_DELAY_EVENT": "Specify a project name and/or assignee (optional). If these fields are left blank, the event will apply for all fulfillment projects.", "TASK_NAME": "Search for a project name (optional)"}, "ACTION": {"SPECIFIED_STATUS": "When a fulfillment project status is changed to <b>{{status}}</b>", "SPECIFIED_STATUS_AND_NAME": "When a fulfillment project named <b>{{task_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_STATUS_AND_USER": "When a fulfillment project assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_ALL": "When a fulfillment project named <b>{{task_name}}</b> and assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_STATUS_ORDER": "When a fulfillment project for an order changes status to <b>{{status}}</b>", "SPECIFIED_STATUS_AND_NAME_ORDER": "When a fulfillment project for an order and named <b>{{task_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_STATUS_AND_USER_ORDER": "When a fulfillment project for an order and assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status", "SPECIFIED_ALL_ORDER": "When a fulfillment project for an order and named <b>{{task_name}}</b> and assigned to <b>{{user_name}}</b> is changed to <b>{{status}}</b> status"}, "ACTIVITY": {"STEP_TITLE": "A fulfillment project status is changed", "ACTIVITY_SUBTITLE": "The fulfillment project named <b>{{task_name}}</b> was changed to <b>{{status}}</b> status\n"}}, "WHOLESALE_PURCHASE_CREATED": {"WHOLESALE_PURCHASE_CREATED": "A partner purchase is created", "DESCRIPTION": "A purchase for the following products", "WHEN_ACTION": "When a partner's purchase <b>{{ approvalState }}</b> for <b>{{ productName }}{{ plusOthers }}", "WAIT_UNTIL_ACTION": "Wait until a partner purchase is created", "ACTIVITY": "A partner purchase has <b>{{ approvalState }}</b> for <b>{{ productName }}</b>", "SELECT_PRODUCT": "Any of the selected products", "CONFIG_ERROR": "Choose a new product or change trigger", "ANY_PRODUCT_CAPITAL": "Any product", "ANY_PRODUCTS": "any product", "STATUS_FAILS": "fails", "STATUS_SUCCEEDS": "succeeds", "STATUS_FAILED": "failed", "STATUS_SUCCEEDED": "succeeded", "SUCCEEDS": "Succeeds", "FAILS": "Fails", "NOTE": "Note: Selecting 'All products' will allow the trigger to fire for any SKU -\n including products or services not enabled/listed here (for example: Volume\n Commitment Adjustment SKU)"}, "CUSTOM_DATA_UPDATED": {"CUSTOM_DATA_UPDATED": {"ACCOUNT": "Account custom data fields are updated"}, "PANEL": {"UPDATE_TYPE": "Select custom field updates you are interested in:", "ANY_FIELDS": "Any fields selected", "ANY_FIELDS_DESCRIPTION": "Any custom fields selected must be updated to trigger automation.", "ALL_FIELDS": "All fields selected", "ALL_FIELDS_DESCRIPTION": "All custom fields selected must be updated to trigger automation.", "SELECT_CUSTOM_FIELDS": "Select one or multiple custom fields"}, "PANEL_ERRORS": {"REQUIRED": "Select at least one field", "ARCHIVED_FIELD": "{{ archivedField }} field is archived", "ARCHIVED_FIELDS": "{{ archivedFields }} fields are archived"}, "OBJECT_TYPE": {"ACCOUNT": "Account"}, "ACTION": {"WAIT_UNTIL_TEMPLATE_TITLE": "Wait until {{ objectType }} custom data fields are updated", "TEMPLATE_TITLE": "When {{ objectType }} custom data fields are updated", "WHEN_TITLE": "When <b>{{ updateType }}</b> of the following  {{ objectType }} custom data fields are updated: \n<b>{{ fieldsUpdated }}</b>", "WAIT_UNTIL_TITLE": "Wait until <b>{{ updateType }}</b> of the following  {{ objectType }} custom data fields are updated: \n<b>{{ fieldsUpdated }}</b>", "ANY_FIELDS_UPDATED": "any", "ALL_FIELDS_UPDATED": "all"}, "ACTIVITY": {"TITLE": "{{ objectType }} custom data fields updated:\n<b>{{ fieldsUpdated }}</b>"}, "ARCHIVED_CONFIG_ERROR": "This step is using an archived custom field. Remove the archived field from this step or <a href=\"/custom-fields\"><strong>unarchive them</strong></a> to continue.", "DEFAULT_CONFIG_ERROR": "There is an issue with your step, please delete it or configure it.", "UNKNOWN_FIELDS": "unknown fields"}, "EMAIL_EVENT": {"ANY": "Any", "EMAIL_EVENT_TITLE": "A link in an email is clicked", "EMAIL_EVENT_INFO": "This trigger only applies to the first click event. Any subsequent clicks will be ignored.", "EMAIL_HEADER": "Email", "EMAIL_LINK_LABEL": "Clicked Link", "MISSING_INFORMATION": "Missing Information", "MISSING_INFORMATION_DETAILS": "This trigger requires information from a previous <b>send an email to users</b> step."}, "RETAIL_INVOICE_OVERDUE": {"TITLE": "An invoice status changes to past due", "NODE": "An invoice status changes to <b>Past due</b>", "WAIT_UNTIL": "Wait until an invoice status is changed to <b>Past due</b>"}, "EMAIL_VERIFIED": {"TITLE": "A user verifies their email address", "DESCRIPTION": "This trigger only applies to {{ businessAppName }} users.", "ACTIVITY_TITLE": "Email address verified", "ACTIVITY_BODY": "A user verified their email address"}, "INTEGRATIONS": {"QUICKBOOKS": {"INVOICE_CREATED_MODIFIED": "A QuickBooks invoice is created or modified for a contact", "SALESRECEIPT_CREATED_MODIFIED": "A QuickBooks sales receipt is created or modified for a contact", "PAYMENT_CREATED_MODIFIED": "A QuickBooks payment is created or modified for a contact"}, "JOBBER": {"JOBBER_VISIT_COMPLETED": "A Jobber visit is completed", "JOBBER_JOB_COMPLETED": "A Jobber job is completed"}, "HOUSECALLPRO": {"HOUSECALLPRO_JOB_FINISHED": "A Housecall Pro job is finished"}, "GINGRAPP": {"RESERVATION_CHECKEDOUT": "A Gingr reservation is checked out"}, "PETEXEC": {"ORDER_COMPLETED": "A Petexec order is completed"}, "PAWPARTNER": {"CUSTOMER_CHECKOUT": "A PawPartner customer is checked out"}, "SHOPMONKEY": {"INVOICE_PAID": "A ShopMonkey invoice is paid"}, "SHOPWARE": {"REPAIR_ORDER_INVOICE_PICKEDUP": "A Shopware repair order invoice picked-up "}, "PROTRACTOR": {"PROTRACTOR_INVOICE_POSTED": "A Protractor invoice is posted"}, "TEKMETRIC": {"TEKMETRIC_REPAIRORDER_POSTED": "Tekmetric repairorder posted"}, "PAWLOYALTY": {"PAWLOYALTY_APPOINTMENT_CHECKED_OUT": "PawLoyalty appointment checked out"}, "MINDBODY": {"MINDBODY_VISIT_COMPLETE": "Mindbody visit complete"}, "PETRESORTPRO": {"PETRESORTPRO_INVOICE_CHECKOUT": "A Pet Resort Pro invoice is checked out"}, "SERVICEMONSTER": {"SERVICEMONSTER_INVOICE_CREATED": "A ServiceMonster invoice is created for a contact"}, "BROADLYPARTNERAPI": {"BROADLYPARTNERAPI_TRANSACTION_COMPLETE": "Transaction completed in partner system for contact"}, "SHOPBOSS": {"SHOPBOSS_REPAIRORDERINVOICE_CLOSED": "A ShopBoss repair order invoice closed"}, "CCC": {"CCCONE_VEHICLEOUT_COMPLETE": "CCC One Vehicle out complete"}, "SERVICEFUSION": {"SERVICEFUSION_JOB_CLOSED": "A ServiceFusion job is closed"}, "RBCONTROLSYSTEMS": {"RBCS_SALESORSERVICE_COMPLETED": "RBCS sales or service completed"}, "LIGHTSPEED": {"LIGHTSPEED_SALE_COMPLETED": "LightSpeed sale completed"}, "FIELDEDGE": {"FIELDEDGE_WORKORDER_FINALIZED": "FieldEdge work order finalized"}, "JOBNIMBUS": {"JOBNIMBUS_CONTACTSTATUS_CHANGED": "JobNimbus Contact Status Changed", "JOBNIMBUS_JOBSTATUS_CHANGED": "JobNimbus Job Status Changed"}, "SERVICETITAN": {"SERVICETITAN_JOB_COMPLETED": "ServiceTitan Job Completed"}, "CLIO": {"CLIO_MATTER_CLOSED": "A Clio matter is closed"}, "FTP": {"FTP_UPLOAD_TRIGGER": "Broadly FTP Upload"}, "QBD": {"QBD_INVOICEORSALESRECEIPT_UPDATED": "QuickBooks Desktop invoice or salesreceipt updated"}, "NAPATRACS": {"NAPATRACS_REPAIRORDER_COMPLETED": "A Napa TRACS repair order invoice closed"}, "NAPATRACSENTERPRISE": {"NAPATRACSENTERPRISE_REPAIRORDER_COMPLETED": "A Napa TRACS Enterprise repair order invoice closed"}, "ROWRITER": {"ROWRITER_REPAIRORDER_COMPLETED": "A ROWriter repair order invoice closed"}, "DENTRIX": {"DENTRIX_APPOINTMENT_COMPLETED": "A Dentrix appointment closed"}, "MITCHELLMANAGER": {"MITCHELL_REPAIRORDER_COMPLETE": "Mitchell Manager SE repair order complete"}}}, "TASKS": {"CREATE_USER_FROM_CONTACT": {"TITLE": "Create a user from a contact", "DESCRIPTION": "Create a user from a contact", "ACTION": "Create a user from the contact", "ACTIVITY": "Created a user from the contact", "ACTIVITY_FAILED": "Failed to create a user from the contact", "ACTIVITY_SUCCESS": " Successful<PERSON> created a user from the contact"}, "CAMPAIGNS": {"COMMON": {"CAMPAIGN": "Campaign", "CONFIG_ERROR": {"DRAFT_ERROR": "The current selected campaign is in draft.<br>Choose a new campaign or delete the step", "ARCHIVED_ERROR": "The current selected campaign was archived.<br>Choose a new campaign or delete the step"}}, "START_GENERAL": {"SEND_A_CAMPAIGN": "Start a campaign for the account", "SEND_A_CAMPAIGN_CONTACT": "Start a campaign for the contact", "SEND_A_YESWARE_CAMPAIGN_CONTACT": "Start a Yesware campaign for the contact", "CONFIG_ERROR": "Choose a new campaign or delete the step", "DESCRIPTION": "The following campaign will be started for all the users and contacts on the accounts that go through this step.", "HINT": "Note: A campaign can only be started once for each user on an account.", "ACTION_LINKED": "Start the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the account", "ACTION_NO_ENTITY_LINKED": "Start the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign", "ACTION_DEFAULT": "Start a campaign for the account", "ACTIVITY_LINKED": "Started the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the account", "ACTIVITY_FAILED": "Failed to start a campaign for the account", "ACTIVITY_DEFAULT": "Start a campaign for the account", "NO_CAMPAIGNS_FOUND": "No campaigns found. To proceed with this action, create a campaign.", "NO_SEARCH_RESULTS": "No campaigns found that match your search.", "CONTACT": {"ACTION_LINKED": "Start the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the contact", "ACTION_DEFAULT": "Start a campaign for the contact", "ACTIVITY_LINKED": "Started the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the contact", "ACTIVITY_FAILED": "Failed to start a campaign for the contact", "ACTIVITY_DEFAULT": "Start a campaign for the contact"}}, "START_FOR_USER": {"SEND_A_CAMPAIGN": "Start a campaign for the user", "CONFIG_ERROR": "Choose a new campaign or delete the step", "DESCRIPTION": "The following campaign will be started for the users that go through this step.", "HINT": "Note: A campaign can only be started once for each user on an account.", "ACTION_LINKED": "Start the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the user", "ACTION_DEFAULT": "Start a campaign for the user", "ACTIVITY_LINKED": "Started the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for <b>{{ userName }}</b>", "ACTIVITY_FAILED": "Failed to start a campaign for the user", "ACTIVITY_DEFAULT": "Started a campaign for <b>{{ userName }}</b>", "THE_USER": "the user"}, "PAUSE_CAMPAIGN": {"PAUSE_A_CAMPAIGN": "Pause a campaign for the account", "CONFIG_ERROR": "Choose a new campaign or delete the step", "DESCRIPTION": "This campaign will be paused for all the users and contacts on the accounts that go through this step.", "ACTION_LINKED": "Pause the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the account", "ACTION_DEFAULT": "Pause a campaign for the account", "ACTIVITY_LINKED": "Paused the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the account", "ACTIVITY_FAILED": "Failed to pause the campaign for the account", "ACTIVITY_DEFAULT": "Paused a campaign for the account"}, "PAUSE_CAMPAIGN_FOR_USER": {"PAUSE_A_CAMPAIGN": "Pause a campaign for the user", "CONFIG_ERROR": "Choose a new campaign or delete the step", "DESCRIPTION": "This campaign will be paused for the users that go through this step.", "ACTION_LINKED": "Pause the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the user", "ACTION_DEFAULT": "Pause a campaign for the user", "ACTIVITY_LINKED": "Paused the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for <b>{{ userName }}</b>", "ACTIVITY_FAILED": "Failed to pause the campaign for the user", "ACTIVITY_DEFAULT": "Paused a campaign for <b>{{ userName }}</b>"}, "PAUSE_CAMPAIGN_FOR_CONTACT": {"PAUSE_A_CAMPAIGN": "Pause campaign for contact", "ACTION_LINKED": "Pause the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the contact", "ACTION_DEFAULT": "Pause a campaign for the contact", "ACTIVITY_LINKED": "Paused the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the contact", "ACTIVITY_FAILED": "Failed to pause the campaign for the contact", "ACTIVITY_DEFAULT": "Paused a campaign for the contact"}, "PAUSE_CAMPAIGN_FOR_COMPANY": {"PAUSE_A_CAMPAIGN": "Pause campaign for company", "ACTION_LINKED": "Pause the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the company", "ACTION_DEFAULT": "Pause a campaign for the company", "ACTIVITY_LINKED": "Paused the <a target=\"_blank\" href=\"{{campaignUrl}}\" style=\"font-weight: 500;\">{{ campaignName }}</a> campaign for the company", "ACTIVITY_FAILED": "Failed to pause the campaign for the company", "ACTIVITY_DEFAULT": "Paused a campaign for the company"}, "START_YESWARE_CAMPAIGN": {"TITLE": "Start a Yesware campaign", "ACTION": "Start Yesware {{ campaignName }} campaign", "ACTIVITY": {"SUCCESS": "Campaign was started for <a target=\"_blank\" href=\"{{url}}\">contact</a>", "FAILED": "Failed to start the campaign"}, "NOT_FOUND": "Campaign not found", "LABEL": "The selected yesware campaign will be sent to a contact"}}, "SEND_IN_APP_NOTIFICATION": {"ADDITIONAL_SALESPEOPLE_DISCLAIMER": "To select this option, use the new Notify a salesperson step", "ASSIGNED_SALESPERSON": "Assigned Salesperson", "SEND_A_NOTIFICATION": "Send an in-app notification", "SEND_IN_APP": "Send an in-app notification to...", "ACTION": {"TO_ASSIGNED_SALESPERSON": "Send an in-app notification to the assigned salesperson", "TO_SINGLE_USER": "Send an in-app notification to <b>{{name}}</b>", "TO_MULTIPLE_SALESPEOPLE": "Send an in-app notification to {{userCount}} salespeople", "TO_MULTIPLE_ADMINS": "Send an in-app notification to {{userCount}} Partner Center admins", "CONFIG_ERROR": "Choose a new recipient or delete the step"}, "ACTIVITY": {"SENT_A_NOTIFICATION": "Sent an in-app notification", "TO_ASSIGNED_SALESPERSON": "Sent an in-app notification to the assigned salesperson", "TO_NAMES": "Sent an in-app notification to <b>{{names}}<b>", "DID_NOT_SEND": "Did not send an in-app notification", "NO_ASSIGNED_FOUND": "No assigned salesperson found", "FAILED": "Failed to send in-app notification", "AND_USER": "and {{name}}", "AND_MORE": "... and {{count}} more"}, "TEAM_MEMBERS": "Team members", "NOTIFICATION_MESSAGE": "Notification message", "NOTIFICATION_MESSAGE_PLACEHOLDER": "New activity for {{business_name}}", "NOTIFICATION_MESSAGE_HINT": "<b>Tip:</b> You can use <b>{{ business_name }}</b> as a placeholder for the account name, and <b>{{ business_id }}</b> for the account identifier (example: AG-**********).", "REQUIRED_FIELD": "This is required.", "LINK": "Notification link URL", "LINK_HELP_TEXT": "{{userType}} will be directed to this URL when they click the notification.", "PARTNER_ADMINS": "Partner Center admins", "SALESPEOPLE": "Salespeople"}, "SEND_EMAIL_NOTIFICATION": {"ASSIGNED_SALESPERSON": "Assigned Salesperson", "SEND_A_NOTIFICATION": "Send an email notification", "SEND_EMAIL": "Send an email notification to...", "ACTION": {"TO_ASSIGNED_SALESPERSON": "Send an email notification to the assigned salesperson", "TO_SINGLE_USER": "Send an email notification to <b>{{name}}</b>", "TO_MULTIPLE_SALESPEOPLE": "Send an email notification to {{userCount}} salespeople", "TO_MULTIPLE_ADMINS": "Send an email notification to {{userCount}} Partner Center admins", "CONFIG_ERROR": "Choose a new recipient or delete the step"}, "ACTIVITY": {"SENT_A_NOTIFICATION": "Sent an email notification", "TO_ASSIGNED_SALESPERSON": "Sent an email notification to the assigned salesperson", "TO_NAMES": "Sent an email notification to <b>{{names}}<b>", "DID_NOT_SEND": "Did not send an email notification", "NO_ASSIGNED_FOUND": "No assigned salesperson found", "FAILED": "Failed to send email notification", "AND_USER": "and {{name}}", "AND_MORE": "... and {{count}} more"}, "TEAM_MEMBERS": "Team members", "NOTIFICATION_SUBJECT": "Email subject", "NOTIFICATION_MESSAGE": "Email message *", "NOTIFICATION_MESSAGE_PLACEHOLDER": "New activity for {{business_name}}", "NOTIFICATION_MESSAGE_HINT": "<b>Tip:</b> You can use <b>{{ business_name }}</b> as a placeholder for the account name, and <b>{{ business_id }}</b> for the account identifier (example: AG-**********).", "REQUIRED_FIELD": "This is required.", "BUTTON_TEXT": "Button text", "LINK": "Button link URL", "LINK_HELP_TEXT": "{{userType}} will be directed to this URL when they click the button in the email.", "PARTNER_ADMINS": "Partner Center admins", "SALESPEOPLE": "Salespeople"}, "SEND_HTML_EMAIL_TO_USER": {"PANEL": {"DESCRIPTION": "The following email will be sent to the users that go through this step. If the automation is based on an account, the email will be sent to all the users associated with that account.", "DESCRIPTION_COMPANY": "The following email will be sent to the first 50 contacts associated with the company. If there are no contacts the step will fail.", "TITLE": "Send an email to users", "HEADING": "Email", "CREATE_NEW_BUTTON": "Create new"}, "ERROR": {"PREVIEW_EMAIL_FAILED": "Failed to load email preview"}, "SEND_HTML_EMAIL_TO_USER": "Send an email to users", "SEND_HTML_EMAIL_TO_CONTACT": "Send an email to contact", "SEND_HTML_EMAIL_TO_COMPANY": "Send an email to company", "ACTIVITY": {"FAILED": "Failed to send email", "TITLE": "Send an email to users", "TITLE_CONTACT": "Send an email to contacts", "TITLE_COMPANY": "Send an email to associated contacts", "SUBTITLE_COMPANY": "Sent an email to <b>{{ count }}</b> contact(s)"}, "ACTION": {"TITLE": "Send an email to users", "TITLE_WITH_TEMPLATE": "Send the <b>{{templateName}}</b> email"}, "EMAIL_BUILDER": {"SEND_PREVIEW": "Send preview email", "EDIT_EMAIL": "Edit email", "SAVE_AND_CLOSE": "Save & close", "ERROR_LOADING_CONTENT": "There was a problem loading the content", "SELECT_ANOTHER_EMAIL": "Select another email", "REPLACE_EMAIL": "Replace email"}}, "SEND_NOTIFICATIONS": {"ALL_MARKETS_WARNING": "Can't be changed for \"All markets\" automations", "NOTIFY_USERS": "Notify Users", "IN_APP_NOTIFICATION": "In-app notification", "USERS_HELP_TEXT": "By default, an in-app and email notification will be sent. However, {{userType}} won't receive notifications they've unsubscribed from.", "USER_TYPES": {"ADMINS": "admins", "FULFILLMENT_AGENTS": "digital agents", "SALESPEOPLE": "salespeople", "USERS": "users"}, "JOB_TITLE_LABEL": "With job title", "JOB_TITLE_HINT": "Only assigned salespeople with the selected job title will be notified.", "SEND_NOTIFICATION_TO": "Send a notification to...", "NOTIFICATION_TYPE_HINT": "Notifications will be sent as this type. If a user unsubscribes from this notification type, they'll still receive notifications for other types.", "NOTIFICATION_LINK": "See all notification types", "ACTIVITY": {"SENT_A_NOTIFICATION": "Sent a notification", "TO_NAMES": "Sent a notification to <b>{{names}}<b>", "DID_NOT_SEND": "Did not send notification", "FAILED": "Failed to send notification", "AND_USER": "and {{name}}", "AND_MORE": "... and {{count}} more"}, "SEND_A_NOTIFICATION": "Send a notification", "ACTIONS": {"TO_ASSIGNED_SALESPERSON": "Notify the assigned salesperson", "TO_ADDITIONAL_SALESPEOPLE": "Notify the additional assignees", "TO_ALL_ACCOUNT_USERS": "Notify all account users", "TO_SINGLE_USER": "Notify <b>{{name}}</b>", "TO_MULTIPLE_SALESPEOPLE": "Notify {{userCount}} salespeople", "TO_MULTIPLE_ADMINS": "Notify {{userCount}} admins", "TO_MULTIPLE_FULFILLMENT_AGENTS": "Notify {{userCount}} digital agents", "TO_ASSIGNED_FULFILLMENT_AGENT": "Notify the assigned digital agent", "TO_A_PARTNER_ADMIN": "Notify an admin", "FROM_PREVIOUS_STEP": "Notify a person from a previous step", "CONFIG_ERROR": "Choose a new recipient or delete the step"}, "NOTIFY_ALL_ACCOUNT_USERS_HELP_TEXT": "Notify all users added on the account", "EMAIL_NOTIFICATION": "Email notification", "TO_PARTNER_ADMIN": "Notify an admin", "TO_SALESPERSON": "Notify a salesperson", "TO_FULFILLMENT_AGENT": "Notify a digital agent", "TO_USER": "Notify the user", "SMB": {"USERS": "Users", "NOTIFY_A_USER": "Notify a user", "NEW_COMPANY_CREATED": "New company created"}}, "SEND_NOTIFICATION_TO_THE_USER": {"TITLE": "Send a notification to the user", "NOTIFICATION_OPTIONS": {"LABEL_TYPE": "Notification type", "GENERIC": "Generic", "ABANDONED_SHOPPING_CART": "Abandoned Shopping Cart", "HIGH_PRIORITY": "High Priority", "HOT_LEADS": "Hot leads", "UNKNOWN": "a"}, "ABANDONED_SHOPPING_CART_HELP_TEXT": "This is a prebuilt notification that currently can’t be customized.", "NOTIFICATION_TYPES": {"LABEL": "Send via... *", "AT_LEAST_ONE_ERROR": "At least one type must be selected", "EMAIL": "Email", "IN_APP": "In app"}, "ACTIVITY": {"TO_NAME": "Sent <b>{{notificationType}}</b> notification to <b>{{name}}</b>", "SENT_A_NOTIFICATION": "Sent <b>{{notificationType}}</b> notification", "DID_NOT_SEND": "Did not send a notification"}, "ACTION": {"TO_USER": "Send <b>{{notificationType}}</b> notification to <b>{{name}}</b>", "WITHOUT_USER": "Send <b>{{notificationType}}</b> notification to the user", "CONFIG_ERROR": "Choose a new recipient or delete the step"}}, "CREATE_SALES_TASK": {"ASSIGNED_SALESPERSON": "Assigned Salesperson", "CREATE_SALES_TASK_V2": "Create a sales task (legacy)", "CONFIG_ERROR": "Choose a new assignee or delete the step", "CREATED_SALES_TASK": "Created a sales task", "INSTRUCTIONS": "This will appear in Sales & Success Center", "DESCRIPTION": "Create a sales task and assign to salesperson in project account", "ACTION": {"TO_ASSIGNED_SALESPERSON": "Create the <b>{{ taskName }}</b> sales task for the assigned salesperson", "TO_SINGLE_USER": "Create the <b>{{ taskName }}</b> sales task for {{ name }}"}, "ACTIVITY": {"TO_SINGLE_USER": "Created the <b>{{ taskName }}</b> sales task for {{ name }}", "DID_NOT_CREATE": "Did not create the sales task", "NO_ASSIGNED_FOUND": "No assigned salesperson found"}, "ACTIVITY_FAILED": "Failed to create a sales task to the assigned user", "TASK_NAME": "Task name", "ASSIGNEE": "Assignee", "TASK_DUE_IN": "Task due in", "DAYS": "days *", "DAY_VALIDATION": "This is not a whole number higher than or equal to 0.", "TASK_PRIORITY": {"LABEL": "Task priority", "PLACEHOLDER": "Select a priority", "OPTIONS": {"HIGH": "High", "MEDIUM": "Medium", "LOW": "Low"}}, "TASK_TYPE": {"LABEL": "Task type", "PLACEHOLDER": "Select a type", "OPTIONS": {"CALL": "Call", "EMAIL": "Email", "MESSAGE": "Message", "CONNECTION": "Connection", "TO-DO": "To-do"}}}, "CREATE_FULFILLMENT_TASK": {"ASSIGNED_DIGITAL_AGENT": "Assigned Digital Agent", "CREATE_FULFILLMENT_TASK": "Create a fulfillment task", "CREATED_FULFILLMENT_TASK": "Created a fulfillment task", "INSTRUCTIONS": "This will appear in Task Manager", "DESCRIPTION": "Create a task and assign to an agent", "ACTION": {"TO_ASSIGNED_FULFILLMENT_AGENT": "Create the <b>{{ taskName }}</b> task for the assigned agent", "TO_SINGLE_AGENT": "Create the <b>{{ taskName }}</b> task for {{ name }}", "TO_NO_AGENT": "Create the <b>{{ taskName }}</b> task with no agent assigned"}, "ACTIVITY": {"TO_SINGLE_AGENT": "Created the <b>{{ taskName }}</b> task for {{ name }}", "DID_NOT_CREATE": "Did not create the task", "TASK_AGENT_NOT_FOUND": "Created the <b>{{ taskName }}</b> task with no agent assigned.", "UNASSIGNED": "Created <b>{{ taskName }}</b> for the account's assigned agent, but there was no assigned agent when the automation ran. The task was created but was not assigned to anyone."}, "ACTIVITY_FAILED": "Failed to create a task for the assigned user", "TASK_NAME": "Task name", "ASSIGNEE": "Assignee", "TASK_DUE_IN": "Task due in", "DAYS": "days *", "DAY_VALIDATION": "This is not a number higher than 0.", "NO_USER": "None"}, "CREATE_BUSINESS_ACTIVITY": {"ASSIGNED_SALESPERSON": "Assigned Salesperson", "CREATE_BUSINESS_ACTIVITY": "Log activity and hotness rating", "CREATED_BUSINESS_ACTIVITY": "Logged activity with hotness rating of ", "INSTRUCTIONS": "The following business activity and hotness rating will be logged for an account in Sales & Success Center.", "ACTION": "Log activity with hotness rating of ", "ACTIVITY_FAILED": "Failed to log activity and hotness rating", "HOTNESS_RATING": {"LABEL": "Hotness rating", "OPTIONS": {"NO_FLAME": "0 flames", "1_FLAME": "1 flame", "2_FLAMES": "2 flames", "3_FLAMES": "3 flames"}}, "ACTIVITY_DETAILS": "Activity", "ACTIVITY_DETAILS_PLACEHOLDER": "New activity for {{business_name}}", "REQUIRED_FIELD": "This is required.", "MESSAGE_HINT": "<b>Tip:</b> You can use <b>{{ business_name }}</b> as a placeholder for the account name, and <b>{{ business_id }}</b> for the account identifier (example: AG-**********)."}, "CREATE_USER": {"CREATE_USER": "Create a user", "CREATED_USER": "Created <b>{{ userName }}</b>", "A_USER": "a user", "DESCRIPTION": "This action will create a partner user (based on information provided by the trigger)."}, "QUERY_OPPORTUNITIES": {"GET_OPPORTUNITY": "Get an opportunity", "QUERY_OPPORTUNITIES": "Query opportunities", "DESCRIPTION": "Select the first opportunity found based on:", "GET_DESCRIPTION": "Retrieve information about the following opportunity:", "FIELD": "Field", "DIRECTION": "Direction", "GET_DISCLAIMER": "This step will search for an opportunity associated with the account, based on the criteria below. The data from the first opportunity found will be available to use in subsequent steps.", "QUERY_DISCLAIMER": "This will query the opportunities for the account and add the first opportunity found to the available automation data. Don't forget to turn on Data Passing to use this value in subsequent steps.", "SEARCH_CRITERIA": "Search criteria", "NEWEST_OPPORTUNITY": "Newest opportunity", "OLDEST_OPPORTUNITY": "Oldest opportunity", "ACTIVITY": "Find the first opportunity sorted by {{ field }} in {{ direction }} order", "ACTIVITY_2": "Retrieve the {{ field }} for the account", "FIELDS": {"CREATED": "Created", "LAST_ACTIVITY": "Last activity"}, "OPPORTUNITY_NOT_FOUND": "Opportunity not found", "FOUND_OPPORTUNITY": "Found opportunity: <b>{{ opportunityName }}</b>", "DIRECTIONS": {"ASCENDING": "Ascending", "DESCENDING": "Descending"}}, "CHANGE_OPPORTUNITY_PIPELINE_STAGE": {"CHANGE_OPPORTUNITY_PIPELINE_STAGE": "Change opportunity stage", "UPDATE_OPPORTUNITY_STAGE": "Update opportunity stage", "DESCRIPTION": "Change the pipeline and/or stage of an opportunity", "UPDATE_DESCRIPTION": "Update the pipeline and/or stage of the opportunity", "ACTIVITY": "Update the opportunity to pipeline <b>{{ pipeline }} </b> and stage <b>{{ stage }}</b>", "PIPELINE_NOT_FOUND": "Pipeline not found", "STAGE_NOT_FOUND": "Stage not found"}, "REOPEN_OPPORTUNITY": {"REOPEN_OPPORTUNITY": "Reopen opportunity", "REASON": "Reason", "DESCRIPTION": "This will reopen the opportunity and allow you to edit its details."}, "ASSIGN_SALESPERSON": {"ASSIGN_SALESPERSON": "Assign a salesperson", "CONFIG_ERROR": "Choose new salespeople or delete the step", "TO_MULTIPLE_SALESPEOPLE": "Assign one of {{ userCount }} salespeople to the account", "TO_SINGLE_SALESPERSON": "Assign <b>{{ salespersonName }}</b> to the account", "ASSIGNED_SALESPERSON": "<b>{{ salesperson<PERSON>ame }}</b> was assigned to the account", "REASSIGNED_SALESPERSON": "Reassigned the account to <b>{{ salespersonName }}</b> from {{ previousSalespersonName }}", "ALREADY_ASSIGNED": "No salesperson was assigned. The account was already assigned to {{ previousSalespersonName }}", "ACTIVITY_FAILED": "Failed to assign salesperson to account", "A_SALESPERSON": "a salesperson", "SALESPEOPLE": "Salespeople", "DESCRIPTION": "One of the selected salespeople will be assigned in random order to an account. If no salesperson is selected, all available salespeople will be considered.", "REASSIGN_SALESPERSON_DESCRIPTION": "If an account already has an assigned salesperson, then...", "KEEP_ORIGINAL_SALESPERSON": "Keep the original salesperson", "REPLACE_SALESPERSON": "Replace the assigned salesperson", "REPLACE_SALESPERSON_HINT": "This only affects the primary assignee. Any additional assignees on the account will remain.", "MARKET_WARNING": "Trying to assign a salesperson to an account that they don't have permissions to will fail. You can add filters before this step to ensure the account is in the correct market."}, "ASSIGN_SALES_TEAM": {"ASSIGN_SALES_TEAM": "Assign a sales team", "CONFIG_ERROR": "Choose a new sales team or delete the step", "TO_SALES_TEAM": "Assign a member from the <b>{{salesTeam}}</b> sales team to the account", "A_SALES_TEAM": "Assign a member from a sales team to the account", "ASSIGNED_SALESPERSON": "<b>{{salesperson<PERSON>ame}}</b> was assigned to the account", "REASSIGNED_SALESPERSON": "Reassigned the account to <b>{{salespersonName}}</b> from {{previousSalespersonName}}", "ALREADY_ASSIGNED": "No salesperson was assigned. The account was already assigned to {{previousSalespersonName}}", "ACTIVITY_FAILED": "Failed to assign salesperson to account", "SALES_TEAM": "Sales team", "A_SALESPERSON": "a salesperson", "DESCRIPTION": "One salesperson from the selected sales team will be assigned in random order to an account.", "REASSIGN_SALESPERSON_DESCRIPTION": "If an account already has an assigned salesperson, then...", "KEEP_ORIGINAL_SALESPERSON": "Keep the original salesperson", "REPLACE_SALESPERSON": "Replace the assigned salesperson", "REPLACE_SALESPERSON_HINT": "This only affects the primary assignee. Any additional assignees on the account will remain."}, "PAUSE_OR_CANCEL_TASK_MANAGER_ACCOUNT": {"GENERAL": {"TITLE": "Pause or cancel the Task Manager account"}, "ACTION": {"PAUSE_ONLY": {"TITLE": "Pause the account", "DESCRIPTION": "Stop any new tasks or projects from being created", "NODE_TEXT": "Pause the Task Manager account"}, "PAUSE_AND_CLOSE": {"TITLE": "Cancel the account", "DESCRIPTION": "Stop any new tasks or projects from being created and close all the existing tasks or projects", "NODE_TEXT": "Cancel the Task Manager account"}}, "ACTIVITY": {"FAILED": "Could not pause and/or cancel the Task Manager account.", "TITLE": "Pause or cancel the Task Manager account"}, "PANEL": {"HINT": "Select which action should be completed for the Task Manager account"}}, "CREATE_TASK_MANAGER_ACCOUNT": {"GENERAL": {"TITLE": "Add account to Task Manager"}, "ACTIVITY": {"FAILED": "Could not add account to Task Manager.", "TITLE": "Add account to Task Manager"}}, "IF_ELSE_BRANCH": {"IF_ELSE_BRANCH": "If/else branch", "DESCRIPTION": "Continue the automation in a different way depending on whether these conditions are met.", "GOAL_DESCRIPTION": "When an automation goal is reached, the automation will stop running. The conditions of the goal are checked before each step in an automation.<b/> Note that if your goal relies on data provided by a step other than the trigger, that step will need to run before the goal is checked.", "NO_ACTION_DATA": "There was an issue loading condition. Please check the setting for this step.", "DELETED_PREVIOUS_STEP_COMPARISON": "A condition on a now deleted previous step", "ACTIVITY": {"CONTINUED_ON_YES": "Continued on the Yes branch", "CONTINUED_ON_NO": "Continued on the No branch", "CONTINUED_ON_BRANCH": "Continued on the <b>{{ branch }}</b> branch", "COMPLETED_AUTOMATION": "Completed automation", "NO_BRANCHES": "No branches to continue on"}}, "DELAY": {"DELAY": "Delay", "WAIT_UNTIL": "Wait until", "MUST_SELECT_ONE": "Must select one of the options to wait until.", "AMOUNT_OF_TIME": "Amount of time", "WAIT_FOR": "Wait for", "TIME_ZONE_EXPLANATION_1": "The settings below will use the time zone selected in the ", "TIME_ZONE_EXPLANATION_2": " section.", "AND_THEN": "And then...", "WAIT_UNTIL_A_MONTH_DAY": "Wait until a specific day of the month", "MONTH_END": "Month End", "INVALID_DATE": "Invalid Date", "WARNING_NOT_ALL_MONTHS": "Not all months contain the dates 29, 30, and 31.", "ADVICE_CHOOSE_MONTH_END": "If you want to delay until the end of the month, select \"Month End\" instead.", "INFO_MONTH_END_SELECTED": "You have selected \"Month End\"", "WILL_RUN_AT_END_OF_MONTH": "This will either be the 28th, 29th, 30th, or 31st. It will depend on the current month when the automation runs.", "WAIT_UNTIL_A_WEEKDAY": "Wait until a weekday", "DAY_EXPLANATION": "The automation will delay until 12:00am on the first available day from your selection", "WAIT_UNTIL_TIME_IS_WITHIN_RANGE": "Wait until time is within a range", "TIME_RANGE_EXPLANATION": "The automation will delay until the time falls within the selected range", "SUNDAY": "Sunday", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "TIME_PERIOD": {"UNIT": "Time Unit", "MINUTES": "minutes", "WAIT_ONE_MINUTE": "Wait for <b>1 minute</b>", "WAIT_MINUTES": "Wait for <b>{{ time }} minutes</b>", "HOURS": "hours", "WAIT_ONE_HOUR": "Wait for <b>1 hour</b>", "WAIT_HOURS": "Wait for <b>{{ time }} hours</b>", "DAYS": "days", "WAIT_ONE_DAY": "Wait for 1 day", "WAIT_DAYS": "Wait for {{ time }} days", "OVER_YEAR_ERROR": "Cannot exceed 365 days", "LESS_THAN_ONE": "Cannot be less than 1", "WAIT_WEEKDAY": "Wait until <b>{{ weekday }}</b>", "WAIT_WEEKDAYS": "Wait until <b>{{ first_weekdays }}, or {{ last_weekday }}</b>", "WAIT_DAY_OF_MONTH": "Wait until the day of the month is <b>{{ day }}</b>", "WAIT_DAYS_OF_MONTH": "Wait until the day of the month is <b>{{ first_days }}, or {{ last_day }}</b>", "WAIT_TIME_RANGE": "Wait until the time is between <b>{{ start_range }} and {{ end_range }}</b>", "THEN_WAIT_WEEKDAY": "And then wait until <b>{{ weekday }}</b>", "THEN_WAIT_WEEKDAYS": "And then wait until <b>{{ first_weekdays }}, or {{ last_weekday }}</b>", "THEN_WAIT_DAY_OF_MONTH": "And then wait until the day of the month is <b>{{ day }}</b>", "THEN_WAIT_DAYS_OF_MONTH": "And then wait until the day of the month is <b>{{ first_days }}, or {{ last_day }}</b>", "THEN_WAIT_TIME_RANGE": "And then wait until the time is between <b>{{ start_range }} and {{ end_range }}</b>"}, "ACTIVITY": {"STARTED_DELAY": "Started delay"}}, "DELAY_UNTIL_EVENT": {"DELAY_UNTIL_EVENT": "Delay until an event happens", "TIME_LIMIT": "Time limit", "TIME_LIMIT_DESCRIPTION": "If the event doesn’t happen within this time limit, the automation will proceed to the next step.", "WAIT_UP_TO": "Wait up to", "WORKFLOW_STEP_SUBTITLE": "Wait a certain amount of time for a specific trigger before proceeding through the workflow.", "WORKFLOW_STEP_DESCRIPTION": "First specify the workflow data for this <b>delay until</b> step. The workflow data will determine what events are available.", "WORKFLOW_STEP_SELECTOR": "Workflow data origin", "WORKFLOW_DATA_TYPE_SELECTOR": "That contains the data field", "WAIT_FOR_DATA": "AND <br /> {{ dataType }} matches the {{ dataType }} from the {{ name }} step", "WAIT_FOR_DATA_PARAM": "<br /> Referencing <b>{{ dataType }}</b> data from the <b>{{ name }}</b> step", "TIME_PERIOD": {"UNIT": "Time Unit", "VALIDATION": "This is not a number higher than 0.", "MINUTES": "minutes *", "WAIT_ONE_MINUTE": "Wait up to 1 minute", "WAIT_MINUTES": "Wait up to {{ time }} minutes", "HOURS": "hours *", "WAIT_ONE_HOUR": "Wait up to 1 hour", "WAIT_HOURS": "Wait up to {{ time }} hours", "DAYS": "days *", "WAIT_ONE_DAY": "Wait up to 1 day", "WAIT_DAYS": "Wait up to {{ time }} days"}, "ACTIVITY": {"EVENT_HAPPENED": "Continued on the <strong>Event happened</strong> branch", "EVENT_DID_NOT_HAPPEN": "Continued on the <strong>Event did not happen</strong> branch", "WAITED_ONE_MINUTE": "Waited for 1 minute", "WAITED_MINUTES": "Waited for {{ time }} minutes", "WAITED_ONE_HOUR": "Waited for 1 hour", "WAITED_HOURS": "Waited for {{ time }} hours", "WAITED_ONE_DAY": "Waited for 1 day", "WAITED_DAYS": "Waited for {{ time }} days"}}, "AB_BRANCH": {"TITLE": "A/B branch", "DESCRIPTION": "Split the workflow traffic into separate paths", "TO_BRANCH_A": "Percentage going to branch A:", "TO_BRANCH_B": "Percentage going to branch B:", "INPUT_VALIDATION": "Must be between 0-100", "ACTION": {"TITLE": "Split <strong>{{branch_a_value}}%</strong> of traffic to branch A and <strong>{{branch_b_value}}%</strong> to branch B", "CONFIG_ERROR": "Specify different branch information"}, "ACTIVITY": {"FAILURE": "Failed to get branch information", "CONTINUED_ON_A": "Continued on the A branch", "CONTINUED_ON_B": "Continued on the B branch", "STEP_TITLE": "A/B branch", "ACTIVITY_TITLE": "Selected branch"}}, "END_ACTION": {"TITLE": "End this automation", "DESCRIPTION": "This step will end the automation.", "REASON_LABEL": "Reason (optional)", "NODE": {"END": "End this automation", "END_WITH_REASON": "End this automation because {{reason}}"}, "ACTIVITY": {"STEP": "End the automation", "ACTIVITY": "Ended the automation"}}, "RATE_FILTER": {"TITLE": "Rate filter", "DESCRIPTION": "Split the workflow into separate paths depending on the following rate count condition<br /><br />If you turn off the automation, the rate counts will be reset.", "CONDITION": "Condition", "SPECIFIC_ENTITY": "The {{entity}}", "ANY_ENTITY": "Any {{entity}}", "AUTOMATION": "The Automation", "ON_ACCOUNT": "on the account", "REACHED_STEP": "has reached this step at least", "REACHED_STEP_MORE": "has reached this step more than", "FREQUENCY": "Frequency", "LENGTH": "Length", "PERIOD": "Period", "TIMES": "times", "IN_THE_PAST": "in the past", "HOURS": "Hours", "DAYS": "Days", "ENTITY_TYPE_INVALID": "Invalid entity type", "PERIOD_INVALID": "Invalid period", "OUT_OF_BOUNDS": "Must be between 1 and 100 (inclusive)", "PREPROCESSOR": {"TITLE": "Activation limit", "DESCRIPTION": "Prevent each item from activating more than", "TIMES_WITHIN": "times within", "OUT_OF_BOUNDS": "Must be between 1 and 500 (inclusive)", "SUB_TITLE": "<br>Activation limit: <strong>{{frequency}} {{time}} within {{length}} {{period}}</strong>", "SUCCESS": "Products activated for the account", "RATE_ERROR": "Activation limit reached - Products were not activated for the account", "PRODUCT_ERROR": "Unable to activate prodcuts due to an error"}, "ACTION": {"TITLE": "Rate filter"}, "ACTIVITY": {"FAILURE": "Failed to execute rate limit", "TITLE": "<strong>{{rateLimitingType}}</strong> has reached this step at least <strong>{{frequency}}</strong> {{time}} in the past {{length}} {{period}}", "TITLE_MORE": "<strong>{{rateLimitingType}}</strong> has reached this step more than <strong>{{frequency}}</strong> {{time}} in the past {{length}} {{period}}", "TIME": "time", "TIMES": "times", "HOUR": "hour", "HOURS": "hours", "DAY": "day", "DAYS": "days", "YES_BRANCH": "Passed the rate filter, took the <b>yes</b> branch", "NO_BRANCH": "Did not pass the rate filter, took the <b>no</b> branch"}}, "JUMP": {"TITLE": "Jump to a step", "DESCRIPTION": "Redirect to another workflow step in the automation", "OUT_OF_BOUNDS": "Jump steps can only run up to 100 times in an automation run", "STEP": "Jump to workflow step", "MAXIMUM_NUMBER_OF_JUMPS": "Maximum number of jumps per run", "MAXIMUM_NUMBER_OF_JUMPS_PRE_INPUT": "Jump to workflow step at most", "MAXIMUM_NUMBER_OF_JUMPS_POST_INPUT": "times", "MAXIMUM_NUMBER_OF_JUMPS_DESCRIPTIONS": "Set the maximum amount of jumps for this step to avoid an infinite loop. If the max is reached, the automation will end.", "UNTITLED_STEP": "Untitled step", "GO_TO_ACTION": "Go to action", "ACTION": {"TITLE": "Jump to a step", "NODE_DATA": "Jump to <b>{{name}}</b> at most <b>{{maximumJumps}}</b> times."}, "ACTIVITY": {"YES_BRANCH": "Jumped to <b>{{name}}</b>", "NO_BRANCH": "Maximum number of jumps reached, ending the automation"}}, "ACTIVATE_PRODUCT": {"ACTIVATE_PRODUCT": "Activate products", "ACTIVATE_EXPRESS_PRODUCT": "Activate express products", "FREQUENCIES": {"MONTHLY": "month", "YEARLY": "year", "ONETIME": "one-time"}, "PRICING_NOT_CONFIGURED": "Pricing is not configured for this product", "DESCRIPTION": "The following products will be activated for an account.", "ACTION": "Activate <b>{{ productName }}{{ plusOthers }}</b>", "ACTION_LOADING_FAILED": "Failed to load data for activate product step", "ACTIVITY_TEXT": "Order to activate <b>{{ productName }}{{ plusOthers }}</b> was created. For more details, view <b>{{ orderLink }}</b>", "ACTIVITY_FAILED": "Failed to activate products", "ACTIVITY_LOADING_FAILED": "Failed to load data for activity", "PRODUCTS": "Products", "PRODUCT_NAME": "Product name", "WHOLESALE_COST": "Wholesale cost", "CONFIG_ERROR": "Choose new products or delete the step", "CAVEAT": "Products that require order forms cannot be activated from this action", "ADD_ITEMS": "Add items", "ADD_TO_ACTIVATE": "Add items to activate"}, "DEACTIVATE_PRODUCT": {"DEACTIVATE_PRODUCT": "Deactivate products", "DESCRIPTION": "The following products will be deactivated for the account.", "ALL_PRODUCTS": "All Products", "HINT": {"CANCEL": "The products will be turned off at the end of the current payment period.", "DEACTIVATE": "The products will be turned off immediately."}, "WARNING": {"WARNING": "Product data will be lost", "CANCEL_WARNING": "at the end of the current payment period. Data will not be recoverable.", "DEACTIVATE_WARNING": "once the product is deactivated. Data will not be recoverable."}, "ACTION": {"CANCEL": "Cancel <b>{{ productName }}</b>", "DEACTIVATE": "Deactivate <b>{{ productName }}</b>", "ACTION_LOADING_FAILED": "Failed to load data for deactivate products step"}, "ACTIVITY": {"CANCEL": "Cancelled <b>{{ productName }}</b>", "DEACTIVATE": "Deactivated <b>{{ productName }}</b>", "NON_DEACTIVATED": "<b>{{ productName }}</b> was not deactivated as the products are not available in this account", "ACTIVITY_LOADING_FAILED": "Failed to load activity data", "ACTIVITY_FAILED": "Failed to deactivate products", "DEACTIVATE_PRODUCT_FAILURE": "Unable to deactivate product", "PRODUCT_NOT_FOUND": "Product not found", "DEACTIVATE_ADDON_FAILURE": "Unable to deactivate add-on", "ADDON_NOT_FOUND": "Add-on not found"}, "PRODUCT": "Product", "EDITION": "Edition", "DEACTIVATION_TYPE": "Deactivation type", "DEACTIVATION_TYPES": {"CANCEL": "Cancel", "DEACTIVATE": "Deactivate immediately"}, "EDITION_AND_ADDON": "All editions and add-ons", "INCLUDE_TRIALS": "Include associated trial products in the deactivation", "CONFIG_ERROR": "Choose new products or delete the step"}, "ADD_TAG": {"AND": "And", "ADD_TAG": "Add tags to the account", "TAG": "Tag", "DESCRIPTION": "The following tags will be added to the account.", "ACTION": {"TITLE": "Add tags to the account", "SUBTITLE_SINGULAR": "Add the <b>{{ tags }}</b> tag to the account", "SUBTITLE_PLURAL": "Add the <b>{{ tags }}</b> tags to the account</b>", "CONFIG_ERROR": "Choose new tags or delete the step"}, "ACTIVITY": {"TITLE": "Added tags to the account", "SUBTITLE_SINGULAR": "Added the <b>{{ tags }}</b> tag to the account", "SUBTITLE_PLURAL": "Added the <b>{{ tags }}</b> tags to the account</b>", "FAILED_TO_ADD_TAGS": "Failed to add tags"}, "VALIDATION": {"MAX_LENGTH": "Tag requires at least one character", "MIN_LENGTH": "Tag can be no longer than 50 characters", "NON_WHITESPACE": "Tag must have at least one non-whitespace character"}}, "SET_ACCOUNT_LIFECYCLE_STAGE": {"DESCRIPTION": "The account will be set to the selected lifecycle stage", "LIFECYCLE_STAGE": "Set account lifecycle stage", "USE_INTERNAL_EVALUATION": "Account lifecycle will be set to one of <b>Lead, Prospect, Customer</b> using internal evaluation", "LIFECYCLE": "Lifecycle", "LIFECYCLE_STAGES": {"LEAD": "Lead", "PROSPECT": "Prospect", "CUSTOMER": "Customer"}, "ACTION": {"TITLE": "Set the account's lifecycle stage", "SUBTITLE": "Set the account's lifecycle stage to <b>{{lifecycleStageName}}</b>", "CONFIG_ERROR": "This step is not configured properly, please fix the step or delete it", "USE_INTERNAL_EVALUATION": "Use internal lifeycle stage evaluation", "SELECT_LIFECYCLE": "Select a lifecycle stage to set the account to", "LIFECYCLE_STAGE": "Lifecycle stage"}, "ACTIVITY": {"FAILED_TO_SET_LIFECYCLE": "Failed to update the account's lifecycle stage"}}, "REMOVE_TAG": {"AND": "And", "REMOVE_TAG": "Remove tags from the account", "REMOVE_ALL": "Remove all tags from the account", "TAG": "Tag", "DESCRIPTION": "The following tags will be removed from the account.", "ACTION": {"TITLE": "Remove tags from the account", "SUBTITLE_SINGULAR": "Remove the <b>{{ tags }}</b> tag from the account", "SUBTITLE_PLURAL": "Remove the <b>{{ tags }}</b> tags from the account</b>", "SUBTITLE_REMOVE_ALL": "Remove <b>all tags</b> from the account</b>", "CONFIG_ERROR": "Choose new tags or delete the step"}, "ACTIVITY": {"TITLE": "Removed tags from the account", "SUBTITLE_SINGULAR": "Removed the <b>{{ tags }}</b> tag from the account", "SUBTITLE_PLURAL": "Removed the <b>{{ tags }}</b> tags from the account</b>", "SUBTITLE_REMOVE_ALL": "Removed all tags from the account", "FAILED_TO_REMOVE_TAGS": "Failed to remove tags from the account"}, "VALIDATION": {"MAX_LENGTH": "Tag requires at least one character", "MIN_LENGTH": "Tag can be no longer than 50 characters", "NON_WHITESPACE": "Tag must have at least one non-whitespace character"}}, "ADD_TAGS_TO_ORDER": {"AND": "And", "ADD_TAGS": "Add tags to the order", "TAG": "Tag", "DESCRIPTION": "The following tags will be added to the order.", "ACTION": {"TITLE": "Add tags to the order", "SUBTITLE_SINGULAR": "Add the <b>{{ tags }}</b> tag to the order", "SUBTITLE_PLURAL": "Add the <b>{{ tags }}</b> tags to the order</b>", "CONFIG_ERROR": "Choose new tags or delete the step"}, "ACTIVITY": {"TITLE": "Added tags to the order", "SUBTITLE_SINGULAR": "Added the <b>{{ tags }}</b> tag to the order", "SUBTITLE_PLURAL": "Added the <b>{{ tags }}</b> tags to the order</b>", "FAILED_TO_ADD_TAGS": "Failed to add tags to the order"}}, "REMOVE_TAGS_FROM_ORDER": {"AND": "And", "REMOVE_TAG": "Remove tags from the order", "REMOVE_ALL": "Remove all tags from the order", "TAG": "Tag", "DESCRIPTION": "The following tags will be removed from the order.", "ACTION": {"TITLE": "Remove tags from the order", "SUBTITLE_SINGULAR": "Remove the <b>{{ tags }}</b> tag from the order", "SUBTITLE_PLURAL": "Remove the <b>{{ tags }}</b> tags from the order</b>", "SUBTITLE_REMOVE_ALL": "Remove <b>all tags</b> from the order</b>", "CONFIG_ERROR": "Choose new tags or delete the step"}, "ACTIVITY": {"TITLE": "Removed tags from the order", "SUBTITLE_SINGULAR": "Removed the <b>{{ tags }}</b> tag from the order", "SUBTITLE_PLURAL": "Removed the <b>{{ tags }}</b> tags from the order</b>", "SUBTITLE_REMOVE_ALL": "Removed all tags from the order", "FAILED_TO_REMOVE_TAGS": "Failed to remove tags"}}, "DECLINE_SALES_ORDER": {"DECLINE_ORDER": "Decline the order", "DECLINED_REASON": "Declined Reason", "ADD_MORE_DETAIL": "Enter more information for the salesperson", "DESCRIPTION": "The order will be declined with the following reason.", "SPECIFY_NEW": "Specify a declined reason", "USE_FROM_PREVIOUS_STEP": "Use declined reason from a previous workflow step", "REQUIRED_FIELD": "Enter a declined reason", "FROM_PREVIOUS_STEP": "from a previous workflow step", "ACTION": {"FROM_PREVIOUS_STEP": "Decline the order with reason from a previous workflow step: {{previousStep}}", "TITLE": "Decline the order with reason: {{ reason }}", "DISCLAIMER": "To decline, this order needs to have one of the following statuses: <b>Submitted</b>, <b>Resubmitted</b>, <b>Processing Payment</b>", "DISCLAIMER_V2": "To decline, this order needs to have one of the following statuses: <b>Drafted</b>, <b>Submitted</b>, <b>Resubmitted</b>, <b>Processing Payment</b>"}, "ACTIVITY": {"TITLE": "Declined the order with reason: <b>{{ reason }}</b>", "FAILED": "Failed to decline the order"}}, "ACTIVATE_SALES_ORDER": {"ACTIVATE_ORDER": "Activate products for the order", "DESCRIPTION": "The products for the order will be activated.", "ACTION": {"TITLE": "Activate products for the order", "DISCLAIMER": "To activate, this order needs to have one of the following statuses: <b>Approved</b>, <b>Scheduled Activation</b>, <b>Awaiting Payment</b>, <b>Processing Payment</b>"}, "TIME_OF_ACTIVATION": {"LABEL": "Time of activation", "SCHEDULE_ACTIVATION": "Activate on start date of the sales  order contract", "ACTIVATE_NOW": "Activate once automation reaches this step", "HINT": "Products will activate on this step if date has passed"}, "ACTIVITY": {"TITLE": "Product activation initiated for the order", "SCHEDULE_TITLE": "Product activation initiated for the order start date", "FAILED": "Failed to activate products for the order"}}, "ARCHIVE_SALES_ORDER": {"ARCHIVE_ORDER": "Archive the order", "DESCRIPTION": "The order will be archived.", "ACTION": {"TITLE": "Archive the order", "DISCLAIMER": "To archive, this order cannot already have the following status: <b>Archived</b>"}, "ACTIVITY": {"TITLE": "Archive the order", "FAILED": "Failed to archive the order"}}, "APPROVE_SALES_ORDER": {"APPROVE_ORDER": "Approve the order", "DESCRIPTION": "The order will be approved.", "ACTION": {"TITLE": "Approve the order", "DISCLAIMER": "To approve, this order needs to have one of the following statuses: <b>Pending</b>, <b>Resubmitted</b>"}, "ACTIVITY": {"TITLE": "Approved the order", "FAILED": "Failed to approve the order"}}, "SUBMIT_ORDER_FOR_ADMIN_APPROVAL": {"SUBMIT_ORDER_FOR_ADMIN_APPROVAL": "Submit the order for admin approval", "DESCRIPTION": "The order will be submitted for admin approval.", "ACTION": {"TITLE": "Submit the order for admin approval", "DISCLAIMER": "To submit for admin approval, this order needs to have the following status: <b>Draft</b>"}, "ACTIVITY": {"TITLE": "Submitted the order for admin approval", "FAILED": "Failed to submit the order for admin approval"}}, "ADD_TO_LIST": {"ADD_TO_LIST": "Add the account to a list", "ACTION": "Add the account to the <a target=\"_blank\" href=\"/action-lists/{{actionListId}}/accounts\" style=\"font-weight: 500;\">{{actionListName}}</a> list", "ACTIVITY": "Added the account to the <a target=\"_blank\" href=\"/action-lists/{{actionListId}}/accounts\" style=\"font-weight: 500;\">{{actionListName}}</a> list", "CONFIG_ERROR": "Choose a new list or delete the step"}, "TRIGGER_WEBHOOK": {"TRIGGER_WEBHOOK": "Trigger a webhook", "ACTIVITY": {"TITLE": "Triggered webhook", "SUBTITLE": "Called <b>{{ endpointUrl }}</b>", "SUBTITLE_WITH_RESPONSE": "Called <b>{{ endpointUrl }}</b> and received response:<pre>{{ responseBody }}</pre>"}, "ACTION": "Call <b>{{ endpointUrl }}</b>", "TEMPLATE": "Trigger a webhook", "PANEL": {"METHOD": "Method", "POST": "Post", "WEBHOOK_URL": "Webhook URL", "WEBHOOK_URL_SECURE": "Webhook URLs must be secure", "VERIFIER_TOKEN": "Verifier token", "VERIFIER_TOKEN_DESC": "Use the verifier token to validate the webhook notifications are from Vendasta.", "SAMPLE_HEADER": "Sam<PERSON> header", "SAMPLE_HEADER_DESC": "The webhook will be delivered with the following header:", "SAMPLE_BODY": "Sample body", "SAMPLE_BODY_DESC": "The webhook will be delivered with the following body:", "SEND_TEST_WEBHOOK": "Send test webhook", "ADDITIONAL_FIELDS": "Additional Fields", "ADD_ADDITIONAL_FIELDS": "Add additional fields to the webhook body", "FIELD": "Field", "VALUE": "Value", "EMPTY_FIELD_VALUE": "Empty", "ADD_FIELD": "Add Field", "HELP_ARTICLE_LINK": "Learn more about triggering a webhook <a target=\"_blank\" href=\"https://developers.vendasta.com/platform/7193b5f26a395-use-the-trigger-a-webhook-action\" style=\"font-weight: 500;\">here</a>"}, "UPGRADE_SUBSCRIPTION_TITLE": "Available on paid subscription plans", "UPGRADE_SUBSCRIPTION_DESCRIPTION": "Pass information from an automation to other web applications in near-real time. Keep external databases up to date, start Zapier automations, and more."}, "OUTGOING_WEBHOOK": {"OUTGOING_WEBHOOK": "Send a webhook", "ACTIVITY": {"TITLE": "Sent webhook", "SUBTITLE": "Called <b>{{ endpointUrl }}</b>", "SUBTITLE_WITH_RESPONSE": "Executed command:<br />{{ curlCommand }}<br />and received response:<br /><pre>{{ responseBody }}</pre>"}, "ACTION": "Send data via webhook to <b>{{ endpointUrl }}</b>", "PANEL": {"METHOD_GET": "GET", "METHOD_POST": "POST", "METHOD_DELETE": "DELETE", "METHOD_PUT": "PUT", "COPY_AS_CURL": "Copy example request as curl", "TO_TEST": "to test this webhook", "CURL_COPIED": "Curl command copied to clipboard", "QUERY_PARAMS": "Query parameters", "HEADERS": "Headers", "COOKIES": "Cookies", "JSON_BODY": "JSON Body"}}, "GET_MY_TEAM_MEMBER": {"TITLE": "Get my team member", "ALERT": "This step outputs a \"found\" signal that allows you to use an \"if/else branch\" step with workflow data to handle when a team member is not found", "ACTIVITY": {"TITLE": "Looked for a team member with email address: {{ email }}", "SUBTITLE_FOUND": "Found team member", "SUBTITLE_NOT_FOUND": "Could not find team member"}}, "GET_MY_BUSINESS_INFO": {"TITLE": "Get my business profile", "DESCRIPTION": "This step outputs details of your business to help with templating and personalization", "ACTIVITY": {"TITLE": "Retrieved your business profile"}}, "GET_CONNECTION_BUSINESS_DATA": {"TITLE": "Get business data for a connection", "ACTION": "Get business data for a connection", "ACTIVITY": "Business data fetched", "DESCRIPTION": "Business details like city, state and country will be obtianed for a specific connection "}, "STRING_FORMATTER": {"TEXT_TITLE": "Format text", "ACTION": "Action", "INPUT_TEXT": "Input text", "ACTIVITY_TITLE": "Formatted string", "ACTIVITY_SUBTITLE": "Result: {{ output }}", "OPERATORS": {"UPPERCASE": "Uppercase", "CAPITALIZE": "Capitalize", "LOWERCASE": "Lowercase", "DEFAULT_VALUE": "Default value", "EXTRACT_EMAIL_ADDRESS": "Extract email address", "REPLACE": "Replace", "TABLE_LOOKUP": "Table lookup"}, "OPERATOR_DESCRIPTIONS": {"UPPERCASE": "Converts the input text to ALL UPPERCASE letters.", "CAPITALIZE": "Capitalizes the first character of every word.", "LOWERCASE": "Converts the input text to all lowercase letters.", "DEFAULT_VALUE": "Returns a default value if the input text is empty.", "EXTRACT_EMAIL_ADDRESS": "Finds and extracts the email address out of an input text.", "REPLACE": "Replaces all instances of any value in the input text with another value.", "TABLE_LOOKUP": "Finds matching values between an input text and the platform."}, "DEFAULT_VALUE_DESCRIPTION": "Value to be used if a matching value is not found in the Lookup table", "TO_REPLACE": "To replace", "REPLACE_WITH": "Replace with", "TABLE_DATA": "Table data", "STEP_SUBTITLE_ACTION": "Action: {{ operator }}"}, "CREATE_PROJECT_IN_TASK_MANAGER": {"CREATE_PROJECT_IN_TASK_MANAGER": "Create a fulfillment project for the account", "CREATE_PROJECT_DESCRIPTION": "Create a project for the account from a Task Manager template. This project will appear in Task Manager.", "ACTION": "Create a fulfillment project for the account from the <b>{{ templateName }}</b> template", "SEARCH": "Search for a template", "YOUR_TEMPLATES": "Your Templates", "SYSTEM_TEMPLATES": "System Templates", "STEP": "Create a fulfillment project for the account from the <b>{{ templateName }}</b> template", "ACTIVITY": "Created a fulfillment project for the account from the <b>{{ templateName }}</b> template", "TEMPLATE": "Create a fulfillment project for the account from a template", "UNSPECIFIED_TEMPLATE": "Unspecified template (selected by partner)", "PRODUCT_ACTIVATION_WARNING": "A project from this template is automatically created if <b>{{ productName }}</b> is activated. Selecting this template may create a duplicate."}, "CREATE_PROJECT_IN_TASK_MANAGER_FOR_ORDER": {"CREATE_PROJECT_IN_TASK_MANAGER_FOR_ORDER": "Create a fulfillment project for the order", "CREATE_PROJECT_DESCRIPTION": "Create a project for the order from a Task Manager template. This project will appear in Task Manager.", "ACTION": "Create a fulfillment project for the order from the <b>{{ templateName }}</b> template", "STEP": "Create a fulfillment project for the order from the <b>{{ templateName }}</b> template", "ACTIVITY": "Created a fulfillment project for the order from the <b>{{ templateName }}</b> template", "TEMPLATE": "Create a fulfillment project for the order from a template"}, "REMOVE_FROM_LIST": {"REMOVE_FROM_LIST": "Remove the account from a list", "ACTION": "Remove the account from the <a target=\"_blank\" href=\"/action-lists/{{actionListId}}/accounts\" style=\"font-weight: 500;\">{{actionListName}}</a> list", "ACTION_PREVIOUS_STEP": "Remove the account from a list from a previous step", "ACTIVITY": "Removed the account from the <a target=\"_blank\" href=\"/action-lists/{{actionListId}}/accounts\" style=\"font-weight: 500;\">{{actionListName}}</a> list", "CONFIG_ERROR": "Choose a new list or delete the step"}, "UNASSIGN_SALESPEOPLE": {"UNASSIGN_SALESPEOPLE": "Unassign salespeople", "DESCRIPTION": "Unassign all salespeople from the account, including any additional salespeople.", "ACTION": "Unassign salespeople", "ACTIVITY": "Unassigned salespeople"}, "CREATE_PROPOSAL": {"TITLE": "Create a proposal", "DESCRIPTION": "Create a proposal based on a template", "ACTION": "Create a proposal", "ACTIVITY": "Created proposal", "ACTIVITY_DESCRIPTION": "The proposal <b>{{ proposal_name }}</b> ({{ proposal_id }}) was created.", "TEMPLATE": "Template"}, "CREATE_OPPORTUNITY": {"TITLE": "Create a sales opportunity", "SUBTITLE": "This will appear in Sales & Success Center", "SELECT_PRODUCTS_TITLE": "Choose products and packages", "ADDITIONAL_INFO_TITLE": "Additional information", "OPTIONAL_INFO_TITLE": "Optional information", "TYPE": "Type", "NOT_SPECIFIED": "Not specified", "NEW_BUSINESS": "New business", "EXISTING_BUSINESS": "Existing business", "EXPECTED_CLOSE_DATE_TITLE": "Number of days to close", "EXPECTED_DURATION_TITLE": "Expected contract length", "LENGTH": "Length", "PERIOD": "Period", "DURATION_PERIOD_OPTIONS": {"DAYS": "Days", "WEEKS": "Weeks", "MONTHS": "Months", "YEARS": "Years"}, "EXPECTED_DURATION_DISCLAIMER": "Use this field for reference only. Orders and product expiration dates will not be impacted by this timeframe.", "ASSIGN_TO": "Assign to", "NAME": "Opportunity name", "ACTION": {"NODE_TITLE": "Create an opportunity named <strong>{{opportunity_name}}</strong> and assigned to <strong>{{assignee_name}}</strong> for the account", "NODE_TITLE_FOR_TEMPLATE": "Create an opportunity for the account", "NODE_TITLE_NAME_ONLY": "Create an opportunity named <strong>{{opportunity_name}}</strong> for the account", "NODE_TITLE_ASSIGNEE_ONLY": "Create an opportunity assigned to <strong>{{assignee_name}}</strong> for the account"}, "ACTIVITY": {"SUBTITLE": "Created a sales opportunity"}, "VALIDATION": {"POSITIVE_OR_ZERO": "Must be greater than or equal to 0", "POSITIVE": "Must be greater than 0"}}, "LOG_SALES_ACTIVITY": {"TITLE": "Create a sales activity", "DESCRIPTION": "Create a sales activity based on a selected activity", "ACTION": {"NODE_TITLE": "Create a sales <strong>{{action}}</strong> action activity", "NODE_TITLE_OTHER_ACTION": "Create a sales <strong>{{action}}</strong>(<strong>{{custom_action_name}}</strong>) action activity"}, "ACTIVITY": "Created a sales activity", "SELECT_ACTIVITY": "Select activity", "CUSTOM_ACTION": "Action", "FOLLOW_UP_REQUIRED": "Follow-up required", "ADD_NOTES": "Add notes", "SELECT_ACTIVITY_OPTION": {"OUTBOUND_CALL": "Outbound Call", "INBOUND_CALL": "Inbound Call", "EMAIL_SENT": "<PERSON><PERSON>", "EMAIL_RECEIVED": "<PERSON><PERSON>", "MEETING": "Meeting", "OTHER": "Other"}}, "UPDATE_BILLING_CONTRACT": {"TITLE": "Update the billing contract", "DESCRIPTION": "Update the billing contract with the accounts service level", "ACTION": "Update the billing contract", "ACTIVITY": "Updated the billing contract", "FAILED": "Failed to update the billing contract"}, "SEND_PUBLIC_SYSTEM_MESSAGE": {"TITLE": "Show a system message in an Inbox conversation", "DESCRIPTION_PART_ONE": "Displays a passive message in a conversation between you and an account, viewable by all participants.", "DESCRIPTION_PART_TWO": "Useful for showing and logging key events for all participants to see, as well as for setting expectations for response times.", "NOTE": "Note: This message won’t send a notification or change the Read status for any participants.", "ACTION": "Send a public system message", "ACTIVITY": "Sent a public system message", "MESSAGE_BODY": "Message body", "MESSAGE_BODY_PLACEHOLDER": "New public system message for {{business_name}}"}, "SEND_PUBLIC_SYSTEM_MESSAGE_INTERNAL": {"TITLE": "Show a system message in an Inbox conversation", "DESCRIPTION_PART_ONE": "Displays a passive message in a conversation between you and an account, viewable by all participants.", "DESCRIPTION_PART_TWO": "Useful for showing and logging key events for all participants to see, as well as for setting expectations for response times.", "NOTE": "Note: This message won’t send a notification or change the Read status for any participants.", "ACTION": "Send a public system message", "ACTIVITY": "Sent a public system message", "MESSAGE_BODY": "Message body", "MESSAGE_BODY_PLACEHOLDER": "New public system message for {{business_name}}"}, "SEND_INBOX_MESSAGE": {"TITLE": "Send a message in an Inbox conversation", "DESCRIPTION_PART_ONE": "A message will be sent through Inbox to the account. The message will be viewable by all participants.", "ACTION": "Send an Inbox message", "ACTIVITY": "Sent an Inbox message", "MESSAGE_BODY": "Message body", "MESSAGE_BODY_PLACEHOLDER": "Type here...", "SEND_MESSAGE_FROM": "Send message from...", "ASSIGNED_SALESPERSON": "Assigned salesperson", "ASSIGNED_SALESPERSON_DESCRIPTION": "If there is no salesperson assigned, the message will be sent from your company", "COMPANY_ONLY": "Your company", "COMPANY_ONLY_DESCRIPTION": "Always send the message from your company, even if there is an assigned salesperson"}, "SEND_INBOX_MESSAGE_INTERNAL": {"TITLE": "Send a message in an Inbox conversation", "DESCRIPTION_PART_ONE": "Sends a message into a conversation between <PERSON><PERSON><PERSON><PERSON> and the <PERSON>, viewable by all participants.", "ACTION": "Send an Inbox message", "ACTIVITY": "Sent an Inbox message", "MESSAGE_BODY": "Message body", "MESSAGE_BODY_PLACEHOLDER": "Type here...", "SEND_MESSAGE_FROM": "Send message from...", "ASSIGNED_SALESPERSON": "Assigned salesperson", "ASSIGNED_SALESPERSON_DESCRIPTION": "If there is no salesperson assigned, the message will be sent from your company", "COMPANY_ONLY": "Your company", "COMPANY_ONLY_DESCRIPTION": "Always send the message from your company, even if there is an assigned salesperson"}, "CLOSE_ALL_OPPORTUNITIES": {"TITLE": "Close all Sales Opportunities", "DESCRIPTION": "Close all Sales Opportunities as Lost on the account", "ACTION": {"TITLE": "Close all Sales Opportunities"}, "ACTIVITY": {"TITLE": "Close all Sales Opportunities", "FAILED": "Failed to close all sales opportunities"}}, "CLOSE_OPPORTUNITY": {"TITLE": "Close opportunity", "DESCRIPTION": "Close opportunity", "ACTION": {"TITLE": "Close opportunity"}, "ACTIVITY": {"TITLE": "Close opportunity", "FAILED": "Failed to close opportunity"}}, "SEND_AI_PROMPT": {"TITLE": "Send a prompt to AI", "DESCRIPTION": "Sends a prompt to AI to generate a response that can be used in a later action", "ACTION": "Send a prompt to AI", "ACTIVITY": "Send a prompt to AI", "MESSAGE_BODY": "Prompt text", "MESSAGE_BODY_PLACEHOLDER": "Write a sales pitch for {{business_name}}", "WARNING": {"HIGHLIGHT": "Caution", "MESSAGE": "Verify the AI response content before sending it to customers, as errors and misinterpretations may occur. You can verify a response by sending the data to yourself."}}, "MODIFY_CUSTOM_FIELD_DATA": {"ACTION_TITLE": "Modify custom {{objectType}}", "ACTIVITY_FAILURE": "Failed to modify custom {{objectType}} for {{fieldName}}", "ACCOUNT_DATA": {"ACTION_TITLE": "Modify custom account data", "ACTION_SUBTITLE": "Data will be displayed in the account edit page."}, "SALES_ORDER": {"ACTION_TITLE": "Modify custom sales order data", "ACTION_SUBTITLE": ""}, "PRODUCT_DATA": {"ACTION_TITLE": "Modify custom product data", "ACTION_SUBTITLE": "Data will be displayed under the product info edit tab."}, "USER_DATA": {"ACTION_TITLE": "Modify custom user data", "ACTION_SUBTITLE": "Data will be displayed in the user edit page."}, "DEFAULT_CONFIG_ERROR": "There is an issue with your step, please delete it or configure it.", "ARCHIVED_CONFIG_ERROR": "This step is using an archived custom field. Remove the archived field from this step or <a href=\"/custom-fields\"><strong>unarchive them</strong></a> to continue.", "SELECT_FIELD": "Select the field to change:", "SEARCH_FIELD": "Search field...", "FIELD_NOT_FOUND": "Field not found", "VALUE": "Value:", "SELECT_OPERATION": "Select operation", "OPERATIONS": {"SET_VALUE": "Set value", "CLEAR_VALUE": "Clear value", "ADD": "Add", "SUBTRACT": "Subtract", "MULTIPLY": "Multiply by ", "DIVIDE": "Divide by ", "APPEND": "Append to value"}, "OPERATIONS_NODE_TEXT": {"SET_VALUE": "Set <strong>{{fieldName}}</strong> custom {{objectType}} to {{value}}", "CLEAR_VALUE": "Clear <strong>{{fieldName}}</strong> custom {{objectType}}", "ADD": "Add {{value}} to <strong>{{fieldName}}</strong> custom {{objectType}}", "SUBTRACT": "Subtract {{value}} from <strong>{{fieldName}}</strong> custom {{objectType}}", "MULTIPLY": "Multiply <strong>{{fieldName}}</strong> custom {{objectType}} by {{value}}", "DIVIDE": "Divide <strong>{{fieldName}}</strong> custom {{objectType}} by {{value}}", "APPEND": "Append {{value}} to <strong>{{fieldName}}</strong> custom {{objectType}}"}, "OPERATIONS_ACTIVITY_TEXT": {"SET_VALUE": "Set <strong>{{fieldName}}</strong> from {{previousValue}} to {{newValue}}", "CLEAR_VALUE": "Cleared <strong>{{fieldName}}</strong> custom {{objectType}}", "ADD": "Added {{value}} to <strong>{{fieldName}}</strong>, changed from {{previousValue}} to {{newValue}}", "SUBTRACT": "Subtracted {{value}} from <strong>{{fieldName}}</strong>, changed from {{previousValue}} to {{newValue}}", "MULTIPLY": "Multiplied <strong>{{fieldName}}</strong> by {{value}}, changed from {{previousValue}} to {{newValue}}", "DIVIDE": "Divided <strong>{{fieldName}}</strong> by {{value}}, changed from {{previousValue}} to {{newValue}}", "APPEND": "Set <strong>{{fieldName}}</strong> from {{previousValue}} to {{newValue}}"}, "OBJECT_TYPE": {"ACCOUNT_GROUP": "account data", "USER": "user data", "SALES_ORDER": "order data", "PRODUCT": "product data"}, "EMPTY_VALUE": "<strong>EMPTY</strong>", "DIVIDE_BY_ZERO_ERROR": "Can't divide by zero", "ARCHIVED_FIELD_ERROR": "{{ fieldName }} field is archived"}, "CREATE_SNAPSHOT_REPORT": {"TITLE": "Create a Snapshot Report", "DESCRIPTION": "A Snapshot Report will be created for the account. If the account already has a Snapshot Report, the automation will skip this step.", "ACTION": "Create a Snapshot Report for the account", "ALERT": "This step will only complete if you have free Snapshot Reports available. If you reach your limit while this automation is on, you can configure how the automation handles the errors in the Settings tab.", "ACTIVITY": {"SUCCESS_CREATED": "Created a new Snapshot Report", "SUCCESS_EXISTS": "Snapshot Report already exists", "SUBTITLE": "Snapshot ID: {{ snapshotId }}", "FAILED": "Failed to create a Snapshot Report for the account"}, "FORM": {"INFER_BUSINESS_DATA": "Automatically fill in missing information using Google", "INFER_BUSINESS_DATA_DESCRIPTION": "To increase the quality of the Snapshot Reports, data from Google will be used to fill in any missing phone numbers, addresses, or websites.", "RATE_LIMIT": {"TITLE": "Activation limit", "DESCRIPTION": "Prevent a Snapshot Report from being generated more than"}}}, "CREATE_NOTE": {"CONTACT_NAME": "Add a note to the contact", "COMPANY_NAME": "Add a note to the company", "NOTE_TITLE": "Note title", "NOTE_BODY": "Note body", "ACTION": "Create note", "ACTIVITY": "Created note with title: {{ title }}", "ACTIVITY_LINK": "Created <a target=\"_blank\" href='{{ url }}'>note</a> with title: {{ title }}"}, "CREATE_TASK": {"CONTACT_NAME_V2": "Create a CRM sales task for the contact", "COMPANY_NAME_V2": "Create a CRM sales task for the company", "TASK_TITLE": "Task title", "TASK_BODY": "Task body", "ACTION": "Create task", "ACTIVITY": "Created task with title: {{ title }}", "ACTIVITY_LINK": "Created <a target=\"_blank\" href='{{ url }}'>task</a> with title: {{ title }}", "PRIMARY_SALESPERSON": "Primary salesperson", "PRIMARY_SALESPERSON_TOOLTIP": "Salesperson selection is not available"}, "CREATE_CALL_ACTIVITY": {"CONTACT_NAME": "Log a call activity to the contact", "COMPANY_NAME": "Log a call activity to the company", "ACTION": "Log a call activity", "ACTIVITY": "Logged a call activity", "ACTIVITY_LINK": "Created <a target=\"_blank\" href='{{ url }}'>call activity</a>", "CALL_DIRECTION": "Call direction", "CALL_DESCRIPTION": "Call description", "CALL_STATUS": "Call status", "CALL_OUTCOME": "Call outcome", "CALL_RECORDING_URL": "Call recording URL", "OWNER": "Owner", "OUTBOUND": "Outbound", "INBOUND": "Inbound", "DEMO": "Demo scheduled", "INTERESTED": "Interested", "NO_INTEREST": "No interest", "QUALITY_CONNECT": "Quality connect", "RESCHEDULING": "Rescheduling", "REFERRED": "Referred to other representatives", "TIMELINE": "Timeline 6 to 12 months", "CONNECTED": "Connected", "BUSY": "Busy", "REJECTED": "Rejected", "VOICEMAIL": "Left voicemail", "NO_ANSWER": "No answer", "NOT_IN_SERVICE": "Not in service", "WRONG_NUMBER": "Wrong number"}, "MODIFY_CALL_ACTIVITY": {"CONTACT_NAME": "Modify a call activity for the contact", "COMPANY_NAME": "Modify a call activity for the company", "ACTION": "Modify call activity", "ACTIVITY": "Modified call activity", "ACTIVITY_LINK": "Modified <a target=\"_blank\" href='{{ url }}'>call activity</a>", "CALL_DIRECTION": "Call direction", "CALL_DESCRIPTION": "Call description", "CALL_STATUS": "Call status", "CALL_OUTCOME": "Call outcome", "CALL_RECORDING_URL": "Call recording URL", "OUTBOUND": "Outbound", "INBOUND": "Inbound", "DEMO": "Demo scheduled", "INTERESTED": "Interested", "NO_INTEREST": "No interest", "QUALITY_CONNECT": "Quality connect", "RESCHEDULING": "Rescheduling", "REFERRED": "Referred to other representatives", "TIMELINE": "Timeline 6 to 12 months", "CONNECTED": "Connected", "BUSY": "Busy", "REJECTED": "Rejected", "VOICEMAIL": "Left voicemail", "NO_ANSWER": "No answer", "NOT_IN_SERVICE": "Not in service", "WRONG_NUMBER": "Wrong number"}, "SEND_REVIEW_REQUEST": {"TITLE": "Send a review request", "ACTION": "Send a review request", "ACTIVITY": "Review request sent", "DESCRIPTION": "Review requests will not be sent to a contact more than once within a 60 day period.", "SCHEDULE_TITLE": "Schedule review request", "REPEAT_CUSTOMERS": "Repeat customers", "SEND_TYPE": {"USE_DEFAULT": "Use default", "SEND_IMMEDIATELY": "Send immediately", "SEND_CUSTOM": "Delay by"}, "USE_DEFAULT_DESCRIPTION": "Review requests will be delayed by 4 hours and sent between 8:00 AM and 8:00 PM on the next available weekday. If the customer does not interact with the initial request, it will be sent again 3 days later.", "SEND_METHOD": {"TITLE": "Send via", "HINT": "Default templates will be utilized.", "BOTH": "Email and SMS", "EMAIL": "Just email", "SMS": "Just SMS", "EMAIL_FALLBACK_SMS": "Email, and if not available, send via SMS", "SMS_FALLBACK_EMAIL": "SMS, and if not available, send via email"}, "SEND_AGAIN": "Send again", "SEND_AGAIN_DESCRIPTION": "If the review request link is not clicked, send it again", "TIME_UNIT": {"HOURS": "Hours", "DAYS": "Days", "WEEKS": "Weeks", "MONTHS": "Months"}, "REPEAT_WAIT": {"30_DAYS": "30 days", "60_DAYS": "60 days", "90_DAYS": "90 days"}, "AND_SEND_BETWEEN": "and send between", "ON": "on", "EVERY": "Every", "AFTER_INITIAL_REQUEST": "after initial request. Send again", "TIMES": "times", "WAIT_AT_LEAST": "Wait at least", "BEFORE_ASKING": "before asking repeat customers for feedback again"}, "GET_COMPANY_FROM_ACCOUNT_GROUP": {"TITLE": "Get the associated company", "ACTION": "Get the associated company", "ACTIVITY": "Company retrieved for associated account", "ACTIVITY_URL": "<a target=\"_blank\" href=\"{{url}}\">Company</a> retrieved from associated account", "DESCRIPTION": "This step will return information about the company associated with the account in the automation"}, "GET_ACCOUNT_FROM_COMPANY": {"TITLE": "Get the associated account", "ACTION": "Get the associated account", "ACTIVITY": "Account retrieved for associated company", "ACTIVITY_URL": "<a target=\"_blank\"  href=\"{{url}}\">Account</a> retrieved for associated company", "DESCRIPTION": "This step will return information about the account associated with the company in the automation."}, "GET_ASSOCIATED_CRM_OBJECT": {"SEARCH_CRITERIA": "Search criteria", "CONTACT_OPPORTUNITY": {"TITLE": "Get contact from opportunity", "ACTION": "Get contact from opportunity", "ACTIVITY": "<a target=\"_blank\"  href=\"{{url}}\">Contact</a> retrieved for associated opportunity", "TIP": "This step will search for a contact associated with the opportunity, based on the criteria below. The data from the first contact found will be available to use in subsequent steps."}, "OPPORTUNITY_COMPANY": {"TITLE": "Get opportunity from company", "ACTION": "Get opportunity from company", "ACTIVITY": "<a target=\"_blank\"  href=\"{{url}}\">Opportunity</a> retrieved for associated company", "TIP": "This step will search for an opportunity associated with the company, based on the criteria below. The data from the first opportunity found will be available to use in subsequent steps."}, "OPPORTUNITY_CONTACT": {"TITLE": "Get opportunity from contact", "ACTION": "Get opportunity from contact", "ACTIVITY": "<a target=\"_blank\"  href=\"{{url}}\">Opportunity</a> retrieved for associated contact", "TIP": "This step will search for an opportunity associated with the contact, based on the criteria below. The data from the first opportunity found will be available to use in subsequent steps."}, "CONTACT": {"CREATED_ASCENDING": "oldest contact", "CREATED_DESCENDING": "newest contact"}, "OPPORTUNITY": {"CREATED_ASCENDING": "oldest opportunity", "CREATED_DESCENDING": "newest opportunity"}}, "CREATE_COMPANY": {"TITLE": "Create a company", "ACTION": "Create a company", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Company</a> was created", "ADD_ADDITIONAL_FIELDS": "+ Add additional fields"}, "CREATE_CONTACT": {"TITLE": "Create a contact", "ACTION": "Create a contact", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Contact</a> was created", "REQUIRED_MESSAGE": "At least one of first name, last name, email or phone number is required"}, "UPDATE_COMPANY": {"TITLE": "Update company", "ACTION": "Update specified company fields", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Company</a> was updated", "ADD_ADDITIONAL_FIELDS": "+ Add additional fields"}, "UPDATE_CONTACT": {"TITLE": "Update contact", "INFO": "This step will update only the fields specified in this form.", "ACTION": "Update specified contact fields", "ACTION_SINGULAR": "Update specified contact field", "ACTION_PRESENT": "Update {{fieldName}} field", "ACTION_DETAILED": "{{fieldName}} field updated", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Contact</a> was updated", "REQUIRED_MESSAGE": "At least one field is required", "EMPTY_FIELD_MESSAGE": "Fill in or remove empty fields before saving"}, "UPDATE_OPPORTUNITY": {"TITLE": "Update opportunity", "INFO": "This step will update only the fields specified in this form.", "ACTION": "Update specified opportunity fields", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Opportunity</a> was updated", "EMPTY_FIELD_MESSAGE": "Fill in or remove empty fields before saving", "ADD_ADDITIONAL_FIELDS": "+ Add additional fields", "FIELD_OVERRIDES": {"PIPELINE_AND_STAGE": "Pipeline and Stage"}}, "LOOKUP_CONTACT": {"TITLE": "Find contact", "ACTION": "Find a contact", "DESCRIPTION": "Find a contact whose values match the given criteria. This step also provides the outputs of 'Found' and 'Multiple Found' to be used in subsequent branching steps.", "SEARCH_FIELDS": "Search fields", "ACTIVITY": {"FOUND": "<a target=\"_blank\" href=\"{{url}}\">Contact</a> was found", "MULTIPLE_FOUND": "Multiple contacts found, using <a target=\"_blank\" href=\"{{url}}\">Contact</a>", "NOT_FOUND": "Contact not found"}, "OPERATOR": {"IS": "= is"}}, "CREATE_CRM_OPPORTUNITY": {"TITLE": "Create an opportunity", "ACTION": "Create an opportunity", "ACTIVITY": "<a target=\"_blank\" href=\"{{url}}\">Opportunity</a> was created", "REQUIRED_MESSAGE": "All required fields must be filled out", "ADD_ADDITIONAL_FIELDS": "+ Add additional fields"}, "CHARGE_INVOICE": {"TITLE": "Charge an invoice", "ACTION": "Charge an invoice", "DESCRIPTION": "This step returns an <b>Accepted</b> true or false value. This can be used to check if the charge was accepted by the payment processor.", "WARNING": "You can only charge a specific invoice once in a 24 hour period. If you would like to charge the same invoice again, you will need to add a delay step to this automation", "ACTIVITY_ACCEPTED": "Charge was <b>accepted</b> by the payment processor", "ACTIVITY_NOT_ACCEPTED": "Charge was <b>rejected</b> by the payment processor", "FAILED_TO_CHARGE": "Failed to send the charge to the payment processor"}, "SEND_SMS_INBOX": {"TITLE": "Send an SMS message via Inbox", "ACTIVITY": "Message {{ message }} sent to inbox to be delivered", "MESSAGE_STATUS": "Message status: {{ status }}. Continuing to the next step of the automation", "MESSAGE_BODY": "SMS content", "QUOTA_DISCLAIMER": "{{ productName }} currently gives you access to unlimited sending. In the future, SMS credits will be required for this action.", "FALLBACK_PRODUCT_NAME": "The product", "DELIVERY_STATUS": {"STATUS_NOT_READ": "not read", "STATUS_READ": "read", "STATUS_SENT": "sent", "STATUS_DELIVERED": "delivered", "STATUS_FAILED": "failed to be delivered", "STATUS_UNDELIVERED": "undelivered", "STATUS_SENDING": "sending", "STATUS_UNKNOWN": "unknown"}}, "SEND_EMAIL_INBOX": {"TITLE": "Send a plain text email via Inbox", "MESSAGE_BODY": "Email content"}, "ASSOCIATE_COMPANY_WITH_CONTACT": {"TITLE": "Associate company with contact", "ACTION": "Associate company with contact", "ACTIVITY": {"SUCCESS": "<a target=\"_blank\" href=\"{{companyUrl}}\">Company</a> was associated with <a target=\"_blank\" href=\"{{contactUrl}}\">Contact</a>", "FAILED": "Failed to associate company with contact"}, "PRIMARY_SELECT": "Set as Primary", "TITLE_TAGS": "Association Tags", "DESCRIPTION_TAGS": "Use tags to describe the relationship"}, "START_CAMPAIGN_FOR_COMPANY": {"TITLE": "Start a campaign for the company", "ACTIONV2": "Start the <a target=\"_blank\" href=\"/marketing/campaign/details/{{ campaignId }}\" >{{ campaignName }}</a> campaign for the company", "ACTIVITY": {"SUCCESS": "<a target=\"_blank\" href=\"{{campaignUrl}}\">Campaign</a> was started for <a target=\"_blank\" href=\"{{companyUrl}}\">Company</a>", "FAILED": "Failed to start the campaign"}, "LABEL": "The selected campaign will be sent to a maximum of 50 contacts per company", "HINT": "Note: A campaign can only be started once for each contact."}, "GET_ORDER_DATA": {"TITLE": "Get order data", "DESCRIPTION": "This step can be used to fetch order data to be used in subsequent steps", "ACTIVITY": {"TITLE": "Got order data for order: <a target=\"_blank\" href=\"{{orderUrl}}\">{{orderId}}</a>"}}, "GET_MULTI_PRODUCT_DATA": {"TITLE": "Get products' custom field data", "ACTIVITY": {"TITLE": "Retrieved custom field data"}}, "SEND_EVENT_INBOX": {"TITLE": "Log an event in a conversation via Inbox", "MESSAGE_BODY": "Event content", "DESCRIPTION": "This action will not notify the conversation participants"}, "GET_CONNECTION_DATA_VENDOR": {"TITLE": "Get connection data for vendor", "DESCRIPTION": "Fetch the connection details for the associated integration connection", "ACTIVITY": {"STARTED": "Initiated to get the connection data", "FAILED": "Failed to get the connection data"}}, "AI_ASSISTANT_ACTION": {"TITLE": "AI Assistant Action", "DESCRIPTION": "Request the AI Assistant to perform an action", "REQUEST_TEXT": "Request text", "ACTIVITY": {"TITLE": "AI Assistant Output", "BODY": "{{output}}"}}}, "FILTERS": {"FILTER_BY": "Condition type", "WORKFLOW_STEP_OPTIONS": "Select workflow step", "WORKFLOW_STEP_HINT": "Only steps with data passing enabled will show up here. Edit a step to enable data passing", "WORKFLOW_STEP_DISCLAIMER": "If this if/else action is unable to access the requested data from the step it will default to the “No” path", "WORKFLOW_STEP_GOAL_DISCLAIMER": "If this goal is unable to access the requested data from the step it will be considered not fulfilled", "ADD_CONDITIONS_LABEL": "Add conditions for starting this automation", "ONLY_ONE_CONDITION": "All branches must have the same condition type", "COMPARISON": {"AND": "AND", "OR": "OR", "CONTAINS": "Contains", "DOES_NOT_CONTAIN": "Does not contain", "EXISTS": "Exists", "DOES_NOT_EXIST": "Does not exist", "AFTER": "After", "BEFORE": "Before", "NOT_EQUALS": "Is not", "EQUALS": "Is", "VALUE_EXISTS": "Value exists", "VALUE_NOT_EXISTS": "Value does not exist"}, "ACCOUNT_GROUP_DATA": {"ACCOUNT_GROUP_DATA": "Account data", "NODE_TEXT": "Account", "DATA_LABELS": {"ACCOUNT_EMAIL": "Email", "LOCATION": "Location", "COUNTRY": "Country", "STATE": "State", "ANY_STATE": "Any {{ zone }}", "CITY": "City", "ANY_CITY": "Any city", "ZIP_CODE": "ZIP Code", "ANY_ZIP_CODE": "Any ZIP Code", "WEBSITE": "Website", "TAGS": "Tags", "ACCOUNTS": "Active products", "PRODUCTS": "Products", "CATEGORIES": "Categories", "ASSIGNED_SALES_PERSON": "Salesperson", "ACCOUNT_ORIGIN": "Origin", "CREATED_DATE": "Created date", "ADDITIONAL_ASSIGNEES": "Additional assignees", "LISTS": "Lists", "LIFECYCLE_STAGE": "Lifecycle Stage", "HOURS_OF_OPERATION": "Hours of operation", "SALES_TEAM": "Sales team", "MARKET": "Market"}, "COMPARISON": {"EQUALS": "Is", "NOT_EQUALS": "Is not", "EXISTS": "Exists", "DOES_NOT_EXIST": "Does not exist", "CONTAINS": "Contains", "INCLUDES_ALL": "Include all:", "INCLUDES_ANY": "Include any:", "DOES_NOT_INCLUDE_ANY": "Do not include any:", "DOES_NOT_INCLUDE_ALL": "Do not include all:", "IS_ONE_OF": "Is one of", "IS_NOT_ONE_OF": "Is not one of", "AFTER": "After", "BEFORE": "Before", "IS_TEAM_MEMBER_OF_ANY": "Is team member of any", "IS_NOT_TEAM_MEMBER_OF_ANY": "Is not team member of any"}}, "PARTNER_DATA": {"PARTNER_DATA": "Partner data", "NODE_TEXT": "Partner", "DATA_LABELS": {"STRIPE_SETUP": "Stripe", "CREATED_DATE": "Created date", "SNAPSHOTS_CREATED": "Snapshots created", "DAYS_ACTIVE_IN_PLATFORM": "Days active in the platform", "ACTIVE_PAID_PRODUCTS": "Active paid products", "ACTIVE_PRODUCTS": "Active products", "ACCOUNTS": "Accounts", "OPEN_OPPORTUNITIES": "Open opportunities", "CONVERSION_POINTS": "Conversion points", "NUMBER_OF_EMPLOYEES": "Number of employees", "ESTIMATED_ANNUAL_REVENUE": "Estimated Annual Revenue", "UTM_CAMPAIGN": "UTM Campaign", "UTM_MEDIUM": "UTM Medium", "UTM_SOURCE": "UTM Source", "UTM_CONTENT": "UTM Content", "UTM_TERM": "UTM Term"}, "COMPARISON": {"HAS_BEEN_SETUP": "Has been set up", "HAS_NOT_BEEN_SETUP": "Has not been set up", "AFTER": "After", "LESS_THAN_EQUAL": "Less than or equal to", "EQUALS": "Equals", "GREATER_THAN": "Greater than", "INCLUDES_ALL": "Include all:", "INCLUDES_ANY": "Include any:", "DOES_NOT_INCLUDE_ANY": "Do not include any:", "DOES_NOT_INCLUDE_ALL": "Do not include all:", "IS_ONE_OF": "Is one of", "IS_NOT_ONE_OF": "Is not one of", "EXISTS": "Is any value", "DOES_NOT_EXIST": "Is missing", "CONTAINS": "Contains", "DOES_NOT_CONTAIN": "Does not contain"}, "OPTION_LABELS": {"REVENUE": {"0_1M": "Up to $1M", "1M_10M": "$1M to $10M", "10M_50M": "$10M to $50M", "50M_100M": "$50M to $100M", "100M_250M": "$100M to $250M", "250M_500M": "$250M to $500M", "500M_1B": "$500M to $1B", "1B_10B": "$1B to $10B", "10B_OR_MORE": "More than $10B"}}, "ERRORS": {"NUMBER_NOT_POSITIVE": "Must be a positive integer"}}, "USER_INTERNAL_DATA": {"USER_INTERNAL_DATA": "User data (internal)", "NODE_TEXT": "User", "DATA_LABELS": {"ONBOARDING_TASK": "Completed onboarding tasks", "TASKS": "Tasks"}, "COMPARISON": {"INCLUDES_ALL": "Include all:", "INCLUDES_ANY": "Include any:", "DOES_NOT_INCLUDE_ANY": "Do not include any:", "DOES_NOT_INCLUDE_ALL": "Do not include all:"}}, "ACCOUNT_INTERNAL_DATA": {"TITLE_TEXT": "Account data (internal)", "NODE_TEXT": "Account"}, "ORDER_DATA": {"TITLE_TEXT": "Order data", "NODE_TEXT": "Order", "DATA_LABELS": {"STATUS": "Status", "CREATED_DATE": "Created date", "REQUESTED_ACTIVATION_DATE": "Contract start date", "TAGS": "Tags", "ATTACHMENT_COUNT": "Attachment count", "ORIGIN": "Origin", "NOTES": "Administrator notes", "DECLINED_REASON": "Declined reason", "ANNUAL_CONTRACT_VALUE": "Annual contract value", "AMOUNT": "Amount", "ORDER_FORM_ENABLED_FOR_PRODUCTS": "Order forms are required"}, "COMPARISON": {"IS": "Is", "IS_NOT": "Is not", "AFTER": "After", "BEFORE": "Before", "INCLUDES_ALL": "Include all:", "INCLUDES_ANY": "Include any:", "DOES_NOT_INCLUDE_ANY": "Do not include any:", "DOES_NOT_INCLUDE_ALL": "Do not include all:", "LESS_THAN_EQUAL": "Less than or equal to", "EQUALS": "Equals", "GREATER_THAN": "Greater than", "EXISTS": "Exists", "DOES_NOT_EXIST": "Does not exist", "CONTAINS": "Contains", "DOES_NOT_CONTAIN": "Does not contain", "ON_FOR_NONE": "for no products", "ON_FOR_SOME": "for some products", "ON_FOR_ALL": "for all products"}, "ERRORS": {"FREE_TEXT_ERROR": "Required, maximum length 200 characters"}}, "PRODUCT_PRICE_DATA": {"TITLE_TEXT": "Product price data", "NODE_TEXT": "Product", "DATA_LABELS": {"PRICE": "Price", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "PRICE_TYPE": "Price type", "WHOLESALE_PRICE": "Wholesale price", "PRICE_TYPE_OPTIONS": {"RECURRING": "Wholesale", "ONE_TIME": "Retail"}}, "COMPARISON": {"LESS_THAN_EQUAL": "Less than or equal to", "EQUALS": "Equals", "GREATER_THAN": "Greater than"}}, "CAMPAIGN_DATA": {"TITLE": "Campaign ID", "NODE_TEXT": "Campaign", "DATA_LABELS": {"ID": "Campaign ID"}, "INVALID_ID": "Invalid campaign"}, "OPPORTUNITY_DATA": {"TITLE_TEXT": "Opportunity data", "NODE_TEXT": "Opportunity", "DATA_LABELS": {"EXPECTED_CLOSE_DATE": "Expected close date", "PROJECTED_FIRST_YEAR_VALUE": "Projected first year value", "PIPELINE_AND_STAGE": "Pipeline and stage", "PRODUCTS": "Products", "PACKAGES": "Packages", "STATUS": "Status", "ANY_STAGE": "Any stage"}, "INVALID_PIPELINE_AND_STAGE": "Invalid pipeline and stage"}, "CUSTOM_DATA": {"CUSTOM_ACCOUNT_DATA": {"CUSTOM_ACCOUNT_DATA": "Custom account data", "NODE_TEXT": "Account"}, "CUSTOM_USER_DATA": {"CUSTOM_USER_DATA": "Custom user data", "NODE_TEXT": "User"}, "CUSTOM_ORDER_DATA": {"CUSTOM_ORDER_DATA": "Custom sales order data", "NODE_TEXT": "Order"}, "CUSTOM_PRODUCT_DATA": {"CUSTOM_PRODUCT_DATA": "Custom product data", "NODE_TEXT": "Product"}, "CUSTOM_OPPORTUNITY_DATA": {"CUSTOM_OPPORTUNITY_DATA": "Custom opportunity data", "NODE_TEXT": "Opportunity"}, "CRM_CONTACT_DATA": {"TITLE": "Contact data", "NODE_TEXT": "Contact"}, "CRM_COMPANY_DATA": {"TITLE": "Company data", "NODE_TEXT": "Company"}, "CRM_OPPORTUNITY_DATA": {"TITLE": "Opportunity data", "NODE_TEXT": "Opportunity"}, "COMPARISON": {"INTEGER_TYPE": {"LESS_THAN_EQUAL": "Less than or equal to", "EQUALS": "Equals", "GREATER_THAN": "Greater than", "EXISTS": "Is any value", "DOES_NOT_EXIST": "Is missing"}, "STRING_TYPE": {"EXISTS": "Is any value", "DOES_NOT_EXIST": "Is missing", "CONTAINS": "Contains", "DOES_NOT_CONTAIN": "Does not contain", "IS_ONE_OF": "Is one of", "IS_NOT_ONE_OF": "Is not one of"}, "DATE_TYPE": {"AFTER": "After", "BEFORE": "Before", "WITHIN": "Within Days"}, "DROPDOWN_TYPE": {"IS": "Is", "IS_NOT": "Is not"}, "CURRENCY_TYPE": {"LESS_THAN_EQUAL": "Less than or equal to", "GREATER_THAN": "Greater than"}}, "DATA_LABELS": {"FREE_TEXT_LIST": "Text", "ENTER_TEXT": "Enter text and hit enter...", "CURRENCY_LABEL": "Amount"}, "ERRORS": {"FREE_TEXT_REQUIRED": "Required, insert at least one text value", "ARCHIVED_FIELD_PANEL_ERROR": "This step is using one or more archived custom fields. Remove archived fields from this step or <a href=\"/custom-fields\"><strong>unarchive them</strong></a> to continue.", "ARCHIVED_FIELD_FILTER_ERROR": "{{ fieldName }} filter is archived"}}, "CRM_TASK_DATA": {"TITLE_TEXT": "Task", "NODE_TEXT": "Task", "DATA_LABELS": {"TITLE": "Title", "DESCRIPTION": "Description", "PRIORITY": "Priority", "DUE_DATE": "Due date", "TYPE": "Type"}, "OPTION_LABELS": {"TASK_TYPE": {"HIGH": "High", "MEDIUM": "Medium", "LOW": "Low", "NONE": "None"}, "TASK_PRIORITY": {"TO_DO": "To-do", "CALL": "Call", "EMAIL": "Email", "MESSAGE": "Message"}}}, "CRM_NOTE_DATA": {"TITLE_TEXT": "Note", "NODE_TEXT": "Note", "DATA_LABELS": {"TITLE": "Title", "BODY": "Body", "SOURCE_NAME": "Source name"}}, "CRM_EMAIL_DATA": {"TITLE_TEXT": "Email activity", "NODE_TEXT": "Email activity"}, "CRM_MEETING_DATA": {"TITLE_TEXT": "Meeting activity", "NODE_TEXT": "Meeting activity"}, "CRM_CALL_DATA": {"TITLE_TEXT": "Call activity", "NODE_TEXT": "Call activity"}, "USER_DATA": {"USER_DATA": "User data", "NODE_TEXT": "User", "DATA_LABELS": {"LAST_SEEN": "Last seen", "DAYS": "days", "X_DAYS": "{{ days }} days", "X_DAY": "{{ days }} day"}, "COMPARISON": {"WITHIN_DAYS": "Within", "NOT_WITHIN_DAYS": "Not within"}}, "WORKFLOW_DATA": {"WORKFLOW_DATA": "Workflow data", "NODE_TEXT": "Workflow data", "VALUE": "Value", "IS": "Is", "TRUE": "True", "FALSE": "False", "YES": "Yes", "NO": "No"}, "SHOPPING_CART_CONTENTS": {"TITLE_TEXT": "Shopping cart", "NODE_TEXT": "Shopping cart", "DATA_LABELS": {"ITEMS": "Items"}, "COMPARISON": {"ARE": "Are"}, "OPTION_LABELS": {"NOT_EMPTY": "Not empty", "EMPTY": "Empty"}}, "INTEGRATIONS": {"QBO": {"TITLE_TEXT": "Connection Data", "NODE_TEXT": "Connection Data"}, "COMPARISON": {"CONNECTED": "Connected", "NOT_CONNECTED": "NotConnected", "IS": "IS"}, "DATA_LABELS": {"REVIEW_REQUEST_ENABLED": "Review request", "CONNECTION_STATUS": "Connection status"}}, "INVOICE_DATA": {"TITLE_TEXT": "Invoice data", "NODE_TEXT": "Invoice", "DATA_LABELS": {"ISSUED_DATE": "Issued date", "DUE_DATE": "Due date", "PAID_DATE": "Paid date", "VOIDED_DATE": "Voided date", "AMOUNT_OWING": "Amount owing", "TOTAL": "Total", "AMOUNT_PAID": "Amount paid", "STATUS": {"VOID": "Void", "PAID": "Paid", "DUE": "Due", "DRAFT": "Draft"}, "ORIGIN": {"ORIGIN": "Origin", "MANUAL": "Manual", "RENEWAL": "Renewal", "TEMPLATE": "Template", "SALES_ORDER": "Sales order"}}}, "CONNECTIONS_DATA": {"BUSINESS_APP": "Business App", "BUSINESS_APP_CONNECTIONS": "Business App connections", "BUSINESS_APP_DATA": "Business App data", "CONNECTIONS": "Connections", "INCLUDES_ANY": "Include any:", "INCLUDES_ALL": "Include all:", "DOES_NOT_INCLUDE_ANY": "Does not include any:", "DOES_NOT_INCLUDE_ALL": "Does not include all:", "FACEBOOK": "Facebook", "GOOGLE_MY_BUSINESS": "Google Business Profile", "NONE": "None"}, "OPEN_OPPORTUNITIES_DATA": {"TITLE_TEXT": "Open Opportunities", "NODE_TEXT": "Open Opportunities", "DATA_LABELS": {"OPEN_OPPORTUNITIES": "Count"}, "COMPARISON": {"LESS_THAN_EQUAL": "Less than or equal to", "GREATER_THAN": "Greater than", "EQUALS": "Equals"}}, "ACCOUNT_GROUP_ORIGIN": {"OPTION_LABELS": {"CREATED_BY_SMB_SELF_SIGN_UP": "Self-signup through public store"}}, "OTHER": "other", "OTHERS": "others", "ADD_ANOTHER_CONDITION": "+ Add another condition", "ADD_ANOTHER_FILTER": "+ Add another condition type"}, "CATEGORIES": {"ACCOUNTS_AND_USERS": "Accounts and Users", "BUSINESSES": "Businesses", "CAMPAIGNS_AND_EMAILS": "Campaigns and Emails", "FULFILLMENT": "Fulfillment", "LISTS": "Lists", "NOTIFICATIONS": "Notifications", "PRODUCTS": "Products", "SALES": "Sales", "BILLING": "Billing", "INTEGRATIONS": "Integrations", "TAGS": "Tags", "DELAYS": "Delays", "CONDITIONS": "Conditions", "MISCELLANEOUS": "Miscellaneous", "INTERNAL": "Internal", "MANUAL": "Manual", "INBOX": "Inbox", "CONTACTS": "Contacts", "COMPANIES": "Companies", "USERS": "Users", "ACCOUNTS": "Accounts", "CAMPAIGNS": "Campaigns", "OPPORTUNITIES": "Opportunities", "CUSTOM_OBJECTS": "Custom Objects"}, "TASK_DESCRIPTIONS": {"INTERNAL": "Internal steps apply to the Partner that an Account Group is linked to, rather than to the Account Group itself"}, "CANCEL": "Cancel"}, "SETTINGS": {"MARKET_DISCLAIMER": "{{ entity_type }} must be in the {{ market_name }} market to enter this automation.", "BUILT_IN_MARKET_DISCLAIMER": {"ENTITIES": "{{ entity_type }} must be in ", "SPECIFIC_MARKETS": "specific markets ", "TO_ENTER": "to enter this automation."}, "MARKET_DISCLAIMER_TOOLTIP_V4": "The market can't be changed once an automation is created. You can duplicate this automation into a specific market.", "MARKET_DISCLAIMER_ALL_MARKETS_V2": "All markets", "RUN_OPTIONS": {"TITLE": "Entry settings", "RUN_INSTRUCTIONS_V2": "When {{ an_entity }} meets the trigger criteria more than once, run this automation:", "RUN_ONCE": "Once, only the first time", "RUN_MULTIPLE": "Every time", "RUN_ONCE_ENTITY": "Only once per {{ entity }}", "RUN_MULTIPLE_ENTITY": "Multiple times per {{ entity }}", "RUN_ONE_AT_A_TIME": "One at a time per {{ entity }}", "RUN_ONE_AT_A_TIME_HINT": "Ignore triggers to run this automation if it's currently running", "RUN_ONCE_DISCLAIMER": "Automation will only run once per {{ entity_type }} when trigger criteria is met.", "RUN_MULTIPLE_DISCLAIMER": "Automation will run every time an {{ entity_type }} meets the trigger criteria.", "VIEW_SETTINGS": "View Settings"}, "ERROR_HANDLING_OPTIONS": {"TITLE": "Error handling settings", "ERROR_HANDLING_INSTRUCTIONS": "When a step fails to complete due to an error...", "ERROR_HANDLING_INSTRUCTIONS_SHORT": "When a step can’t be completed due to an error:", "STOP": "Stop that specific automation run", "STOP_RUN": "Stop the automation run", "STOP_HINT": "Retry errored automations in the Activity table", "CONTINUE": "Ignore the error and continue the automation", "SKIP": "Skip the step and continue the automation run"}, "CONFIGURATION": "Configuration", "PERMISSIONS": {"TITLE": "Permissions", "TITLE_HINT": "Automations require user permissions to gain access to data required to run the workflow", "AUTHORIZED": "Authorized by: ", "RUNNING_WITH_SERVICE_ACCOUNT": "Running with Service Account:", "DESCRIPTION": "This will allow the automation to:", "TURN_ON": "Turn on automation to acquire permissions", "TRANSFER": "Transfer permissions", "TRANSFER_HINT": "Automation will transfer to use your user permissions", "TRANSFER_HINT_PARTNER_SERVICE_ACCOUNT": "Automation will transfer to use a partner's service account's permissions", "TRANSFER_STOPPED": "You can only transfer permission for a running automation", "SUPERADMIN_TRANSFER": "For security purposes, automations cannot transfer permissions to a superadmin", "SUPERADMIN_TRANSFER_HINT": "Automations must access permissions of a user on the account", "PRELAUNCH": "Automation will require user permissions next time the automation is turned on. To grant the automation access, transfer the automation to use your user permissions."}, "SUCCESS": "Automation settings saved.", "NOTIFICATION_SETTINGS": "Notification settings", "SUBSCRIBE_TO_ERRORS": "Subscribe to error notifications for this automation", "SUBSCRIBE_ERRORS_SHORT": "Subscribe to error notifications", "UNSUBSCRIBE_FROM_ERRORS": "Unsubscribe from error notifications for this automation", "UNSUBSCRIBE_ERRORS_SHORT": "Unsubscribe from error notifications"}, "ACTIVITY": {"TABLE": {"NO_RESULTS": "No results found", "FILTERS": {"EVENTS": {"TITLE": "Events", "SHOW_EVENTS_THAT_DIDNT_START": "Show events that didn't start", "ONLY_SHOW_ERRORS": "Only show events with errors", "SPECIFIC_RUN": "Specific run"}}}, "CANCELED": {"TITLE": "Automation canceled", "SUBTITLE": "Automation was turned off", "SUBTITLE_V2": "Ended manually before completion"}, "COMPLETED": {"TITLE": "Automation completed"}, "PROCESSING": {"TITLE": "Processing..."}, "COMPLETED_FROM_ERROR": {"TITLE": "Canceled automation", "SUBTITLE": "Automation was canceled due to an error"}, "COMPLETED_FROM_GOAL": {"TITLE": "Automation completed from goal"}, "DID_NOT_RUN": {"TITLE": "Did not start this automation", "SUBTITLE": {"RUN_TYPE_ONCE_ENTITY": "The entity already entered this automation and the entry setting is set to <b>Once, only the first time</b>", "RUN_TYPE_ONE_AT_A_TIME_ENTITY": "The entity already entered this automation and the entry setting is set to <b>One at a time</b>", "TRIGGER_FILTERS": "The {{entity}} didn't match the trigger conditions", "TRIGGER_OPTION": "The event didn't match the trigger options", "RATE_LIMITED": "The event was rate limited"}}, "ERROR_TITLE": "Failed to complete due to an error", "CONTINUED": {"CONTINUED_FROM_ERROR": "Continued on new step due to an error", "CONTINUED": "Continued running on new step"}}, "TURN_ON_AUTOMATION_DIALOG": {"TITLE_V2": "Turn on automation?", "SUBTITLE": "Make sure to review your entry settings:", "REVIEW_SETTINGS": "Make sure to review your settings:", "TURN_ON": "Turn on", "BUILT_IN_ORIGIN": {"SUBTITLE": "Review the original built-in automation:", "TURNED_ON_BANNER": "This automation was duplicated from <b>{{built_in_automation_name}}</b>, which is currently ON.", "TURNED_OFF_BANNER": "This automation was duplicated from <b>{{built_in_automation_name}}</b>, which is currently OFF.", "REVIEW": "Review", "WARNING_MESSAGE": "If any steps in this duplicate are the same as in the original automation, there may be duplicate actions in the platform."}, "TURN_ON_FROM_DRAINING": {"DESCRIPTION": "Would you like to turn this automation back on?", "ALERT": "Automations runs that are draining will continue when the automation is turned on"}, "ALERT_TITLE": "New default settings", "ALERT_BODY": "The default entry settings have changed. Please review before proceeding."}, "TURN_OFF_AUTOMATION_DIALOG": {"TITLE": "Turn off {{ name }}?", "DRAIN_OPTION": "Drain automation runs", "DRAIN_OPTION_HINT": "Allow runs in progress to complete. The automation will be turned off automatically once all the ongoing runs have been processed", "STOP_OPTION": "Stop automation runs in progress", "STOP_OPTION_HINT": "Automation runs that are stopped will not resume if the automation is turned on again", "TURN_OFF": "Turn off", "STOP_ALERT": "Automations runs that are stopped will <b>not</b> resume if the automation is turned on again", "STOP_DRAINING": {"STOP_DRAINING": "Stop draining", "TITLE": "Stop draining {{ name }}?", "DESCRIPTION": "Stop the current automation runs that are draining?"}}, "TURN_OFF_ALL_AUTOMATIONS_DIALOG": {"CTA": "Turn off all automations", "TITLE": "Turn off all automations?", "DESCRIPTION": "Built-in automations will be turned off as well. All automation runs in progress will stop, and will not resume if the automation is turned back on.", "TURN_OFF": "Turn off", "SUCCESS": "All automations are now off", "FAILURE": "Failed to turn off all automations"}, "PUBLISH_TEMPLATE_DIALOG": {"TITLE": "Publish {{ name }}?", "DESCRIPTION": "This will make the template available to your customers.", "PUBLISH": "Publish"}, "UNPUBLISH_TEMPLATE_DIALOG": {"TITLE": "Unpublish {{ name }}?", "DESCRIPTION": "This will hide the template from your customers. It will not affect automations that have already been created from the template."}, "PUBLISH_BUILT_IN_AUTOMATION_DIALOG": {"TITLE": "Publish {{ name }}?", "DESCRIPTION": "This will publish the built-in automation out so that it will be on for <strong>all partners</strong>.", "PUBLISH": "Publish"}, "UNPUBLISH_BUILT_IN_AUTOMATION_DIALOG": {"TITLE": "Unpublish {{ name }}?", "DESCRIPTION": "This will unpublish the built-in automation so that it will no longer be on for <strong>all partners</strong>. This will <strong>not</strong> affect automations that have been duplicated from this built-in automation.", "UNPUBLISH": "Unpublish"}, "DELETE_DIALOG": {"TITLE": "Delete {{ automationName }}?", "MESSAGE": "This can’t be undone.", "CONFIRM": "Delete", "DECLINE": "Cancel"}, "DELETE_TEMPLATE_DIALOG": {"TITLE": "Delete {{ name }}?", "MESSAGE": "This can’t be undone.", "CONFIRM": "Delete template", "DECLINE": "Keep template"}, "DELETE_BUILT_IN_AUTOMATION_DIALOG": {"TITLE": "Delete {{ name }}?", "MESSAGE": "This can’t be undone.", "CONFIRM": "Delete built-in automation", "DECLINE": "Keep built-in automation"}, "DETAILS_DIALOG": {"EDIT_TITLE": "Edit details", "READ_ONLY_TITLE": "Details"}, "ERRORS": {"LOADING_MARKETS": "There was an error loading markets. Please refresh the page.", "LOADING_TAGS": "There was an error loading tags. Please refresh the page.", "SAVE_SETTINGS": "There was a problem saving the settings. Refresh the page and try again.", "ERROR_DISPLAY": "There was an error showing this data. Refresh the page and try again.", "ACCOUNT_NOT_FOUND": "Account not found", "LIST_TOO_LARGE": "Unable to run automations on a list with more than 10,000 accounts", "RULE_DISPLAY": "Click here to view more details about this step", "AUTOMATIONS": {"STARTING": "There was an error starting the automation. Please try again.", "STARTING_WITH_ERRORS": "There was an error starting the automation. Please fix errors and try again.", "STOPPING": "There was an error stopping the automation. Please try again.", "DELETING": "There was an error deleting the automation. Please try again.", "DUPLICATING": "There was an error duplicating the automation. Please try again."}, "TEMPLATES": {"PUBLISHING_LIMIT_REACHED": "The limit of published templates has been reached. Unpublish another template and try again.", "PUBLISHING": "There was an error publishing the template. Please try again.", "PUBLISHING_WITH_ERRORS": "There was an error publishing the template. Please fix errors and try again.", "UNPUBLISHING": "There was an error unpublishing the template. Please try again.", "DELETING": "There was an error deleting the template. Please try again."}, "MANAGE_BUILT_IN_AUTOMATIONS": {"PUBLISHING": "There was an error publishing the built-in automation. Please try again.", "PUBLISHING_WITH_ERRORS": "There was an error publishing the built-in automation. Please fix errors and try again.", "UNPUBLISHING": "There was an error unpublishing the built-in automation. Please try again.", "DELETING": "There was an error deleting the built-in automation. Please try again."}}, "WARNINGS": {"START_CAMPAIGN_AFTER_ACCOUNT_CREATED": "To send a campaign there needs to be a user associated with the account. Add a step to wait until a user is associated with the account.", "CUSTOM_DATA_IN_TRIGGER": "Custom data is not available to use within triggers. Add a delay step to wait 5 minutes after the trigger, then filter on the custom data."}, "VALIDATORS": {"INVALID_URL": "Please enter a valid URL"}, "SUCCESS": {"STARTED": "Automation started.", "STOPPED": "Automation stopped.", "DELETED": "Automation deleted.", "DELETED_TEMPLATE": "Template deleted.", "DELETED_BUILT_IN_AUTOMATION": "Built-in automation deleted."}, "TAGS_TITLE": "Automation tags", "AUTOMATION_RUNS": {"TITLE": "Automation Runs", "DELAY_UNTIL_STEP": "Delay until {{ stepName }}"}, "CREATE_TEMPLATES": {"NAVIGATION": {"ALL": "All templates", "FUNCTION": "Function", "OBJECTIVE": "Objective", "FEATURE": "Feature", "ADMINISTRATION": "Administration", "FULFILLMENT": "Fulfillment", "MARKETING": "Marketing", "SALES": "Sales", "SET_UP_ACCOUNTS": "Set up accounts", "CONVERT_LEADS": "Convert leads", "SEGMENT_ACCOUNTS": "Segment accounts", "NOTIFY_YOUR_TEAM": "Notify your team", "BUSINESS_APP": "Business App", "CAMPAIGNS": "Campaigns", "CRM": "CRM", "INBOX": "Inbox", "LISTS": "Lists", "MARKETPLACE": "Marketplace", "PAYMENTS": "Payments", "TASKS": "Tasks", "FEATURED": "Featured"}, "NO_TEMPLATES_FOUND": "No templates found", "RECOMMENDED_TEMPLATES": "Recommended for you", "RECENTLY_ADDED": "Recently added", "POPULAR": "Popular", "SEE_ALL": "See all", "SEE_LESS": "See less", "SELECT_INSTRUCTIONS": "Select a template or start from scratch"}, "GROUPING": {"SELECT_REQUIREMENTS": "Select at least two consecutive steps to group", "NUM_SELECTED": "{{ count }} selected", "CREATE_GROUP": "Create group", "EDIT_GROUP": "Edit group", "GROUP_NAME": "Group name", "UNGROUP_ALL": "Ungroup all steps", "EDIT_GROUP_DETAILS": "Edit group details", "SUBMIT_ERROR": "There was a problem creating a group with the actions chosen.", "DISABLE_GROUP_BUTTON_TEXT": "A group cannot be created inside a group", "DISABLE_DELETE_BUTTON_TEXT": "Must have at least two actions in a group", "GROUP_NOTES": "Notes", "TOO_MANY_CHARACTERS": "Too many characters", "HAS_GROUP_AS_A_CHILD": "Cannot group a branch that contains a group", "DELETE_GROUP": "Delete group", "DELETE_GROUP_SUCCESS": "Group deleted Succesfully", "DELETE_GROUP_ERROR": "Error deleting group"}, "SNIPPET": {"SAVE_AS_ACTION_SET": "Save as action set", "CREATE_SNIPPET": "Create action set", "SNIPPET_SUBTITLE": "Action sets are a saved group of actions that can be reused across your automations.", "SNIPPET_NAME": "Name", "SNIPPET_DESCRIPTION": "Description", "SUCCESS_MESSAGE": "Action set created"}, "PREVIEW_SNIPPET_DIALOG": {"USE_SNIPPET": "Use action set", "DELETE_SNIPPET": "Delete", "DELETE_SUCCESS_MESSAGE": "Action set deleted successfully", "DELETE_ERROR_MESSAGE": "Something went wrong, please try again.", "INVALID_SNIPPET_WARNING": "One or more actions are missing required information"}}, "HTML_EDITOR": {"DYNAMIC_CONTENT": {"DYNAMIC_CONTENT": "Dynamic content", "INSERT_DYNAMIC_CONTENT": "Insert dynamic content", "FROM_PREVIOUS_STEP": "From a previous step", "AUTOMATION_DETAILS": "Automation details", "AUTOMATION_NAME": "Automation name", "WHEN_RUN": "Date and time the step runs", "NO_DATA_FOUND": "No content found", "NO_DATA_FOUND_DESCRIPTION": "No compatible content could be found for this input", "ADD_DYNAMIC_CONTENT": "Add dynamic content", "SALESPERSON_CUSTOM_DATA": "Assigned salesperson custom data", "BUSINESS_CUSTOM_DATA": "Business custom data", "CATEGORY_DESCRIPTION": "What type of content do you want to add?"}}, "TEMPLATES": {"BUSINESS_NAME": "Business name", "BUSINESS_WEBSITE": "Business website", "BUSINESS_STREET_ADDRESS": "Business street address", "BUSINESS_STREET_ADDRESS_2": "Business street address 2", "CUSTOMER_IDENTIFIER": "Customer identifier", "ACCOUNT_IDENTIFIER": "Account identifier", "BUSINESS_POSTAL_CODE": "Business postal code", "BUSINESS_CITY": "Business city", "BUSINESS_STATE": "Business state", "BUSINESS_COUNTRY": "Business country", "BUSINESS_PHONE_NUMBER": "Business phone number", "BUSINESS_ORIGIN": "Business origin", "BUSINESS_CATEGORY_IDS": "Business category IDs", "BUSINESS_EMAIL": "Business email", "BUSINESS_RAW": "Raw business data", "COMPANY_IDENTIFIER": "Company identifier", "ASSIGNED_SALESPERSON_FIRST_NAME": "Assigned salesperson first name", "ASSIGNED_SALESPERSON_LAST_NAME": "Assigned salesperson last name", "ASSIGNED_SALESPERSON_PHONE_NUMBER": "Assigned salesperson phone number", "ASSIGNED_SALESPERSON_EMAIL": "Assigned salesperson email", "ORDER_ID": "Order ID", "ORDER_LINE_ITEMS": "Order line items", "ORDER_STATUS": "Order status", "ORDER_SALESPERSON_ID": "Order salesperson ID", "ORDER_EXPIRY": "Order expiry date", "ORDER_CONTRACT_START": "Order contract start date", "ORDER_RAW": "Raw order data", "USER_FIRST_NAME": "First name", "USER_LAST_NAME": "Last name", "USER_EMAIL": "Email", "USER_RAW": "Raw user data", "PRODUCT_NAME": "Product name"}, "CAMPAIGN_DETAILS": {"ADD_TEMPLATE": {"ADD_EXISTING": "Add existing email"}}, "CRM_FIELDS_DIALOG": {"TITLE": "Add additional fields", "COMPANY_FIELDS": "Company fields", "CONTACT_FIELDS": "Contact fields", "OPPORTUNITY_FIELDS": "Opportunity fields", "CUSTOM_OBJECT_FIELDS": "Custom object fields", "CUSTOM_OBJECT_TYPE": "Custom object type"}, "DEBUG": {"INPUT_PARAMETERS_COUNT": "Input parameters ({{ count }})", "OUTPUT_PARAMETERS_COUNT": "Output parameters ({{ count }})"}, "AUTOMATION_RUNS": {"INCLUDE_DEFAULT": "Include system automations", "STARTED_BY": "Started by", "ONLY_SHOW_ERRORS": "Only show runs that stopped due to an error", "OF_MANY": "of many", "OF": "of", "STARTED_AFTER": "Started after", "STARTED_BEFORE": "Started before"}, "ARCHIVED_SUFFIX": " (archived)"}