import { Inject, Injectable } from '@angular/core';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';
import { ActivityObjectService, CompanyService, ContactService, OpportunityService } from '@galaxy/crm/static';
import { ListFieldOptionsRequestInterface, ListFieldOptionsResponseInterface } from '@vendasta/crm';
import { Observable, of } from 'rxjs';
import { map, switchMap, take } from 'rxjs/operators';
import { AutomationReplacementChipsDataService, PagedResponse } from '../common/components';

export interface ExpectedExtraData {
  fieldId: string;
  objectType: string;
}

interface ListFieldOptions {
  listFieldOptions(request: ListFieldOptionsRequestInterface): Observable<ListFieldOptionsResponseInterface>;
}

@Injectable({ providedIn: 'root' })
export class CrmFieldOptionsSelectorService implements AutomationReplacementChipsDataService<string> {
  supportsSearching = true;

  constructor(
    private readonly contactService: ContactService,
    private readonly companyService: CompanyService,
    private readonly activityService: ActivityObjectService,
    private readonly opportunityService: OpportunityService,
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
  ) {}

  convertDataToDisplay(x: string): Observable<string> {
    return of(x);
  }

  convertDataToFormValue(x: string): any {
    return x;
  }

  doLookup(
    searchTerm: string,
    cursor: string,
    pageSize: number,
    extraData?: ExpectedExtraData,
  ): Observable<PagedResponse<string>> {
    let objectService: ListFieldOptions = this.contactService;
    if (extraData?.objectType === 'Company') {
      objectService = this.companyService;
    } else if (extraData?.objectType === 'Activity') {
      objectService = this.activityService;
    } else if (extraData?.objectType === 'Opportunity') {
      objectService = this.opportunityService;
    }

    return this.namespace$.pipe(
      switchMap((namespace) =>
        objectService.listFieldOptions({
          namespace: namespace,
          fieldId: extraData?.fieldId,
          pageSize: 100, //This is not a paged api
          searchTerm: searchTerm,
        }),
      ),
      take(1),
      map((resp) => {
        return {
          results: (resp.options || []).sort(),
          hasMore: false,
          nextCursor: '',
        };
      }),
    );
  }

  getMulti(formValues: any[]): Observable<string[]> {
    return of(formValues);
  }
}
