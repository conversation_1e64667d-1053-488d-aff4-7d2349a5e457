import { Inject, Injectable } from '@angular/core';
import { BillingService, ProductPricing } from '@galaxy/billing';
import { Environment, EnvironmentService } from '@galaxy/core';
import { App, AppKey, AppKeyInterface, AppPartnerService } from '@galaxy/marketplace-apps';
import { TranslateService } from '@ngx-translate/core';
import { Addon, MarketplaceAppService, MarketplacePackagesApiService } from '@vendasta/marketplace-packages';
import { combineLatest, Observable, of } from 'rxjs';
import { map, shareReplay, switchMap } from 'rxjs/operators';
import { AUTOMATION_MARKET_ID_INJECTION_TOKEN$, AUTOMATION_NAMESPACE_INJECTION_TOKEN$ } from '@galaxy/automata/shared';

const LBOTProducts = {
  [Environment.DEMO]: [
    { appId: 'RM', editionId: 'EDITION-38SMW45H' },
    { appId: 'MS', editionId: '' },
    { appId: 'SM', editionId: 'EDITION-SWVF3WH8' },
    { appId: 'MP-fba21121b71148c9bb33e11fcd92d520', editionId: 'EDITION-4WWZC3RJ' }, // Customer Voice
    { appId: 'MP-94072e44d5364872b672d7ab4fc7a7e8', editionId: '' }, // AdIntel
    { appId: 'MP-9cc9f21f0a234a46ad78087fc09f16bc', editionId: 'EDITION-RC58KN73' }, // Website | Express
    { appId: 'MP-M2R4V4KZH4QLSRM3ZZ6DQB7N8Z6PL5NL', editionId: '' },
  ],
  [Environment.PROD]: [
    { appId: 'RM', editionId: 'EDITION-F7JZ5TV8' },
    { appId: 'MS', editionId: '' },
    { appId: 'SM', editionId: 'EDITION-FVGBNLVZ' },
    { appId: 'MP-c4974d390a044c28aec31e421aa662b2', editionId: 'EDITION-TC8HJZNS' }, // Customer Voice
    { appId: 'MP-94072e44d5364872b672d7ab4fc7a7e8', editionId: '' }, // AdIntel
    { appId: 'MP-ee4ea04e553a4b1780caf7aad7be07cd', editionId: 'EDITION-VFNL43ZF' }, // Website | Express
    { appId: 'MP-58G85F84DZBND7CK228578MWLD38SS4S', editionId: '' },
  ],
};

export const ANY_ADDON = '<ANY-ADDON>';
export const ANY_EDITION = '<ANY-EDITION>';
export const ANY_TRIAL_OPTION = '<ANY-TRIAL-OPTION>';

/** Used to summarize an account by its ID and name. */
export interface AppAndNames {
  appId: string;
  editionId?: string;
  addonId?: string;
  name: string;
}

export interface FullPriceInformation {
  retail: ProductPricing;
}

@Injectable({ providedIn: 'root' })
export class AppsService {
  private apps$: Observable<App[]>;
  private allApps$: Observable<App[]>;
  private lbotApps$: Observable<App[]>;

  private lbotPricingMap$: Observable<{ [sku: string]: ProductPricing }>;
  private lbotWholesalePricingMap$: Observable<{ [sku: string]: ProductPricing }>;

  constructor(
    private readonly appv2Service: AppPartnerService,
    private readonly marketplacePackagesService: MarketplaceAppService,
    private readonly environmentService: EnvironmentService,
    private readonly translate: TranslateService,
    private readonly billingService: BillingService,
    private readonly marketplacePackagesApiService: MarketplacePackagesApiService,
    @Inject(AUTOMATION_MARKET_ID_INJECTION_TOKEN$)
    private readonly marketId$: Observable<string>,
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) public readonly namespace$: Observable<string>,
  ) {
    const activatableApps$ = combineLatest([this.namespace$, this.marketId$]).pipe(
      switchMap(([partnerId, marketId]) => this.marketplacePackagesService.getAllActivatableApps(partnerId, marketId)),
      shareReplay(1),
    );

    // Extract all app keys
    const appKeys$ = activatableApps$.pipe(
      map((apps) =>
        apps.map((a) => {
          return { appId: a.appId } as AppKey;
        }),
      ),
    );

    // Fetch the apps
    this.apps$ = combineLatest([appKeys$, this.namespace$, this.marketId$]).pipe(
      switchMap(([appKeys, partnerId, marketId]) => {
        return this.appv2Service.getMulti(appKeys, partnerId, marketId, null, false).pipe(
          map((apps) => {
            return apps.filter((a) => !!a.key);
          }),
        );
      }),
      shareReplay(1),
    );

    this.allApps$ = combineLatest([appKeys$, this.namespace$, this.marketId$]).pipe(
      switchMap(([appKeys, partnerId, marketId]) => {
        return this.appv2Service.getMulti(appKeys, partnerId, marketId, null, true).pipe(
          map((apps) => {
            return apps.filter((a) => !!a.key);
          }),
        );
      }),
      shareReplay(1),
    );

    const lbotAppKeys$ = of(LBOTProducts[this.environmentService.getEnvironment()]);
    this.lbotApps$ = combineLatest([lbotAppKeys$, this.namespace$, this.marketId$]).pipe(
      switchMap(([appKeys, partnerId, marketId]) => {
        return this.appv2Service.getMulti(appKeys, partnerId, marketId, null, true).pipe(
          map((apps) => {
            return apps.filter((a) => !!a.key);
          }),
        );
      }),
      shareReplay(1),
    );

    this.lbotPricingMap$ = combineLatest([lbotAppKeys$, this.namespace$, this.marketId$]).pipe(
      switchMap(([appKeys, partnerId, marketId]) => {
        const appIds = appKeys.map((key) => this.appKeyToKeySku(key));
        return this.billingService.getMultiRetailPricing(partnerId, null, appIds, marketId);
      }),
      shareReplay(1),
    );
    this.lbotWholesalePricingMap$ = combineLatest([lbotAppKeys$, this.namespace$]).pipe(
      switchMap(([appKeys, partnerId]) => {
        const appIds = appKeys.map((key) => this.appKeyToKeySku(key));
        return this.billingService.getMultiProductPricing(partnerId, appIds);
      }),
      shareReplay(1),
    );
  }

  appKeyToKeySku(appKey: AppKeyInterface): string {
    return `${appKey.appId}${appKey.editionId ? ':' + appKey.editionId : ''}`;
  }

  skuToAppKey(appKeyString: string): AppKeyInterface {
    const keyParts = appKeyString.split(':');
    return {
      appId: keyParts[0],
      editionId: keyParts.length > 1 ? keyParts[1] : '',
    };
  }

  getApps(): Observable<App[]> {
    return this.apps$;
  }

  getLBOTApps(): Observable<App[]> {
    return this.lbotApps$;
  }

  getAddons(appId: string): Observable<Addon[]> {
    return this.namespace$.pipe(
      switchMap((partnerId) =>
        this.marketplacePackagesApiService.getMultiAddonsByApp({
          appIds: [appId],
          partnerId: partnerId,
        }),
      ),
      map((a) => a?.addonMap[appId].addons),
    );
  }

  /**
   * Returns all apps names, returning separate names per each edition if the app uses editions
   * @param includeNotEnabled - whether to include apps the partner has not 'enabled'
   * @param isLbot - whether to fetch from the LBOT-designated list of apps
   * @param anyEdition - whether 'any edition' has been selected by the user,
   *        in which case "(any edition)" is appended to the product's name (in place of a specific edition name)
   */
  getProductNames(
    includeNotEnabled: boolean,
    isLbot = false,
    anyEdition = false,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    givenAppKey?: AppKeyInterface,
  ): Observable<AppAndNames[]> {
    // Get the correct list of apps.
    let appsList = isLbot ? this.lbotApps$ : this.apps$;
    if (includeNotEnabled) {
      appsList = this.allApps$;
    }

    // Map each product to its name.
    return appsList.pipe(
      map((apps) => {
        const appNames: AppAndNames[] = [];

        apps.forEach((app) => {
          let productName = app.sharedMarketingInformation?.name;

          // handle apps with editions
          if (app.editionInformation?.editions) {
            // if "any edition of this product" is selected, append that to the product name instead of a specific edition name
            if (anyEdition) {
              productName += ` (${this.translate
                .instant('AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.ANY_EDITION')
                .toLowerCase()})`;
              // editionId is used downstream to match up with what was selected
              appNames.push({ appId: app.key.appId, editionId: ANY_EDITION, name: productName });
              return;
            }

            // return an entry for each edition name, unless editionsIncluded is false in which
            // case only return the Pro edition (edition id with '')
            app.editionInformation?.editions?.forEach((e) => {
              productName = app.sharedMarketingInformation?.name + ` | ${e.name}`;
              // if the editionId is undefined it's the "pro" version and is captured elsewhere as empty string, need to do
              // so similarly here so that downstream can properly match on it
              const appWithEditionData = {
                appId: e.appKey.appId,
                editionId: e.appKey.editionId || '',
                name: productName,
              };
              appNames.push(appWithEditionData);
            });
            return;
          }

          // otherwise it's a regular base app with no edition - still marking it as editionId '' so downstream
          // can match on it
          appNames.push({ appId: app.key.appId, editionId: '', name: productName });
          return;
        });
        return appNames;
      }),
    );
  }

  getProductName(givenAppKey: AppKeyInterface, includeNotEnabled: boolean, isLbot = false): Observable<string> {
    return this.getProductNames(includeNotEnabled, isLbot, givenAppKey.editionId === ANY_EDITION, givenAppKey).pipe(
      map((apps: AppAndNames[]) => {
        return (apps || []).find((app) => {
          if (givenAppKey.editionId === ANY_EDITION) {
            return app?.appId === givenAppKey?.appId;
          }
          return app?.appId === givenAppKey?.appId && app.editionId === givenAppKey.editionId;
        });
      }),
      switchMap((app) => {
        if (!app) {
          if (givenAppKey.appId !== '') {
            return of(
              this.translate.instant('AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_NOT_FOUND', {
                appId: givenAppKey.appId,
              }),
            );
          }

          if (includeNotEnabled) {
            return of(this.translate.instant('AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.PRODUCT_OR_ADDON'));
          }
          return of(this.translate.instant('AUTOMATIONS.EDITOR.TRIGGERS.PRODUCT_ACTIVATION.ANY_PRODUCT'));
        }
        return of(app.name);
      }),
    );
  }

  // TODO: Currently is LBOT-only, add option for all apps when needed
  getMultiProductRetailPricing(skus: string[]): Observable<{ [sku: string]: FullPriceInformation }> {
    return this.lbotPricingMap$.pipe(
      map((pricingMap) => {
        const result: { [sku: string]: FullPriceInformation } = {};
        skus.map((sku) => {
          result[sku] = {
            retail: pricingMap[sku],
          };
        });
        return result;
      }),
    );
  }
}
