import { AutomataDevelopmentService, Context, PublishedState, TaskType } from '@vendasta/automata';
import { of } from 'rxjs';
import { take } from 'rxjs/operators';
import { TaskDefinitionsService } from './task-definitions.service';

class MockAutomationService {
  automation$ = of({ id: 'A-123', context: Context.AUTOMATION_CONTEXT_PARTNER, isDefaultAutomation: false });
}

let featureFlagsIncludeDraft: any;
let featureFlagsOn: any;
let featureFlagsOff: any;
let featureFlagsInternalNoDraft: any;
let mockAutomationService: any;
let mockContextService: any;

describe('TaskDefinitionService', () => {
  beforeEach(() => {
    featureFlagsIncludeDraft = {
      featureFlags$: of({}),
      includeInternalFeatures$: of(false),
      includeDraftFeatures$: of(true),
    };
    featureFlagsOn = {
      featureFlags$: of({}),
      includeInternalFeatures$: of(true),
      includeDraftFeatures$: of(true),
    };
    featureFlagsOff = {
      featureFlags$: of({}),
      includeInternalFeatures$: of(false),
      includeDraftFeatures$: of(false),
    };
    featureFlagsInternalNoDraft = {
      featureFlags$: of({}),
      includeInternalFeatures$: of(true),
      includeDraftFeatures$: of(false),
    };
    mockAutomationService = new MockAutomationService();
    mockContextService = {};
    mockContextService.currentContext = Context.AUTOMATION_CONTEXT_PARTNER;
  });

  describe('actions$', () => {
    it('should list the actions without internals an no draft when includeDraftFeatures is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOff,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.actions$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_ACTION,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the actions without internals and include draft when includeDraftFeatures is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.actions$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_ACTION,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });
  });

  describe('internalActions$', () => {
    it('should list the actions for internals and no draft when includeDraftFeatures is false and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsInternalNoDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalActions$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_ACTION,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the actions for internals and draft when includeDraftFeatures is true and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOn,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalActions$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_ACTION,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should return empty list when includeInternals is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalActions$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledTimes(0);
        done();
      });
    });
  });

  describe('triggers$', () => {
    it('should list the triggers without internals an no draft when includeDraftFeatures is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOff,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.triggers$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_TRIGGER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the triggers without internals and include draft when includeDraftFeatures is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.triggers$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_TRIGGER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });
  });

  describe('internalTriggers$', () => {
    it('should list the internalTriggers for internals and no draft when includeDraftFeatures is false and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsInternalNoDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalTriggers$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_TRIGGER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the internalTriggers for internals and draft when includeDraftFeatures is true and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOn,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalTriggers$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_TRIGGER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should return empty list when includeInternals is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalTriggers$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledTimes(0);
        done();
      });
    });
  });

  describe('filters$', () => {
    it('should list the filters without internals an no draft when includeDraftFeatures is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOff,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.currentTaskDefinitionId$$.next('');
      svc.currentTriggerTaskDefinitionId$$.next('');
      svc.setPrecedingStepIds(['step1']);
      svc.filters$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_FILTER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', triggerTaskDefinitionId: '', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the filters without internals and include draft when includeDraftFeatures is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.currentTaskDefinitionId$$.next('');
      svc.currentTriggerTaskDefinitionId$$.next('');
      svc.setPrecedingStepIds(['step1']);
      svc.filters$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_FILTER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', triggerTaskDefinitionId: '', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });
  });

  describe('internalFilters$', () => {
    it('should list the internalFilters for internals and no draft when includeDraftFeatures is false and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsInternalNoDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalFilters$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_FILTER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the internalFilters for internals and draft when includeDraftFeatures is true and includeInternals is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsOn,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalFilters$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_FILTER,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
            internal: true,
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should return empty list when includeInternals is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.internalFilters$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledTimes(0);
        done();
      });
    });
  });

  describe('buildingBlocks$', () => {
    it('should list the buildingBlocks without internals an no draft when includeDraftFeatures is false', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsInternalNoDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.buildingBlocks$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: PublishedState.PUBLISHED_STATE_PUBLISHED,
            taskType: TaskType.TASK_TYPE_BUILDING_BLOCKS,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });

    it('should list the buildingBlocks without internals and include draft when includeDraftFeatures is true', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.buildingBlocks$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_BUILDING_BLOCKS,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });
  });

  describe('checkTaskDefinitionAvailableAtSubscriptionLevel', () => {
    it('should return true if task definition', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.buildingBlocks$.pipe(take(1)).subscribe(() => {
        expect(automataDevelopmentService.listTaskDefinitions).toHaveBeenCalledWith(
          {
            onlyAvailableInDefaultAutomations: false,
            publishedState: null,
            taskType: TaskType.TASK_TYPE_BUILDING_BLOCKS,
            supportedContexts: [Context.AUTOMATION_CONTEXT_PARTNER],
          },
          { automationId: 'A-123', workflowStepParentIds: ['step1'] },
          '',
          500,
        );
        done();
      });
    });
  });

  describe('doesTaskDefinitionHaveOutputParameters', () => {
    it('should return true if task definition has output parameters', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() =>
        of({
          results: [
            {
              valid: true,
              hasAccessToRun: true,
              taskDefinition: {
                id: 'TaskDefinition-add-tag',
                outputParameters: [
                  {
                    optional: false,
                    isEntity: false,
                  },
                ],
              },
            },
          ],
        }),
      );

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.currentTaskDefinitionId$$.next('');
      svc.currentTriggerTaskDefinitionId$$.next('');
      svc.setPrecedingStepIds(['step1']);
      svc
        .doesTaskDefinitionHaveOutputParameters('TaskDefinition-add-tag')
        .pipe(take(1))
        .subscribe((z) => {
          expect(z).toEqual(true);
          done();
        });
    });
    it('should return false if task definition does not have output parameters', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() =>
        of({
          results: [
            {
              valid: true,
              hasAccessToRun: true,
              taskDefinition: {
                id: 'TaskDefinition-add-tag',
              },
            },
          ],
        }),
      );

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.currentTaskDefinitionId$$.next('');
      svc.currentTriggerTaskDefinitionId$$.next('');
      svc
        .doesTaskDefinitionHaveOutputParameters('TaskDefinition-add-tag')
        .pipe(take(1))
        .subscribe((z) => {
          expect(z).toEqual(false);
          done();
        });
    });
    it('should return false if task definition not found', (done) => {
      const automataDevelopmentService = new AutomataDevelopmentService(null);
      automataDevelopmentService.listTaskDefinitions = jest.fn().mockImplementation(() => of({ results: [] }));

      const svc = new TaskDefinitionsService(
        automataDevelopmentService,
        featureFlagsIncludeDraft,
        null,
        null,
        null,
        mockContextService,
        mockAutomationService,
      );
      svc.setPrecedingStepIds(['step1']);
      svc.currentTaskDefinitionId$$.next('');
      svc.currentTriggerTaskDefinitionId$$.next('');
      svc
        .doesTaskDefinitionHaveOutputParameters('TaskDefinition-add-tag')
        .pipe(take(1))
        .subscribe((z) => {
          expect(z).toEqual(false);
          done();
        });
    });
  });
});
