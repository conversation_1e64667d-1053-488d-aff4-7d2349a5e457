import { Inject, Injectable } from '@angular/core';
import {
  AccessService,
  AutomataContextService,
  AUTOMATIONS_ACCESS_SERVICE,
  AutomationsFeatureCache,
  CurrentAutomationService,
} from '@galaxy/automata/shared';
import { TranslateService } from '@ngx-translate/core';
import {
  AutomataDevelopmentService,
  Automation,
  DataType,
  ListTaskDefinitionInterface,
  ListTaskDefinitionsRequestFiltersInterface,
  PublishedState,
  TaskType,
} from '@vendasta/automata';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import Fuse from 'fuse.js';
import { BehaviorSubject, combineLatest, Observable, of, ReplaySubject } from 'rxjs';
import {
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  shareReplay,
  switchMap,
  withLatestFrom,
} from 'rxjs/operators';
import {
  AccountGroupCreatedTrigger,
  ManualAccountGroupTrigger,
  ManualCompanyTrigger,
  ManualContactTrigger,
  ManualOrderTrigger,
  SendEmailEventTrigger,
  WorkflowTaskCategoryDescription,
  WorkflowTaskCategoryLabel,
} from '../automation-editor/component-loader/constants';
import { WORKFLOW_COMPONENTS } from '../automation-editor/component-loader/definitions';
import { Category } from './category';
import { FullTaskDefinitionInterface } from './full-task-definition';

interface FuseSearch {
  name: string;
  category: string;
  data: FullTaskDefinitionInterface;
}

export const TRIGGERS_INVALID_FOR_DELAY_UNTIL = [
  AccountGroupCreatedTrigger,
  ManualAccountGroupTrigger,
  ManualOrderTrigger,
  ManualCompanyTrigger,
  ManualContactTrigger,
];

export const TRIGGERS_INVALID_FOR_STARTING_AUTOMATIONS = [SendEmailEventTrigger];

@Injectable({ providedIn: 'root' })
export class TaskDefinitionsService {
  automationContext = this.automationContextService.currentContext;
  showInternalTriggers$: Observable<boolean> = this.automationsFeatureFlags.includeInternalFeatures$;
  includeDraftFeatures$: Observable<boolean> = this.automationsFeatureFlags.includeDraftFeatures$;
  searchTerm$$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  searchTerm$: Observable<string> = this.searchTerm$$.asObservable().pipe(debounceTime(100));

  precedingStepIds$$: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);

  currentTaskDefinitionId$$: ReplaySubject<string> = new ReplaySubject<string>(1);
  currentTriggerTaskDefinitionId$$: ReplaySubject<string> = new ReplaySubject<string>(1);

  waitForDataType$$: BehaviorSubject<DataType> = new BehaviorSubject<DataType>(undefined);
  waitForDataType$: Observable<DataType> = this.waitForDataType$$.asObservable();

  automation$: Observable<Automation> = this.currentAutomationService.automation$.pipe(
    distinctUntilChanged((first, second) => {
      return first?.entityType === second?.entityType && first?.triggerDefinitionId == second?.triggerDefinitionId;
    }),
  );

  private actions$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.includeDraftFeatures$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, includeDraft, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      return this.automataDevelopmentService.listTaskDefinitions(
        {
          taskType: TaskType.TASK_TYPE_ACTION,
          publishedState: publishedState,
          onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
          supportedContexts: automation?.context ? [automation.context] : [this.automationContext],
        },
        { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
        '',
        500,
      );
    }),
    catchError((err) => {
      console.error(err);
      this.alertService.errorSnack(this.translateService.instant('COMMON.ERROR_TRY_AGAIN'));
      return of({ results: [] });
    }),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    withLatestFrom(this.automationsFeatureFlags.featureFlags$),
    map(([taskDefs, featureFlags]) =>
      taskDefs.filter((t) => {
        const featureFlagId = t.uiDefinition?.featureFlag;
        if (!featureFlagId) {
          return true;
        }
        return featureFlags[featureFlagId];
      }),
    ),
    shareReplay(1),
  );

  public actionsWithSearch$: Observable<Category[]> = combineLatest([this.actions$, this.searchTerm$]).pipe(
    map(([tasks, searchTerm]) => this.searchFullTaskDefinitions(tasks, searchTerm)),
    categorizeTasks,
  );

  private internalActions$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.showInternalTriggers$,
    this.includeDraftFeatures$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, internalFeatures, includeDraft, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      if (internalFeatures) {
        return this.automataDevelopmentService.listTaskDefinitions(
          {
            taskType: TaskType.TASK_TYPE_ACTION,
            publishedState: publishedState,
            internal: true,
            onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
            supportedContexts: automation?.context ? [automation.context] : [this.automationContext], //TODO: this should fallback to the injected default context
          },
          { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
          '',
          500,
        );
      }
      return of({ results: [] });
    }),
    catchError(() => of({ results: [] })),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    shareReplay(1),
  );

  public internalActionsWithSearch$: Observable<Category[]> = combineLatest([
    this.internalActions$,
    this.searchTerm$,
  ]).pipe(
    map(([tasks, searchTerm]) => this.searchFullTaskDefinitions(tasks, searchTerm)),
    categorizeTasks,
  );

  public triggers$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.includeDraftFeatures$,
    this.waitForDataType$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, includeDraft, waitForDataType, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      const filters: ListTaskDefinitionsRequestFiltersInterface = {
        taskType: TaskType.TASK_TYPE_TRIGGER,
        publishedState: publishedState,
        onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
        supportedContexts: automation?.context ? [automation.context] : [this.automationContext], //TODO: this should fallback to the injected default context
      };
      if (waitForDataType) {
        filters.waitForDataTypeScope = waitForDataType;
      }

      return this.automataDevelopmentService.listTaskDefinitions(
        filters,
        { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
        '',
        500,
      );
    }),
    catchError((err) => {
      console.error(err);
      this.alertService.errorSnack(this.translateService.instant('COMMON.ERROR_TRY_AGAIN'));
      return of({ results: [] });
    }),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    shareReplay(1),
  );

  private triggersWithSearch$: Observable<Category[]> = combineLatest([this.triggers$, this.searchTerm$]).pipe(
    map(([tasks, searchTerm]) => this.searchFullTaskDefinitions(tasks, searchTerm)),
    categorizeTasks,
  );

  public internalTriggers$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.showInternalTriggers$,
    this.includeDraftFeatures$,
    this.waitForDataType$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, internalFeatures, includeDraft, waitForDataType, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      if (internalFeatures) {
        const filters: ListTaskDefinitionsRequestFiltersInterface = {
          taskType: TaskType.TASK_TYPE_TRIGGER,
          publishedState: publishedState,
          internal: true,
          onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
          supportedContexts: automation?.context ? [automation.context] : [this.automationContext], //TODO: this should fallback to the injected default context
        };
        if (waitForDataType) {
          filters.waitForDataTypeScope = waitForDataType;
        }
        return this.automataDevelopmentService.listTaskDefinitions(
          filters,
          { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
          '',
          500,
        );
      }
      return of({ results: [] });
    }),
    catchError(() => of({ results: [] })),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    shareReplay(1),
  );

  public internalTriggersWithSearch$: Observable<Category[]> = combineLatest([
    this.internalTriggers$,
    this.searchTerm$,
  ]).pipe(
    map(([tasks, searchTerm]) => this.searchFullTaskDefinitions(tasks, searchTerm)),
    categorizeTasks,
  );

  public filters$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.includeDraftFeatures$,
    this.precedingStepIds$$,
    this.currentTriggerTaskDefinitionId$$,
  ]).pipe(
    switchMap(([automation, includeDraft, precedingStepIds, triggerIdIfDifferentFromAutomationTrigger]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      return this.automataDevelopmentService.listTaskDefinitions(
        {
          taskType: TaskType.TASK_TYPE_FILTER,
          publishedState: publishedState,
          onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
          supportedContexts: automation?.context ? [automation.context] : [this.automationContext],
        },
        {
          automationId: automation?.id,
          workflowStepParentIds: precedingStepIds,
          triggerTaskDefinitionId: triggerIdIfDifferentFromAutomationTrigger,
        },
        '',
        500,
      );
    }),
    catchError((err) => {
      console.error(err);
      this.alertService.errorSnack(this.translateService.instant('COMMON.ERROR_TRY_AGAIN'));
      return of({ results: [] });
    }),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    withLatestFrom(this.automationsFeatureFlags.featureFlags$),
    map(([taskDefs, featureFlags]) =>
      taskDefs.filter((t) => {
        const featureFlagId = t.uiDefinition?.featureFlag;
        if (!featureFlagId) {
          return true;
        }
        return featureFlags[featureFlagId];
      }),
    ),
    shareReplay({ bufferSize: 1, refCount: true }),
  );

  public internalFilters$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.showInternalTriggers$,
    this.includeDraftFeatures$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, internalFeatures, includeDraft, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      if (internalFeatures) {
        return this.automataDevelopmentService.listTaskDefinitions(
          {
            taskType: TaskType.TASK_TYPE_FILTER,
            publishedState: publishedState,
            internal: true,
            onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
            supportedContexts: automation?.context ? [automation.context] : [this.automationContext], //TODO: this should fallback to the injected default context
          },
          { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
          '',
          500,
        );
      }
      return of({ results: [] });
    }),
    catchError(() => of({ results: [] })),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    shareReplay(1),
  );

  private buildingBlocks$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.automation$,
    this.includeDraftFeatures$,
    this.precedingStepIds$$,
  ]).pipe(
    switchMap(([automation, includeDraft, precedingStepIds]) => {
      const isBuiltInAutomationView = automation?.isDefaultAutomation;
      let publishedState = PublishedState.PUBLISHED_STATE_PUBLISHED;
      if (includeDraft) {
        publishedState = null;
      }
      return this.automataDevelopmentService.listTaskDefinitions(
        {
          taskType: TaskType.TASK_TYPE_BUILDING_BLOCKS,
          publishedState: publishedState,
          onlyAvailableInDefaultAutomations: isBuiltInAutomationView,
          supportedContexts: automation?.context ? [automation.context] : [this.automationContext], //TODO: this should fallback to the injected default context
        },
        { automationId: automation?.id, workflowStepParentIds: precedingStepIds },
        '',
        500,
      );
    }),
    catchError((err) => {
      console.error(err);
      this.alertService.errorSnack(this.translateService.instant('COMMON.ERROR_TRY_AGAIN'));
      return of({ results: [] });
    }),
    map((resp) => this.listTaskDefinitionInterfacesToFullTaskDefinitions(resp?.results)),
    shareReplay(1),
  );

  public buildingBlocksWithSearch$: Observable<Category[]> = combineLatest([
    this.buildingBlocks$,
    this.searchTerm$,
  ]).pipe(
    map(([tasks, searchTerm]) => this.searchFullTaskDefinitions(tasks, searchTerm)),
    categorizeTasks,
  );

  public allTaskDefinitions$: Observable<FullTaskDefinitionInterface[]> = combineLatest([
    this.actions$,
    this.internalActions$,
    this.triggers$,
    this.internalTriggers$,
    this.filters$,
    this.internalFilters$,
    this.buildingBlocks$,
  ]).pipe(
    map((allTaskDefinitionsArrays) => [].concat(...allTaskDefinitionsArrays)),
    shareReplay(1),
  );

  constructor(
    private readonly automataDevelopmentService: AutomataDevelopmentService,
    private readonly automationsFeatureFlags: AutomationsFeatureCache,
    private readonly alertService: SnackbarService,
    private readonly translateService: TranslateService,
    @Inject(AUTOMATIONS_ACCESS_SERVICE) private accessService: AccessService,
    private readonly automationContextService: AutomataContextService,
    private readonly currentAutomationService: CurrentAutomationService,
  ) {}

  getTriggers(isWaitingForEvent: boolean): Observable<Category[]> {
    let triggers$: Observable<Category[]>;
    if (isWaitingForEvent) {
      // TODO deal with this in the backend list
      triggers$ = this.triggersWithSearch$.pipe(
        map((triggers) => this.filterTriggers(triggers, TRIGGERS_INVALID_FOR_DELAY_UNTIL)),
      );
    } else {
      triggers$ = this.triggersWithSearch$.pipe(
        map((triggers) => this.filterTriggers(triggers, TRIGGERS_INVALID_FOR_STARTING_AUTOMATIONS)),
      );
    }

    return combineLatest([triggers$, this.automationsFeatureFlags.featureFlags$]).pipe(
      map(([categories, featureFlags]) => {
        return categories.map((c) => {
          const x = JSON.parse(JSON.stringify(c));
          x.contents = x.contents.filter((t) => {
            if (t?.uiDefinition?.featureFlag) {
              return featureFlags[t.uiDefinition.featureFlag];
            }
            return true;
          });
          return x;
        });
      }),
    );
  }

  private filterTriggers(categories: Category[], excludedTaskDefinitionIds: string[]) {
    return categories.map((c) => {
      const x = JSON.parse(JSON.stringify(c));
      x.contents = x.contents.filter(
        (t) => !excludedTaskDefinitionIds.find((invalid) => t.taskDefinition.id === invalid),
      );
      return x;
    });
  }

  setPrecedingStepIds(precedingStepIds: string[]): void {
    this.precedingStepIds$$.next(precedingStepIds);
  }

  setSearchTerm(searchTerm: string): void {
    this.searchTerm$$.next(searchTerm);
  }

  setWaitForDataType(waitForDataType: DataType): void {
    this.waitForDataType$$.next(waitForDataType);
  }

  setCurrentTaskDefinitionId(taskDefinitionId: string, isTrigger: boolean): void {
    this.currentTaskDefinitionId$$.next(taskDefinitionId);
    // If the currentTaskDefiniton is a trigger we want to send it to the list task definitions for filters so that we can load the trigger filters properly if adding a new trigger
    // if the current task definition is not a trigger we'll just fall back to the automation's saved trigger.
    if (isTrigger) {
      this.currentTriggerTaskDefinitionId$$.next(taskDefinitionId);
    } else {
      this.currentTriggerTaskDefinitionId$$.next('');
    }
  }

  searchFullTaskDefinitions(tasks: FullTaskDefinitionInterface[], searchTerm: string): FullTaskDefinitionInterface[] {
    if (!searchTerm) {
      return tasks;
    }
    const fuseSearchData: FuseSearch[] = (tasks || []).map((a) => ({
      name: this.translateService.instant(a.uiDefinition.name),
      category: this.translateService.instant(
        a.uiDefinition.categories.map((c) => WorkflowTaskCategoryLabel[c]).join(' '),
      ),
      data: a,
    }));
    const fuse = new Fuse(fuseSearchData, {
      keys: ['name', 'category'],
      includeScore: true,
      ignoreLocation: true,
      threshold: 0.1,
    });
    const results = fuse.search(searchTerm);
    return results.map((r) => r.item.data);
  }

  checkTaskDefinitionAvailableAtSubscriptionLevel(taskDefinitionId: string): Observable<boolean> {
    return this.allTaskDefinitions$.pipe(
      map((allTaskDefinitions) => allTaskDefinitions.find((td) => td.taskDefinition.id === taskDefinitionId)),
      switchMap((td) => this.accessService.hasAccessToFeatures(td?.taskDefinition?.requiredSubscriptionFeatures)),
    );
  }

  doesTaskDefinitionHaveOutputParameters(taskDefinitionId: string): Observable<boolean> {
    return this.allTaskDefinitions$.pipe(
      map((allTaskDefinitions) => allTaskDefinitions.find((td) => td.taskDefinition.id === taskDefinitionId)),
      map((fullTaskDefinition) => fullTaskDefinition?.taskDefinition?.outputParameters?.length > 0),
    );
  }

  listTaskDefinitionInterfacesToFullTaskDefinitions(
    ltds: ListTaskDefinitionInterface[],
  ): FullTaskDefinitionInterface[] {
    return (ltds || []).map((ltd) => this.listTaskDefinitionInterfaceToFullTaskDefinition(ltd)).filter((x) => !!x);
  }

  listTaskDefinitionInterfaceToFullTaskDefinition(ltd: ListTaskDefinitionInterface): FullTaskDefinitionInterface {
    if (!ltd.taskDefinition) {
      return null;
    }
    const ui = WORKFLOW_COMPONENTS[ltd.taskDefinition.id];
    if (!ui) {
      return null;
    }

    return {
      taskDefinition: ltd.taskDefinition,
      uiDefinition: ui,
      metadata: {
        valid: ltd.valid,
        hasAccessToRun: ltd.hasAccessToRun,
      },
    };
  }
}

/**
 * Sorts the task definitions (when available) by a natural ordering, e.g. category then alphabetical.
 * @param tasks
 *  The tasks to sort once they become available.
 */
function categorizeTasks(fullTaskDefinitions: Observable<FullTaskDefinitionInterface[]>): Observable<Category[]> {
  return fullTaskDefinitions.pipe(
    map((tasks) => {
      const categoriesMap: { [key: number]: Category } = {};
      tasks.map((t) => {
        t.uiDefinition.categories?.map((category) => {
          if (!categoriesMap[category]) {
            categoriesMap[category] = {
              category: category,
              contents: [],
              label: WorkflowTaskCategoryLabel[category],
              description: WorkflowTaskCategoryDescription[category],
            };
          }
          categoriesMap[category].contents.push(t);
        });
      });
      return categoriesMap;
    }),
    map((categoriesMap: { [key: number]: Category }) => {
      //sort each individual list alphabetically
      const categories: Category[] = [];
      for (const [categoryEnum] of Object.entries(categoriesMap)) {
        categoriesMap[categoryEnum].contents.sort((a, b) => a.uiDefinition.name.localeCompare(b.uiDefinition.name));
        categories.push(categoriesMap[categoryEnum]);
      }
      return categories;
    }),
  );
}
