import {
  ABBranchTaskDefinitionId,
  CompanyFilterTaskDefinitionId,
  ContactFilterTaskDefinitionId,
  DelayUntilEventTaskDefinitionId,
  IfElseBranchCrmCallDataTaskDefinitionId,
  IfElseBranchCrmEmailDataTaskDefinitionId,
  IfElseBranchCrmMeetingDataTaskDefinitionId,
  IfElseBranchCrmNoteDataTaskDefinitionId,
  IfElseBranchCrmTaskDataTaskDefinitionId,
  IfElseIntegrationConnectionFilterTaskDefinitionId,
  InvoiceFilterTaskDefinitionId,
  OpportunityFilterTaskDefinitionId,
  ProductPriceFilterTaskDefinitionId,
  RateLimitTaskDefinitionId,
} from './constants';

export const RIGHT = 'RIGHT';
export const NONE = 'NONE';
export const NO_CONDITIONS_MET = -1;
export type Branch = number | 'RIGHT' | 'NONE';

export interface BranchOptionsInterface {
  class: string;
  translationString: string;
}

export interface BranchesInterface {
  left?: BranchOptionsInterface;
  right?: BranchOptionsInterface;
}

// Hacky way to tell the label selector to use the branch# (+1) as the branch label
export const USE_DYNAMIC_BRANCH_LABEL = 'use-dynamic-branch-label';
const IF_ELSE_BRANCH_OPTIONS = {
  left: {
    translationString: USE_DYNAMIC_BRANCH_LABEL,
    class: 'yes-node',
  },
  right: {
    translationString: USE_DYNAMIC_BRANCH_LABEL,
    class: 'no-node',
  },
};
// This would ideally be on the action definitions, but the problem is that IFElseBranch needs this for it's activity so
// that creates a circular dependency
export const BRANCH_OPTIONS: { [taskKey: string]: BranchesInterface } = {
  'TaskDefinition-25932478-3111-4462-b342-ee94628245df': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-d15e86da-4166-429d-b054-4006f55d0ea5': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-user-data': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-3733042b-2e31-42e1-a08a-da3d494b1e85': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-765792f2-c9e6-41d4-8a91-a3b17f403657': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-check-shopping-cart-contents': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-connections-filter': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-order-filter': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-opportunity-filter': IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-internal-account-filter': IF_ELSE_BRANCH_OPTIONS,
  [IfElseBranchCrmTaskDataTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [IfElseBranchCrmNoteDataTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [IfElseBranchCrmEmailDataTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [IfElseBranchCrmMeetingDataTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [IfElseBranchCrmCallDataTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [ContactFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [CompanyFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [OpportunityFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [ProductPriceFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [IfElseIntegrationConnectionFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  'TaskDefinition-get-open-opportunities': IF_ELSE_BRANCH_OPTIONS,
  [InvoiceFilterTaskDefinitionId]: IF_ELSE_BRANCH_OPTIONS,
  [DelayUntilEventTaskDefinitionId]: {
    left: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.EVENT_HAPPENED',
      class: 'ab-node',
    },
    right: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.EVENT_DID_NOT_HAPPEN',
      class: 'ab-node',
    },
  },
  [ABBranchTaskDefinitionId]: {
    left: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.A',
      class: 'ab-node',
    },
    right: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.B',
      class: 'ab-node',
    },
  },
  [RateLimitTaskDefinitionId]: {
    left: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.YES',
      class: 'yes-node',
    },
    right: {
      translationString: 'AUTOMATIONS.EDITOR.BRANCH.NO',
      class: 'no-node',
    },
  },
};
