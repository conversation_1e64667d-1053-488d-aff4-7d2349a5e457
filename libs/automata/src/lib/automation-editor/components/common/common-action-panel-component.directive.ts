import { ChangeDetectorRef, Directive, inject, Input, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormArray, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import {
  AutomationInterface,
  ChoiceContinue,
  ChoiceEnd,
  OutputParameterInterface,
  RuleInterface,
  SleepWaitConditions,
  WaitForTriggerFilterInterface,
  WorkflowStepInterface,
} from '@vendasta/automata';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Observable, of, throwError } from 'rxjs';
import { catchError, finalize, take, tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { AutomationVariableMenuService } from '../../../common/components/automation-variable-menu/automation-variable-menu.service';
import { Branch } from '../../component-loader/branch-options';
import { AutomationErrorHandlerService, ValidationService } from '../../services';
import { AutomationsSideDrawerService } from '../../side-drawer/automations-side-drawer-service';
import { WorkflowStepService } from '../../workflow-step.service';

const TRIGGER_STEP_ID = 'trigger';

export interface CommonActionPanelInterface {
  step: WorkflowStepInterface;
  title: string;
  automation: AutomationInterface;
  precedingSteps: string[];
  branch: Branch;
  viewTemplate: TemplateRef<any>;
  isProcessing: boolean;
  taskDefinitionId: string;
  isGoal: boolean;

  /**
   * True when the user is allowed to try saving the contents of the panel.
   * This should be used in conjunction with isFormValid() to ensure that the
   * contents of the panel are valid when saving is attempted.
   */
  savingEnabled: boolean;
  showSaveButton: boolean;

  showBackButton: boolean;

  showNextButton: boolean;

  showDataPassing: boolean;

  getRules(): RuleInterface[];

  getData(): any;

  /*
   * isFormValid is called after the user clicks the save button. With a return of `true`, the form is considered valid
   * and the rest of the save workflow will happen. If the return value is `false` a generic error message is shown and
   * nothing is saved. If the return value is a string, this is the same as returning false but instead of the generic error
   * message the (translated) string will be shown to users.
   */
  isFormValid(): boolean | string;

  getSleepTimeInNanos(): number;

  getWaitForTriggerTaskDefinitionId(): string;

  save(
    name: string,
    customOutputParameters: OutputParameterInterface[],
    notes?: string,
    groupId?: string,
  ): Observable<boolean>;

  delete(): void;

  back(): void; //back is only used for delay until event
  next(): void;

  getWorkflowIdOverride(): string | undefined;
}

export const NanoSecondsPerMinute = 6 * 10 ** 10;
export const NanoSecondsPerHour = NanoSecondsPerMinute * 60;
export const NanoSecondsPerDay = NanoSecondsPerHour * 24;
export const NanoSecondsPerYear = NanoSecondsPerDay * 365;

@Directive()
export abstract class CommonActionPanelComponent implements CommonActionPanelInterface, OnInit {
  @Input() step: WorkflowStepInterface;
  @Input() title: string;
  @Input() automation: AutomationInterface;
  @Input() precedingSteps: string[];
  @Input() branch: Branch;
  @Input() viewTemplate: TemplateRef<any>;
  @Input() isGoal = false;

  @Input() set taskDefinitionId(id: string) {
    this._taskDefinitionId = id;
  }

  get taskDefinitionId(): string {
    return this._taskDefinitionId;
  }

  private _taskDefinitionId: string;

  isProcessing = false;
  savingEnabled = false;
  private _showBackButton = false;
  public get showBackButton() {
    return this._showBackButton;
  }

  public set showBackButton(value) {
    this._showBackButton = value;
  }

  showNextButton = false;
  showSaveButton = true;
  showDataPassing = true;

  abstract getRules(stepId?: string): RuleInterface[];

  abstract getData(): any;

  // getControlsToValidateOnSave should return all controls to be touched and updated for validation display purposes
  abstract getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[];

  abstract isFormValid(): boolean | string;

  protected readonly workflowStepService: WorkflowStepService = inject(WorkflowStepService);
  protected readonly sideMenuService: AutomationsSideDrawerService = inject(AutomationsSideDrawerService);
  protected readonly snackbarService: SnackbarService = inject(SnackbarService);
  protected readonly translateService: TranslateService = inject(TranslateService);
  protected readonly errorHandler: AutomationErrorHandlerService = inject(AutomationErrorHandlerService);
  protected readonly validationService: ValidationService = inject(ValidationService);
  protected readonly cdr: ChangeDetectorRef = inject(ChangeDetectorRef);
  protected readonly automationVariableMenuService: AutomationVariableMenuService =
    inject(AutomationVariableMenuService);

  ngOnInit(): void {
    // trigger is not included in the list of workflow steps
    this.automationVariableMenuService.init(this.automation, this.precedingSteps.concat([TRIGGER_STEP_ID]));
  }

  getAutomation(): AutomationInterface {
    return this.automation;
  }

  getSleepTimeInNanos(): number {
    return 0;
  }

  getSleepWaitConditions(): SleepWaitConditions {
    return new SleepWaitConditions({
      weekdaySelection: [],
    });
  }

  getWaitForTriggerTaskDefinitionId(): string {
    return '';
  }

  getWaitForTriggerFilter(): WaitForTriggerFilterInterface {
    return {};
  }

  getWorkflowIdOverride(): string | undefined {
    return undefined;
  }

  delete(): void {
    this.isProcessing = true;
    let action$ = this.workflowStepService.deleteStep(this.step, this.precedingSteps);
    if (this.isGoal) {
      action$ = this.workflowStepService.deleteGoal(this.step);
    }
    action$
      .pipe(
        tap(() => this.sideMenuService.close()),
        tap(() => (this.isProcessing = false)),
        catchError((err) => {
          this.isProcessing = false;
          this.cdr.detectChanges();
          return throwError(err);
        }),
      )
      .subscribe();
  }

  save(
    name: string,
    customOutputParameters: OutputParameterInterface[],
    notes?: string,
    groupId?: string,
  ): Observable<boolean> {
    this.isProcessing = true;
    this.getControlsToValidateOnSave().forEach((control) => this.validationService.updateControlValidity(control));
    const validationReason = this.isFormValid();
    if (typeof validationReason === 'string' && validationReason) {
      this.snackbarService.openErrorSnack(this.translateService.instant(validationReason));
      this.isProcessing = false;
      return of(false);
    } else if (!validationReason) {
      this.snackbarService.openErrorSnack(this.translateService.instant('AUTOMATIONS.COMMON.FORM_INVALID'));
      this.isProcessing = false;
      return of(false);
    }

    const stepId = this?.step?.id || uuidv4();
    const data = JSON.stringify(this.getData());
    const sleepNanos = this.getSleepTimeInNanos();
    const sleepConds = this.getSleepWaitConditions();
    const rules = this.getRules(stepId);
    const taskDefinitionId = this.taskDefinitionId;
    const waitForTriggerTaskDefinitionId = this.getWaitForTriggerTaskDefinitionId();
    const waitForTriggerFilter = this.getWaitForTriggerFilter();

    let action$;
    if (this.isGoal) {
      // check if step exists here
      let step = this.step;
      if (!step) {
        step = {
          choices: this.workflowStepService.createChoices(rules, ChoiceEnd, '', 'NONE', ChoiceContinue, false),
        };
      }
      step.id = stepId;
      step.data = data;
      step.sleep = sleepNanos;
      step.sleepWaitConditions = sleepConds;
      step.taskDefinitionId = taskDefinitionId;
      step.waitForTriggerTaskDefinitionId = waitForTriggerTaskDefinitionId;
      step.choices.rules = rules;
      step.name = name;
      step.customOutputParameters = customOutputParameters;
      step.waitForTriggerFilter = waitForTriggerFilter;
      step.notes = notes;
      action$ = this.workflowStepService.saveGoal(step);
    } else if (this.step) {
      this.step.data = data;
      this.step.sleep = sleepNanos;
      this.step.sleepWaitConditions = sleepConds;
      this.step.taskDefinitionId = taskDefinitionId;
      this.step.waitForTriggerTaskDefinitionId = waitForTriggerTaskDefinitionId;
      this.step.choices.rules = rules;
      this.step.name = name;
      this.step.customOutputParameters = customOutputParameters;
      this.step.waitForTriggerFilter = waitForTriggerFilter;
      this.step.notes = notes;
      action$ = this.workflowStepService.saveStep(this.step);
    } else {
      action$ = this.workflowStepService.addStep(
        stepId,
        taskDefinitionId,
        data,
        sleepNanos,
        sleepConds,
        waitForTriggerTaskDefinitionId,
        waitForTriggerFilter,
        rules,
        this.precedingSteps,
        this.branch,
        name,
        customOutputParameters,
        this.getWorkflowIdOverride(),
        notes,
        groupId,
      );
    }

    return action$.pipe(
      catchError((err) => {
        this.errorHandler.displayError(err);
        this.isProcessing = false;
        this.cdr.detectChanges();
        return throwError(err);
      }),
      tap(() => this.sideMenuService.close()),
      take(1),
      finalize(() => (this.isProcessing = false)),
    );
  }

  back(): void {
    // this is only used for delay-until-event
  }

  next(): void {
    // currently used in delay until event, can be implemented to allow multiple panels
  }
}
