<div>
  <div class="space" *ngIf="filteredDataComponentOptions$ | async as dataComponentOptions; else loading">
    <glxy-form-field>
      <mat-select [formControl]="dataOptionControl">
        <ng-container *ngFor="let option of dataComponentOptions">
          <mat-option [value]="option.value">
            {{ option.label | translate }}
          </mat-option>
        </ng-container>
      </mat-select>
      <mat-error *ngIf="filterError$ | async as filterError">
        {{ filterError | translate }}
      </mat-error>
    </glxy-form-field>
  </div>
  <div class="space" *ngIf="(selectedFilterComponent$ | async) && !resetComparisonSelect">
    <ng-container *ngIf="comparisonOptions$ | async as comparisonOptions">
      <glxy-form-field>
        <mat-select [formControl]="comparisonOptionControl">
          <ng-container *ngFor="let option of comparisonOptions">
            <mat-option [value]="option.value">
              {{ option.label | translate }}
            </mat-option>
          </ng-container>
        </mat-select>
        <glxy-error *ngIf="filterError$ | async as filterError">
          {{ filterError | translate }}
        </glxy-error>
      </glxy-form-field>
    </ng-container>
  </div>
  <div>
    <ng-template automataDynamicComponent></ng-template>
  </div>
</div>

<ng-template #loading>
  <ng-container [ngSwitch]="hasError">
    <div *ngSwitchCase="false" class="content side-drawer-shimmer-container">
      <span class="control-container selector stencil-shimmer"></span>
      <span class="control-container selector stencil-shimmer"></span>
    </div>
    <div *ngSwitchCase="true" class="filter-load-error">
      {{ 'AUTOMATIONS.EDITOR.ERRORS.UNABLE_TO_LOAD_FILTER' | translate }}
    </div>
  </ng-container>
</ng-template>
