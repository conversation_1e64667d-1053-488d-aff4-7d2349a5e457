import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { Rule, WorkflowStepOutputParameterContainer } from '@vendasta/automata';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SelectInputOptionInterface } from '@vendasta/galaxy/input';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { cloneDeep } from 'lodash';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  skipWhile,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';
import { DynamicComponentDirective } from '../../../../../common/dynamic-component.directive';
import { DataFilter, FilterType } from '../../definitions';
import { DynamicOptionsComponent } from '../../definitions/dynamic-options/dynamic-options';
import { FilterDataService } from './filter-data.service';

@Component({
  selector: 'automata-filter-data',
  templateUrl: './filter-data-selector.component.html',
  styleUrls: ['./filter-data-selector.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    TranslateModule,
    GalaxyAlertModule,
    MatButtonModule,
    MatCardModule,
    FormsModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatTooltipModule,
    GalaxyTooltipModule,
    DynamicComponentDirective,
  ],
})
export class FilterDataSelectorComponent implements OnInit, OnDestroy {
  @Input() taskDefinitionId: string;
  @Input() initialRule: Rule;
  @Input() predecessorOutputParams: WorkflowStepOutputParameterContainer[];
  @Input() currentlySelectedSubgroupId$: Observable<string>;

  @ViewChild(DynamicComponentDirective, { static: true })
  dynamicComponent: DynamicComponentDirective;

  private componentRef: ComponentRef<DynamicOptionsComponent>;
  private destroyed$$ = new Subject<void>();
  private subComponentDestroyed$$ = new Subject<void>();
  private selectedFilterComponent$$: BehaviorSubject<DataFilter> = new BehaviorSubject<DataFilter>(null);
  filterType: FilterType;
  filteredDataComponentOptions$: Observable<SelectInputOptionInterface[]>;
  filteredDataComponents$: Observable<Map<string, DataFilter>>;
  hasError = false;
  selectedFilterComponent$: Observable<DataFilter> = this.selectedFilterComponent$$
    .asObservable()
    .pipe(filter((selectedFilterComponent) => !!selectedFilterComponent));
  comparisonOptions$$: BehaviorSubject<SelectInputOptionInterface[]> = new BehaviorSubject<
    SelectInputOptionInterface[]
  >([]);
  comparisonOptions$: Observable<SelectInputOptionInterface[]> = this.comparisonOptions$$.asObservable();
  resetComparisonSelect = false;
  dataOptionControl: UntypedFormControl = new UntypedFormControl('');
  comparisonOptionControl: UntypedFormControl = new UntypedFormControl('');
  previouslySelectedDataComponent: string;
  filterError$: Observable<string>;

  constructor(
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private filterDataService: FilterDataService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.filterType = this.filterDataService.getFilter(this.taskDefinitionId, this.predecessorOutputParams);

    const dataOptionControlValueChanges$ = this.dataOptionControl.valueChanges.pipe(
      startWith(this.dataOptionControl.value),
      distinctUntilChanged(),
    );

    if (!this.filterType.subgroups$) {
      // For static components
      this.filteredDataComponents$ = this.filterType.dataComponents$;
    } else {
      // For dynamic components
      this.filteredDataComponents$ = combineLatest([
        this.filterType.subgroups$,
        this.filterType.dataComponents$,
        this.currentlySelectedSubgroupId$.pipe(skipWhile((s) => !s)),
      ]).pipe(
        map(([, dataComponents, currentlySelectedSubgroupId]) => {
          const components: Map<string, DataFilter> = new Map();

          for (const [key, dataComponent] of dataComponents) {
            if (dataComponent.subgroup.id === currentlySelectedSubgroupId) {
              components.set(key, dataComponent);
            }
          }

          return components;
        }),
        shareReplay({ refCount: true, bufferSize: 1 }),
      );
    }

    this.filterError$ = combineLatest([this.filteredDataComponents$, dataOptionControlValueChanges$]).pipe(
      map(([dataComponents, selectedValue]) => {
        let currentComponent;
        if (selectedValue) {
          currentComponent = dataComponents.get(selectedValue);
        } else {
          currentComponent = dataComponents.get(this.initialRule?.getVariablePath());
        }
        return currentComponent;
      }),
      map((currentComponent) => currentComponent?.validationError?.filterError || ''),
      tap((error) => {
        if (error) {
          this.dataOptionControl.setErrors({
            invalid: true,
          });
        } else {
          this.dataOptionControl.setErrors(null);
        }
        this.dataOptionControl.markAsTouched();
        this.hasError = !!error;
      }),
    );

    this.filteredDataComponentOptions$ = combineLatest([
      this.filteredDataComponents$,
      dataOptionControlValueChanges$,
    ]).pipe(
      map(([components, selectedValue]) => {
        // take out the options that don't apply to the current component/step
        const optionsForCurrentComponent: SelectInputOptionInterface[] = [];

        for (const [key, component] of components) {
          optionsForCurrentComponent.push({
            label: component.label,
            value: key,
            selected: key === selectedValue,
          });
        }

        if (optionsForCurrentComponent.findIndex((option) => option.value === selectedValue) === -1 || !selectedValue) {
          // if the selected value isn't in the options then select the first option instead
          optionsForCurrentComponent[0].selected = true;
          if (selectedValue) {
            this.dataOptionControl.setValue(optionsForCurrentComponent[0].value);
          }
        }

        return optionsForCurrentComponent;
      }),
    );

    this.filteredDataComponents$
      .pipe(
        take(1),
        tap((dataComponents) => {
          const initialDataComponent = dataComponents.get(this.initialRule?.getVariablePath());

          if (!!this.initialRule && initialDataComponent?.rules.length > 0) {
            const index = initialDataComponent.rules.findIndex((r) => r.deepEquals(this.initialRule, true));
            const foundRule = initialDataComponent.rules[index];
            this.comparisonOptionControl.setValue(index); // set value: should be the index
            this.dataOptionControl.setValue(foundRule.getVariablePath());
            this.previouslySelectedDataComponent = foundRule.getVariablePath();
            this.selectedFilterComponent$$.next(initialDataComponent);
            this.loadComponent(initialDataComponent, index);
          } else {
            // when it comes to the workflow step filtering, the datacomponents are the source of truth
            // of what options should be made available, whereas the dataComponentOptions are ALL the possible
            // options spanning all the possible steps
            const firstDataComponent = dataComponents.values().next().value;
            const firstRule = firstDataComponent.rules[0];
            this.comparisonOptionControl.setValue(0);
            this.dataOptionControl.setValue(firstRule.getVariablePath());
            this.previouslySelectedDataComponent = firstRule.getVariablePath();
            this.selectedFilterComponent$$.next(firstDataComponent);
            this.loadComponent(firstDataComponent, 0);
          }

          this.comparisonOptions$$.next(
            this.mapSelectedDataComponentsToOptions(
              dataComponents.get(this.dataOptionControl.value),
              this.comparisonOptionControl.value,
            ),
          );
        }),
        catchError((err) => {
          this.hasError = true;
          return err;
        }),
      )
      .subscribe();

    dataOptionControlValueChanges$
      .pipe(
        switchMap((valueSelected) => {
          return this.filteredDataComponents$.pipe(
            map((value) => {
              return value.get(valueSelected);
            }),
          );
        }),
        takeUntil(this.destroyed$$),
      )
      .subscribe((selectedComponent) => {
        if (selectedComponent) {
          this.selectedFilterComponent$$.next(selectedComponent);
        }
      });

    const comparisonOptionControlValueChanges$ = this.comparisonOptionControl.valueChanges.pipe(
      startWith(this.comparisonOptionControl.value),
      distinctUntilChanged(),
    );
    combineLatest([this.selectedFilterComponent$, dataOptionControlValueChanges$, comparisonOptionControlValueChanges$])
      .pipe(
        tap(([selectedFilterComponent, dataOption, comparisonIndex]) => {
          if (!selectedFilterComponent) {
            return;
          }

          this.loadComponent(selectedFilterComponent, comparisonIndex);
          let newComparisonIndex = 0;
          //If it was that only the comparison changed, don't change the comparison index back to 0
          if (dataOption === this.previouslySelectedDataComponent) {
            newComparisonIndex = comparisonIndex;
          }
          this.previouslySelectedDataComponent = dataOption;
          this.comparisonOptions$$.next(
            this.mapSelectedDataComponentsToOptions(selectedFilterComponent, newComparisonIndex),
          );
          // There is an issue with the select component ui not updating with the new options. This will kill the component
          // and reload it to work around that.
          this.resetComparisonSelect = true;
          this.cdr.detectChanges();
          this.resetComparisonSelect = false;
          this.cdr.detectChanges();
        }),
        takeUntil(this.destroyed$$),
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroyed$$.next();
    this.destroyed$$.unsubscribe();
    if (this.componentRef) {
      this.componentRef.destroy();
    }
    this.subComponentDestroyed$$.next();
    this.subComponentDestroyed$$.unsubscribe();
  }

  loadComponent(dataFilter: DataFilter, comparisonIndex: number): void {
    let dataEntryComponent = dataFilter.dataEntryComponent;
    if (comparisonIndex >= dataFilter?.dataEntryComponentList?.length) {
      comparisonIndex = 0;
    }
    if (dataFilter?.dataEntryComponentList?.length > 0) {
      dataEntryComponent = dataFilter.dataEntryComponentList[comparisonIndex];
    }
    const componentFactory = this.componentFactoryResolver.resolveComponentFactory(dataEntryComponent);

    const viewContainerRef = this.dynamicComponent.viewContainerRef;
    viewContainerRef.clear();
    this.subComponentDestroyed$$.next();

    try {
      this.componentRef = viewContainerRef.createComponent(componentFactory);
    } catch (e) {
      console.error(e);
    }
    if (dataFilter.updateComponentInstanceFn) {
      dataFilter.updateComponentInstanceFn(this.componentRef.instance);
    }
    if (!!this.initialRule && dataFilter.rules.find((r) => r.deepEquals(this.initialRule, true))) {
      this.componentRef.instance.initialValue = this.initialRule.extractValue();
    }
    if (this.componentRef.instance.initialize) {
      this.componentRef.instance.initialize(dataFilter.label, dataFilter.filterData);
    }
    this.componentRef.changeDetectorRef.detectChanges();
    this.componentRef.instance.selectedValue$.pipe(takeUntil(this.subComponentDestroyed$$)).subscribe();
  }

  getValues(): Rule {
    const currentDataComponent = this.selectedFilterComponent$$.getValue();
    const index = this.comparisonOptionControl.value;
    //Get a copy of the rule so when there are multiple it doesn't override the values
    const rule = cloneDeep(currentDataComponent.rules[index]);
    rule.setValue(this.componentRef.instance.getSelectedValue());
    return rule;
  }

  getControlsToValidateOnSave(): (UntypedFormControl | UntypedFormGroup | UntypedFormArray)[] {
    if (this.hasError) {
      return [];
    }
    return this.componentRef.instance.getControlsToValidateOnSave();
  }

  isFormValid(): boolean {
    return !this.hasError && this.componentRef.instance.isFormValid();
  }

  mapSelectedDataComponentsToOptions(selected: DataFilter, selectedIndex: number): SelectInputOptionInterface[] {
    const options: SelectInputOptionInterface[] = selected.rules.map((rule, index) => ({
      label: selected.comparisonLabels[index],
      value: index,
    }));

    if (options[selectedIndex]) {
      options[selectedIndex].selected = true;
    } else {
      // Scenario where indexes between filters don't match
      // reset selection to first rule
      options[0].selected = true;
      this.comparisonOptionControl.setValue(0);
    }
    return options;
  }
}
