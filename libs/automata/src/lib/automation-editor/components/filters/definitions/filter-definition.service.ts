import { Injectable } from '@angular/core';
import { DataType, Rule, WorkflowStepOutputParameterContainer } from '@vendasta/automata';
import { Observable, of } from 'rxjs';
import {
  CompanyFilterTaskDefinitionId,
  ContactFilterTaskDefinitionId,
  CustomAccountFilterTaskDefinitionId,
  CustomOrderFilterTaskDefinitionId,
  CustomProductFilterTaskDefinitionId,
  CustomUserFilterTaskDefinitionId,
  IfElseBranchOpportunityFilterTaskDefinitionId,
  IfElseBranchWorkflowStepTaskDefinitionId,
  IfElseIntegrationConnectionFilterTaskDefinitionId,
  InvoiceFilterTaskDefinitionId,
  OpportunityFilterTaskDefinitionId,
  ProductPriceFilterTaskDefinitionId,
} from '../../../component-loader/constants';
import { ACCOUNT_DATA_COMPONENTS } from './account-data';
import { ACCOUNT_INTERNAL_DATA_COMPONENTS } from './account-internal-data';
import { AuxiliaryDataFilterService } from './auxiliary-data-filter/auxiliary-data-filter.service';
import { BUSINESS_APP_DATA_COMPONENTS } from './business-app-data';
import {
  getDataTypeFromRule,
  getTypeMetaData,
  isValidParameterDataType,
} from './dynamic-options/dynamic-type-selectors';
import {
  DataFilter,
  FilterType,
  FilterTypeDefinition,
  FilterTypeInputParameter,
  ReplaceableInputParam,
  Subgroup,
} from './interface';
import { OPPORTUNITIES_COMPONENTS } from './opportunities-data';
import { ORDER_DATA_COMPONENTS } from './order-data';
import { PARTNER_DATA_COMPONENTS } from './partner-data';
import { SHOPPING_CART_CONTENTS_COMPONENTS } from './shopping_cart_contents';
import { USER_DATA_COMPONENTS } from './user-data';

import { map } from 'rxjs/operators';
import { buildStepReferenceString } from '../../common/utils';
import {
  customActivityDataKey,
  customCompanyDataKey,
  customContactDataKey,
  customOpportunityDataKey,
} from './auxiliary-data-filter/data-keys';
import { OPPORTUNITY_DATA_COMPONENTS } from './opportunity-data';
import { PRODUCT_PRICE_COMPONENTS } from './product-price-data';
import { CONNECTION_DETAILS_COMPONENTS } from './connection-details';
import { INVOICE_DATA_COMPONENTS } from './invoice-data';

@Injectable({ providedIn: 'root' })
export class FilterDefinitionService {
  constructor(private readonly auxiliaryDataFilterService: AuxiliaryDataFilterService) {}

  private STATIC_FILTER_TYPES: { [key: string]: FilterTypeDefinition } = {
    // 'account-data'
    'TaskDefinition-d15e86da-4166-429d-b054-4006f55d0ea5': {
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.NODE_TEXT',
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.ACCOUNT_GROUP_DATA',
      dataComponents$: of(ACCOUNT_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ACCOUNT_GROUP_ID,
        id: 'account_group_id',
      },
    },
    // 'user-data'
    'TaskDefinition-user-data': {
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.USER_DATA.NODE_TEXT',
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.USER_DATA.USER_DATA',
      dataComponents$: of(USER_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_SMB_USER_ID,
        id: 'user_id',
      },
    },
    // 'partner-data'
    'TaskDefinition-3733042b-2e31-42e1-a08a-da3d494b1e85': {
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.PARTNER_DATA.NODE_TEXT',
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.PARTNER_DATA.PARTNER_DATA',
      dataComponents$: of(PARTNER_DATA_COMPONENTS),
    },
    // 'business-app-data'
    'TaskDefinition-connections-filter': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CONNECTIONS_DATA.BUSINESS_APP',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CONNECTIONS_DATA.BUSINESS_APP',
      dataComponents$: of(BUSINESS_APP_DATA_COMPONENTS),
    },
    // 'shopping-cart-data'
    'TaskDefinition-check-shopping-cart-contents': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.SHOPPING_CART_CONTENTS.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.SHOPPING_CART_CONTENTS.NODE_TEXT',
      dataComponents$: of(SHOPPING_CART_CONTENTS_COMPONENTS),
    },
    // 'account internal data'
    'TaskDefinition-internal-account-filter': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_INTERNAL_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_INTERNAL_DATA.NODE_TEXT',
      dataComponents$: of(ACCOUNT_INTERNAL_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ACCOUNT_GROUP_ID,
        id: 'account_group_id',
      },
    },
    // 'order data'
    'TaskDefinition-order-filter': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ORDER_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.ORDER_DATA.NODE_TEXT',
      dataComponents$: of(ORDER_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ORDER_ID,
        id: 'order_id',
      },
    },
    // 'product price data'
    [ProductPriceFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.PRODUCT_PRICE_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.PRODUCT_PRICE_DATA.NODE_TEXT',
      dataComponents$: of(PRODUCT_PRICE_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_PRODUCT,
        id: 'product_id',
      },
    },
    // 'opportunity data'
    'TaskDefinition-opportunity-filter': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.OPPORTUNITY_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.OPPORTUNITY_DATA.NODE_TEXT',
      dataComponents$: of(OPPORTUNITY_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_OPPORTUNITY_ID,
        id: 'opportunity_id',
      },
    },
    // 'custom account data'
    [CustomAccountFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_ACCOUNT_DATA.CUSTOM_ACCOUNT_DATA',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_ACCOUNT_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.customAccountGroupDataFilters$,
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ACCOUNT_GROUP_ID,
        id: 'account_group_id',
      },
    },
    // 'custom user data'
    [CustomUserFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_USER_DATA.CUSTOM_USER_DATA',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_USER_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.customUserDataFilters$,
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_SMB_USER_ID,
        id: 'user_id',
      },
    },
    // 'custom order data'
    [CustomOrderFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_ORDER_DATA.CUSTOM_ORDER_DATA',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_ORDER_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.customOrderDataFilters$,
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ORDER_ID,
        id: 'order_id',
      },
    },
    // 'contact data'
    [ContactFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_CONTACT_DATA.TITLE',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_CONTACT_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(customContactDataKey, 'Contact'),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CONTACT_ID,
        id: 'contact_id',
      },
    },
    // 'company data'
    [CompanyFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_COMPANY_DATA.TITLE',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_COMPANY_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(customCompanyDataKey, 'Company'),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_COMPANY_ID,
        id: 'company_id',
      },
    },
    // 'opportunity data'
    [OpportunityFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_OPPORTUNITY_DATA.TITLE',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CRM_OPPORTUNITY_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(customOpportunityDataKey, 'Opportunity'),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CRM_OPPORTUNITY_ID,
        id: 'opportunity_id',
      },
    },
    'TaskDefinition-CRM-call-data': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_CALL_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_CALL_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(
        customActivityDataKey,
        'Activity',
        'Call',
      ),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CRM_CALL,
        id: 'call_id',
      },
    },
    // 'task data'
    'TaskDefinition-CRM-task-data': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_TASK_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_TASK_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(
        customActivityDataKey,
        'Activity',
        'Task',
      ),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_TASK,
        id: 'task_id',
      },
    },
    'TaskDefinition-CRM-note-data': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_NOTE_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_NOTE_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(
        customActivityDataKey,
        'Activity',
        'Note',
      ),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CRM_NOTE,
        id: 'note_id',
      },
    },
    'TaskDefinition-CRM-email-data': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_EMAIL_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_EMAIL_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(
        customActivityDataKey,
        'Activity',
        'Email',
      ),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CRM_EMAIL,
        id: 'email_activity_id',
      },
    },
    'TaskDefinition-CRM-meeting-data': {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_MEETING_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CRM_MEETING_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.getCRMObjectDataFilters(
        customActivityDataKey,
        'Activity',
        'Meeting',
      ),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_CRM_MEETING,
        id: 'meeting_activity_id',
      },
    },
    [InvoiceFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.INVOICE_DATA.TITLE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.INVOICE_DATA.NODE_TEXT',
      dataComponents$: of(INVOICE_DATA_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_INVOICE,
        id: 'invoice_id',
      },
    },
    // 'custom product data'
    [CustomProductFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_PRODUCT_DATA.CUSTOM_PRODUCT_DATA',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.CUSTOM_PRODUCT_DATA.NODE_TEXT',
      dataComponents$: this.auxiliaryDataFilterService.customProductDataFilters$,
    }, // 'Opportunity Data'
    [IfElseBranchOpportunityFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.OPEN_OPPORTUNITIES_DATA.NODE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.OPEN_OPPORTUNITIES_DATA.TITLE_TEXT',
      dataComponents$: of(OPPORTUNITIES_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_ACCOUNT_GROUP_ID,
        id: 'account_group_id',
      },
    }, // 'Quickbooks Invoice'
    [IfElseIntegrationConnectionFilterTaskDefinitionId]: {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.INTEGRATIONS.QBO.NODE_TEXT',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.INTEGRATIONS.QBO.TITLE_TEXT',
      dataComponents$: of(CONNECTION_DETAILS_COMPONENTS),
      replaceableInputParam: {
        dataType: DataType.DATA_TYPE_INTEGRATION_CONNECTION,
        id: 'connection_id',
      },
    },
  };

  private DYNAMIC_FILTER_TYPES: {
    [key: string]: (predecessorOutputParams: WorkflowStepOutputParameterContainer[]) => FilterType;
  } = {
    [IfElseBranchWorkflowStepTaskDefinitionId]: this.createDynamicWorkflowDataFilter,
  };

  private createDynamicWorkflowDataFilter(containers: WorkflowStepOutputParameterContainer[]): FilterType {
    const dataComponents: Map<string, DataFilter> = new Map();
    // We need another selector in here somewhere to choose simple/complex selectors
    for (const workflowStepOutputParameter of containers) {
      for (const outputParameter of workflowStepOutputParameter.outputParameters || []) {
        if (!isValidParameterDataType(outputParameter.settings.dataType)) {
          continue;
        }

        const key = buildStepReferenceString(workflowStepOutputParameter.workflowStep.id, outputParameter.settings.id);
        const dataType = outputParameter.settings.dataType;

        const dataTypeMetaData = getTypeMetaData(dataType, key, outputParameter.settings.repeated);

        if (!dataTypeMetaData) {
          continue;
        }

        dataComponents.set(key, {
          rules: dataTypeMetaData.rules,
          subgroup: {
            name: workflowStepOutputParameter.workflowStep.name,
            id: workflowStepOutputParameter.workflowStep.id,
          },
          dataEntryComponent: dataTypeMetaData.dataEntryComponent,
          dataEntryComponentList: dataTypeMetaData.dataEntryComponentList,
          displayService: dataTypeMetaData.displayService,
          label: outputParameter.settings.name,
          comparisonLabels: dataTypeMetaData.comparisonLabels,
        });
      }
    }
    const subgroups: Subgroup[] = [];
    for (const [, dataFilter] of dataComponents) {
      // only need one entry per subgroup
      if (subgroups.findIndex((subgroup) => subgroup.id === dataFilter.subgroup?.id) === -1) {
        subgroups.push({
          id: dataFilter.subgroup.id,
          name: dataFilter.subgroup.name,
        });
      }
    }
    return {
      titleTextKey: 'AUTOMATIONS.EDITOR.FILTERS.WORKFLOW_DATA.WORKFLOW_DATA',
      nodeTextKey: 'AUTOMATIONS.EDITOR.FILTERS.WORKFLOW_DATA.NODE_TEXT',
      dataComponents$: of(dataComponents),
      taskDefinitionId: IfElseBranchWorkflowStepTaskDefinitionId,
      subgroups$: of(subgroups),
      inputParameters: [],
    };
  }

  getFilter(taskDefinitionId: string, predecessorOutputParams?: WorkflowStepOutputParameterContainer[]): FilterType {
    if (this.STATIC_FILTER_TYPES[taskDefinitionId]) {
      const definition = this.STATIC_FILTER_TYPES[taskDefinitionId];
      const replaceableInputParams = this.filterOutputParamsToReplacementInputParams(
        definition.replaceableInputParam,
        predecessorOutputParams,
      );
      return {
        dataComponents$: definition.dataComponents$,
        inputParameters: replaceableInputParams,
        nodeTextKey: definition.nodeTextKey,
        taskDefinitionId: taskDefinitionId,
        titleTextKey: definition.titleTextKey,
      };
    }
    if (
      !predecessorOutputParams ||
      predecessorOutputParams.length == 0 ||
      !this.DYNAMIC_FILTER_TYPES[taskDefinitionId]
    ) {
      return null;
    }

    return this.DYNAMIC_FILTER_TYPES[taskDefinitionId](predecessorOutputParams);
  }

  getFilterDataComponentsForDisplay(taskDefinitionId: string, rule: Rule): Observable<RulesDisplay> {
    const definition = this.STATIC_FILTER_TYPES[taskDefinitionId];
    if (!definition) {
      const dataType = getDataTypeFromRule(rule);
      const dataFilter = getTypeMetaData(dataType.dataType, rule.getVariablePath(), dataType.repeated);
      return of({ dataFilter: dataFilter as DataFilter, comparisonIndex: dataType.comparisonIndex });
    }
    return definition.dataComponents$.pipe(
      map((dataComponents) => dataComponents.get(rule.getVariablePath())),
      map((dataFilter) => {
        const comparisonIndex = dataFilter.rules.findIndex((r) => r.deepEquals(rule, true));
        const nodeTextKey = definition.nodeTextKey;
        return { dataFilter, comparisonIndex, nodeTextKey };
      }),
    );
  }

  private filterOutputParamsToReplacementInputParams(
    replaceableInputParam: ReplaceableInputParam,
    predecessorOutputParams?: WorkflowStepOutputParameterContainer[],
  ): FilterTypeInputParameter[] {
    if (!replaceableInputParam) {
      return [];
    }

    const inputParams: FilterTypeInputParameter[] = [];
    for (const container of predecessorOutputParams || []) {
      const specificStepInputParams = [];
      for (const outputParam of container?.outputParameters || []) {
        if (outputParam?.settings.dataType === replaceableInputParam.dataType) {
          specificStepInputParams.push({
            id: outputParam.settings.id,
            name: outputParam.settings.name,
          });
        }
      }
      if (specificStepInputParams.length > 0) {
        inputParams.push({
          stepId: container.workflowStep.id,
          stepName: container.workflowStep.name,
          paramId: replaceableInputParam.id,
          paramDataType: replaceableInputParam.dataType,
          options: specificStepInputParams,
        });
      }
    }

    return inputParams;
  }
}

export interface RulesDisplay {
  dataFilter: DataFilter;
  comparisonIndex: number;
  nodeTextKey?: string;
}
