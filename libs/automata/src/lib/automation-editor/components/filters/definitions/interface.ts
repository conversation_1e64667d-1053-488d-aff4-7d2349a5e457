import { Observable } from 'rxjs';
import { DataType, Rule } from '@vendasta/automata';
import { Type } from '@angular/core';
import { DynamicEntryComponent, DynamicOptionsComponent } from './dynamic-options/dynamic-options';

export interface DataFilterDisplayService {
  initialize?(filterData: unknown): void;

  /**
   * getDisplay is used in automations DataServices to get a display value for the service.
   *
   * If you're using an Observable<string>, you must terminate the observable (use a take(1) in your implementation) otherwise, it will not work.
   */
  getDisplay(value: unknown): Observable<string> | string;
}

export interface DataFilterValidationError {
  panelError: string;
  filterError: string;
}

export interface DataFilter {
  rules: Rule[];
  // Subgroup is used to group different filters together, typically grouping different workflow steps together
  subgroup?: Subgroup;
  //dataEntrycomponent is the component that will be loaded for the 3rd row of the filter
  // type - Country
  // comparison (rule) - is
  // dataEntryComponent - [Canada]
  //If a single data entry component is specified then that will be loaded for all comparisons(rules)
  dataEntryComponent?: Type<DynamicOptionsComponent>;
  //If a list of data entry components is speicified then the one that has the same index as the comparison(rule) will
  //be loaded
  dataEntryComponentList?: Type<DynamicOptionsComponent>[];
  displayService: Type<DataFilterDisplayService>;
  label: string;
  comparisonLabels: string[];
  //Any data that is required to be passed down to the filter components, and services when initialized
  filterData?: unknown;
  //If an object for a validationError is available
  //panelError: will be displayed at the top of the panel
  //filterError: will be displayed under the filter field that is invalid
  validationError?: DataFilterValidationError;
  // updateComponentInstanceFn is run on your component to do any instance specific setup, such as setting a property for
  // a generic component
  updateComponentInstanceFn?: (component: any) => void;
}

export interface DataEntry {
  fieldId: string;
  label: string;
  dataEntryComponent: Type<DynamicEntryComponent>;
  crmObjectType: string;
}

// ReplaceableInputParam are the input params from the task definition that would make sense for a user to replace
// to look at a different entity than the one in the stream for filtering. Essentially any "stream" input params
// that aren't partner ID or market ID. This should be able to be generated from the task definition but we need
// to see if this concept even works before putting that effort in.
export interface ReplaceableInputParam {
  // The data type of the param so that it can be matched with output params from preceding steps
  dataType: DataType;
  // The id that will be injected into the workflow step's data to replace the stream data for this step
  id: string;
}

export interface FilterTypeInputParameter {
  stepId: string;
  stepName: string;
  paramId: string;
  paramDataType: DataType;
  options: {
    id: string;
    name: string;
  }[];
}

export interface FilterType {
  nodeTextKey: string;
  titleTextKey: string;
  dataComponents$: Observable<Map<string, DataFilter>>;
  taskDefinitionId: string;
  // While very similar, raw data types like string and boolean use subgroups, where as rich datatypes like
  // account group uses inputParameters
  subgroups$?: Observable<Subgroup[]>;
  // The steps and corresponding outputParameters that can be used as a replacement for the input parameters of this filter
  inputParameters: FilterTypeInputParameter[];
}

// The definition that is used to build a FilterType
export interface FilterTypeDefinition {
  nodeTextKey: string;
  titleTextKey: string;
  dataComponents$: Observable<Map<string, DataFilter>>;
  // Replaceable Input Param is the input param from the task definition that can be reasonably replaced in the workflow to act upon a different
  // entity. Instead of filtering for the stream's account group has a website you could filter for an account group returned from a webhook having
  // a website. Currently it's only one parameter per filter type that can be replaced because these filters focus on fetching the data for one entity.
  // This is purely a frontend restriction, the backend doesn't care.
  replaceableInputParam?: ReplaceableInputParam;
}

export interface Subgroup {
  id: string;
  name: string;
}
