import { Injectable, Type } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  Bo<PERSON>Equals,
  IsOneOfInStringListFold,
  Not,
  NumberEquals,
  NumberExists,
  NumberGreater<PERSON>han,
  Rule,
  StringContainsFold,
  StringEquals,
  StringExists,
  StringListContains,
  StringListContainsAll,
  TimeAfter,
  TimeAfterRelative,
  TimeBefore,
} from '@vendasta/automata';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  BoolDisplayService,
  BoolOptionsComponent,
  DynamicEntryComponent,
  PositiveIntegerComponent,
  TagOptionsDisplayService,
} from '../dynamic-options';
import { AuxiliaryDataCurrencyInputDisplayService } from '../dynamic-options/auxiliary-data-currency-input/auxiliary-data-currency-input-display.service';
import { AuxiliaryDataCurrencyInputComponent } from '../dynamic-options/auxiliary-data-currency-input/auxiliary-data-currency-input.component';
import { AuxiliaryDataDropdownDisplayService } from '../dynamic-options/auxiliary-data-dropdown-options/auxiliary-data-dropdown-display.service';
import { AuxiliaryDataDropdownComponent } from '../dynamic-options/auxiliary-data-dropdown-options/auxiliary-data-dropdown.component';
import { DateInputDisplayService } from '../dynamic-options/date-input/date-input-display.service';
import { DateInputComponent } from '../dynamic-options/date-input/date-input.component';
import { DynamicStringDisplayService } from '../dynamic-options/dynamic-string.service';
import { EmptyStringComponent } from '../dynamic-options/empty-string-component';
import { FreeTextInputComponent } from '../dynamic-options/free-text-input/free-text-input.component';
import { FreeTextListInputComponent } from '../dynamic-options/free-text-list-input/free-text-list-input.component';
import { IntegerDisplayService } from '../dynamic-options/integer/integer-display.service';
import { IntegerComponent } from '../dynamic-options/integer/integer.component';
import { Option } from '../dynamic-options/static-string-select/static-string-select.component';
import { DataEntry, DataFilter, DataFilterValidationError } from '../interface';
import { CrmStringAutocompleteComponent } from '../dynamic-options/crm-string-autocomplete';
import { CrmMultiStringAutocompleteComponent } from '../dynamic-options/crm-multi-string-autocomplete';
import { FloatComponent, FloatDisplayService } from '../dynamic-options/float';
import { StandardIds } from '@galaxy/crm/static';
import {
  PIPELINE_PARENT_RULE_NAME,
  PipelineStageComponent,
} from './custom-filters/opportunity-pipeline-stage/pipeline-stage-filter.component';
import { CrmCurrencyInputComponent } from '../dynamic-options/crm-currency-input/crm-currency-input.component';
import {
  CAMPAIGN_ID_RULE_NAME,
  CampaignIdFilterComponent,
} from './custom-filters/campaign-id/campaign-id-filter.component';

export enum FieldType {
  FIELD_TYPE_STRING = 1,
  FIELD_TYPE_INTEGER,
  FIELD_TYPE_DATE,
  FIELD_TYPE_DROPDOWN,
  FIELD_TYPE_CURRENCY,
  FIELD_TYPE_EMAIL,
  FIELD_TYPE_PHONE,
  FIELD_TYPE_BOOLEAN,
  FIELD_TYPE_TAG,
  FIELD_TYPE_DATETIME,
  FIELD_TYPE_STRING_LIST,
  FIELD_TYPE_GEOPOINT,
  FIELD_TYPE_FLOAT,
}

export interface FieldConfig {
  // the crm object type is used as a flag to choose to use crm inputs instead of just base inputs.
  // crm input use the object type and the field id so autocomplete values.
  crmObjectType?: string;
}

export interface FieldSchema {
  partnerId: string;
  fieldId: string;
  fieldType: FieldType;
  fieldName: string;
  fieldDescription: string;
  staticDropdownOptions?: Option[];
  currencyCode?: string;
  archived?: Date;
  fieldConfig?: FieldConfig;
}

// We don't support all the field types for building filters or other data inputs so some of these are commented out
export const supportedFilterFieldTypes = [
  FieldType.FIELD_TYPE_STRING,
  FieldType.FIELD_TYPE_INTEGER,
  FieldType.FIELD_TYPE_DATE,
  FieldType.FIELD_TYPE_DROPDOWN,
  FieldType.FIELD_TYPE_CURRENCY,
  FieldType.FIELD_TYPE_EMAIL,
  FieldType.FIELD_TYPE_PHONE,
  FieldType.FIELD_TYPE_BOOLEAN,
  FieldType.FIELD_TYPE_TAG,
  // FieldType.FIELD_TYPE_DATETIME,
  FieldType.FIELD_TYPE_STRING_LIST,
  FieldType.FIELD_TYPE_FLOAT,
];

// These are the field types for when selecting fields just for the fields, not for input with filters or user data (like update/create)
export const supportedSelectionFieldTypes = [
  FieldType.FIELD_TYPE_STRING,
  FieldType.FIELD_TYPE_INTEGER,
  FieldType.FIELD_TYPE_DATE,
  FieldType.FIELD_TYPE_DROPDOWN,
  FieldType.FIELD_TYPE_CURRENCY,
  FieldType.FIELD_TYPE_EMAIL,
  FieldType.FIELD_TYPE_PHONE,
  FieldType.FIELD_TYPE_BOOLEAN,
  FieldType.FIELD_TYPE_TAG,
  FieldType.FIELD_TYPE_DATETIME,
  FieldType.FIELD_TYPE_STRING_LIST,
  FieldType.FIELD_TYPE_FLOAT,
];

function getRulePropertyName(fieldPath: string, fieldId: string): string {
  if (filterOverrides.has(fieldId) && filterOverrides?.get(fieldId)?.customRuleName) {
    return filterOverrides?.get(fieldId)?.customRuleName ?? '';
  }

  return `{.${fieldPath}.${fieldId}}`;
}

function getFieldIdFromRule(ruleVariablePath: string): string {
  const [, fieldId] = ruleVariablePath.replace('.', '').split('.');
  return fieldId;
}

export function extractFieldIdsFromRule(ruleData: any): string[] {
  const rule = Rule.fromRuleInterface(ruleData);
  const fullVariablePath = rule.getVariablePath();
  const separatePaths = fullVariablePath.slice(1, -1).split('}{');
  const fieldIds = [];
  for (const path of separatePaths) {
    fieldIds.push(getFieldIdFromRule(path));
  }
  return fieldIds;
}

function formatToTitleCase(str: string): string {
  return str.charAt(0).toUpperCase() + str.toLowerCase().slice(1);
}

interface CRMOverridableFilter {
  customRuleName?: string;
  // presence of filter indicates override, null indicates to hide it
  filterComponent: DataFilter | null;
}

const HIDE_FILTER = null;
// this object contains filters that require specific behavior, think:  opportunity pipeline stages, salespeople, and the like
// where CRM stores identifiers but the UI needs to display a readable name, or combination of values
const filterOverrides: Map<string, CRMOverridableFilter> = new Map<string, CRMOverridableFilter>([
  [
    StandardIds.OpportunityPipelineID,
    {
      filterComponent: HIDE_FILTER,
    },
  ],
  [
    StandardIds.OpportunityCalculatedStageID,
    {
      filterComponent: HIDE_FILTER,
    },
  ],
  [
    StandardIds.OpportunityProbability,
    {
      filterComponent: new PipelineStageComponent(),
      customRuleName: PIPELINE_PARENT_RULE_NAME,
    },
  ],
  [
    StandardIds.ActivityCampaignID,
    {
      filterComponent: new CampaignIdFilterComponent(),
      customRuleName: CAMPAIGN_ID_RULE_NAME,
    },
  ],
]);

@Injectable({ providedIn: 'root' })
export class AuxiliaryDataFilterGeneratorService {
  constructor(private readonly translate: TranslateService) {}

  private createValidationError(archived: boolean, fieldName: string): DataFilterValidationError | undefined {
    if (!archived) {
      return;
    }

    return {
      panelError: this.translate.instant('AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.ERRORS.ARCHIVED_FIELD_PANEL_ERROR'),
      filterError: this.translate.instant('AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.ERRORS.ARCHIVED_FIELD_FILTER_ERROR', {
        fieldName: formatToTitleCase(fieldName),
      }),
    };
  }

  private generateDynamicFilter(
    fieldPath: string,
    fieldId: string,
    fieldType: FieldType,
    fieldName: string,
    dropdownOptions: Option[],
    currencyCode: string,
    archived: boolean,
    fieldConfig?: FieldConfig,
  ): DataFilter | null {
    const rulePropertyName = getRulePropertyName(fieldPath, fieldId);
    const validationError = this.createValidationError(archived, fieldName);
    if (filterOverrides.has(fieldId)) {
      const componentOverride = filterOverrides.get(fieldId);
      if (!componentOverride?.filterComponent) {
        return null;
      }
      componentOverride.filterComponent.validationError = validationError;
      return componentOverride?.filterComponent;
    }
    if (archived) {
      fieldName = fieldName + this.translate.instant('ARCHIVED_SUFFIX');
    }
    switch (fieldType) {
      case FieldType.FIELD_TYPE_INTEGER:
        return new DynamicIntegerFilterComponent(fieldName, rulePropertyName, validationError);
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_EMAIL:
      case FieldType.FIELD_TYPE_PHONE:
        if (fieldConfig?.crmObjectType) {
          return new DynamicCrmStringComponent(
            fieldName,
            rulePropertyName,
            fieldId,
            fieldConfig.crmObjectType,
            validationError,
          );
        }
        return new DynamicStringComponent(fieldName, rulePropertyName, validationError);
      case FieldType.FIELD_TYPE_DATE:
        return new DynamicDateComponent(fieldName, rulePropertyName, validationError);
      case FieldType.FIELD_TYPE_DROPDOWN:
        return new DynamicDropdownComponent(fieldName, rulePropertyName, dropdownOptions, validationError);
      case FieldType.FIELD_TYPE_CURRENCY:
        if (fieldConfig?.crmObjectType) {
          return new DynamicFloatComponent(fieldName, rulePropertyName, validationError);
        }
        return new DynamicCurrencyComponent(fieldName, rulePropertyName, currencyCode, validationError);
      case FieldType.FIELD_TYPE_STRING_LIST:
      case FieldType.FIELD_TYPE_TAG:
        if (fieldConfig?.crmObjectType) {
          return new DynamicCrmStringListComponent(
            fieldName,
            rulePropertyName,
            fieldId,
            fieldConfig.crmObjectType,
            validationError,
          );
        }
        return new DynamicStringListComponent(fieldName, rulePropertyName, validationError);
      case FieldType.FIELD_TYPE_FLOAT:
        return new DynamicFloatComponent(fieldName, rulePropertyName, validationError);
      case FieldType.FIELD_TYPE_BOOLEAN:
        return new DynamicBooleanComponent(fieldName, rulePropertyName, validationError);
      default:
        console.warn('Unsupported type of dynamic component', rulePropertyName);
        return null;
    }
  }

  private generateDataEntry(
    fieldId: string,
    fieldType: FieldType,
    fieldName: string,
    fieldConfig: FieldConfig | undefined,
  ): DataEntry | undefined {
    switch (fieldType) {
      case FieldType.FIELD_TYPE_INTEGER:
        return new DynamicDataEntryComponent(fieldName, fieldId, IntegerComponent);
      case FieldType.FIELD_TYPE_EMAIL:
      case FieldType.FIELD_TYPE_PHONE:
      case FieldType.FIELD_TYPE_STRING:
        return new DynamicDataEntryComponent(fieldName, fieldId, FreeTextInputComponent);
      case FieldType.FIELD_TYPE_DATE:
        return new DynamicDataEntryComponent(fieldName, fieldId, DateInputComponent);
      case FieldType.FIELD_TYPE_DROPDOWN:
        return new DynamicDataEntryComponent(fieldName, fieldId, AuxiliaryDataDropdownComponent);
      case FieldType.FIELD_TYPE_CURRENCY:
        if (fieldConfig?.crmObjectType) {
          return new DynamicDataEntryComponent(fieldName, fieldId, CrmCurrencyInputComponent, fieldConfig);
        }
        return new DynamicDataEntryComponent(fieldName, fieldId, AuxiliaryDataCurrencyInputComponent);
      case FieldType.FIELD_TYPE_STRING_LIST:
      case FieldType.FIELD_TYPE_TAG:
        if (fieldConfig?.crmObjectType) {
          return new DynamicDataEntryComponent(fieldName, fieldId, CrmMultiStringAutocompleteComponent, fieldConfig);
        }
        return new DynamicDataEntryComponent(fieldName, fieldId, FreeTextListInputComponent);
      case FieldType.FIELD_TYPE_FLOAT:
        return new DynamicDataEntryComponent(fieldName, fieldId, FloatComponent);
      case FieldType.FIELD_TYPE_BOOLEAN:
        return new DynamicDataEntryComponent(fieldName, fieldId, BoolOptionsComponent);
      default:
        return;
    }
  }

  generateDataFilters(
    fieldPath: string,
    fieldSchemas$: Observable<FieldSchema[]>,
  ): Observable<Map<string, DataFilter>> {
    return fieldSchemas$.pipe(
      map((fieldSchemas) => {
        const filterComponentMap = new Map();
        for (const f of fieldSchemas) {
          const { fieldId, fieldType, fieldName, staticDropdownOptions, currencyCode, archived, fieldConfig } = f;
          if (!fieldId) {
            continue;
          }
          if (!supportedFilterFieldTypes.includes(fieldType)) {
            continue;
          }
          const dynamicFilter = this.generateDynamicFilter(
            fieldPath,
            fieldId,
            fieldType,
            fieldName,
            staticDropdownOptions,
            currencyCode,
            !!archived,
            fieldConfig,
          );
          if (dynamicFilter === null) {
            continue;
          }
          filterComponentMap.set(getRulePropertyName(fieldPath, fieldId), dynamicFilter);
        }
        return filterComponentMap;
      }),
      catchError((error) => {
        console.error('Error generating data filters', error);
        return of(new Map<string, DataFilter>());
      }),
    );
  }

  generateDataEntryComponents(fieldPath: string, fieldSchemas$: Observable<FieldSchema[]>): Observable<DataEntry[]> {
    return fieldSchemas$.pipe(
      map((fieldSchemas) => {
        const dataEntryComponents: DataEntry[] = [];
        for (const f of fieldSchemas) {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { fieldId, fieldType, fieldName } = f;
          if (!fieldId) {
            continue;
          }
          if (!supportedFilterFieldTypes.includes(fieldType)) {
            continue;
          }
          const dynamicFilter = this.generateDataEntry(fieldId, fieldType, fieldName, f.fieldConfig);
          if (dynamicFilter === null || dynamicFilter === undefined) {
            continue;
          }
          dataEntryComponents.push(dynamicFilter);
        }
        return dataEntryComponents;
      }),
    );
  }
}

class DynamicIntegerFilterComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [
    IntegerComponent,
    IntegerComponent,
    IntegerComponent,
    EmptyStringComponent,
    EmptyStringComponent,
  ];
  displayService = IntegerDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.LESS_THAN_EQUAL',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.EQUALS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.GREATER_THAN',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.DOES_NOT_EXIST',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new Not(new NumberGreaterThan(rulePropertyName)),
      new NumberEquals(rulePropertyName),
      new NumberGreaterThan(rulePropertyName),
      new NumberExists(rulePropertyName),
      new Not(new NumberExists(rulePropertyName)),
    ];
  }
}

class DynamicStringComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [
    EmptyStringComponent,
    EmptyStringComponent,
    FreeTextInputComponent,
    FreeTextInputComponent,
    FreeTextListInputComponent,
    FreeTextListInputComponent,
  ];
  displayService = DynamicStringDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.DOES_NOT_EXIST',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.CONTAINS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.DOES_NOT_CONTAIN',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.IS_ONE_OF',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.IS_NOT_ONE_OF',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new Not(new StringEquals(rulePropertyName)),
      new StringEquals(rulePropertyName),
      new StringContainsFold(rulePropertyName),
      new Not(new StringContainsFold(rulePropertyName)),
      new IsOneOfInStringListFold(rulePropertyName),
      new Not(new IsOneOfInStringListFold(rulePropertyName)),
    ];
  }
}

class DynamicDateComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [DateInputComponent, DateInputComponent, PositiveIntegerComponent];
  displayService = DateInputDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DATE_TYPE.AFTER',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DATE_TYPE.BEFORE',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DATE_TYPE.WITHIN',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new TimeAfter(rulePropertyName),
      new TimeBefore(rulePropertyName),
      new TimeAfterRelative(rulePropertyName),
    ];
  }
}

class DynamicDropdownComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [
    EmptyStringComponent,
    EmptyStringComponent,
    AuxiliaryDataDropdownComponent,
    AuxiliaryDataDropdownComponent,
  ];
  displayService = AuxiliaryDataDropdownDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.DOES_NOT_EXIST',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DROPDOWN_TYPE.IS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DROPDOWN_TYPE.IS_NOT',
  ];
  filterData: Option[];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly dropdownOptions: Option[],
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.filterData = dropdownOptions;
    this.rules = [
      new StringExists(rulePropertyName),
      new Not(new StringExists(rulePropertyName)),
      new StringEquals(rulePropertyName),
      new Not(new StringEquals(rulePropertyName)),
    ];
  }
}

class DynamicCurrencyComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [
    AuxiliaryDataCurrencyInputComponent,
    AuxiliaryDataCurrencyInputComponent,
    EmptyStringComponent,
    EmptyStringComponent,
  ];
  displayService = AuxiliaryDataCurrencyInputDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.CURRENCY_TYPE.LESS_THAN_EQUAL',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.CURRENCY_TYPE.GREATER_THAN',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.DOES_NOT_EXIST',
  ];

  filterData: string;
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly currencyCode: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.filterData = currencyCode;
    this.rules = [
      new Not(new NumberGreaterThan(rulePropertyName)),
      new NumberGreaterThan(rulePropertyName),
      new NumberExists(rulePropertyName),
      new Not(new NumberExists(rulePropertyName)),
    ];
  }
}

class DynamicStringListComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [FreeTextListInputComponent, FreeTextListInputComponent];
  displayService = TagOptionsDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.INCLUDES_ANY',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.INCLUDES_ALL',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.DOES_NOT_INCLUDE_ANY',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.DOES_NOT_INCLUDE_ALL',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new StringListContains(rulePropertyName),
      new StringListContainsAll(rulePropertyName),
      new Not(new StringListContains(rulePropertyName)),
      new Not(new StringListContainsAll(rulePropertyName)),
    ];
  }
}

class DynamicCrmStringComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [
    EmptyStringComponent,
    EmptyStringComponent,
    CrmStringAutocompleteComponent,
    CrmStringAutocompleteComponent,
    CrmMultiStringAutocompleteComponent,
    CrmMultiStringAutocompleteComponent,
  ];
  displayService = DynamicStringDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.DOES_NOT_EXIST',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.CONTAINS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.DOES_NOT_CONTAIN',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.IS_ONE_OF',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.STRING_TYPE.IS_NOT_ONE_OF',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;
  fieldId: string;
  objectType: string;

  updateComponentInstanceFn = (component: DynamicEntryComponent) => {
    component.config = {
      fieldId: this.fieldId,
      crmObjectType: this.objectType,
    };
  };

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly crmFieldId: string,
    private readonly crmObjectType: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new Not(new StringEquals(rulePropertyName)),
      new StringEquals(rulePropertyName),
      new StringContainsFold(rulePropertyName),
      new Not(new StringContainsFold(rulePropertyName)),
      new IsOneOfInStringListFold(rulePropertyName),
      new Not(new IsOneOfInStringListFold(rulePropertyName)),
    ];
    this.fieldId = crmFieldId;
    this.objectType = crmObjectType;
  }
}

class DynamicCrmStringListComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [CrmMultiStringAutocompleteComponent, CrmMultiStringAutocompleteComponent];
  displayService = TagOptionsDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.INCLUDES_ANY',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.INCLUDES_ALL',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.DOES_NOT_INCLUDE_ANY',
    'AUTOMATIONS.EDITOR.FILTERS.ACCOUNT_GROUP_DATA.COMPARISON.DOES_NOT_INCLUDE_ALL',
  ];
  label: string;
  validationError: DataFilterValidationError | undefined;
  fieldId: string;
  objectType: string;

  updateComponentInstanceFn = (component: DynamicEntryComponent) => {
    component.config = {
      fieldId: this.fieldId,
      crmObjectType: this.objectType,
    };
  };

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly crmFieldId: string,
    private readonly crmObjectType: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new StringListContains(rulePropertyName),
      new StringListContainsAll(rulePropertyName),
      new Not(new StringListContains(rulePropertyName)),
      new Not(new StringListContainsAll(rulePropertyName)),
    ];
    this.fieldId = crmFieldId;
    this.objectType = crmObjectType;
  }
}

class DynamicFloatComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [FloatComponent, FloatComponent, EmptyStringComponent, EmptyStringComponent];
  displayService = FloatDisplayService;
  comparisonLabels = [
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.CURRENCY_TYPE.LESS_THAN_EQUAL',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.CURRENCY_TYPE.GREATER_THAN',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.EXISTS',
    'AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.INTEGER_TYPE.DOES_NOT_EXIST',
  ];

  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [
      new Not(new NumberGreaterThan(rulePropertyName)),
      new NumberGreaterThan(rulePropertyName),
      new NumberExists(rulePropertyName),
      new Not(new NumberExists(rulePropertyName)),
    ];
  }
}

class DynamicBooleanComponent implements DataFilter {
  rules: Rule[];
  dataEntryComponentList = [BoolOptionsComponent];
  displayService = BoolDisplayService;
  comparisonLabels = ['AUTOMATIONS.EDITOR.FILTERS.CUSTOM_DATA.COMPARISON.DROPDOWN_TYPE.IS'];
  label: string;
  validationError: DataFilterValidationError | undefined;

  constructor(
    private readonly fieldName: string,
    private readonly rulePropertyName: string,
    private readonly filterValidationError?: DataFilterValidationError,
  ) {
    this.label = fieldName;
    this.validationError = filterValidationError;
    this.rules = [new BoolEquals(rulePropertyName)];
  }
}

class DynamicDataEntryComponent implements DataEntry {
  dataEntryComponent: Type<DynamicEntryComponent>;
  label: string;
  fieldId: string;
  crmObjectType: string;

  // Need to add the component update to optionally include the field id and object type

  constructor(
    private readonly fieldName: string,
    private readonly id: string,
    private readonly component: Type<DynamicEntryComponent>,
    private readonly fieldConfig: FieldConfig = {},
  ) {
    this.label = fieldName;
    this.fieldId = id;
    this.crmObjectType = fieldConfig.crmObjectType || '';
    this.dataEntryComponent = component;
  }
}
