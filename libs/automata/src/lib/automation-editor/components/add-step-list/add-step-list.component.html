<automata-closing-side-drawer [title]="title">
  <automata-side-drawer-content>
    @if (!groupId) {
      <glxy-button-group class="toggle-buttons">
        <button
          class="toggle-button"
          [ngClass]="{ 'button-selected': thingToShow === 'actions' }"
          mat-stroked-button
          (click)="showActions()"
        >
          {{ 'AUTOMATIONS.EDITOR.ADD_STEPS_LIST.ACTIONS' | translate }}
        </button>
        <button
          class="toggle-button"
          [ngClass]="{ 'button-selected': thingToShow === 'actionSets' }"
          mat-stroked-button
          (click)="showActionSets()"
        >
          {{ 'AUTOMATIONS.EDITOR.ADD_STEPS_LIST.ACTION_SETS' | translate }}
          <glxy-badge [size]="'small'" [color]="'green-solid'" class="new-badge">New</glxy-badge>
        </button>
      </glxy-button-group>

      @if (thingToShow === 'actions') {
        <ng-container *ngTemplateOutlet="actions"></ng-container>
      } @else {
        <ng-container *ngTemplateOutlet="actionSets"></ng-container>
      }
    } @else {
      <ng-container *ngTemplateOutlet="actions"></ng-container>
    }
  </automata-side-drawer-content>
</automata-closing-side-drawer>

<!--List of Action Sets (a.k.a snippets)-->
<ng-template #actionSets>
  <div class="content">
    <automata-action-sets-list [precedingSteps]="precedingSteps" [branch]="branch"></automata-action-sets-list>
  </div>
</ng-template>

<!--List of actions -->
<ng-template #actions>
  <div class="content">
    <glxy-form-field bottomSpacing="false" suffixIcon="search" class="search-bar">
      <input matInput automataMatInputAutofocusDelayed [formControl]="searchControl" />
    </glxy-form-field>
    <glxy-form-field bottomSpacing="false">
      <mat-checkbox [(ngModel)]="showDisabledItems">
        {{ 'AUTOMATIONS.TABLE.FILTERS.SHOW_DISABLED' | translate }}
      </mat-checkbox>
    </glxy-form-field>
    <ng-container *ngIf="actions$ | async as categories; else loadingCard">
      <automata-task-category-list
        [categories]="categories"
        (taskClicked)="addStep($event)"
        [showDisabledItems]="showDisabledItems"
      ></automata-task-category-list>
    </ng-container>
    <ng-container *ngIf="buildingBlocks$ | async as categories; else loadingCard">
      <automata-task-category-list
        [categories]="categories"
        (taskClicked)="addStep($event)"
        [showDisabledItems]="showDisabledItems"
      ></automata-task-category-list>
    </ng-container>
    <ng-container *ngIf="showInternal$ | async">
      <ng-container *ngIf="internalActions$ | async as categories; else loadingCard">
        <automata-task-category-list
          [categories]="categories"
          (taskClicked)="addStep($event)"
          [showDisabledItems]="showDisabledItems"
        ></automata-task-category-list>
      </ng-container>
    </ng-container>
  </div>
  <ng-container
    *ngIf="
      ((actions$ | async)?.length === 0 && (showInternal$ | async) === false) ||
      ((showInternal$ | async) === true && (internalActions$ | async)?.length === 0 && (actions$ | async)?.length === 0)
    "
  >
    <div class="no-results">{{ 'AUTOMATIONS.EDITOR.NO_RESULTS' | translate }}</div>
  </ng-container>
</ng-template>

<ng-template #loadingCard>
  <div class="loading-card stencil-shimmer"></div>
</ng-template>
