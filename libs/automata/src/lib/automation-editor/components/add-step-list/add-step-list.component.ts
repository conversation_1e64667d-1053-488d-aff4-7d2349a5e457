import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { TaskDefinition } from '@vendasta/automata';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Category } from '../../../data-services/category';
import { TaskDefinitionsService } from '../../../data-services/task-definitions.service';
import { MatInputAutofocusDelayedDirective } from '../../../shared/auto-focus.directive';
import { AutomationsFeatureCache } from '@galaxy/automata/shared';
import {
  AutomataClosingSideDrawerComponent,
  AutomataSideDrawerContentDirective,
} from '../../side-drawer/closing-side-drawer/automata-closing-side-drawer.component';
import { TaskCategoryListComponent } from '../common/controls/task-category-list/task-category-list.component';
import { GalaxyButtonGroupModule } from '@vendasta/galaxy/button-group';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { ActionSetsListComponent } from './action-sets-list/action-sets-list.component';
import { Branch } from '../../component-loader/branch-options';

// tslint:disable: component-selector
@Component({
  selector: 'automata-add-step-list',
  templateUrl: './add-step-list.component.html',
  styleUrls: ['./add-step-list.component.scss'],
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    GalaxyFormFieldModule,
    TaskCategoryListComponent,
    MatInputModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputAutofocusDelayedDirective,
    AutomataClosingSideDrawerComponent,
    AutomataSideDrawerContentDirective,
    MatCheckboxModule,
    GalaxyButtonGroupModule,
    GalaxyBadgeModule,
    ActionSetsListComponent,
  ],
})
export class AddStepListComponent implements OnInit, OnDestroy {
  @Input() title: string;
  @Input() precedingSteps: string[];
  @Output() selectedStep: EventEmitter<any> = new EventEmitter();
  @Input() groupId: string;
  @Input() branch: Branch = 'NONE';
  searchControl: UntypedFormControl = new UntypedFormControl();
  actions$: Observable<Category[]> = this.taskDefinitionsService.actionsWithSearch$;
  buildingBlocks$: Observable<Category[]> = this.taskDefinitionsService.buildingBlocksWithSearch$;
  internalActions$: Observable<Category[]> = this.taskDefinitionsService.internalActionsWithSearch$;

  destroy$: Subject<void> = new Subject();

  /** True, if disabled items should be shown in the list, false otherwise. */
  showDisabledItems = false;
  showInternal$: Observable<boolean> = this.automationsFeatureFlags.includeInternalFeatures$;

  thingToShow: 'actions' | 'actionSets' = 'actions';

  constructor(
    private readonly automationsFeatureFlags: AutomationsFeatureCache,
    private readonly taskDefinitionsService: TaskDefinitionsService,
  ) {}

  ngOnInit(): void {
    this.taskDefinitionsService.setPrecedingStepIds(this.precedingSteps);
    this.taskDefinitionsService.setSearchTerm('');
    this.searchControl.valueChanges.pipe(takeUntil(this.destroy$)).subscribe((searchTerm) => {
      this.taskDefinitionsService.setSearchTerm(searchTerm);
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  addStep(taskDefinition: TaskDefinition): void {
    this.selectedStep.emit(taskDefinition.id);
  }

  showActions(): void {
    this.thingToShow = 'actions';
  }

  showActionSets(): void {
    this.thingToShow = 'actionSets';
  }
}
