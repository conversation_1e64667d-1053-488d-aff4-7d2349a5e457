import {
  GetContactFromCrmOpportunity,
  GetCrmOpportunityFromCompany,
  GetCrmOpportunityFromContact,
} from '../../../component-loader/constants';

export type SortOptionValue = {
  id: string;
  direction: string;
};

export type SortOption = {
  label: string;
  value: SortOptionValue;
};

type AssociatedCrmObjectConfig = {
  activityColumn: string;
  automationNode: string;
  automationPanelTip: string;
  sortOptions: SortOption[];
  associatedObjectUrl: string;
};

export const ASSOCIATED_CRM_OBJECT_CONFIG: { [key: string]: AssociatedCrmObjectConfig } = {
  [GetContactFromCrmOpportunity]: {
    activityColumn: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT_OPPORTUNITY.ACTIVITY',
    automationNode: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT_OPPORTUNITY.ACTION',
    automationPanelTip: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT_OPPORTUNITY.TIP',
    sortOptions: [
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT.CREATED_DESCENDING',
        value: { id: 'created', direction: 'desc' },
      },
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.CONTACT.CREATED_ASCENDING',
        value: { id: 'created', direction: 'asc' },
      },
    ],
    associatedObjectUrl: '/crm/contact/profile',
  },
  [GetCrmOpportunityFromContact]: {
    activityColumn: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_CONTACT.ACTIVITY',
    automationNode: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_CONTACT.ACTION',
    automationPanelTip: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_CONTACT.TIP',
    sortOptions: [
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY.CREATED_DESCENDING',
        value: { id: 'created', direction: 'desc' },
      },
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY.CREATED_ASCENDING',
        value: { id: 'created', direction: 'asc' },
      },
    ],
    associatedObjectUrl: '/crm/opportunity/details',
  },
  [GetCrmOpportunityFromCompany]: {
    activityColumn: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_COMPANY.ACTIVITY',
    automationNode: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_COMPANY.ACTION',
    automationPanelTip: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY_COMPANY.TIP',
    sortOptions: [
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY.CREATED_DESCENDING',
        value: { id: 'created', direction: 'desc' },
      },
      {
        label: 'AUTOMATIONS.EDITOR.TASKS.GET_ASSOCIATED_CRM_OBJECT.OPPORTUNITY.CREATED_ASCENDING',
        value: { id: 'created', direction: 'asc' },
      },
    ],
    associatedObjectUrl: '/crm/opportunity/details',
  },
};
