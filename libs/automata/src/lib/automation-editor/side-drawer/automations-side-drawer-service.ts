import { Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest } from 'rxjs';
import { AutomationInterface, Choices, ChoicesInterface, WorkflowStepInterface } from '@vendasta/automata';
import { SideMenuTypes } from '../automation-editor.service';
import { Branch } from '../component-loader/branch-options';
import { filter, take, tap } from 'rxjs/operators';
import { NavigationStart, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

function canOpenSidePanelWhileAutomationIsNotEditable(menuType: SideMenuTypes): boolean {
  return menuType === 'editStep' || menuType === 'editTrigger';
}

function canOpenSidePanelWhileStepChangesDisabled(menuType: SideMenuTypes): boolean {
  return !['selectNewStep', 'addStep', 'editEnd'].find((s) => s === menuType);
}

function canOpenSidePanelReadOnly(_menuType: SideMenuTypes): boolean {
  //You can't open the side while in read only mode
  return false;
}

@Injectable({ providedIn: 'root' })
export class AutomationsSideDrawerService {
  private open$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public open$ = this.open$$.asObservable();

  private detectChanges$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public detectChanges$ = this.detectChanges$$.asObservable();

  public currentSideMenuID = '';
  public currentSideMenuType: SideMenuTypes = '';
  public currentSideMenuTitle = '';
  public newStepTaskDefinitionId = '';
  public selectedTaskDefinitionId = '';
  public precedingSteps: string[] = [];
  public branch: Branch = 'NONE';
  public groupId = '';
  public automation: AutomationInterface;
  public automationIsEditable = true;
  public triggerChoices: ChoicesInterface;
  private readOnly = false;

  constructor(
    private readonly router: Router,
    private readonly translateService: TranslateService,
  ) {}

  private open(): void {
    this.open$$.next(true);
  }

  public close(): void {
    this.open$$.next(false);
  }

  public selectNewStep(taskDefinitionId: string): void {
    this.newStepTaskDefinitionId = taskDefinitionId;
    this.changeSideMenuContent(
      this.automation,
      this.automationIsEditable,
      this.readOnly,
      '',
      'addStep',
      '',
      taskDefinitionId,
      this.precedingSteps,
      this.branch,
      this.groupId,
    );
  }

  public getStep(stepId: string): WorkflowStepInterface | undefined {
    const stepMap = new Map<string, WorkflowStepInterface>();
    if (this.automation?.triggerStep) {
      stepMap.set(this.automation.triggerStep.id, this.automation.triggerStep);
    }
    if (this.automation?.triggerFilters) {
      this.automation.triggerFilters.forEach((filterStep) => stepMap.set(filterStep.id, filterStep));
    }
    if (this.automation?.workflow) {
      this.automation.workflow.forEach((workflowStep) => stepMap.set(workflowStep.id, workflowStep));
    }
    if (this.automation?.goal) {
      stepMap.set(this.automation.goal.id, this.automation.goal);
    }
    return stepMap.get(stepId);
  }

  public changeSideMenuContent(
    automation: AutomationInterface,
    automationIsEditable: boolean,
    readOnly: boolean,
    requestingId: string,
    menuType: SideMenuTypes,
    selectedTaskDefinitionId: string,
    newStepTaskDefinitionId: string,
    precedingSteps: string[] = [],
    branch: Branch = 'NONE',
    groupId = '',
  ): void {
    // We got a new automation, update the trigger choices
    this.setTriggerChoices(automation);

    // When the side panel is updated, record the preceding steps (as we might need to update links)
    // and task to be created or updated.

    // There are a few circumstances where we shouldn't open the side panel:
    // - If the automation isn't editable, we can only open the trigger and step panels
    // - If the automation isn't editable but step changes are disabled (e.g. trigger being replaced),
    //   don't let the panel be opened. (This case is important because if you add two steps that are
    //   mutually incompatible in their data then there will never be a trigger that satisfies both.
    //   More typically, some steps restrict trigger options substantially and we don't want to accidentally
    //   get the user into a hard state to escape from.)
    if (!automationIsEditable && !canOpenSidePanelWhileAutomationIsNotEditable(menuType)) {
      return;
    }
    const stepChangesDisabled = readOnly || !automationIsEditable || !automation?.triggerStep;
    if (stepChangesDisabled && !canOpenSidePanelWhileStepChangesDisabled(menuType)) {
      return;
    }
    if (readOnly && !canOpenSidePanelReadOnly(menuType)) {
      return;
    }

    this.automation = automation;
    this.automationIsEditable = automationIsEditable;
    this.newStepTaskDefinitionId = newStepTaskDefinitionId;
    this.selectedTaskDefinitionId = selectedTaskDefinitionId;
    this.precedingSteps = precedingSteps;
    this.branch = branch;
    this.groupId = groupId;
    this.readOnly = readOnly;

    const sideMenuOpen$ = this.open$;
    sideMenuOpen$
      .pipe(
        take(1),
        // Open the side panel if necessary.
        tap((sideMenu) => {
          let timeout = 0;
          if (sideMenu) {
            // If this item is already open, then do nothing
            if (!!this.currentSideMenuID && this.currentSideMenuID === requestingId) {
              return;
            }
            // allow time for closing/opening animation
            timeout = 200;
          }
          this.currentSideMenuType = '';
          this.close();
          window.setTimeout(() => {
            this.updateSideMenu(requestingId, menuType);
            this.open();
            this.detectChanges$$.next(true);

            // Close the menu if we navigate away, otherwise clean up the subscription when it's closed
            combineLatest([this.open$, this.router.events])
              .pipe(
                filter(([open, event]) => !open || event instanceof NavigationStart),
                take(1),
              )
              .subscribe(([open]) => {
                if (open) {
                  this.close();
                }
              });
          }, timeout);
        }),
      )
      .subscribe();

    sideMenuOpen$.pipe(filter((open) => !open)).subscribe(() => {
      this.currentSideMenuID = '';
      this.detectChanges$$.next(true);
    });
  }

  updateSideMenu(requestingId: string, menuType: SideMenuTypes): void {
    this.currentSideMenuID = requestingId;
    this.currentSideMenuType = menuType;

    // Update SideMenu Title
    switch (menuType) {
      case 'selectNewTrigger':
      case 'addTrigger': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.EDITOR.TRIGGERS.COMMON.ADD');
        break;
      }
      case 'editTrigger': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.EDITOR.TRIGGERS.COMMON.EDIT');
        break;
      }
      case 'selectNewStep':
      case 'addStep': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.EDITOR.STEP.ADD');
        break;
      }
      case 'editStep': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.EDITOR.STEP.EDIT');
        break;
      }
      case 'addGoal': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.COMMON.ADD_GOAL');
        break;
      }
      case 'editGoal': {
        this.currentSideMenuTitle = this.translateService.instant('AUTOMATIONS.COMMON.EDIT_GOAL');
        break;
      }
      default: {
        this.currentSideMenuTitle = '';
        break;
      }
    }
  }

  setTriggerChoices(a: AutomationInterface) {
    const c = a?.triggerStep?.choices;
    let triggerChoices = c;
    if (this.currentSideMenuType === 'addTrigger') {
      triggerChoices = new Choices({
        rules: [],
      });
    } else if (!c) {
      triggerChoices = new Choices({
        rules: [],
      });
    }
    this.triggerChoices = triggerChoices;
  }
}
