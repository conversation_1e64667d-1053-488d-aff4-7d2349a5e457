<mat-drawer
  mode="over"
  position="end"
  autoFocus="false"
  #sideMenuDrawer
  style="position: absolute; top: 40px; z-index: 1000; overflow: visible; height: 100%"
>
  <ng-container *ngTemplateOutlet="sideDrawerWrapper"></ng-container>
</mat-drawer>

<ng-template #sideDrawerWrapper>
  @if (!dontOpen()) {
    @if (sideMenuService.automation) {
      @if (
        sideMenuService.currentSideMenuType === 'addTrigger' ||
        sideMenuService.currentSideMenuType === 'editTrigger' ||
        sideMenuService.currentSideMenuType === 'selectNewTrigger'
      ) {
        <automata-trigger-panel
          [title]="sideMenuService.currentSideMenuTitle"
          [automation]="sideMenuService.automation"
          [taskDefinitionId]="sideMenuService.selectedTaskDefinitionId"
          [triggerChoices]="sideMenuService.triggerChoices"
          [preventClose]="!sideMenuService.automation.triggerStep"
        ></automata-trigger-panel>
      }
      @if (sideMenuService.currentSideMenuType === 'selectNewStep') {
        <automata-add-step-list
          [title]="sideMenuService.currentSideMenuTitle"
          [precedingSteps]="sideMenuService.precedingSteps"
          (selectedStep)="sideMenuService.selectNewStep($event)"
          [groupId]="sideMenuService.groupId"
          [branch]="sideMenuService.branch"
        ></automata-add-step-list>
      }
      @if (sideMenuService.currentSideMenuType === 'addStep' || sideMenuService.currentSideMenuType === 'addGoal') {
        <automata-dynamic-action-panel-loader
          [taskDefinitionId]="sideMenuService.newStepTaskDefinitionId"
          [title]="sideMenuService.currentSideMenuTitle"
          [automation]="sideMenuService.automation"
          [precedingSteps]="sideMenuService.precedingSteps"
          [branch]="sideMenuService.branch"
          [groupId]="sideMenuService.groupId"
          [isGoal]="sideMenuService.currentSideMenuType === 'addGoal'"
        ></automata-dynamic-action-panel-loader>
      }

      @if (sideMenuService.currentSideMenuType === 'editStep' || sideMenuService.currentSideMenuType === 'editGoal') {
        <automata-dynamic-action-panel-loader
          [taskDefinitionId]="sideMenuService.getStep(sideMenuService.currentSideMenuID)?.taskDefinitionId"
          [title]="sideMenuService.currentSideMenuTitle"
          [step]="sideMenuService.getStep(sideMenuService.currentSideMenuID)"
          [automation]="sideMenuService.automation"
          [precedingSteps]="sideMenuService.precedingSteps"
          [branch]="sideMenuService.branch"
          [isGoal]="sideMenuService.currentSideMenuType === 'editGoal'"
        ></automata-dynamic-action-panel-loader>
      }
    }
  }
</ng-template>
