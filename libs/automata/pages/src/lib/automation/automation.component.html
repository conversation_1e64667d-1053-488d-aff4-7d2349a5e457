<mat-drawer-container class="side-drawer-container" hasBackdrop="false">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button
        *ngIf="previousPageInfo$ | async as previousPageInfo"
        [previousPageTitle]="previousPageInfo.titleKey | translate"
        [previousPageUrl]="previousPageInfo.url"
      ></glxy-page-nav-button>
    </glxy-page-nav>

    <glxy-page-title>
      {{ 'AUTOMATIONS.COMMON.WORKFLOW' | translate }}
    </glxy-page-title>

    <glxy-page-actions>
      <ng-container *ngIf="pageData$ | async as pageData">
        <ng-container *ngIf="!pageData.loadError && !pageData.mismatchedNamespaces">
          <ng-container *ngTemplateOutlet="pageActions; context: { pageData: pageData }"></ng-container>
        </ng-container>
      </ng-container>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <ng-container *ngIf="pageData$ | async as pageData; else pageLoading">
    <ng-container *ngIf="!pageData.loadError && !pageData.mismatchedNamespaces; else notFound">
      <div class="content-wrapper">
        <mat-drawer-container>
          <mat-drawer mode="side" position="start" role="region" [opened]="leftPanelOpen" class="hide-when-mobile">
            <ng-container *ngTemplateOutlet="leftPanelTemplate; context: { pageData: pageData }"></ng-container>
          </mat-drawer>
          <div class="automation-page-content">
            <ng-container *ngTemplateOutlet="pageContent; context: { pageData: pageData }"></ng-container>
          </div>
        </mat-drawer-container>
      </div>
    </ng-container>
  </ng-container>
</mat-drawer-container>

<ng-template let-pageData="pageData" #pageActions>
  <!-- TODO: Copy workflow for built-in (default) automations -->
  <div class="toggle" *ngIf="{ value: (changingState$ | async) } as changingState">
    <div *ngIf="!changingState.value">
      <button
        mat-flat-button
        color="warn"
        class="stop-draining-button"
        *ngIf="isAutomationDraining(pageData.automation)"
        (click)="turnOffAutomation()"
      >
        {{ 'AUTOMATIONS.COMMON.STOP_DRAINING' | translate }}
      </button>
      <mat-slide-toggle
        class="start-toggle"
        [checked]="pageData.slideToggleOn"
        [disabled]="(disableToggleRunning$ | async) !== ''"
        (change)="toggleRunning($event.checked)"
        [glxyTooltip]="slideToggleTooltipMessage$ | async"
        [highContrast]="false"
        [tooltipTitle]="slideToggleTooltipTitle$ | async"
        [tooltipPositions]="TOOLTIP_BOTTOMRIGHT"
      >
        <span *ngIf="pageData.slideToggleOn">
          <ng-container *ngIf="showPublishedDraftToggle$ | async; else onToggle">
            {{ 'AUTOMATIONS.TEMPLATES_TABLE.FILTERS.PUBLISHED' | translate }}
          </ng-container>
          <ng-template #onToggle>
            {{ 'AUTOMATIONS.COMMON.ON' | translate }}
          </ng-template>
        </span>
        <span *ngIf="!pageData.slideToggleOn">
          <ng-container *ngIf="showPublishedDraftToggle$ | async; else offToggle">
            {{ 'AUTOMATIONS.TEMPLATES_TABLE.FILTERS.DRAFT' | translate }}
          </ng-container>
          <ng-template #offToggle>
            {{ 'AUTOMATIONS.COMMON.OFF' | translate }}
          </ng-template>
        </span>
      </mat-slide-toggle>
    </div>
    <div *ngIf="changingState.value">
      <mat-spinner [diameter]="20" [strokeWidth]="2" [color]="'primary'"></mat-spinner>
    </div>
  </div>
  <div class="global-actions">
    <button mat-icon-button *ngIf="!isLoading" [matMenuTriggerFor]="globalActionsMenu">
      <mat-icon>more_vert</mat-icon>
    </button>
    <div class="global-actions-spinner" *ngIf="isLoading">
      <mat-spinner [diameter]="20" [strokeWidth]="2" [color]="'primary'"></mat-spinner>
    </div>
    <mat-menu #globalActionsMenu="matMenu">
      <button
        mat-menu-item
        *ngIf="pageData.isPartnerViewingBuiltInAutomation"
        (click)="createFromBuiltInAutomationClicked(pageData.automation)"
      >
        {{ 'AUTOMATIONS.COMMON.DUPLICATE' | translate }}
      </button>
      <button
        mat-menu-item
        *ngIf="pageData.isPartnerViewingBuiltInAutomation === false && (isAdminView() | async) === false"
        (click)="copyAutomation(pageData.automation)"
      >
        {{ 'AUTOMATIONS.COMMON.DUPLICATE' | translate }}
      </button>
      <button
        mat-menu-item
        (click)="editDetails(pageData.automation, pageData.isPartnerViewingBuiltInAutomation)"
        class="show-when-mobile"
      >
        {{ 'AUTOMATIONS.COMMON.DETAILS' | translate }}
      </button>
      <button
        mat-menu-item
        *ngIf="pageData.isPartnerViewingBuiltInAutomation === false"
        [disabled]="pageData.slideToggleOn"
        (click)="deleteAutomation(pageData.automation)"
      >
        {{ 'AUTOMATIONS.COMMON.DELETE' | translate }}
      </button>
    </mat-menu>
  </div>
</ng-template>

<ng-template let-pageData="pageData" #leftPanelTemplate>
  <div class="side-panel-content">
    <div>
      <div class="name-container">
        <div *ngIf="pageData.automation as automation">
          <div class="title-bar">
            <span class="automation-name">
              {{ automation.name }}
            </span>
          </div>
        </div>
        <div *ngIf="pageData.automation as automation" class="automation-description">
          <div class="subtitle-bar">
            <div class="description-container respect-newlines" *ngIf="!!automation.description">
              <span>{{ automation.description }}</span>
            </div>
          </div>
        </div>
        <div *ngIf="pageData.automation?.tags?.length > 0" class="automation-tags">
          <div class="tag-container">
            <glxy-badge *ngFor="let tag of pageData.automation.tags" class="tag-badge" aria-label="tags">
              {{ tag }}
            </glxy-badge>
          </div>
        </div>
      </div>
      <mat-divider></mat-divider>
      <div class="data-container" *ngIf="pageData.automation as automation">
        <div class="crm-read-only-container">
          <div class="crm-field-label">Last updated</div>
          <div class="crm-field-value">
            <ng-container *ngIf="automation.updated">
              <span [matTooltip]="momentFormatDate(automation.updated, 'ddd MMM D YYYY h:mm:ss a')">
                {{ fromNow(automation.updated) }}
              </span>
            </ng-container>
          </div>
          <div class="crm-field-label">Last updated by</div>
          <div class="crm-field-value">
            <ng-container *ngIf="lastEditedBy$ | async as lastEditedBy; else yourExpert">
              <ng-container *ngIf="lastEditedBy">
                {{ lastEditedBy }}
              </ng-container>
            </ng-container>
            <ng-template #yourExpert>
              <glxy-badge [color]="'green'" [size]="'small'">
                {{ 'AUTOMATIONS.COMMON.YOUR_EXPERT' | translate }}
              </glxy-badge>
            </ng-template>
          </div>
          @if (automation.goal) {
            <div class="crm-field-label">{{ 'AUTOMATIONS.COMMON.GOAL' | translate }}</div>
            <div class="crm-field-value">
              @if (pageData.isPartnerViewingBuiltInAutomation) {
                <span>{{ automation.goal.name }}</span>
              } @else {
                <a (click)="editGoal(pageData.isPartnerViewingBuiltInAutomation)" data-action="view-goal-link">{{
                  automation.goal.name
                }}</a>
              }
            </div>
          }

          <div class="crm-field-label">Notes</div>
          <div class="crm-field-value respect-newlines">
            <ng-container *ngIf="automation.notes">
              {{ automation.notes }}
            </ng-container>
          </div>
        </div>
      </div>
    </div>
    <div class="actions">
      <mat-divider></mat-divider>
      <div class="button-container">
        <button
          mat-button
          mat-stroked-button
          (click)="editInformation(pageData.isPartnerViewingBuiltInAutomation)"
          [disabled]="pageData.isPartnerViewingBuiltInAutomation"
        >
          {{ 'AUTOMATIONS.COMMON.EDIT_DETAILS' | translate }}
        </button>
        <!-- don't allow adding/editing a goal if the automation doesn't have a trigger -->
        @if (pageData?.automation?.triggerDefinitionId) {
          @if (pageData?.automation?.goal) {
            <button
              class="goal-button"
              mat-button
              mat-stroked-button
              [attr.data-action]="pageData.slideToggleOn ? 'view-goal-button' : 'edit-goal'"
              (click)="editGoal(pageData.isPartnerViewingBuiltInAutomation)"
              [disabled]="pageData.isPartnerViewingBuiltInAutomation"
            >
              @if (pageData.slideToggleOn) {
                {{ 'AUTOMATIONS.COMMON.VIEW_GOAL' | translate }}
              } @else {
                {{ 'AUTOMATIONS.COMMON.EDIT_GOAL' | translate }}
              }
            </button>
          } @else {
            <div [glxyTooltip]="pageData.slideToggleOn ? 'AUTOMATIONS.EDITOR.CANNOT_ADD_GOAL_WHEN_RUNNING' : ''">
              <button
                class="goal-button"
                mat-button
                mat-stroked-button
                data-action="add-goal"
                (click)="editGoal(pageData.isPartnerViewingBuiltInAutomation)"
                [disabled]="pageData.isPartnerViewingBuiltInAutomation || pageData.slideToggleOn"
              >
                {{ 'AUTOMATIONS.COMMON.ADD_GOAL' | translate }}
              </button>
            </div>
          }
        }
      </div>
    </div>
  </div>
</ng-template>
<ng-template let-pageData="pageData" #pageContent>
  <div class="main-panel-content">
    <div class="panel-closer hide-when-mobile" (click)="toggleLeftPanel()">
      <div class="arrow-up" [ngClass]="{ closed: !leftPanelOpen }"></div>
    </div>
    <nav mat-tab-nav-bar mat-stretch-tabs="false" *ngIf="pageData.automation" [tabPanel]="tabPanel">
      <a
        *ngFor="let tab of tabs$ | async; let index = index"
        [active]="rla.isActive"
        [routerLink]="tab.path"
        class="tab-link"
        mat-tab-link
        routerLinkActive
        #rla="routerLinkActive"
      >
        {{ tab.label | translate }}
      </a>
    </nav>
    <mat-tab-nav-panel #tabPanel>
      <div class="automation-node-graph">
        <automata-automations-side-menu-drawer [dontOpen]="nodeActionsService.grouping()" #sideMenu>
        </automata-automations-side-menu-drawer>
        <router-outlet></router-outlet>
      </div>
    </mat-tab-nav-panel>
  </div>
</ng-template>

<ng-template #notFound>
  <automata-empty-state></automata-empty-state>
</ng-template>

<ng-template #pageLoading>
  <glxy-loading-spinner></glxy-loading-spinner>
</ng-template>
