# va-goal-table

```ts
import { VaGoalTableModule } from '@vendasta/uikit';
```

This provides a table component that displays Google Analytics goals used in exec reports and website pro. 

## Implementation

Implementation is a multi-step process:
1. Add va-goal-table element to template:

        <va-goal-table></va-goal-table>

2. It's required to take a list of data source as input, where `dataSource` is of type `GaGoalData`

        <va-goal-table [dataSource]='dataSource'></va-goal-table>

3. Add table config to define titles, subtitles, column names and heading column names, where `tableConfig` is of type `GoalTableConfig`:

        <va-goal-table [dataSource]='dataSource' [tableConfig]="tableConfig"></va-goal-table>

4. Add location configuration as an optional input, this is usually specified when it's displayed in multi-location exec report, where showing the location count footer:
   
        <va-goal-table [dataSource]='dataSource' [tableConfig]="tableConfig"></va-goal-table>

