# Changelog for @vendasta/galaxy

## [1.39.0]
- Add `heightOffset` input to the `ThreePanelComponent`

## [1.38.0]
- Add `openLinksOnNewTabs` option to `email-viewer` component

## [1.37.0]
- Add `borderColor` as an optional input to `glxy-avatar`

## [1.36.0]
- Added onchange to tags component

## [1.35.0]
- Galaxy table column organizers should only take into consideration a translation key if the resolved translation is a string. Default to original title if it can't find a string.

## [1.34.0]
- Use HTML5 date picker for exact date match filter
  - Normalize date for exact date filter to use UTC dates at all times
- Display local time and UTC time in date model driven cell

## [1.33.0]
- Add `useGrayScale` to `glxy-avatar` to allow users to toggle grayscale on/off on the avatar giving the "disabled" effect

## [1.30.5]
- Add `standalone: false` to all standalone components to prepare for Angular 19 update

## [1.30.4]
- Freeing memory wasn't enough, explicitly destroy model driven cell components

## [1.30.3]
- Explicitly free memory for model driven cells in galaxy table

## [1.30.2]
- Fix numbers for integer/float cell in galaxy table

## [1.30.1]
- Add `hideRequiredMarker` as an optional input `glxy-form-field`

## [1.30.0]
- Add `glxyError` as projectable content to `glxy-currency-field`

## [1.29.0]
- Set selection limit for galaxy tables
  - Default to 500
  - Can be customized by providing a `GalaxyTableSelectionCustomizationToken` token

## [1.28.2]
- Only enable theming in `RichTextEditorComponent` when input `enableThemes` is explicitly passed in as true

## [1.28.1]
- Scope `hostListener` in `ChatMessageComponent` to element instead of document to improve performance

## [1.28.0]
- Add dark mode support for `RichTextEditorComponent`
  - Make statusbar hidden as default
- Expose active theme as Observable in `StyleService`

## [1.27.7]
- Remove `transition` input from `mat-mdc-table-sticky-border-elem-right` to prevent a column line divider to show during the load of the table.

## [1.27.6]
- Fix an issue in `ChatComposerComponent` when `chatSources` emits after `ngOnInit`

## [1.27.5]
- Remove `showDisplayOption` and `selectedDisplayOption` input from `GalaxyContentHeaderComponent`
- Add `displayOption` to `GalaxyTableContainerComponent`

## [1.27.4]
- Add `disableSelection` input to `GalaxySelectionComponent`

## [1.27.3]
- Handle empty `sourceName` for `ChatComposerComponent`

## [1.27.2]
- Add `action` option to `CellData` for cell type text

## [1.27.1]
- Fix advanced column selector, properly display columns without groups within 'Other' group

## [1.27.0]
- Add color theme support to Galaxy resize

## [1.26.0]
- Disable default filter inputs and operator dropdown selector when `lockedValue` flag for `GalaxyFilterDefinitionInterface` is set to true

## [1.25.0]
- Add `dateRangeForCustomDefault` input to `glxy-date-range-presets` component

## [1.24.0]
- Add `lockedValue` flag to `GalaxyFilterDefinitionInterface` so that some filters cannot have their values cleared up after they are added in

## [1.23.0]
- Add new AI icons to the `GalaxyAiIconService`
- Fix svg display in Galaxy Avatar

## [1.22.0]
- Make the `DynamicOpenCloseTemplateRefService` a singleton service, remove from `SideDrawer` module providers

## [1.21.0]
- Handle float filters in `GalaxyTable`

## [1.20.1]
- Add secondary content in `StickyFooterComponent`

## [1.20.0]
- Add advanced column organizer in `GalaxyTable`

## [1.19.0]
- Handle MIME type and extension in `GalaxyUploaderComponent`

## [1.19.0]
- Add `maxlength` input to `ChatComposerComponent`

## [1.18.1]
- Fix issue where `GalaxyImageUploaderComponent` does not use the default hintText for `GalaxyUploaderComponent` when one is not provided
- Fix issue where `GalaxyUploaderConstraintsTextComponent` pluralizes "images" when it should be singular

## [1.18.0]
- Fix partner could not upload the logo using `GalaxyUploaderComponent`

## [1.17.2]
- Add `deleteCount` optional parameter to `Page Service` `pop()` function.

## [1.17.1]
- Fix issue where the `glxyTooltip` directive was not cleaning up it's overlay ref when destroyed

## [1.17.0]
- Fix throw error for unsupported file when uploading in `GalaxyUploaderComponent`

## [1.16.1]
- Fix prefix and suffix icons in `FormFieldComponent` from clipping when parent container shrinks

## [1.16.0]
- Add `glxy-page-not-found-404` and `glxy-page-no-access-unauthorized` page components
- Add `glxy-additional-metadata` directive for `glxy-chat-message`

## [1.15.2]
- GalaxyFilterChips: Fixed a display issue that would allow the contents to be cutoff from the viewport. TopLeft and TopRight are now valid popover positions for when there is not enough space

## [1.15.1]
- Popover: change padding of internal elements to be inside the overflow scrollbar

## [1.15.0]
- Add `mobileFullScreen` and related options to Popover component

## [1.14.0]
- Add `linkedInUrl` and `jobTitle` to `CardDataType`

## [1.13.0]
- Add `glxy-sticky-footer` component

## [1.12.2]
- Fix bug in `ColumnSortSimpleComponent` where when dragging any column on the column sort it would enable pinned(disabled) columns, alowing people to hide columns that should be always visible (e.g. actions) 

## [1.12.1]
- Fix support wild cards for accepted file types for drag and drop in `GalaxyUploaderCompoenent`

## [1.12.0]
- Fix accepted file types for drag and drop in `GalaxyUploaderCompoenent`

## [1.11.0]
- Add 'preSelectedRows' input to `GalaxySelectionComponent` to allow for pre-selected rows

## [1.10.1]

- Align single source text to end in `ChatComposerComponent`
- Add space after action item in `ChatComposerComponent`

## [1.10.0]
- Fix the error when no value is passed into `previousPageUrl` in `PageNavButtonComponent`

## [1.9.0]
- Allow `'account'` type for `CardDataType` in `ContactInfoCardComponent`

## [1.8.0]
- Add `sortable` to `GalaxyColumnDef`
- Add support for single-column sorting for the glxy-table

## [1.7.0]
- Add `'platform'` and `'openai'` to `ChatSourceId` type of `ChatComposerComponent`
- Rename the following properties in `ChatComposerComponent`:
  - `selectedSource` -> `selectedChatSourceId`
  - `selectedSourceChange` -> `selectedChatSourceIdChange`
  - `currentChatSource` -> `currentChatSourceId`
  - `currentChatObject` -> `currentChatSource`
- modify `ChatComposerComponent` to display chatSourceAccount alongside chatSourceName when only a single chatSource is available

## [1.6.0]
- Update `ConfirmationModalConfig` to take in `hideCancel` boolean that will hide the secondary action button

## [1.5.2]
- Rename glxy-chat-bubble-multi-item to glxy-chat-message
- Rename glxy-chat-bubble-multi to glxy-chat-message-group

## [1.5.1]
- Fix `GalaxyFilterChips` not displaying the popover at the correct position for the chip

## [1.5.0]
- Add possibility of having a `GalaxyAbstractFilterChip` for `GalaxyFilterInputOverride`
  - Allow using a component to change how filter chips are displayed after a filter is saved

## [1.4.0]
- Add `GalaxyFilterInputOverride` for `GalaxyFilterChips`
  - Allow passing in custom components to override the default filter input behavior if necessary

## [1.3.2]
- Fix icons not being displayed for `GalaxyFilterChips` when operators are `FILTER_OPERATOR_IS_NOT_EMPTY` or `FILTER_OPERATOR_IS_EMPTY`

## [1.3.1]
- `Galaxy Table` persists searching, sorting, and filtering settings when the page size changes

## [1.3.0]
- Introduce column subscription in `columnSub`
- Add a subscription to track changes in columns.
- Emits `columnsChanged` event when columns are updated.

## [1.2.0]
- Add default filter, preset filter, and checkbox select support to `GalaxyFilterChips`.
- Add `showAddFilter` to toggle showing the add filter button. Setting this to false is useful for preset-only scenarios.
- Fix the `tags` filter input in `GalaxyFilterChips` and prevent it from throwing a signal error.
- Fix the `GalaxyFilterChips` component preventing the edit dialog from opening for an existing filter.

## [1.1.0]
- Refactor Galaxy Table custom filter button logic

## [1.0.0]
- Add `GalaxyFilterChips` along with an example of using it with `GalaxyTable`.

## [0.136.0]
- Export `BadgeColor` from `BadgeComponent`

## [0.135.0]
- HTML-escape `messageFrom` and `messageMetadata` in `ChatBubbleMultiComponent`
- HTML-escape `fullBusinessAddress` in `ContactInfoCardComponent`

## [0.134.0]
- Update copy icon in `ContactInfoCardComponent`

## [0.133.0]
- Add loading spinner to the `EmailViewerComponent` when the HTML input is falsy

## [0.132.0]
- Add `ordinal` function to ordinal-utils
- Add `GalaxyOrdinalPipe`

## [0.131.1]
- `CustomCell` should be able to take any type of value
- Adjust `CustomCellComponent` definition
- Adjust styling for existing wrappers, so their values do not wrap

## [0.131.0]
- Add `CustomCell` to `ModelDrivenCellComponent` to allow for custom views for column cells

## [0.130.0]
- Add `allowCustomStyling` boolean input to `GalaxyFrequencyComponent` which defaults to false

## [0.129.0]
- Add `showFooter` boolean input to glxy-table-container which defaults to true

## [0.128.0]
- Modify `processing` from an observable to a boolean value

## [0.127.0]
- Add pre-selected row for `glxy-table-selection`

## [0.126.0]
- Add `disableSendEmptyText` to `ChatComposerComponent` to disable sending empty text messages

## [0.125.0]
- Add Export button to glxy-table-content-header

## [0.124.0]
- Add 'full' widthPreset option to glxy-page-wrapper

## [0.123.0]
- Disable the context menu within the rich text editor (will now use browser native menu)

## [0.122.0]
- Danitize content before displaying in `glxy-chat-bubble-multi-item`

## [0.121.0]
- Add design tokens for font sizing for small/medium scenarios in mat-select styling
- Mat-select styling for height and font-size inconsistent on mobile compared to text inputs

## [0.120.0]
- Update `glxy-timezone-selector` to use `glxy-form-field`
## [0.119.0]
- Add phone rendering support for `glxy-table-model-driven-cell`

## [0.118.0]
- Add link support for `glxy-table-model-driven-cell`

## [0.117.1]
- Improvements to the animations of the `glxy-chat-bubble-multi-item` component.

## [0.117.0]
- Change `glxy-chat-bubble-multi-item`'s metadata to slide down from that chat bubble instead of using a tooltip.

## [0.116.0]
- Add data-actions to table buttons

## [0.115.0]
- Allow popups opened from `glxy-email-viewer` to use scripts

## [0.114.0]
- Add `glxy-tags` component

## [0.113.0]
- Add `chatId` input to `glxy-chat-container`
- Update `ChatContainerComponent` to scroll to bottom when switching chat views
- Add support to `ChatContainerComponent` to save location per chat view

## [0.112.3]
- Fix `glxy-avatar` to handle empty spaces on names.

## [0.112.2]
- Handle empty or undefined `currentChatObject.sourceName` in `ChatComposerComponent`

## [0.112.1]
- Fix `table-content-header` component by re-adding missing `ng-content` line for `[after-search]`

## [0.112.0]
- Adds a loading spinner to the `TableContainerComponent`

## [0.111.1]
- Fix `glxy-uploader` button edge case when file has the same name and is removed and then re-uploaded.
- Fix `glxy-form-field` extra space above form field when no label

## [0.111.0]
- Change dateRangeDefaultPeriod$ to observable

## [0.110.0]
- Table: Add explicit types to model-driven columns.

## [0.109.0]
- Add `totalResults` to `pagingMetadata` in table datasource

## [0.108.0]
- Add `pageSize` Input to the `glxy-table-content-footer` and `glxy-table-container` components, to allow overrides of the default page size

## [0.107.1]
- glxy-page-nav-button - Fix bug where previous page navigation containing query params breaks routing

## [0.107.0]
- Display/handle tags in `glxy-table-container`, for model-driven columns.

## [0.106.1]
- Fix mat-button font styles

## [0.106.0]
- Add `disabled` to `ChatComposerComponent` to disable composer and send button

## [0.105.1]
- Update: `getStartOf` with optional `options` arg.

## [0.105.0]
- glxy-popover - Add options to properly close on background clice and escape key
- glxy-popover - Add methods to open, close, and toggle the popover on the component itself
- glxy-popover - Fix bug where escape key can close popover so it never reopens
- glxy-page-nav-button - Fix bug where navigateByUrl clears secondary router outlets

## [0.104.0]
- Display/handle booleans in `glxy-table-container`, for model-driven columns.

## [0.103.0]
- Update `glxy-page-nav-button` to get previous page in navigation handler instead of in ngOnInit

## [0.102.0]
- Update `PageService` to track the first url the app loads into

## [0.101.0]
- Feat: `glxy-table-container` will add columns dynamically from column definitions,
  if they're not already defined in your HTML template. (Model-driven columns.)

## [0.100.0]
- Feat: new tooltip directive parameter: glxyTooltipDisabled

## [0.99.1]
- Change the Galaxy Table to allow adding components after the search bar

## [0.99.0]
- Feat: Add `getStartOfWeek` and `getStartOf` to date-utils

## [0.98.3]
- Adjust mobile styles in `glxy-in-page-nav` to reflow with `glxy-nav.nav-is-mobile`

## [0.98.2]
- Add z-index to `glxy-in-page-nav` overlay so it is clickable over content

## [0.98.1]
- Adding theme support to `glxy-avatar` and `glxy-avatar-group`

## [0.98.0]
- Update `glxy-image-editor` to keep crop image type same as original image
- Add `maxFileSize` to `glxy-image-uploader` to activate image validation check

## [0.97.1]
- Force datepicker to use GalaxyDateAdapter and formats

## [0.97.0]
- update `glxy-chat-composer` with optional `processing` input

## [0.96.2]
- Fix comparison date type

## [0.96.1]
- Change datepicker and date-range-presets to have consistent date results in executive report and BCC dashboard

## [0.96.0]
- remove `glxy-rich-text-editor`'s `height` and `width` inputs

## [0.95.0]
- update `glxy-rich-text-editor`'s inner editor component to inherit the `height` from `glxy-rich-text-editor`

## [0.94.0]
- Add filter button options to table components

## [0.93.0]
- Add `glxy-rich-text-editor` a WYSIWYG rich text editor component

## [0.92.1]

- Update to stricter array comparison standards in table datasource

## [0.92.0]
- Feat: Add `glxy-currency-field` component (replaces `glxy-currency-input`)

## [0.91.1]
- change getMonthRange to return both start and end in local time

## [0.91.0]

- Add In-page Nav (beta) and separate Nav Layout, Items, and List components to create sidenavs outside of a Galaxy Nav.

## [0.90.1]

- reverted changes on the operation order of `normalizeDateFunc` to do convertion to UTC first and then func

## [0.90.0]

- Feat: Chip lists in Galaxy form fields support sizing

## [0.89.2]

- change the operation order of `normalizeDateFunc` to do func first then convert to UTC

## [0.89.1]

- update raw form value for `glxy-phone-input` on blur to avoid mismatching country code while users are typing in
  number

## [0.89.0]

- add new `openWithOptions` method to `SnackBarService` and deprecate `openSnackBar`
- refactor `SnackBarService` non-functional tests to be functional

## [0.88.0]

- Feat: The `glxy-comment` can now have a `subtitleText` passed in.

## [0.87.1]

- The 6 month and 12 month presets were not working correctly

## [0.87.0]

- Ensure that the date selection component assertions match the functionality of the component
- Remove moment from the date range preset component
- Emit date ranges from date selection and date range present component within inclusive date / time selection

## [0.86.0]

- Add option to Galaxy Page toolbar to show a global nav toggle (for Products)

## [0.85.3]

- Fix bug where `glxy-alert` action click would submit form if nested in a form element.

## [0.85.2]

- Fix bug where multiple uploader components would receive each others file
  upload updates.
- NOTE: If using the `GalaxyUploaderService` or the `GalaxyImageUploaderService`,
  please update your calls to `addFile` to include your current context (usually
  component reference) to ensure your upload events don't escape your context.

## [0.85.1]

- Adds const `NAV_OPEN_CLOSE_ANIMATION_DURATION` to the nav component to allow for consistent reference of delays, etc.

## [0.85.0]

- [BREAKING CHANGE] GalaxyUploaderService no longer uses BehaviorSubjects for
  its `fileUploaded$$` observable/subject. It now uses Subject so that new
  subscribers will not receive replays of old uploads.
- [DEPRECATION] Added `fileUploaded$` (Observable) to replace `fileUploaded$$`
  because there is no reason to expose a writable interface for this value.

## [0.84.0]

- Add optional "params" so query parameters can be tacked on to the image
  uploader URL.

## [0.83.0]

- Feat: add glxy-button-loading-indicator component to show a loading state in material buttons.

## [0.82.0]

- Feat: add glxy-statistic component to display statistic numbers.

## [0.81.2]

- Refactor: change glxy-nav-item-list component to use Angular animations

## [0.81.1]

- Refactor: change glxy-field colors to themeable tokens

## [0.81.0]

- Feat: add glxy-avatar and glxy-avatar-group
- Feat: add glxy-chat, glxy-chat-container, and glxy-chat-divider-text

## [0.80.2]

- Fix: Make the date picker shows the correct date range when select monthly.

## [0.80.1]

- Fix: Make va-nav submenu listen for height changes.
  Ensure navigation items do not bleed out of submenu.

## [0.80.0]

- Fix: Eliminate dependency on @locl/core

## [0.79.0]

- Feat: new tooltip parameter: positions

## [0.78.2]

- Chore: remove unused, non-source, translation files
- Fix: (I forgot to increment version) hide tooltips with empty text

## [0.78.1]

- Fix: Add missing appearance to datepicker and timezone selector

## [0.78.0]

- Feat: Add en_devel.json

## [0.77.1]

- Chore: change `glxy-badge` to use a `size` input for consistency

## [0.77.0]

- Feat: adding early access `glxy-form-field` component, a replacement for `mat-form-field`

## [0.76.0]

- Feat: Add Badge component

## [0.75.1]

- Fix: Add missing `GALAXY.DATE_RANGE.ERROR_MESSAGE` translation key to base translation file
- Fix: The end date is now inclusive in the date range selection of `glxy-datepicker`

## [0.75.0]

- Feat: `glxy-datepicker` now supports null values in the range selector, allowing for a single bound selection and for
  the range to be cleared

## [0.74.0]

- Feat: add styling overrides and dispaly options to `mat-button-toggle-group`

## [0.73.5]

- Fix: `page-nav-button`'s page service no longer adds skipped location changes to navigation history

## [0.73.4]

- Fix: `glxy-page-toolbar` now works properly if you have no title and actions
- Feat: `glxy-nav-item` now handles external urls, in addition to routerLinks

## [0.73.3]

- Feat: Add width presets for `glxy-page-wrapper`

## [0.73.2]

- Feat: Support current platform nav themes in Galaxy Nav

## [0.73.1]

- Chore: Update glxy-image-editor to reflect that its `closed` event emitter can output a `null` value

## [0.73.0]

- Feat: Add `localizedWeekOrder` and `localizedWeekday` to date-utils

## [0.72.1]

- Feat: Handle no image being passed to image uploader

## [0.72.0]

- Feat: Add address format pipe

## [0.71.1]

- fix(glxy): add symbol fallback to currency pipe for better browser support

## [0.71.0]

- BREAKING: remove country code selector and default country code from phone input

## [0.70.1]

- Fix: Use `navigateByUrl` when clicking on the text version of `glxy-page-nav-button`

## [0.70.0]

- Feat: galaxy column def supports sticky prop

## [0.69.1]

- Fix: Formatting Phone number label

## [0.69.0]

- Feat: Load translations using window.partnerId in browser-locale workflow

## [0.68.2]

- Test: add tests for column sort/arrange components

## [0.68.1]

- Fix: filter error messages for async validators based on whether there were results returned or not

## [0.68.0]

- Feat: Add spanish locale to language options

## [0.67.0]

- Feat: Add option `minContainerHeight` to `GalaxyImageEditorComponent`
- Feat: Add option `autoCropArea` to `GalaxyImageEditorComponent`

## [0.66.1]

- Test: setup tests for composite table feature testing

## [0.66.0]

- Feat: Add option to change Image Editor action alignment, update styles

## [0.65.0]

- Feat: Alert Add `actionRouterLink` for router links, and `actionExternalLink` to open external links in a new tab on
  Action button click

## [0.64.7]

- Test: Add tests for selection service. Update some interfaces.

## [0.64.6]

- Fix: Make image editor not spam new object urls

## [0.64.5]

- Test: Add data source tests for search

## [0.64.4]

- Fix: update datasource members on next frame

## [0.64.3]

- Fix: remove bad filter from data members in data source

## [0.64.2]

- Fix: rollback changes from 0.62.0

## [0.64.1]

- Fix: change the CurrencyPipe currencyDisplay option from narrowSymbol to Symbol to fix issue with errors in Safari

## [0.64.0]

- Chore: add options to hide all header actions in table

## [0.63.0]

- Feat: Add initial implementation of a searchable select input

## [0.62.0]

- Feat: Read partner ID in browser-locale and pass value to lexicon

## [0.61.0]

- Chore: Change Paginated API Name to include Interface

## [0.60.1]

- Fix: shrink down page size selector

## [0.60.0]

- Feat: Add Experimental `glxy-infinite-scroll-trigger` component

## [0.59.1]

- Fix: galaxy/i18n owns it's own Language definitions, rather than relying on Atlas

## [0.59.0]

- Fix: Galxy Page toolbar button spacing

## [0.58.1]

- Fix: rig up paginator text and translate labels

## [0.58.0]

- Feat: expose column sort component

## [0.57.1]

- Test: add tests for paginator datasource

## [0.57.0]

- Feat: add paginator components to table

## [0.56.0]

- Feat: Add helper function `subtractYears` in date utils
- Fix: Add the comma to the `mediumDate` and `longDate` formats

## [0.55.0]

- BREAKING: removing galaxy forms

## [0.54.2]

- Fix: refactor blur handler to not prematurely clear async errors

## [0.54.1]

- Fix: Remove unnecessary logging in getUTCTime

## [0.54.0] 2021-05-21

- Feat: Allow base translation loading in browser-locale

## [0.53.0] 2021-05-13

- Fix: refactor the galaxy inputs to work with the async validators

## [0.52.4] 2021-05-10

- Fix: add separate date methods for startOf/endOf day/month for relative dates and UTC dates.

## [0.52.3] 2021-05-07

- Fix: Format parsed xliff text units for interpolation

## [0.52.2] 2021-05-06

- Fix: Parse xliff text units using regex

## [0.52.1] 2021-05-06

- Fix: Cancel extra space when glxy-wrapping a whole form

## [0.52.0] 2021-05-04

- Feat: add max editor height to image editor

## [0.51.3] 2021-04-30

- Fix: translate the generic error message from the uploader API response

## [0.51.2] 2021-04-29

- Fix: Galaxy Nav Item now properly disables it's link text underline
- Fix: Galaxy Page properly unsets height on mobile to not artificially limit scroll height for `position:sticky`
  elements

## [0.51.1] 2021-04-28

- Feat: Helper functions in date utils for returning dates in UTC

## [0.51.0] 2021-04-26

- Feat: Add PageService to assist in page navigation decisions for previous navigation behaviour

## [0.50.1] 2021-04-20

- Chore: Remove experimental `EXP__` prefix from Galaxy Resize (#3989)

## [0.50.0] 2021-03-30

- Feat: add browser locale utility code to i18n
- Allow specifying file format for translation files in i18n/browser-locale's `bootstrapI18n`
  function with the new parameter.

## [0.49.5] 2021-04-15

- Feat: Add `borderRadius` input to Alert (#4031)

## [0.49.4] 2021-04-13

- Feat: Galaxy Nav has `mobileMaxWidth` (#3929)(#3982)

## [0.49.3] 2021-04-12

- Fix: Galaxy Image Editor now properly selects initial aspect ratio

## [0.49.2] 2021-04-08

- Fix: Galaxy Page now properly reads `mobileMaxWidth` (#3919)

## [0.49.1] 2021-04-05

- Fix: Add classes to galaxy components that were missing them

## [0.49.0] 2021-04-05

- Feat: return specific error message when duplicate file is rejected

## [0.48.0] 2021-03-30

- Feat: add browser locale utility code to i18n

## [0.47.0] 2021-03-18

- Feat: add Galaxy Nav Items and other layout directives to use with Nav

## [0.46.0] 2021-03-12

- Chore: expose autoUpload prop on Image Uploader
- Feat: expose image uploader tile
- Feat: allow for reading url for image tile and image list

## [0.45.2] 2021-03-12

- Revert: Revert 0.45.1 as it breaks form groups

## [0.45.1] 2021-03-11

- Fix: select input valuesChanges event no longer fires twice when subscribed to it.

## [0.45.0] 2021-03-09

- Feat: add image uploader components (tile list, uploader, and editor)

## [0.44.1] 2021-02-26

- Fix: remove event from params list of host listeners for popover, as they aren't needed

## [0.44.0] 2021-02-24

- BREAKING CHANGE: Galaxy uploader service is now provided via an injection token

## [0.43.1] 2021-02-22

- Fix: phone input will set country code when loading initial value from an Angular FormControl

## [0.43.0] 2021-02-22

- Feat: Add new Galaxy Nav component and it's documentation. Galaxy nav is now used in Observatory, Sandbox, and VCC

## [0.42.1] 2021-02-18

- Fix: Uploader, NgOnChanges was not properly allowing for first time changes to be applied to service- Feat: Uploader,
  expose clear method. Minor fix to use remove file instead of brute for array clear

## [0.42.0]

- Re-expose the currency pipe to be exportable and to be included in the GalaxyPipeModule.

## [0.41.4] 2021-02-08

- Remove timezone service.

## [0.41.3] 2021-02-08

- Fix: select input to respect form control value provided

## [0.41.2] 2021-02-08

- Fix: Use vendasta core email validator instead of ng validator for correct email validation

## [0.41.1] 2021-02-07

- Fix: Add injectable to GalaxyDateAdapter

## [0.41.0] 2021-02-05

- Fix/Breaking: Galaxy Validator methods no longer use name

## [0.40.4]

- Fix: "buildTimezoneDisplay" return an empty string if the first param is null

## [0.40.3] 2021-02-02

- Add optional form control input to the timezone selector

## [0.40.2] 2021-02-02

- Fix: Uploader service set params on ngOnChanges, and not ngOnInit.

## [0.40.1] 2021-02-01

- Update @HostBinding() usage to ensure value is truthy

## [0.40.0] 2021-02-01

- Remove Currency Pipe as an exportable pipe. Remove the Currency Pipe from the Pipes Module.

## [0.39.2] 2021-01-28

- Fix: (Galaxy Base Styles) Change how and where spacing is applied in mat-dialogs around the title and content

## [0.39.1] 2021-01-28

- Add upload response interface.

## [0.39.0] 2021-01-26

- Feat: add more arrows to the popover positions, and default popover positions

## [0.38.2] 2021-01-25

- Fix: Uploader service can now match for file info entries without a file object

## [0.38.1] 2021-01-25

- Fix: Tooltip hides when host element is missing from DOM

## [0.38.0] 2021-01-19

- Add timezone-selector component

## [0.37.0] 2020-01-22

- Add hint prop to email input

## [0.36.1] 2020-01-19

- Fix bug where Chrome no longer ignores `autocomplete="off"`

## [0.36.0] 2021-01-15

- [Uploader] Update uploader file to show x button for uploaded files.
- [Uploader] Allow setting base set of files to manage, via Uploader component.
- [Uploader] More robust file removal when files are pre-added without a source file.
- [Uploader] Prepend protocol when missing.

## [0.35.0] 2021-01-13

- Update uploader file component so that the delete button is only shown if the event emitter has been listened to.
- Update uploader component so that services can be overridden and provided, but defaults to its own instance of none
  available.

## [0.34.2] 2020-01-14

- Actually fix bug where Chrome ignores `autocomplete="off"` or anything other than `chrome-off`. Added addition dummy
  fields to disable the other kind of autofill (yes, there are two kinds on Chrome)

## [0.34.1] 2020-01-12

- Fix bug where Chrome ignores `autocomplete="off"`

## [0.34.0] 2020-01-11

- Add currency format pipe, which produces a more human-readable currency based on the user's preferred browser language
  or by passing a currency code and a locale.

## [0.33.0] 2021-01-08

- Add optional `disableAutoComplete` input to all applicable input components for disabling autocomplete

## [0.32.0] 2021-01-04

- Add LexiconModule.forChild() to GalaxyI18NModule (requires LexiconModule.forRoot() in app)

## [0.31.0] 2020-12-28

- Add `date-range-presets.service` to `date-range-presets`.
- Update Year control in Quarter & Month sections.
- Add default date value to `date-range-presets`.

## [0.30.0] 2021-01-04

- Feature: Add Galaxy Uploader and Galaxy Uploader List components

## [0.29.0] 2020-12-28

- Merge galaxy-experimental with galaxy (resize, container-query, galaxy-wrap are still marked as experimental) but are
  not contained within a separate library

## [0.28.2] 2020-12-22

- Fix: force input validator names to be lowercase to match form control errors

## [0.28.1] 2020-12-21

- Add a high contrast setting for the Galaxy Tooltip Component

## [0.28.0] 2020-12-08

- Add Tooltip Component and a Tooltip directive for creating easy tooltips using the Popover Component.

## [0.27.2] 2020-12-7

- Changes to Galaxy typography for inputs on small screen to avoid iOS zoom

## [0.27.1] 2020-11-30

- Fix glxy-popover shadow bug

## [0.27.0] 2020-11-25

- Add GalaxyDatePipe for formatting date string, date timestamps, and Date objects into readable, localized date
  strings.

## [0.26.3] 2020-11-24

- Empty State component update:

- Added optional size input with 'small' option

## [0.26.2]

- Datepicker and DateRangePresets are now using 'date-fns' instead of 'moment' for date logic.
- Fixed issue with the type of the date range being emitted from the date range picker

## [0.26.1] - 2020-11-17

- Fix: realign select input arrows for Galaxy Select Input

## [0.26.0] - 2020-11-17

- Add EmptyState component

## [0.25.0] - 2020-11-12

- Add DateRangePresets component

## [0.24.1] - 2020-11-06

- Fix phone input blur removing country code

## [0.24.0] - 2020-11-06

- Tsconfig for lib prod to match other projects

## [0.23.1] - 2020-11-05

- Add "error" method to SnackbarService, which shows an error snack with a default error message

## [0.23.0] - 2020-10-22

- BREAKING: Update Galaxy Select Input to use Mat Select

- Galaxy Select is now data driven
- Ported all instances of glxy-select

## [0.22.2] - 2020-10-21

- Add an Inject decorator to injected TranslateService in GalaxyI18NModule

## [0.22.1] - 2020-10-15

- Export `AlertTypes` in Galaxy Alert

- Also changed case from `alertTypes` to `AlertTypes`.
- Export local imports in modules to fix new Angular build process

## [0.22.0] - 2020-10-13

- Add Galaxy Snackbar service

## [0.21.1] - 2020-10-02

- Add overflow-x auto scroll to Galaxy Page extended toolbar

## [0.21.0] - 2020-10-02

- Add Galaxy Button Group

## [0.20.4] - 2020-09-16

- Update Phone Input

- Phone number is not stuck with the area code value if the field is cleared.

## [0.20.3] - 2020-09-16

- Update Phone Input
- If the initial value is invalid the field shows it as invalid.
- Prevent adding an additional country code if the input phone number already has one

## [0.20.2] - 2020-09-10

### Updated Galaxy Page

- Galaxy Page is now properly mobile responsive. When viewport < 768px, the toolbar is no longer sticky, but scrolls
  with the page
- You can customize Galaxy Page's mobile responsive breakpoint with mobileMaxWidth
- You can now customize the max-width of glxy-page-wrapper with maxWidth. The default max-width of glxy-page-wrapper is
  900px, to match the ol' va-page

## [0.20.1] - 2020-09-09

- Updated styles of Galaxy Alert based on implimenting it in PC

## [0.20.0] - 2020-09-09

- Add `dense` styles for Galaxy inputs and forms

## [0.19.1] - 2020-08-26

- Updates to `glxy-page` and docs

## [0.19.0] - 2020-08-19

- Add new Galaxy Popover Layout components: `<glxy-popover-title>` and `<glxy-popover-actions>`
- Add new Input for controlling whether or not to pad the popover

## [0.18.0] - 2020-08-19

- Add new Galaxy Popover Component

## [0.17.0] - 2020-08-11

- Added Galaxy Alert `glxy-alert` component and documentation

## [0.16.0] - 2020-07-28

- Galaxy Input Core Directive now supports value input

## [0.15.1] - 2020-07-28

- Updated Galaxy Page based on dogfooding in Mission Control

## [0.14.3] - 2020-07-22

- Fixed: Galaxy From Control Name Directive, restructure component access

## [0.14.2] - 2020-07-22

- Fixed: Galaxy Form Control Name Directive component access to ngOnInit

## [0.14.1] - 2020-07-20

- Now shipping the styles with the npm package!
  - Import into your scss like `@import "~@vendasta/galaxy/styles/design-tokens";`

## [0.14.0] - 2020-07-20

- Phone number selector support to `glxy-phone-input`

## [0.13.0] - 2020-07-15

- Set GalaxyFromFieldInterface.type to be optional

## [0.12.0] - 2020-07-14

- Add content display type for glxy-page

## [0.11.0] - 2020-07-07

- `glxy-currency-input` for handling inputs for currency
- Add currency as an option for forms
- Fixed: Core input components will now set config values itself
- Fixed: Fix issue with not aligned fields

## [0.10.0] - 2020-06-26

- import `glxy-page` from the LABS repo

## [0.9.0] - 2020-06-26

- glxyFormControlName support for reactive forms
- Fix: placeholder styling

## [0.8.0] - 2020-06-19

- I18N Support

## [0.7.2] - 2020-06-22

- Fix: email input style issues not always being applied

## [0.7.1] - 2020-06-10

- Fix: error message layout

## [0.7.0] - 2020-06-10

- Add tracking support for `Glxy-forms`

## [0.6.1] - 2020-06-03

- Fix: layout of folder structure of library

## [0.6.0] - 2020-06-02

- Galaxy Forms
  - GalaxyFormGroup
  - GalaxyFormArray

## [0.5.1] - 2020-05-29

- Fix: Inputs check last state of disabled to prevent stack overflow errors
- Fix: Email input missing classes based on state

## [0.5.0] - 2020-05-28

- Add `glxy-select-input`

## [0.4.1] - 2020-05-25

- Fix: disable support for input fields

## [0.4.0] - 2020-05-25

- Add `glxy-checkbox`

## [0.3.0] - 2020-05-21

- Add `glxy-phone-input` for Phone Inputting

## [0.2.0] - 2020-05-21

Password input field

- Fix: Name and id for email field is set

## [0.1.0] - 2020-05-20

- Fix: Shared base core input for all input fields.

## [0.0.1] - 2020-05-11

- Initial publish to npm
