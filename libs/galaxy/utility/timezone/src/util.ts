import { Timezone, getAllTimezones, getTimezone as getCTTimezone } from 'countries-and-timezones';
import { format } from 'date-fns-tz';
import { DateTime } from 'luxon';

const NUMBER_OF_MILLISECONDS_IN_A_MINUTE = 60000;

/** @return {string[]} All of the known timezones from the 'countries-and-timezones' */
export function getTimezoneNames(): string[] {
  return Object.values(getAllTimezones())
    .map((tz: Timezone) => tz.aliasOf || tz.name)
    .filter((value, i, arr) => arr.indexOf(value) === i);
}

/**
 * @param {string} timeZone a timeZone in IANA format
 */
export function getTimezoneObj(timeZone: string): Timezone {
  return getCTTimezone(timeZone) || getCTTimezone('UTC');
}

/**
 * @param {string} timeZone Local timezone of the user  in IANA format, used to get the TimeZone object from 'countries-and-timezones'.
 * @param {Date} fromStart The date that the timezone offset is being fetched for.
 * @return {number} Returns the offset in minutes for the given timezone and date
 */
export function getTimezoneOffsetFromTimezone(timeZone: string, fromStart: Date = new Date()): number {
  const tz: Timezone = getTimezoneObj(timeZone);
  const date = new Date(fromStart.setHours(12));
  if (isTimezoneDST(date, tz)) {
    return tz.dstOffset;
  }
  return tz.utcOffset;
}

/**
 * @param {Date} date The date to check if is during daylight savings time.
 * @return {boolean} Is the given date during daylight savings time
 */
export function isDST(date: Date): boolean {
  const jan = new Date(date.getFullYear(), 0, 1).getTimezoneOffset();
  const jul = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
  return Math.max(jan, jul) !== date.getTimezoneOffset();
}

/**
 * @param {string} userTimeZone A timezone in the IAN format that the user/client is in.
 * @param {string} timeZone A timezone in IANA format that we want 'fromStart' to be localized to.
 * @param {Date} fromStart A date from the user's timezone to be localized to the supplied timezone
 * @return {string} String for display a timezone option in the timezone selector. Format is '${Timezone abbr} (${timeZone}) - ${current time of "timeZone"}'
 */
export function buildTimezoneDisplay(
  timeZone: string,
  userTimeZone: string = getTimezone(),
  fromStart: Date = new Date(),
): string {
  if (!timeZone) {
    return '';
  }
  let utcDate;
  let zonedDate;
  try {
    utcDate = convertDateToUTCDate(fromStart, userTimeZone);
    zonedDate = convertUTCDateToLocalDate(utcDate, timeZone);
  } catch (err) {
    return '';
  }

  let timeZoneShort = '';
  try {
    // returns a string with the current time and the timezone, we'll split the string to the timezone
    timeZoneShort = zonedDate.toLocaleTimeString(undefined, { timeZone, timeZoneName: 'short' }).split(' ').pop() || '';
  } catch (err) {
    // toLocaleTimeString throws an error when given an unfamiliar timeZone. We'll build our own
    timeZoneShort = buildTimeZoneShortFromTimeZone(timeZone, fromStart);
  }
  let timeDisplay = '';
  try {
    timeDisplay = format(zonedDate, 'h:mm a');
  } catch (err) {
    console.warn('Error formatting time display with', zonedDate, err);
  }
  return `${timeZoneShort} (${timeZone}) - ${timeDisplay}`;
}

/**
 * @param {string} timeZone A timezone, in IANA format
 * @param {Date} date Optional date to determine if DST is in effect
 */
function buildTimeZoneShortFromTimeZone(timeZone: string, date: Date = new Date()): string {
  const tz = getTimezoneObj(timeZone);

  // Determine if the date is in DST for this timezone
  const isDstForTimezone = isTimezoneDST(date, tz);

  // Use the appropriate offset based on DST status
  const tzOffset: number = isDstForTimezone ? tz.dstOffset : tz.utcOffset;

  const offsetInHours = tzOffset / 60;
  let timeZoneShort = 'GMT';
  if (offsetInHours !== 0) {
    if (offsetInHours > 0) {
      timeZoneShort += '+';
    }
    timeZoneShort += offsetInHours;
    if (tzOffset % 60 !== 0) {
      timeZoneShort += `:${tzOffset % 60}`;
    }
  }
  return timeZoneShort;
}

/**
 * @param {string} timeZone The local timezone of the user/client, in IANA format
 * @param {Date} date A local date to be converted to the current time in UTC
 * @return {Date} The date in UTC
 */
function convertDateToUTCDate(date: Date, timeZone: string): Date {
  const tz = getTimezoneObj(timeZone);

  // Determine if the date is in DST for this timezone
  const isDstForTimezone = isTimezoneDST(date, tz);

  // Use the appropriate offset based on DST status
  const tzOffset = isDstForTimezone ? tz.dstOffset : tz.utcOffset;

  const now_utc = Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    date.getUTCHours(),
    date.getUTCMinutes(),
    date.getUTCSeconds(),
  );

  return new Date(now_utc - NUMBER_OF_MILLISECONDS_IN_A_MINUTE * tzOffset);
}

/**
 * Converts a UTC Date to a local Date in the specified IANA timezone.
 *
 * This function calculates the local time by determining whether the input date
 * falls within Daylight Saving Time (DST) for the target timezone. It then applies
 * the correct UTC offset (standard or DST) to the UTC date.
 *
 * Note: This approach avoids relying on browser-local timezone logic and ensures
 * consistent results across environments by using a timezone object with defined
 * offsets.
 *
 * @param {Date} date - A JavaScript Date object in UTC.
 * @param {string} timeZone - The IANA timezone identifier to convert the UTC date to.
 * @return {Date} A new Date object representing the local time in the specified timezone.
 */
function convertUTCDateToLocalDate(date: Date, timeZone: string): Date {
  const tz = getTimezoneObj(timeZone);

  const isDstForTimezone = isTimezoneDST(date, tz);
  const tzOffset = isDstForTimezone ? tz.dstOffset : tz.utcOffset;

  return new Date(date.getTime() + NUMBER_OF_MILLISECONDS_IN_A_MINUTE * tzOffset);
}

/**
 * Determines if a given date is in Daylight Saving Time (DST) for a specific timezone.
 *
 * This function uses the `countries-and-timezones` package to inspect whether the
 * timezone has different UTC offsets for standard time and DST. If there is no difference,
 * DST is not observed, and the function returns false.
 *
 * If DST is observed, Luxon is used to determine if the specific date is within the DST
 * period. If Luxon fails to resolve a valid DateTime for the provided timezone, a fallback
 * browser-based DST check (`isDST`) is used.
 *
 * @param {Date} date - The JavaScript Date object to check.
 * @param {Timezone} timezone - A timezone object from the `countries-and-timezones` package.
 * @return {boolean} True if the date is in DST for the given timezone; false otherwise.
 */
function isTimezoneDST(date: Date, timezone: Timezone) {
  if (timezone.utcOffset === timezone.dstOffset) {
    return false;
  }

  try {
    const dateInZone = DateTime.fromJSDate(date).setZone(timezone.name);
    if (dateInZone.isValid) {
      return dateInZone.isInDST;
    } else {
      return isDST(date);
    }
  } catch (err) {
    return isDST(date);
  }
}

/**
 * @return {string} Returns the timezone based on client's location, returns UTC when the 'Intl' lib is unavailable, mostly in older browsers.
 */
export function getTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (e) {
    // Intl unavailable assume UTC
    return 'UTC';
  }
}
