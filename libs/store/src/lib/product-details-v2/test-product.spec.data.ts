import { Currency } from '@galaxy/billing';
import { BillingModel, MarketplaceBillingFrequency } from '@vendasta/shared';
import { Product } from '../shared/product';

export const product = Product.fromApi({
  origin: 'marketplace_app',
  recommendedSellPrice: 0,
  billingFrequencyOther: '',
  entryUrl: 'https://www.websiteprodashboard.com/vdc-sso/session/login/<accountId>',
  usesCustomizationForm: false,
  iconUrl:
    'https://lh3.googleusercontent.com/rLnhZY0SBlWTo3hg3ZAois0Zk2BbNgLqmIMdtco2_MAcaVlwDsTs1gw' +
    'WAa_nH8hosYR6EEnf10X5MdYMFYt2-BxhLyUf',
  partnerId: 'ABC',
  endDate: '',
  pdfUploadUrls: [
    '//storage.googleapis.com/mediarepo-prod/marketplace-products/e632f968-ab7e-406d-af36-fcf8164e59' +
      '8d/Live Chat - One Pager with a longer name on purpose.pdf',
    '//storage.googleapis.com/mediarepo-prod/marketplace-products/e632f968-ab7e-406d-af36-fcf8164e59' +
      '8d/Live Chat - One Pager with a longer name on purpose.pdf',
  ],
  headerImageUrl:
    'https://lh3.googleusercontent.com/aSYd5_NvKUA3cPviKTre4cz_H4Bu9RXFepN1I5nW6dM4VIn' +
    'gHWgAZUslZNKVai8HEr1wd_SzTSNntLWqybhlhJqn7pm0Bg',
  category: 'vendor_app',
  tagline: 'WordPress hosting on Google Cloud Platform',
  serviceModel: ['diwm'],
  faqs: [
    {
      answer:
        'WordPress is one of the most intuitive and powerful blogging and website content' +
        ' management system (CMS) available today. It takes care of all the background code and' +
        ' programming so people can focus on designing great looking websites. And it’s open source,' +
        ' which means people around the world are continuously improving it and adding' +
        ' awesome new features.',
      question: 'What is WordPress?',
    },
    {
      answer:
        'There are no restrictions on plugins or themes—any plugin or theme can be added' +
        ' into Website Pro. Website Pro has its own migration, security, and backup tools, so we' +
        ' discourage using other plugins for these features.',
      question: 'Are WordPress plugins and themes restricted?',
    },
  ],
  needHelp: '<p>Call me during working hours at (306) xxx-xxxx</p>',
  isArchived: true,
  sessionUrl: 'https://www.websiteprodashboard.com/vdc-sso/session/',
  productId: 'MP-ee4ea04e553a4b1780caf7aad7be07cd',
  wholesalePrice: 1200,
  updated: '2017-09-07T21:21:59.419330',
  description:
    '<p>Burbio is an event sharing platform that provides businesses with an easy way' +
    ' to create, track and promote their events. A business owner can easily import their existing events' +
    ' calendar into Burbio or create and publish events right through the platform. The online calendar' +
    ' gets your events on Google search, Amazon Alexa and social media. Watch our video below' +
    '</p>\n\n<div style="text-align:center;">\n<iframe scrolling="no" ' +
    'allowTransparency="true" allowfullscreen frameborder="0" width="640" height="360" ' +
    'style="max-width:100%;" src="https://goanimate.com/player/embed/0GwtMpbWQ-Ho"></iframe>\n</div>',
  billingFrequency: 'monthly',
  lmiCategories: ['website', 'social'],
  screenshotUrls: [
    'https://lh3.googleusercontent.com/ffrN9fzS_XxYFv0Wt5ri1pVhSUdT2tWN9JQD-wIFEAGbvKRfxI2XAPK1' +
      '34lgdrYszULA2z5sL_ABLiRy1Ao900nhlmrE',
    'https://lh3.googleusercontent.com/HR8meMC3dFOxXNlKfPAFQWCSLB1NzEfsid3Dgjlc9leVCUObdd8U74' +
      'AG_i5_G3I63g71B9ZOxVI-b5qrjEjWw_LVAJNM',
  ],
  videos: [
    'https://www.youtube.com/watch?v=gdlKHSU5EVg',
    'https://www.dailymotion.com/video/x7vumwe',
    'https://vimeo.com/104677142',
    'https://vendasta-9.wistia.com/medias/k6y3jhv3y1',
  ],
  updatedBy: 'Product Sync',
  name: 'Reputation Management',
  currency: 'USD',
  created: '2017-01-24T21:34:23.806370',
  websiteUrl: '',
  keySellingPoints: [
    'One-click WordPress setup. With Website Pro’s one-click setup wizard, anyone can set up or' +
      ' import a site within minutes! Eliminate the technical backend work required to set up your' +
      ' website hosting.',
    'World-class hosting with Google. Hosted on the Google Cloud Platform, enjoy world-class and' +
      ' automated storage, speed, and security. Gain peace of mind knowing your sites are hosted with' +
      ' the most trusted force on the internet.',
    'Engaging reporting. Website Pro comes with a detailed reporting dashboard that highlights key ' +
      'performance metrics such as your page speed, number of website visitors, session duration, and more.',
    'A robust flexible CMS— WordPress. WordPress is the #1 CMS on the internet— powering over 60% of all' +
      ' CMS based websites.',
  ],
  addons: [
    {
      addonId: 'A-1441445671',
      appId: 'MP-136d75c201bb4c1c894e26c835e6a924',
      price: 500,
      title: 'Fries with that Add-on name should never be this long but what happens',
      description: 'Would you like?',
      tagline: 'This is a tagline of reasonable length that should not be truncated or mess with ' + 'the pricing part',
      icon: null,
    },
    {
      addonId: 'A-1633858314',
      appId: 'MP-136d75c201bb4c1c894e26c835e6a924',
      price: {
        currency: 'USD',
        prices: [
          {
            price: 500,
            frequency: 'Yearly',
          },
          {
            price: 100,
            frequency: 'Monthly',
          },
        ],
      },
      title: 'Fries with that',
      description: 'Would you like?',
      tagline: 'HEyyy there',
      icon:
        'https://lh3.googleusercontent.com/JQBSm7gOIOTdYvqZwcu3kMHKsxNIBt2HklT-e75ODN8GDtM0pssGct5gB8-_1DJo5' +
        'bRuOsCUIUn52IF8ofaQbMkganBS',
    },
  ],
  trialConfiguration: { trialEnabled: 'true' },
  restrictions: {
    country: {
      whitelist: ['CA', 'AU', 'CN', 'US', 'AU', 'AZ', 'BH', 'BD', 'CV', 'CL', 'CX', 'CO', 'KM', 'CK'],
      blacklist: [],
    },
  },
});

export const editionMarketing = [
  {
    appId: 'RM',
    editionId: '',
    name: 'Express',
    billingId: 'RM',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi. Cras vel ante velit. Nullam non ante suscipit justo accumsan suscipit pulvinar sed lectus. Proin fringilla justo nec diam vulputate, id ultrices erat pretium',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    appId: 'RM',
    editionId: 'EDITION-F7JZ5TV8',
    name: 'Something',
    billingId: 'RM:EDITION-F7JZ5TV8',
    endUserMarketing: {
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
      ],
    },
  },
  {
    appId: 'RM',
    editionId: 'test1',
    name: 'Another',
    billingId: 'RM:test1',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi. Cras vel ante velit.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    appId: 'RM',
    editionId: 'test2',
    name: 'End Row',
    billingId: 'RM:test1',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    appId: 'RM',
    editionId: 'test3',
    name: 'Wrapped',
    billingId: 'RM:test1',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
      ],
    },
  },
];

export const editionPricing = {
  RM: {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STANDARD',
    currency: 'USD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 15000,
        minUnits: 1,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-a999e9a7-76df-4e85-9d4a-e463c01dbcec',
  },
  'RM:EDITION-F7JZ5TV8': {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'USD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 100,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
  },
  'RM:test1': {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'USD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 100,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
  },
  'RM:test2': {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'USD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 100,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
  },
  'RM:test3': {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'USD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 100,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
  },
};

export const billedProduct = {
  billingFrequency: MarketplaceBillingFrequency.Monthly,
  pricingTiers: [
    { rangeMin: 0, rangeMax: 10, price: 0 },
    { rangeMin: 11, rangeMax: 20, price: 89999 },
    { rangeMin: 21, rangeMax: 30, price: 79999 },
  ],
  currency: Currency.CAD,
  notes: [],
  productName: '',
  productId: '',
  billingModel: BillingModel.Stairstep,
  price: 0,
  setupFee: 0,
  iconURL: '',
  productCategory: null,
  commitment: { initial: 4, recurring: 2 },
};

export const tagData = [
  {
    appId: 'RM',
    scope: '/',
    appData: [
      {
        kind: 'tag/lmi',
        values: ['Whitelabel', 'SEO', 'Website', 'Marketing', 'Content & Experience '],
      },
      {
        kind: 'tag/country',
        values: ['All'],
      },
      {
        kind: 'tag/product_stats',
        values: ['top_selling'],
      },
      {
        kind: 'tag/source',
        values: ['Vendor'],
      },
      {
        kind: 'tag/source',
        values: ['VENDOR-8N6C24DLFS'],
      },
    ],
  },
];

export const editionTabData = [
  {
    strategy: 'END_OF_PERIOD',
    pricingType: 'TIERED',
    currency: 'CAD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 1400,
        minUnits: 1,
        maxUnits: 1,
      },
      {
        price: 1500,
        minUnits: 2,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-6bcca32b-7ec5-4b97-a926-682ccfb9bfb6',
    appId: 'RM',
    name: 'Pro',
    billingId: 'RM',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'CAD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 150,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
    appId: 'RM',
    editionId: 'EDITION-F7JZ5TV8',
    name: 'Express',
    billingId: 'RM:EDITION-F7JZ5TV8',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'CAD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 150,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
    appId: 'RM',
    editionId: 'EDITION-F7JZ5TV8',
    name: 'Another',
    billingId: 'RM:EDITION-F7JZ5TV8',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
  {
    strategy: 'END_OF_PERIOD',
    pricingType: 'STAIRSTEP',
    currency: 'CAD',
    frequency: 'MONTHLY',
    pricingRules: [
      {
        price: 0,
        minUnits: 1,
        maxUnits: 750,
      },
      {
        price: 150,
        minUnits: 751,
        maxUnits: -1,
      },
    ],
    commitment: {
      initial: 1,
      recurring: 1,
    },
    resolvedFrom: 'PP-f6a60130-878b-4a33-9a10-d5bdeac0cdef',
    appId: 'RM',
    editionId: 'EDITION-F7JZ5TV8',
    name: 'Last Edition',
    billingId: 'RM:EDITION-F7JZ5TV8',
    endUserMarketing: {
      description:
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed iaculis id sapien ut efficitur. Etiam ut nunc eu lorem blandit aliquet ut quis sem. Ut sit amet justo ultrices, vestibulum ligula quis, faucibus mi.',
      keySellingPoints: [
        'Morbi tincidunt ipsum a leo imperdiet molestie eu eget velit.',
        'Ut suscipit ipsum quis nisi finibus, nec porta leo pulvinar.',
        'Vestibulum egestas velit erat, sit amet aliquet risus tincidunt vulputate.',
      ],
    },
  },
];
