import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { HttpClient } from '@angular/common/http';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ActivatedRoute, Router } from '@angular/router';
import { ProductPricing } from '@galaxy/billing';
import { Environment, EnvironmentService } from '@galaxy/core';
import { AppEditionsMarketing } from '@galaxy/marketplace-apps/v1';
import { TranslateService } from '@ngx-translate/core';
import { ImageTransformationService } from '@vendasta/image-transformation';
import { AppPrice, AppPrices, FulfillmentOrderConfig } from '@vendasta/marketplace-apps';
import { OrderForm } from '@vendasta/marketplace-apps/v1';
import { Observable, ReplaySubject, Subscription, combineLatest } from 'rxjs';
import { map, shareReplay, startWith, take } from 'rxjs/operators';
import { MarketplaceAppData } from '../shared/marketplace-app-data';
import { Product } from '../shared/product';
import { HeaderActionType } from '../store-item';
import { MarketingType, PersonaType, SelectedTab } from './product-details-v2.interface';

// The definition for the libs config API response.
export interface AppConfigResponse {
  canEnableApps: boolean;
}

export interface iAppConfig {
  canEnableApps: boolean;
}

export class AppConfig implements iAppConfig {
  canEnableApps: boolean;

  constructor(props?: AppConfigResponse) {
    Object.assign(this, props);
  }
}

export interface EditionActionClickEvent {
  appId: string;
  editionId: string;
}

@Component({
  selector: 'store-product-details-v2',
  templateUrl: './product-details-v2.component.html',
  styleUrls: ['./product-details-v2.component.scss'],
  standalone: false,
})
export class VaProductDetailsV2Component implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  @Input() product: Product;
  @Input() partnerId: string;
  @Input() marketId: string;
  @Input() parentProduct: Product;
  @Input() appTags: MarketplaceAppData[];
  @Input() businessCountryCode: string;
  @Input() marketingType: MarketingType = MarketingType.Partner;
  @Input() personaType: PersonaType = PersonaType.Partner;
  @Input() billingPrice: ProductPricing;
  @Input() appPrice: AppPrice;
  @Input() addonPrices: AppPrices[];
  @Input() isAddon = false;
  @Input() currency: string;
  @Input() editionsMarketing: AppEditionsMarketing[];
  @Input() editionOrder: string[];
  @Input() headerActionType: HeaderActionType;
  @Input() isEditionActionUsed = false;
  @Input() hasAccessToAllRequiredAppsAndEditions = true;
  @Input() onlyShowContactActionWhenHeaderActionIsDisabled = false;
  @Input() orderForm: OrderForm;
  @Input() usesActivationSpecificEntryUrl: boolean;
  @Input() fulfillmentOrderConfig: FulfillmentOrderConfig;
  @Input() hideTags = false;
  @Output() headerActionClick = new EventEmitter();
  isHeaderActionUsed: boolean;
  @Output() addonSelected = new EventEmitter<string>();
  isAddonSelectedUsed: boolean;
  @Output() contactActionClick = new EventEmitter();
  isContactActionUsed: boolean;
  @Output() editionActionClick = new EventEmitter<EditionActionClickEvent>();

  appConfig$: Observable<AppConfig>;
  canEnableApps: boolean;

  tabState$: Observable<{ tab: SelectedTab; editionsTabVisible: boolean }>;
  updateState$$ = new ReplaySubject<null>();

  chipLabels: string[];
  appTagArr: string[];
  srcsetHeaderImage = '';

  mobileLayout: boolean;
  overflowTagsList = '';
  showMoreChip = false;
  moreChipPosition = '';
  hiddenChipNum = 0;
  moreList = [];
  onlyMore = false;

  tabScrollHack: string;
  tabRef: any;

  ownedAndOperated: string[] = null;
  isOO: boolean;

  readonly defaultHeaderImage =
    'https://firebasestorage.googleapis.com/v0/b/partner-central-demo.appspot.com/o/no-banner.png?alt=media&token=e5299062-ea20-498b-b37e-39f3cf8a78a8';
  subscriptions: Subscription[] = [];

  constructor(
    private imageTransformationService: ImageTransformationService,
    breakpointObserver: BreakpointObserver,
    private translateService: TranslateService,
    private environmentService: EnvironmentService,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
  ) {
    breakpointObserver.observe([Breakpoints.HandsetPortrait]).subscribe((result) => {
      if (result.matches) {
        this.mobileLayout = true;
      } else {
        this.mobileLayout = false;
      }
    });
  }

  ngOnInit(): void {
    this.appConfig$ = this.http.get<AppConfigResponse>('/api/v1/app-config').pipe(
      map((resp) => new AppConfig(resp)),
      shareReplay(1),
    );
    this.subscriptions.push(this.appConfig$.subscribe((config) => (this.canEnableApps = config.canEnableApps)));

    this.setOwnedOperatedList();
    this.isHeaderActionUsed =
      this.headerActionType !== HeaderActionType.NONE && this.headerActionClick.observers.length > 0;
    this.isAddonSelectedUsed = this.addonSelected.observers.length > 0;
    if (this.onlyShowContactActionWhenHeaderActionIsDisabled) {
      this.isContactActionUsed = this.isHeaderActionButtonDisabled() && this.contactActionClick.observers.length > 0;
    } else {
      this.isContactActionUsed = this.contactActionClick.observers.length > 0;
    }
    if (!!this.appTags && this.appTags.length && !!this.appTags[0].appData) {
      this.appTagArr = [];
      this.appTags[0].appData.forEach((entry) => {
        if (
          entry.kind.startsWith('tag') &&
          entry.kind !== 'tag/country' &&
          entry.kind !== 'tag/source' &&
          entry.kind !== 'tag/vendor'
        ) {
          entry.values.forEach((value) => {
            const capitalized = value.charAt(0).toUpperCase() + value.slice(1).replace(/_/g, ' ');
            this.appTagArr = this.appTagArr.concat(capitalized);
          });
        }
      });
      this.chipLabels = this.appTagArr;
    } else {
      this.chipLabels = this.getTags();
    }
    if (this.product.headerImageUrl) {
      const imageWidthSizes = [680, 900, 1200];
      this.srcsetHeaderImage = this.imageTransformationService.getSrcSetForImage(
        this.product.headerImageUrl,
        imageWidthSizes,
      );
    }
    if (this.ownedAndOperated) {
      this.isOO = this.ownedAndOperated.includes(this.product.productId);
    }

    this.tabState$ = combineLatest([this.route.queryParams, this.updateState$$]).pipe(
      map(([params]) => {
        const tabIndex = Number(params['tab']);
        let selectedTab = !SelectedTab[tabIndex] ? SelectedTab.ProductInformation : tabIndex;
        if (!this.editionsMarketing && selectedTab === SelectedTab.EditionsAndPricing) {
          selectedTab = SelectedTab.ScreenshotsAndFiles;
        }
        return {
          tab: selectedTab,
          editionsTabVisible: this.editionsMarketing?.length > 0 && this.product.usesEditions,
        };
      }),
      startWith({
        tab: SelectedTab.ProductInformation,
        editionsTabVisible: this.editionsMarketing?.length > 0 && this.product.usesEditions,
      }),
    );
  }

  ngAfterViewInit(): void {
    window.setTimeout(() => {
      this.detectWrap('chipItem');
      this.onTabChanged();
    });
  }

  ngOnChanges(): void {
    this.updateState$$.next(null);
  }

  setOwnedOperatedList(): void {
    switch (this.environmentService.getEnvironment()) {
      case Environment.PROD: {
        this.ownedAndOperated = [
          'RM', // rep man
          'SM', // social marketing
          'MP-94072e44d5364872b672d7ab4fc7a7e8', // ad intel
          'MP-c4974d390a044c28aec31e421aa662b2', // customer voice
          'MP-ee4ea04e553a4b1780caf7aad7be07cd', // website
        ];
        break;
      }
      case Environment.DEMO: {
        this.ownedAndOperated = [
          'RM', // rep man
          'SM', // social marketing
          'MP-94072e44d5364872b672d7ab4fc7a7e8', // ad intel
          'MP-fba21121b71148c9bb33e11fcd92d520', // customer voice
          'MP-9cc9f21f0a234a46ad78087fc09f16bc', // website
        ];
        break;
      }
      default: {
        this.ownedAndOperated = [
          'RM', // rep man
          'SM', // social marketing
          'MP-94072e44d5364872b672d7ab4fc7a7e8', // ad intel
          'MP-fba21121b71148c9bb33e11fcd92d520', // customer voice
          'MP-9cc9f21f0a234a46ad78087fc09f16bc', // website
        ];
        break;
      }
    }
  }

  //  Will be deprecated when the new tagging system is fully implemented
  getTags(): string[] {
    const tags = [];
    if (this.product.trialConfiguration?.trialEnabled) {
      tags.push('Free Trial');
    }
    tags.push(...this.product.getLmiCategoryNames());
    return tags;
  }

  @HostListener('window:resize', ['$event'])
  onResize(): void {
    this.detectWrap('chipItem');
  }

  detectWrap(className): void {
    const wrappedItems: string[] = [];
    const items = Array.from(document.getElementsByClassName(className));

    const initialEl = items[0];
    if (!initialEl) {
      return;
    }
    const initialBounds = initialEl.getBoundingClientRect();
    const itemsLength = items.length;
    let breakIndex = itemsLength;

    items.forEach((itemEl: HTMLElement, index) => {
      const bounds = itemEl.getBoundingClientRect();
      if (initialBounds.top < bounds.top) {
        wrappedItems.push(itemEl.textContent);
        if (breakIndex === itemsLength) {
          breakIndex = index;
        }
      }
    });

    let breakItem;
    if (breakIndex === 1 && this.detectMoreClip()) {
      breakItem = document.getElementById('chipStart');
      wrappedItems.push((<HTMLElement>items[0]).textContent);
      (<HTMLElement>items[0]).style.visibility = 'hidden';
      this.onlyMore = true;
    } else {
      breakItem = <HTMLElement>items[breakIndex - 1];
      (<HTMLElement>items[0]).style.visibility = 'visible';
      this.onlyMore = false;
    }

    this.showMoreChip = wrappedItems.length > 0;
    this.hiddenChipNum = wrappedItems.length;
    this.moreChipPosition = breakItem.offsetLeft + breakItem.offsetWidth + 4 + 'px';
    this.moreList = wrappedItems;
  }

  detectMoreClip(): boolean {
    const moreChip = document.getElementsByClassName('more-chip-spacer')[0];
    const webActionContainer = document.getElementById('web-action-container');
    const chipListWrapper = document.getElementsByClassName('mat-chip-list-wrapper')[0] as HTMLElement;
    if (webActionContainer) {
      const actionContainerBounds = webActionContainer.getBoundingClientRect();
      return !(moreChip.getBoundingClientRect().right < actionContainerBounds.left);
    } else if (chipListWrapper.scrollWidth > chipListWrapper.offsetWidth) {
      return true;
    } else {
      return false;
    }
  }

  isProductEnabled(): boolean {
    if (this.parentProduct) {
      return !this.parentProduct.isArchived && this.product && !this.product.isArchived;
    } else {
      return this.product && !this.product.isArchived;
    }
  }

  isProductSuspended(): boolean {
    if (this.parentProduct) {
      return this.parentProduct.isSuspended && this.product && this.product.isSuspended;
    } else {
      return this.product && this.product.isSuspended;
    }
  }

  /*
   * Resolves the string that should be used for the Enable button.
   */
  getEnableButtonText(): string {
    const turnOnAppTranslationKey = 'FRONTEND.STORE.STORE_STATE.START_SELLING';
    const productAlreadyOnTranslationKey = 'FRONTEND.STORE.STORE_STATE.SELLING';
    const turnOnAddOnTranslationKey = 'FRONTEND.STORE.STORE_STATE.ADD_ON.START_SELLING';
    if (this.headerActionType === HeaderActionType.ADD_TO_CART) {
      return this.translateService.instant('FRONTEND.STORE.ADD_TO_CART');
    } else if (this.headerActionType === HeaderActionType.BUY_IT_NOW) {
      return this.translateService.instant('FRONTEND.STORE.BUY_IT_NOW');
    } else if (this.headerActionType === HeaderActionType.NONE) {
      return null;
    } else if (this.isProductEnabled()) {
      return this.translateService.instant(productAlreadyOnTranslationKey);
    } else {
      if (this.parentProduct) {
        if (this.parentProduct.isArchived) {
          return this.translateService.instant(turnOnAppTranslationKey);
        } else {
          return this.translateService.instant(turnOnAddOnTranslationKey);
        }
      } else {
        return this.translateService.instant(turnOnAppTranslationKey);
      }
    }
  }

  isHeaderActionButtonDisabled(): boolean {
    if (!this.currency) {
      return true;
    }

    if (!this.hasAccessToAllRequiredAppsAndEditions) {
      return true;
    }
    if (
      this.headerActionType === HeaderActionType.ADD_TO_CART ||
      this.headerActionType === HeaderActionType.BUY_IT_NOW
    ) {
      return false;
    }
    return this.isProductEnabled() || this.isProductSuspended() || !this.canEnableApps;
  }

  getHeaderActionTooltip(): string {
    if (!this.hasAccessToAllRequiredAppsAndEditions) {
      return this.translateService.instant('FRONTEND.STORE.CANNOT_ADD_TO_CART_TOOLTIP');
    }
    if (
      this.headerActionType !== HeaderActionType.ADD_TO_CART &&
      this.headerActionType !== HeaderActionType.BUY_IT_NOW &&
      !this.isProductEnabled() &&
      !this.isProductSuspended() &&
      !this.canEnableApps
    ) {
      return this.translateService.instant('FRONTEND.STORE.CAN_ENABLE_PRODUCTS_PERMISSION_DISABLED_TOOLTIP');
    }
    return '';
  }

  onHeaderActionClicked(): void {
    this.headerActionClick.emit();
  }

  onEditionActionClicked(product: { appId: string; editionId: string }): void {
    this.editionActionClick.emit(product);
  }

  onTabChanged(event?: MatTabChangeEvent): void {
    if (event) {
      this.route.queryParams.pipe(take(1)).subscribe((queryParams) =>
        this.router.navigate([], {
          queryParams: {
            ...queryParams,
            tab: event.index,
          },
        }),
      );
    }
    this.tabRef = Array.from(document.getElementsByClassName('mat-tab-body-active'));
    // hack to fix mat-tab bug of scrolling to top of page on tab switch
    this.tabScrollHack = this.tabRef[0]?.clientHeight + 369 + 'px;';
  }

  selectEditionTab(editionTabSelected: boolean): void {
    if (editionTabSelected) {
      this.route.queryParams.pipe(take(1)).subscribe((queryParams) =>
        this.router.navigate([], {
          queryParams: {
            ...queryParams,
            tab: 2,
          },
        }),
      );
    }
    this.tabRef = Array.from(document.getElementsByClassName('mat-tab-body-active'));
    // hack to fix mat-tab bug of scrolling to top of page on tab switch
    this.tabScrollHack = this.tabRef[0]?.clientHeight + 369 + 'px;';
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }
}
