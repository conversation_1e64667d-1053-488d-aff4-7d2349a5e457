@use 'design-tokens' as dt;

.clickable:hover {
  cursor: pointer;
}

.knowledge-icon {
  margin-right: dt.$spacing-3 !important;
}

.knowledge-source-title {
  glxy-badge {
    margin-left: dt.$spacing-2;
  }
}

.knowledge-source-title-link {
  color: dt.$blue;
}

.last-scraped {
  color: dt.$tertiary-font-color;
  font-size: dt.$font-preset-5-size;
}

.knowledge-source-list {
  height: auto !important;
  padding-bottom: dt.$spacing-2;
}
