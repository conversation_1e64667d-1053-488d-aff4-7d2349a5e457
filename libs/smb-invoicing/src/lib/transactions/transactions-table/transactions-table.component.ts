import { CommonModule, DatePipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  EventEmitter,
  Inject,
  input,
  Input,
  OnInit,
  Optional,
  Output,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import {
  DeclineReasons,
  isRefundableStatus,
  PaymentService,
  RetailPaymentMethodType,
  Transaction,
  TransactionReferenceType,
} from '@galaxy/billing';
import { DateFormat, DateRange } from '@vendasta/galaxy/utility/date-utils';
import { skip, startWith } from 'rxjs/operators';
import { TransactionFilters, TransactionsTableDataSource } from './transactions-table-datasource';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { GalaxyDatepickerModule } from '@vendasta/galaxy/datepicker';
import { MatMenuModule } from '@angular/material/menu';
import { BillingUiModule } from '@vendasta/billing-ui';
import { TransactionRowActionsComponent } from './transaction-row-actions/transaction-row-actions.component';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { PadInvoiceNumberPipe } from '../../shared/pipes';
import { PaymentStatusChipComponent } from '../../payment-status-chip/payment-status-chip.component';
import { ExportButtonComponent } from '../../export-financial-reports/components/export-button';
import { CUSTOMER_DATA_SERVICE, CustomerDataService } from '../../customer/customer-data';
import { paymentCardImageUrls } from '../../shared/image-src';

@Component({
  standalone: true,
  selector: 'smb-invoicing-transactions-table',
  templateUrl: './transactions-table.component.html',
  styleUrls: ['./transactions-table.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe],
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    MatProgressBarModule,
    MatTableModule,
    MatPaginatorModule,
    MatIconModule,
    MatButtonModule,
    GalaxyPipesModule,
    GalaxyEmptyStateModule,
    VaMaterialTableModule,
    PaymentStatusChipComponent,
    GalaxyDatepickerModule,
    ExportButtonComponent,
    MatMenuModule,
    BillingUiModule,
    TransactionRowActionsComponent,
    PadInvoiceNumberPipe,
  ],
})
export class TransactionsTableComponent implements OnInit, AfterViewInit {
  public defaultPageSize = 10;

  DateFormat = DateFormat;
  imageUrls = paymentCardImageUrls;
  isRefundableStatus = isRefundableStatus;

  startDate: Date | null = null;
  endDate: Date | null = null;

  cardPaymentMethodType = RetailPaymentMethodType.Card;
  achDebitPaymentMethodType = RetailPaymentMethodType.ACHDebit;
  acssDebitPaymentMethodType = RetailPaymentMethodType.ACSSDebit;

  @ViewChild(MatPaginator) paginator: MatPaginator;

  @Input() displayedColumns = [
    'description',
    'customerId',
    'created',
    'payment-method',
    'status',
    'amount',
    'fee',
    'net',
    'actions',
  ];
  @Input() filters: TransactionFilters;
  merchantId = input.required<string>();
  hasEmptyStateSecondaryCallToAction = input<boolean>(true);
  canClickTransactionReference = input<boolean>(true);
  displayTableBorder = input<boolean>(true);
  customerTitleTranslationKey = input<string>('PAYMENTS_PAGE.CUSTOMER');

  @Output() issueCreditNote = new EventEmitter<string>();
  @Output() customerClicked = new EventEmitter<string>();
  @Output() transactionReferenceClicked = new EventEmitter<Transaction>();
  @Output() transactionClicked = new EventEmitter<Transaction>();
  @Output() emptyStatePrimaryCallToAction = new EventEmitter<boolean>();
  @Output() emptyStateSecondaryCallToAction = new EventEmitter<boolean>();

  // removes the header/footer
  readonly displayTableContentsOnly = input<boolean>(false);

  dataSource: TransactionsTableDataSource;

  //transactionTypeEnum included for use in template
  transactionTypeEnum = TransactionReferenceType;

  netShown: boolean = this.displayedColumns.some((v) => v === 'net');

  constructor(
    private paymentService: PaymentService,
    @Optional() @Inject(CUSTOMER_DATA_SERVICE) private customerDataService: CustomerDataService,
    public dialog: MatDialog,
    private datePipe: DatePipe,
    private destroyRef: DestroyRef,
  ) {
    toObservable(this.merchantId)
      .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
      .subscribe((merchantId) => {
        this.dataSource?.updatePage(merchantId, this.paginator.pageSize, this.paginator.pageIndex, this.filters);
      });
  }

  ngOnInit(): void {
    this.dataSource = new TransactionsTableDataSource(this.paymentService, this.datePipe, this.customerDataService);
  }

  ngAfterViewInit(): void {
    this.paginator.page
      .pipe(startWith({ pageSize: this.defaultPageSize, pageIndex: 0 }), takeUntilDestroyed(this.destroyRef))
      .subscribe((paginator) => {
        this.dataSource.updatePage(this.merchantId(), paginator.pageSize, paginator.pageIndex, this.filters);
      });
  }

  getNextSteps(failureCode: string, failureMessage: string): string {
    let message = '';
    const declineReason = DeclineReasons.get(failureCode);
    if (declineReason) {
      message = `${declineReason.description} ${declineReason.nextSteps}`;
    } else {
      message = failureMessage;
    }
    return message;
  }

  onDateSelection(dateRange: DateRange): void {
    this.startDate = dateRange?.start || null;
    this.endDate = dateRange?.end || null;
    this.filters = {
      ...this.filters,
      createdDateGte: dateRange?.start || null,
      createdDateLte: dateRange?.end || null,
    } as TransactionFilters;
    this.dataSource?.updatePage(this.merchantId(), this.paginator.pageSize, this.paginator.pageIndex, this.filters);
  }

  reload(): void {
    this.dataSource.reload();
  }

  public customerClickEvent(event: Event, customerId: string): void {
    this.customerClicked.emit(customerId);
    this.consumeEvent(event);
  }

  public transactionReferenceClickEvent(event: Event, transaction: Transaction): void {
    this.transactionReferenceClicked.emit(transaction);
    this.consumeEvent(event);
  }

  public transactionClickEvent(event: Event, transaction: Transaction): void {
    this.transactionClicked.emit(transaction);
    this.consumeEvent(event);
  }

  public issueCreditNoteHandler(referenceId: string): void {
    this.issueCreditNote.emit(referenceId);
  }

  public emptyStatePrimaryCallToActionEvent(): void {
    this.emptyStatePrimaryCallToAction.emit(true);
  }

  public emptyStateSecondaryCallToActionEvent(): void {
    this.emptyStateSecondaryCallToAction.emit(true);
  }

  public consumeEvent(event: Event): void {
    event.stopPropagation();
  }
}
