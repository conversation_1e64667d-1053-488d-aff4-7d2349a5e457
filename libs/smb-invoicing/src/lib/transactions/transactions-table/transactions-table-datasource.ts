import { CollectionViewer, DataSource } from '@angular/cdk/collections';
import { DatePipe } from '@angular/common';
import { PaymentService, Transaction } from '@galaxy/billing';
import { Scrollable } from '@vendasta/rx-utils';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { catchError, map, shareReplay, switchMap, withLatestFrom } from 'rxjs/operators';
import { stripReferencePrefixFromPaymentDescriptions } from '../common';
import { CustomerData, CustomerDataService } from '../../customer/customer-data';

/**
 * Filters are applied to the payments rows to reduce the result set.
 */
export interface TransactionFilters {
  /**
   * Filter by a payout identifier.
   */
  payoutId?: string;
  /**
   * Filter down to a specific customer identifier.
   */
  customerId?: string;
  /**
   * Filter to payments created on or after the date
   */
  createdDateGte?: Date;
  /**
   * Filter to payments created on or before the date
   */
  createdDateLte?: Date;
  /**
   * Filter to payments only
   */
  paymentsOnly?: boolean;
  /**
   * Filter by an invoice ID (only applicable to payments/charges)
   */
  invoiceId?: string;
}

interface TransactionTableCriteria {
  pageSize?: number;
  filters?: TransactionFilters;
  merchantId?: string;
}

export class TransactionsTableDataSource implements DataSource<Transaction> {
  private criteria$$: BehaviorSubject<TransactionTableCriteria> = new BehaviorSubject<TransactionTableCriteria>({});
  private pageIndex$$: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  private scrollable: Scrollable<Transaction, TransactionTableCriteria>;
  public hasMore$: Observable<boolean>;
  public loading$: Observable<boolean>;
  public empty$: Observable<boolean>;
  public knownLength$: Observable<number>;

  constructor(
    private paymentService: PaymentService,
    private datePipe: DatePipe,
    private customerDataService?: CustomerDataService,
  ) {
    this.scrollable = new Scrollable<Transaction, TransactionTableCriteria>(
      this.criteria$$.asObservable(),
      (criteria, cursor) => {
        return this.loadMore(criteria, cursor);
      },
    );

    this.hasMore$ = this.scrollable.hasMore$;
    this.loading$ = this.scrollable.loading$;
    this.empty$ = this.scrollable.empty$;
    this.knownLength$ = this.scrollable.items$.pipe(
      withLatestFrom(this.hasMore$),
      map(([items, hasMore]) => {
        return items.length + (hasMore ? 1 : 0);
      }),
    );
  }

  connect(_collectionViewer: CollectionViewer): Observable<readonly Transaction[]> {
    return combineLatest([
      this.scrollable.items$,
      this.criteria$$.asObservable(),
      this.pageIndex$$.asObservable(),
    ]).pipe(
      map(([items, criteria, pageIndex]) => {
        const start = pageIndex * criteria.pageSize;
        const end = (pageIndex + 1) * criteria.pageSize;
        if (end > items.length) {
          this.scrollable.loadMore();
        }
        return items.slice(start, Math.min(end, items.length));
      }),
    );
  }

  disconnect(_collectionViewer: CollectionViewer): void {
    return;
  }

  loadMore(
    criteria: TransactionTableCriteria,
    cursor: string,
  ): Observable<{ items: Transaction[]; cursor: string; hasMore: boolean; totalResults?: number }> {
    if (!criteria.merchantId) {
      return of({ items: [], hasMore: false, cursor: null });
    }

    const payments$ = this.paymentService
      .listRetailTransactions(
        criteria.merchantId,
        cursor,
        criteria.pageSize,
        criteria.filters?.payoutId,
        criteria.filters?.createdDateGte,
        criteria.filters?.createdDateLte,
        criteria.filters?.paymentsOnly,
        criteria.filters?.customerId,
        criteria.filters?.invoiceId,
      )
      .pipe(
        catchError((e) => {
          console.error(e);
          return of({
            results: [],
            hasMore: false,
            nextCursor: '',
          });
        }),
        map((resp) => {
          resp.results = stripReferencePrefixFromPaymentDescriptions(resp.results);
          return resp;
        }),
        shareReplay({ bufferSize: 1, refCount: true }),
      );

    const customerData$: Observable<CustomerData[]> = payments$.pipe(
      map((resp) => {
        return resp.results
          .map((p) => {
            if (p.payment) {
              return p.payment.customerId;
            }
            if (p.refund) {
              return p.refund.payment?.customerId;
            }
            return null;
          })
          .filter((id) => !!id);
      }),
      switchMap((ids) => {
        if (!ids || ids.length === 0) {
          return of([]);
        }
        return this.customerDataService?.getMulti(ids) || of([]);
      }),
      catchError((_) => {
        // in the event of an account group error, just return an empty list
        // sometimes the user doesn't have permission to access the account group
        return of([]);
      }),
    );

    return combineLatest([payments$, customerData$]).pipe(
      map(([resp, customerData]) => {
        const customers: { [customerId: string]: CustomerData } = {};
        customerData.forEach((customerData) => {
          if (!customerData) {
            return;
          }
          customers[customerData.customerId] = customerData;
        });

        const rows: Transaction[] = [];
        resp.results.forEach((p) => {
          if (p.payment) {
            const customer = customers[p.payment.customerId];
            rows.push(Transaction.fromRetailPayment(customer?.companyName || '', p.payment));
          } else if (p.refund) {
            const customer = customers[p.refund.payment?.customerId];
            rows.push(Transaction.fromRetailRefund(customer?.companyName, p.refund));
          } else if (p.adjustment) {
            rows.push(Transaction.fromAdjustment(p.adjustment));
          } else if (p.payoutFailure) {
            rows.push(Transaction.fromRetailPayout(p.payoutFailure, this.datePipe));
          }
        });

        return { items: rows, cursor: resp.nextCursor, hasMore: resp.hasMore };
      }),
    );
  }

  public updatePage(merchantId: string, pageSize: number, pageIndex: number, filters: TransactionFilters): void {
    const currentCriteria = this.criteria$$.getValue();
    if (
      merchantId !== currentCriteria.merchantId ||
      pageSize !== currentCriteria.pageSize ||
      filters !== currentCriteria.filters
    ) {
      this.criteria$$.next({
        merchantId,
        pageSize,
        filters,
      });
    }

    this.pageIndex$$.next(pageIndex);
  }

  public reload(): void {
    this.criteria$$.next(this.criteria$$.getValue());
  }
}
