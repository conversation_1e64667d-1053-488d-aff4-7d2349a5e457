import { Component, Input } from '@angular/core';

import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { ExportDialogComponent } from '../export-dialog/export-dialog.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ExportDialogData } from '../../models';

@Component({
  standalone: true,
  selector: 'smb-invoicing-export-financial-reports-button',
  templateUrl: './export-button.component.html',
  styleUrls: ['./export-button.component.scss'],
  imports: [TranslateModule, MatButtonModule, MatDialogModule, MatIconModule],
})
export class ExportButtonComponent {
  @Input() merchantId: string;
  @Input() startDate: Date | null = null;
  @Input() endDate: Date | null = null;

  constructor(public dialog: MatDialog) {}

  openDialog(): void {
    this.dialog.open(ExportDialogComponent, {
      width: '650px',
      data: {
        merchantId: this.merchantId,
        startInterval: this.startDate,
        endInterval: this.endDate,
      } as ExportDialogData,
    });
  }
}
