import { formatDate } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import {
  BillingI18nModule,
  PaymentFacilitatorType,
  RetailPayment,
  RetailPaymentReferenceTypeEnum,
  RetailPaymentStatus,
  RetailRefund,
} from '@galaxy/billing';
import { GalaxyCurrencyPipe } from '@vendasta/galaxy/pipes';

import { MatCard, MatCardContent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { LexiconModule } from '@galaxy/lexicon';
import { formatInvoiceNumber } from '../../../shared/invoice';
import { paymentCardImageUrls } from '../../../shared/image-src';
import { DetailRowComponent, Row } from '../details-row/detail-row.component';
import { PaymentBusinessComponent } from '../customer-section/payment-business.component';
import { stripReferencePrefixFromPaymentDescription } from '../../../transactions/common';
import { ViewConfig } from '../shared/view-config';
import { firstValueFrom } from 'rxjs';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { MatButton } from '@angular/material/button';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'smb-invoicing-payment-card',
  templateUrl: './payment-card.component.html',
  styleUrls: ['../shared/card.scss', './payment-card.component.scss'],
  standalone: true,
  imports: [
    MatCard,
    MatCardTitle,
    MatCardHeader,
    MatCardContent,
    DetailRowComponent,
    PaymentBusinessComponent,
    BillingI18nModule,
    LexiconModule,
    GalaxyAlertModule,
    MatButton,
    RouterLink,
  ],
})
export class PaymentCardComponent implements OnInit {
  @Input() payment: RetailPayment;
  @Input() paymentOutstanding: boolean;
  @Input() viewConfig: ViewConfig = {
    hideInvoiceReference: false,
    hideBusinessReference: false,
  };
  rows: Row[];
  showInvoicePaymentCTA = false;

  protected readonly RetailPaymentReferenceTypeEnum = RetailPaymentReferenceTypeEnum;

  constructor(
    private readonly translateService: TranslateService,
    private currencyFormatter: GalaxyCurrencyPipe,
  ) {}

  async ngOnInit(): Promise<void> {
    this.rows = await this.paymentToRows(this.payment);
    this.showInvoicePaymentCTA =
      this.payment.referenceType === RetailPaymentReferenceTypeEnum.RETAIL_PAYMENT_REFERENCE_TYPE_INVOICE &&
      this.payment.status === RetailPaymentStatus.Failed &&
      this.paymentOutstanding === true;
  }

  async paymentToRows(payment: RetailPayment): Promise<Row[]> {
    const applicationFee = payment.applicationFee ?? 0;
    const paymentFacilitatorFee = payment.paymentFacilitatorFee ?? 0;
    let paymentFeeRows = [
      {
        id: 'fee',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.FEE'),
        value: this.currencyFormatter.transform(
          -((applicationFee + paymentFacilitatorFee) / 100),
          payment.currencyCode,
        ),
      },
    ];
    if (payment.paymentFacilitatorType === PaymentFacilitatorType.STANDARD_STRIPE) {
      paymentFeeRows = [
        {
          id: 'applicationFee',
          asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.APPLICATION_FEE'),
          value: this.currencyFormatter.transform(-(applicationFee / 100), payment.currencyCode),
        },
        {
          id: 'stripeFee',
          asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.STRIPE_FEE'),
          value: this.currencyFormatter.transform(-(paymentFacilitatorFee / 100), payment.currencyCode),
        },
      ];
    }

    const rows: Row[] = [
      {
        id: 'date',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.DATE'),
        value: formatDate(payment.created, 'MMM d, y - h:mm a', 'en-US'),
      },
      {
        id: 'amount',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.AMOUNT'),
        value: this.currencyFormatter.transform(payment.amount / 100, payment.currencyCode),
      },
      ...paymentFeeRows,
      {
        id: 'net',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.NET'),
        value: this.currencyFormatter.transform(
          (payment.amount - applicationFee - paymentFacilitatorFee) / 100,
          payment.currencyCode,
        ),
      },
      {
        id: 'status',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.STATUS'),
        value: payment.status,
        dispute: payment.dispute,
      },
    ];

    if (payment.acssDetails || payment.achDetails) {
      rows.push({
        id: 'settledDate',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.SETTLED_DATE'),
        value: payment.settledAt ? formatDate(payment.settledAt, 'MMM d, y - h:mm a', 'en-US') : '-',
      });
    }

    if (this.viewConfig.hideInvoiceReference) {
      rows.push({
        id: 'description',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.DESCRIPTION'),
        value: formatDescription(payment),
      });
    } else {
      rows.push({
        id: 'description',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.DESCRIPTION'),
        value: formatDescription(payment),
        routerLink: buildDescriptionRouterLink(payment),
      });
    }

    const totalRefunded = calculateTotalRefund(payment.refunds);
    if (totalRefunded > 0) {
      rows.splice(2, 0, {
        id: 'refunded',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.REFUNDED'),
        value: this.currencyFormatter.transform(totalRefunded / 100, payment.currencyCode),
      });
    }

    if (payment.cardDetails) {
      rows.push({
        id: 'paymentMethod',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.PAYMENT_METHOD'),
        value: '•••• ' + payment.cardDetails.lastFourDigits,
        valuePrefixImage: {
          src: paymentCardImageUrls[payment.cardDetails.cardType],
          alt: payment.cardDetails.cardType,
          height: 25,
        },
      });
    }
    if (payment.status === 'Failed') {
      let failureMessage = '';
      if (payment.declineReasonDescription) {
        const description = await firstValueFrom(this.translateService.stream(payment.declineReasonDescription));
        const nextSteps = await firstValueFrom(this.translateService.stream(payment.declineReasonNextSteps));
        failureMessage = `${description} ${nextSteps}`;
      } else {
        failureMessage = payment.failureMessage;
      }
      rows.push({
        id: 'failureReason',
        asyncTranslatedName: this.translateService.stream('PAYMENT_PAGE.FAILURE_REASON'),
        value: failureMessage,
      });
    }
    return rows;
  }
}

export function calculateTotalRefund(refunds: RetailRefund[]): number {
  return refunds.reduce((total, refund) => total + refund.amount, 0);
}

function buildDescriptionRouterLink(payment: RetailPayment): string[] {
  if (payment.referenceType === RetailPaymentReferenceTypeEnum.RETAIL_PAYMENT_REFERENCE_TYPE_INVOICE) {
    return ['/invoices', payment.referenceId, 'edit'];
  } else if (payment.referenceType === RetailPaymentReferenceTypeEnum.RETAIL_PAYMENT_REFERENCE_TYPE_SALES_ORDER) {
    return ['/order-management', payment.customerId, 'info', payment.referenceId];
  }
  return null;
}

/**
 * Format description removes any prefixes applied to the backend data to make the displayed value cleaner.
 * If the value is an invoice number, it will format it with the correct padding.
 */
function formatDescription(payment: RetailPayment): string {
  const description = stripReferencePrefixFromPaymentDescription(payment.description, payment.referenceType);
  if (payment.referenceType === RetailPaymentReferenceTypeEnum.RETAIL_PAYMENT_REFERENCE_TYPE_INVOICE) {
    return formatInvoiceNumber(parseInt(description, 10));
  }
  return description;
}
