import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MerchantService } from '@galaxy/billing';
import { Observable } from 'rxjs';
import { PayoutListDataSource } from './payout-list-datasource';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { MODULE_IMPORTS } from '@vendasta/galaxy/table';
import { MatTooltip } from '@angular/material/tooltip';
import { VaMaterialTableModule } from '@vendasta/uikit';
import { PayoutStatusBadgeComponent } from '../payout-status-badge/payout-status-badge.component';

@Component({
  selector: 'smb-invoicing-payout-list',
  templateUrl: './payout-list.component.html',
  styleUrls: ['./payout-list.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    GalaxyPipesModule,
    MODULE_IMPORTS,
    MatTooltip,
    VaMaterialTableModule,
    PayoutStatusBadgeComponent,
  ],
})
export class PayoutListComponent implements OnInit {
  payoutListDataSource: PayoutListDataSource;
  displayColumns = ['arrivalDate', 'status', 'bankAcct', 'amount', 'actions'];
  @Input() shouldShowPayouts: boolean;
  @Input() merchantId$: Observable<string>;
  @Output() payoutSelected: EventEmitter<string> = new EventEmitter();

  constructor(private merchantService: MerchantService) {}

  ngOnInit() {
    this.payoutListDataSource = new PayoutListDataSource(this.merchantService, this.merchantId$);
  }

  onPayoutSelected(payoutId: string) {
    this.payoutSelected.emit(payoutId);
  }
}
