<va-mat-table>
  <table mat-table [dataSource]="payoutListDataSource" class="table-border">
    <!-- Amount Column -->
    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.AMOUNT' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <span>{{ row.amount / 100 | glxyCurrency: row.currencyCode }}</span>
      </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.STATUS' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <smb-invoicing-payout-status-badge
          [status]="row.status"
          [failureMessage]="row.failureMessage"
        ></smb-invoicing-payout-status-badge>
      </td>
    </ng-container>

    <!-- Arrival Column -->
    <ng-container matColumnDef="arrivalDate">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.DATE' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        {{ row.arrivalDate | glxyDate }}
      </td>
    </ng-container>

    <!-- Bank Account Column -->
    <ng-container matColumnDef="bankAcct">
      <th mat-header-cell *matHeaderCellDef>{{ 'PAYOUTS_PAGE.COLUMNS.BANK' | translate }}</th>
      <td mat-cell *matCellDef="let row">
        <span *ngIf="row.lastFourDigits"> {{ row.bankName }} ****** {{ row.lastFourDigits }} </span>
        <i *ngIf="!row.lastFourDigits" [matTooltip]="'BANK_ACCOUNT.REMOVED_TOOLTIP' | translate">
          {{ 'BANK_ACCOUNT.REMOVED' | translate }}
        </i>
      </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let row" class="actions-cell">
        <a (click)="onPayoutSelected(row.id)">{{ 'PAYOUTS_PAGE.COLUMNS.VIEW' | translate }}</a>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
    <tr
      mat-row
      class="payout-row"
      *matRowDef="let row; columns: displayColumns"
      [hidden]="!shouldShowPayouts"
      (click)="onPayoutSelected(row.id)"
    ></tr>
    <ng-container matColumnDef="manageInStripe">
      <td mat-footer-cell *matFooterCellDef [attr.colspan]="displayColumns.length" class="to-stripe-table">
        {{ 'PAYOUTS_PAGE.DEFER_TO_STRIPE.TABLE' | translate }}
      </td>
    </ng-container>
    <tr
      mat-footer-row
      *matFooterRowDef="['manageInStripe']"
      [hidden]="(payoutListDataSource.loading$() | async) || shouldShowPayouts"
    ></tr>
  </table>
</va-mat-table>
