import { CollectionViewer } from '@angular/cdk/collections';
import { DataSource } from '@angular/cdk/table';
import { MerchantService, RetailPayout } from '@galaxy/billing';
import { BehaviorSubject, Observable } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

export class PayoutListDataSource extends DataSource<RetailPayout> {
  private _payouts$$ = new BehaviorSubject<RetailPayout[]>([]);
  private _loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  private _loading$ = this._loading$$.asObservable();

  constructor(
    private merchantService: MerchantService,
    private merchantId$: Observable<string>,
  ) {
    super();
    this.merchantId$
      .pipe(
        tap((_) => this._loading$$.next(true)),
        switchMap((merchantId) => {
          return this.merchantService.listRetailPayouts(merchantId, '', 100);
        }),
      )
      .subscribe((response) => {
        this._payouts$$.next(response.results);
        this._loading$$.next(false);
      });
  }

  public connect(_collectionViewer: CollectionViewer): Observable<RetailPayout[]> {
    return this._payouts$$.asObservable();
  }

  public disconnect(_collectionViewer: CollectionViewer): void {
    this._payouts$$.complete();
  }

  public loading$(): Observable<boolean> {
    return this._loading$;
  }
}
