import { CommonModule } from '@angular/common';
import { Component, computed, Input, OnChanges, Signal, signal, SimpleChanges } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { TokenStatus } from '@vendasta/facebook';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { filter, firstValueFrom, switchMap } from 'rxjs';
import { isNativeApp } from '../../../../core/src/lib/mobile';
import { SocialPageFacebook } from '../interfaces';
import { ManageOnWebComponent } from '../manage-on-web/manage-on-web.component';
import { FacebookMessengerService, FacebookPageConnectionInfo } from './facebook-messenger.service';

type badgeValue = {
  color: 'red' | 'green';
  i18nKey: string;
};

type displayedButton = 'sign-in' | 'enable' | 'disable' | null;

@Component({
  selector: 'inbox-facebook-messenger',
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    TranslateModule,
    GalaxyBadgeModule,
    MatProgressSpinnerModule,
    GalaxyAlertModule,
    GalaxyPopoverModule,
    GalaxySnackbarModule,
    ManageOnWebComponent,
  ],
  templateUrl: './facebook-messenger.component.html',
  styleUrl: './facebook-messenger.component.scss',
})
export class FacebookMessengerComponent implements OnChanges {
  readonly isNativeApp = isNativeApp();

  readonly loading = computed(() => this.loadingAccounts() || this.loadingPage());
  private readonly loadingAccounts = signal<boolean>(true);
  private readonly loadingPage = signal<boolean>(false);

  readonly pageName: Signal<string>;
  readonly badgeValue: Signal<badgeValue | null>;
  readonly manyPages: Signal<boolean>;
  readonly noPages: Signal<boolean>;
  readonly buttonToDisplay: Signal<displayedButton>;
  readonly authTokenMissingPermissions: Signal<boolean>;
  readonly unknownError: Signal<boolean>;
  // user does not have required auth to view connection status, connect, or disconnect
  readonly authError: Signal<boolean>;
  readonly messengerEnabled: Signal<boolean>;

  private readonly connectedPages = signal<SocialPageFacebook[]>([]);
  private readonly pageInfo = signal<FacebookPageConnectionInfo | null>(null);

  @Input({ required: true }) accountID = '';
  @Input({ required: true }) partnerID = '';

  constructor(
    private readonly facebookMessengerService: FacebookMessengerService,
    private readonly snackbarService: SnackbarService,
  ) {
    this.manyPages = computed(() => {
      return this.connectedPages().length > 1;
    });

    this.noPages = computed(() => {
      return this.connectedPages().length === 0;
    });

    toObservable(this.connectedPages)
      .pipe(
        takeUntilDestroyed(),
        filter((connectedPages) => connectedPages.length > 0),
        switchMap((connectedPages) =>
          this.facebookMessengerService.getPageInfo(connectedPages[0], this.partnerID, this.accountID),
        ),
      )
      .subscribe((pageInfo) => {
        this.loadingPage.set(false);
        this.pageInfo.set(pageInfo);
      });

    this.messengerEnabled = computed(
      () =>
        (this.pageInfo()?.messengerEnabled &&
          this.pageInfo()?.tokenStatus === TokenStatus.VALID &&
          this.pageInfo()?.isAssignedPageOwner) ??
        false,
    );

    this.badgeValue = computed(() => {
      return {
        color: this.messengerEnabled() ? 'green' : 'red',
        i18nKey: this.messengerEnabled() ? 'INBOX.SETTINGS.ACTIVE' : 'INBOX.SETTINGS.INACTIVE',
      };
    });
    this.pageName = computed(() => this.pageInfo()?.name ?? '');
    this.authTokenMissingPermissions = computed(() => this.pageInfo()?.tokenStatus === TokenStatus.MISSING_PERMISSIONS);
    this.unknownError = computed(() => this.pageInfo()?.tokenStatus === TokenStatus.INVALID);
    this.authError = computed(() => this.pageInfo()?.statusCheckPermissionError ?? false);

    this.buttonToDisplay = computed(() => {
      if (this.noPages()) {
        return 'sign-in';
      } else if (this.messengerEnabled()) {
        return 'disable';
      } else if (this.unknownError()) {
        return null;
      } else if (!this.messengerEnabled() && this.pageInfo()?.tokenStatus === TokenStatus.VALID) {
        return 'enable';
      } else {
        return null;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['accountID']) {
      this.loadingAccounts.set(true);
      firstValueFrom(this.facebookMessengerService.getPagesForAccount(changes['accountID'].currentValue))
        .then((pages) => {
          if (pages.length > 0) {
            this.connectedPages.set(pages);
            this.loadingPage.set(true);
          } else {
            this.connectedPages.set([]);
          }
        })
        .catch(() => {
          this.connectedPages.set([]);
        })
        .finally(() => {
          this.loadingAccounts.set(false);
        });
    }
  }

  authClick() {
    this.facebookMessengerService.redirectToFacebookAuth(this.accountID);
  }

  async connect(): Promise<void> {
    this.loadingAccounts.set(true);
    return firstValueFrom(this.facebookMessengerService.connectPage(this.accountID, this.connectedPages()[0]))
      .then((success) => {
        if (success) {
          this.snackbarService.openSuccessSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.SUCCESS.SUCCESSFULLY_ENABLED');
          const pages = this.connectedPages();
          this.connectedPages.update(() => []); // update only emmits if the value changes
          this.connectedPages.update(() => pages);
        } else {
          this.snackbarService.openErrorSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.FAILED_TO_ENABLE');
        }
      })
      .finally(() => this.loadingAccounts.set(false));
  }

  async disconnect(): Promise<void> {
    this.loadingAccounts.set(true);
    return firstValueFrom(this.facebookMessengerService.disconnectPage(this.accountID, this.connectedPages()[0]))
      .then((success) => {
        if (success) {
          this.snackbarService.openSuccessSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.SUCCESS.SUCCESSFULLY_DISABLED');
          const pages = this.connectedPages();
          this.connectedPages.update(() => []); // update only emmits if the value changes
          this.connectedPages.update(() => pages);
        } else {
          this.snackbarService.openErrorSnack('INBOX.SETTINGS.FACEBOOK_MESSENGER.ERRORS.FAILED_TO_DISABLE');
        }
      })
      .finally(() => this.loadingAccounts.set(false));
  }
}
