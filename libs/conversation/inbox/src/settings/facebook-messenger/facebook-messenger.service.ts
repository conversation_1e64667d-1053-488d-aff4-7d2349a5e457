import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { FacebookApiService, TokenStatus } from '@vendasta/facebook';
import { Observable, catchError, firstValueFrom, map, of, shareReplay } from 'rxjs';
import {
  AuthLinksResponse,
  SocialConnectionLinks,
  SocialConnections,
  SocialPageFacebook,
  VAPIResponse,
} from '../interfaces';

export interface FacebookPageConnectionInfo {
  name: string;
  tokenStatus: TokenStatus;
  messengerEnabled: boolean;
  isAssignedPageOwner: boolean;
  statusCheckPermissionError?: boolean;
}

@Injectable({
  providedIn: 'any',
})
export class FacebookMessengerService {
  private readonly fbAPIService = inject(FacebookApiService);
  private readonly http = inject(HttpClient);

  connectPage(accountID: string, facebookPageData: SocialPageFacebook): Observable<boolean> {
    const facebookPageID = facebookPageData.profileUrl?.split('id=')[1] ?? '';
    if (!facebookPageID) {
      return of(false);
    }

    return this.fbAPIService
      .assignFacebookPageOwner({
        organizationId: accountID,
        facebookPageId: facebookPageID,
      })
      .pipe(
        map(() => {
          return true;
        }),
        catchError(() => {
          return of(false);
        }),
      );
  }

  async redirectToFacebookAuth(accountID: string): Promise<void> {
    const protocol = window.location.protocol;
    const host = window.location.host;
    const baseUrl = `${protocol}//${host}`;
    const newUrl: URL = new URL(baseUrl);
    newUrl.pathname = `/account/location/${accountID}/settings/connections/target/inbox`;

    const authUrls = await firstValueFrom(this.getAuthUrls(accountID, newUrl));
    const serviceAuthURL = authUrls.facebook;
    const serviceName = 'Facebook';
    const oauthUrl = new URL(serviceAuthURL);
    const nextUrl = new URLSearchParams(oauthUrl.search).get('nextUrl');
    if (nextUrl !== null) {
      const nextUrlObject = new URL(nextUrl);
      nextUrlObject.searchParams.set('type', serviceName);
      oauthUrl.searchParams.set('nextUrl', nextUrlObject.toString());
    }
    window.location.href = oauthUrl.toString();
  }

  disconnectPage(accountID: string, facebookPageData: SocialPageFacebook): Observable<boolean> {
    const facebookPageID = facebookPageData.profileUrl?.split('id=')[1] ?? '';
    if (!facebookPageID) {
      return of(false);
    }

    return this.fbAPIService
      .disableMessengerForPage({
        organizationId: accountID,
        facebookPageId: facebookPageID,
      })
      .pipe(
        map(() => true),
        catchError(() => of(false)),
      );
  }

  getPagesForAccount(accountGroupId: string): Observable<SocialPageFacebook[]> {
    return this.getSocialProfileConnections(accountGroupId).pipe(
      map((connections) => {
        let fbPages: SocialPageFacebook[] = [];
        const fbUsers = connections.facebook ?? [];
        for (const fbUser of fbUsers) {
          fbPages = fbPages.concat(
            fbUser.pages.map((page: SocialPageFacebook) => {
              page.facebookUserID = fbUser.ssid.split('-')[1];
              return page;
            }),
          );
        }
        return fbPages;
      }),
    );
  }

  getPageInfo(
    facebookPageData: SocialPageFacebook,
    partnerID: string,
    accountGroupID: string,
  ): Observable<FacebookPageConnectionInfo> {
    const enabledForPage$ = this.fbAPIService.facebookPageMessengerState({
      organizationId: partnerID + ':' + accountGroupID,
      pageId: facebookPageData.profileUrl?.split('id=')[1],
      facebookUserId: facebookPageData.facebookUserID,
    });

    return enabledForPage$.pipe(
      map((enabledForPage) => {
        return {
          name: facebookPageData.name,
          tokenStatus: enabledForPage.tokenValid,
          messengerEnabled: !!enabledForPage.messengerEnabled,
          isAssignedPageOwner: !!enabledForPage.isPageOwner,
        };
      }),
      catchError((e: HttpErrorResponse) => {
        const statusCheckPermissionError = e.status === 403;
        return of({
          name: facebookPageData.name,
          tokenStatus: TokenStatus.UNKNOWN,
          messengerEnabled: false,
          isAssignedPageOwner: false,
          statusCheckPermissionError,
        });
      }),
    );
  }

  private getSocialProfileConnections(accountGroupId: string): Observable<SocialConnections> {
    return this.http
      .get<VAPIResponse>('/ajax/v1/get-social-profile-connections/', {
        params: {
          accountGroupId: accountGroupId,
        },
      })
      .pipe(
        map((response) => response.data),
        shareReplay({ refCount: true, bufferSize: 1 }),
        catchError((_) => of({} as SocialConnections)),
      );
  }

  private getAuthUrls(accountGroupId: string, nextUrl: URL): Observable<SocialConnectionLinks> {
    let newUrl = nextUrl.href;
    const urlParts = nextUrl.pathname.split('/');
    urlParts.forEach((part) => {
      if (part.startsWith('AG-')) {
        newUrl = newUrl.replace(part, accountGroupId);
      }
    });
    return this.http
      .get<AuthLinksResponse>('/ajax/v1/get-auth-urls/', {
        params: {
          accountGroupId: accountGroupId,
          nextUrl: newUrl,
        },
      })
      .pipe(
        map((response) => {
          return response.data;
        }),
      );
  }
}
