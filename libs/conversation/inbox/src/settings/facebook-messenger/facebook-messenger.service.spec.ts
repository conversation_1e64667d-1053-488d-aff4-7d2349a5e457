import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { FacebookApiService } from '@vendasta/facebook';
import { SocialPageFacebook } from '../interfaces';
import { FacebookMessengerService } from './facebook-messenger.service';

describe('FacebookMessengerService', () => {
  let service: FacebookMessengerService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        {
          FacebookApiService,
          useValue: {
            facebookPageMessengerState() {
              throw new Error('Failed to fetch Facebook page messenger state');
            },
          },
        },
      ],
    });
    service = TestBed.inject(FacebookMessengerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return an empty FacebookPageConnectionInfo object if the request fails', () => {
    const accountID = '123';
    const facebookPageData = { profileUrl: 'https://www.facebook.com/123', name: "<PERSON>'s Je<PERSON>" } as SocialPageFacebook;
    service.connectPage(accountID, facebookPageData).subscribe((result) => {
      expect(result).toBe({
        name: facebookPageData.name,
        validAuthToken: false,
        messengerEnabled: false,
        isAssignedPageOwner: false,
      });
    });
  });
});
