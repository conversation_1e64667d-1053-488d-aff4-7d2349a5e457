import { Component, EventEmitter, Inject, OnInit, Output } from '@angular/core';
import { FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN } from '../../data-providers/providers';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'meeting-scheduler-calendar-selection',
  templateUrl: './calendar-selection.component.html',
  styleUrl: './calendar-selection.component.scss',
  standalone: false,
})
export class CalendarSelectionComponent implements OnInit {
  selectedCalendarProvider: 'google' | 'microsoft' = 'google';
  @Output() calendarProviderSelected = new EventEmitter<string>();
  readonly googleTooltipText: string;
  readonly microsoftTooltipText: string;

  constructor(
    @Inject(FEATURE_MEETING_MICROSOFT_TEAMS_TOKEN)
    readonly featureMicrosoftTeamsEnabled$: Observable<boolean>,
    private readonly translate: TranslateService,
  ) {
    this.googleTooltipText = this.translate.instant(
      'MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_SIGN_IN_TOOLTIP_TEXT',
    );
    this.microsoftTooltipText = this.translate.instant(
      'MEETING_SCHEDULER.MEETING_INTEGRATIONS.MICROSOFT_SIGN_IN_TOOLTIP_TEXT',
    );
  }
  ngOnInit(): void {
    this.emitSelectedProvider();
  }
  onCalendarProviderChange() {
    this.emitSelectedProvider();
  }
  private emitSelectedProvider(): void {
    this.calendarProviderSelected.emit(this.selectedCalendarProvider);
  }
}
