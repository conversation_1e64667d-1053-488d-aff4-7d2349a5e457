export const MEETINGS_LIST = 'MEETING_SCHEDULER.MEETING_LIST.BREADCRUMB';
export const MEETINGS_SETTINGS = 'MEETING_SCHEDULER.SCHEDULE_SETTINGS.BREADCRUMB';
export const BOOKING_LINK = 'MEETING_SCHEDULER.BOOKING_LINK.BREADCRUMB';

export const INVALID_MEETING_ID = 'invalid_meeting_id';

export const CURRENT_VIEW = 'settings';

export const MEETING_SCHEDULER_CONTEXT_INJECTION_TOKEN$ = 'MS_CONTEXT';
export const PERSONAL_EVENT_TYPE = 'Personal event type';
export const TEAM_EVENT_TYPE = 'Team event type';
export const GROUP = 'group';
export const SERVICE = 'service';
export const EVENTS = 'events';

export const CALENDAR_ID_PREFIX = 'MCAL-';
export const GROUP_ID_PREFIX = 'GRP-';
export const SERVICE_ID_PREFIX = 'SVS-';

export const GRID_VIEW = 'grid';
export const LIST_VIEW = 'list';

export enum MS_CONTEXT {
  MS_CONTEXT_PARTNER,
  MS_CONTEXT_SMB,
}

export const GOOGLE_MAP_URL_PREFIX = 'https://www.google.com/maps?q=';

export const POSTHOG_KEYS = {
  SETUP_WIZARD_OPENED: 'meeting-scheduler-setup-wizard-opened',
  STEP_1_COMPLETED: 'meeting-scheduler-step-1-completed',
  STEP_2_COMPLETED: 'meeting-scheduler-step-2-completed',
  STEP_3_COMPLETED: 'meeting-scheduler-step-3-completed',
  STEP_4_COMPLETED: 'meeting-scheduler-step-4-completed',
  EVENT_TYPE_CREATED: 'meeting-scheduler-event-type-created',
  GROUP_CREATED: 'meeting-scheduler-group-created',
  SERVICE_CREATED: 'meeting-scheduler-service-created',
  MEETING_BOOKED_FROM_MY_MEETINGS: 'meeting-scheduler-meeting-booked-from-my-meetings',
};

export const POSTHOG_CATEGORIES = {
  USER: 'user',
};

export const POSTHOG_ACTIONS = {
  CLICK: 'click',
};

export const BOOOKING_URL_DEMO = 'https://meetings-demo.apigateway.co';
export const BOOOKING_URL_PROD = 'https://bookmenow.info';
