import {
  ConfigInterface,
  SalespersonOptionsInterface,
  Status,
  WorkflowStepOptionsInterface,
} from '@vendasta/sales-orders';
import { RetailProvider, RetailStatus } from '@galaxy/billing';

type GetSubmitForCustomerApprovalPermissionsParams = {
  orderStatus: Status;
  workflowStepOptions: WorkflowStepOptionsInterface;
  canManageOrders: boolean;
};

export function GetSubmitForCustomerApprovalPermissions({
  orderStatus,
  workflowStepOptions,
  canManageOrders,
}: GetSubmitForCustomerApprovalPermissionsParams): boolean {
  return (
    [
      Status.DRAFTED,
      Status.SUBMITTED,
      Status.RESUBMITTED,
      Status.APPROVED,
      Status.SCHEDULED_ACTIVATION,
      Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
    ].includes(orderStatus) &&
    (canManageOrders || workflowStepOptions.allowSendToCustomer)
  );
}

type GetIgnoreAllErrorsOnOrderPermissionsParams = {
  orderStatus: Status;
};

export function GetIgnoreAllErrorsOnOrderPermissions({
  orderStatus,
}: GetIgnoreAllErrorsOnOrderPermissionsParams): boolean {
  return orderStatus === Status.ACTIVATION_ERRORS;
}

type GetCreateInvoiceFromOrderPermissionsParams = {
  userCanAccessRetailBilling: boolean;
};

export function GetCreateInvoiceFromOrderPermissions({
  userCanAccessRetailBilling,
}: GetCreateInvoiceFromOrderPermissionsParams): boolean {
  return userCanAccessRetailBilling;
}

type GetCanSubmitOrderParams = {
  canManageOrders: boolean;
  salespersonConfig: SalespersonOptionsInterface;
};

export function GetCanSubmitOrderWithoutRequiredFields({
  canManageOrders,
  salespersonConfig,
}: GetCanSubmitOrderParams): boolean {
  return canManageOrders || !salespersonConfig.validateRequiredFields;
}

type GetArchiveOrderPermissionsParams = {
  canManageOrders: boolean;
  orderStatus: Status;
};

export function GetArchiveOrderPermissions({
  canManageOrders,
  orderStatus,
}: GetArchiveOrderPermissionsParams): boolean {
  if (canManageOrders) {
    return orderStatus !== Status.ARCHIVED;
  }
  return orderStatus === Status.DRAFTED;
}

type GetSubscribeToUpdatesPermissionsParams = {
  orderStatus: Status;
};

export function GetSubscribeToUpdatesPermissions({ orderStatus }: GetSubscribeToUpdatesPermissionsParams): boolean {
  return orderStatus !== Status.ARCHIVED;
}

type GetDuplicateOrderPermissionsParams = {
  orderStatus: Status;
};

export function GetDuplicateOrderPermissions({ orderStatus }: GetDuplicateOrderPermissionsParams): boolean {
  return orderStatus !== Status.DRAFTED;
}

type GetRequestToCancelOrderPermissionsParams = {
  canManageOrders: boolean;
  orderStatus: Status;
};

type GetCancelOrderPermissionsParams = {
  canManageOrders: boolean;
  orderStatus: Status;
};

export function GetCancelOrderPermissions({ canManageOrders, orderStatus }: GetCancelOrderPermissionsParams): boolean {
  const visibleStatuses = [
    Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
    Status.SCHEDULED_ACTIVATION,
    Status.APPROVED,
    Status.FULFILLED,
    Status.ACTIVATION_ERRORS,
  ];
  return canManageOrders && visibleStatuses.includes(orderStatus);
}

export function GetRequestToCancelOrderPermissions({
  canManageOrders,
  orderStatus,
}: GetRequestToCancelOrderPermissionsParams): boolean {
  const visibleStatuses = [
    Status.SUBMITTED,
    Status.RESUBMITTED,
    Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
    Status.SCHEDULED_ACTIVATION,
    Status.APPROVED,
    Status.FULFILLED,
    Status.ACTIVATION_ERRORS,
  ];
  return !canManageOrders && visibleStatuses.includes(orderStatus);
}

export function CanReviewCancellationRequest({
  canManageOrders,
  orderStatus,
}: {
  canManageOrders: boolean;
  orderStatus: Status;
}) {
  return canManageOrders && orderStatus === Status.CANCELLATION_REQUESTED;
}

type GetActivateOrderPermissionsParams = {
  canManageOrders: boolean;
  orderStatus: Status;
};

export function GetActivateOrderPermissions({
  canManageOrders,
  orderStatus,
}: GetActivateOrderPermissionsParams): boolean {
  const activatableStatuses = [Status.APPROVED, Status.SCHEDULED_ACTIVATION];
  return canManageOrders && activatableStatuses.includes(orderStatus);
}

type GetScheduleActivationPermissionsParams = {
  canManageOrders: boolean;
  orderStatus: Status;
};

export function GetScheduleActivationPermissions({
  canManageOrders,
  orderStatus,
}: GetScheduleActivationPermissionsParams): boolean {
  return canManageOrders && orderStatus === Status.APPROVED;
}

export function CanReviewSubmittedOrder({
  canManageOrders,
  orderStatus,
}: {
  canManageOrders: boolean;
  orderStatus: Status;
}) {
  if (!canManageOrders) return false;
  const statuses = [Status.SUBMITTED, Status.RESUBMITTED];
  return statuses.includes(orderStatus);
}

export function CanUpdateInvalidContractStartDatePermissions(orderStatus: Status): boolean {
  const statuses: Status[] = [
    Status.DRAFTED,
    Status.SUBMITTED,
    Status.RESUBMITTED,
    Status.APPROVED,
    Status.SCHEDULED_ACTIVATION,
    Status.SUBMITTED_FOR_CUSTOMER_APPROVAL,
  ];
  return statuses.includes(orderStatus);
}

type GetCanChargeSMBOnOrderSubmissionPermissionsParams = {
  canManageOrders: boolean;
  canAccessRetailBilling: boolean;
  orderConfig: ConfigInterface;
  retailProvider: RetailProvider;
  retailStatus: RetailStatus;
};

export function GetCanChargeSMBOnOrderSubmissionPermissions({
  canManageOrders,
  canAccessRetailBilling,
  orderConfig,
  retailProvider,
  retailStatus,
}: GetCanChargeSMBOnOrderSubmissionPermissionsParams): boolean {
  return (
    ((canManageOrders && canAccessRetailBilling) || orderConfig?.workflowStepOptions?.canChargeSmbOnOrderSubmission) &&
    !!retailProvider &&
    retailStatus?.canAcceptPayment
  );
}

export function GetCanBypassAdminWorkflowAndActivate(partnerId: string, canManageOrders: boolean): boolean {
  return (partnerId === 'T41P' || partnerId === 'TOFU') && canManageOrders;
}
