import { Inject, Injectable } from '@angular/core';
import {
  ConfigInterface,
  Order,
  SalespersonOptionsInterface,
  Status,
  WorkflowStepOptionsInterface,
} from '@vendasta/sales-orders';
import { combineLatest, Observable, of } from 'rxjs';
import { catchError, defaultIfEmpty, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { OrderStoreService } from '../orders.service';
import { PAGE_ORDER_CONFIG_TOKEN, PageOrderConfig } from '../tokens';
import {
  CanReviewCancellationRequest,
  CanReviewSubmittedOrder,
  GetActivateOrderPermissions,
  GetArchiveOrderPermissions,
  GetCancelOrderPermissions,
  GetCanSubmitOrderWithoutRequiredFields,
  GetCreateInvoiceFromOrderPermissions,
  GetDuplicateOrderPermissions,
  GetIgnoreAllErrorsOnOrderPermissions,
  GetRequestToCancelOrderPermissions,
  GetScheduleActivationPermissions,
  GetSubmitForCustomerApprovalPermissions,
  GetSubscribeToUpdatesPermissions,
  CanUpdateInvalidContractStartDatePermissions,
  GetCanChargeSMBOnOrderSubmissionPermissions,
  GetCanBypassAdminWorkflowAndActivate,
} from './actions';
import {
  GetActiveItemsPermissions,
  GetBusinessHeaderPermissions,
  GetCondensedOrderDetailsPermissions,
  GetCreateOrderDetailsPermissions,
  GetEditOrderContentsPermissions,
  GetEditOrderContractStartAndDurationPermissions,
  GetFulfillmentStatusesPermissions,
  GetInvalidLineItemsBannerPermissions,
  GetInvoicePermissions,
  GetOrderFormPermissions,
  GetTagPermissions,
  GetWholesaleColumnPermissions,
} from './features';
import { OrderAction, OrderActionPermissions, OrderFeature, OrderFeaturePermissions } from './permissions';
import { RetailCustomerConfigurationService, RetailStatus } from '@galaxy/billing';
import { FeatureFlagService } from '@galaxy/partner';
import { Features } from '../features';
import { PaymentService, RetailProvider } from '@galaxy/billing';

interface OrderPermissionsServiceInterface {
  CanView(feature: OrderFeature): Observable<boolean>;

  CanEdit(feature: OrderFeature): Observable<boolean>;

  CanDoAction(action: OrderAction): Observable<boolean>;
}

@Injectable()
export class OrderPermissionsService implements OrderPermissionsServiceInterface {
  canManageOrders$: Observable<boolean>;
  canManageMarketplace$: Observable<boolean>;

  retailPaymentsEnabled$: Observable<boolean>;

  constructor(
    @Inject(PAGE_ORDER_CONFIG_TOKEN) public readonly pageOrderConfig: PageOrderConfig,
    @Inject(OrderStoreService) readonly orderStoreService: OrderStoreService,
    private readonly retailCustomerConfigurationService: RetailCustomerConfigurationService,
    private readonly featureFlagService: FeatureFlagService,
    private readonly paymentService: PaymentService,
  ) {
    const order$ = this.orderStoreService.order$;
    const orderStatus$ = order$.pipe(map((order) => order.status));
    const orderConfig$ = this.orderStoreService.orderConfig$;
    const workflowStepOptions$ = orderConfig$.pipe(map((config) => config.workflowStepOptions));
    const salespersonConfig$ = orderConfig$.pipe(map((config) => config.salespersonOptions));
    const retailProvider$ = order$.pipe(
      switchMap((order) => this.paymentService.getRetailProvider(order.partnerId)),
      catchError(() => of(null)),
    );
    const retailStatus$: Observable<RetailStatus> = order$.pipe(
      switchMap((order) => this.paymentService.retailStatus(order.partnerId)),
      catchError(() => {
        return of(null);
      }),
    );
    // view is optional so we default to false
    this.canManageOrders$ = (this.pageOrderConfig.orderPermissions$ || of(null)).pipe(
      map((permissions) => permissions.accessManageOrders),
      defaultIfEmpty(false),
      shareReplay(1),
    );

    this.canManageMarketplace$ = (this.pageOrderConfig.orderPermissions$ || of(null)).pipe(
      map((permissions) => permissions.accessMarketplace),
      defaultIfEmpty(false),
      shareReplay(1),
    );

    this.retailPaymentsEnabled$ = combineLatest([retailProvider$, retailStatus$]).pipe(
      map(([retailProvider, retailStatus]) => !!retailProvider && !!retailStatus?.canAcceptPayment),
      defaultIfEmpty(false),
      shareReplay(1),
    );

    const autoGenerateRetailSubscriptions$ = order$.pipe(
      switchMap((order) =>
        this.retailCustomerConfigurationService.get(order.partnerId, order.businessId).pipe(
          take(1),
          map((retailConfig) => retailConfig?.autoGenerateRetailSubscriptions ?? false),
          catchError(() => of(false)),
        ),
      ),
    );

    const featureFlags$ = order$.pipe(
      switchMap((order) =>
        this.featureFlagService.batchGetStatus(order.partnerId, order.marketId, [
          Features.CREATE_ORDERS_CONTRACT_TERMS,
          Features.PC_UNIFIED_ORDERS_PAGE,
        ]),
      ),
    );

    const hasFeatureAccessToUnifiedOrders$ = featureFlags$.pipe(map((res) => res[Features.PC_UNIFIED_ORDERS_PAGE]));

    // features
    this[OrderFeature.OrderForms] = this.setupOrderFormPermissions(orderStatus$, orderConfig$, this.canManageOrders$);
    this[OrderFeature.Tags] = this.setupTagPermissions(salespersonConfig$, this.canManageOrders$);
    this[OrderFeature.OrderContents] = this.setupEditOrderContentsPermissions(orderStatus$, this.canManageOrders$);
    this[OrderFeature.Invoices] = this.setupIncludeInInvoicesTogglePermissions(
      this.canManageOrders$,
      autoGenerateRetailSubscriptions$,
    );
    this[OrderFeature.FulfillmentStatuses] = this.setupFulfillmentStatusesPermissions(orderStatus$);
    this[OrderFeature.CreateOrderDetails] = this.setupCreateOrderDetailsPermissions(
      orderStatus$,
      this.canManageOrders$,
    );
    this[OrderFeature.CondensedOrderDetails] = this.setupCondensedOrderDetailsPermissions(orderStatus$);
    this[OrderFeature.ActiveItems] = this.setupActiveItemsPermissions(orderStatus$);
    this[OrderFeature.BusinessHeader] = this.setupBusinessHeaderPermissions(orderStatus$);
    this[OrderFeature.ContractStartAndDuration] = this.setupEditContractStartAndDurationPermissions(
      orderStatus$,
      this.canManageOrders$,
    );
    this[OrderFeature.InvalidLineItemsBanner] = this.setupInvalidLineItemsBannerPermissions(orderStatus$);
    this[OrderFeature.WholesalePriceColumn] = this.setupWholesalePriceColumnPermissions(this.canManageMarketplace$);

    // actions
    this[OrderAction.SubmitWithoutRequiredFields] = this.setupCanSubmitOrderWithoutRequiredFields(
      this.canManageOrders$,
      salespersonConfig$,
    );
    this[OrderAction.IgnoreAllErrorsOnOrder] = this.setupIgnoreAllErrorsOnOrderPermissions(orderStatus$);
    this[OrderAction.CreateInvoiceFromOrder] = this.setupCreateInvoiceFromOrderPermissions(
      this.pageOrderConfig.userCanAccessRetailBilling$ ?? of(false),
    );
    this[OrderAction.SubmitForCustomerApproval] = this.setupSubmitForCustomerApprovalPermissions(
      orderStatus$,
      workflowStepOptions$,
      this.canManageOrders$,
    );
    this[OrderAction.DuplicateOrder] = this.setupDuplicateOrderPermissions(orderStatus$);
    this[OrderAction.SubscribeToUpdates] = this.setupSubscribeToUpdatesPermissions(orderStatus$);
    this[OrderAction.CancelOrder] = this.setupCancelOrderPermissions(this.canManageOrders$, orderStatus$);
    this[OrderAction.RequestToCancelOrder] = this.setupRequestToCancelOrderPermissions(
      this.canManageOrders$,
      orderStatus$,
    );
    this[OrderAction.ReviewCancellationRequest] = this.setupCanReviewCancellationRequest(
      this.canManageOrders$,
      orderStatus$,
    );
    this[OrderAction.ArchiveOrder] = this.setupArchiveOrderPermissions(this.canManageOrders$, orderStatus$);
    this[OrderAction.CancelOrder] = this.setupCancelOrderPermissions(this.canManageOrders$, orderStatus$);
    this[OrderAction.ActivateOrder] = this.setupActivateOrderPermissions(this.canManageOrders$, orderStatus$);
    this[OrderAction.ScheduleActivation] = this.setupScheduleActivationPermissions(this.canManageOrders$, orderStatus$);
    this[OrderAction.ReviewSubmittedOrder] = this.setupCanReviewSubmittedOrder(this.canManageOrders$, orderStatus$);
    this[OrderAction.SelectOrderCurrency] = this.setupCanSelectOrderCurrency(order$);
    this[OrderAction.AccessOnlySalespersonAutomations] = this.setupCanAccessOnlySalespersonAutomations(
      this.canManageOrders$,
    );
    this[OrderAction.UpdateInvalidContractStartDate] =
      this.setupUpdateInvalidContractStartDatePermissions(orderStatus$);
    this[OrderAction.RedirectToCompanyProfileAfterSubmitting] =
      this.setupCanRedirectToCompanyProfileAfterSubmittingOrder(this.canManageOrders$);
    this[OrderAction.CollectPaymentFromCustomer] = this.setupCanCollectPaymentFromCustomerPermissions(
      this.canManageOrders$,
      retailProvider$,
      hasFeatureAccessToUnifiedOrders$,
      retailStatus$,
      orderConfig$,
    );
    this[OrderAction.ChargeSMBOnOrderSubmission] = this.setupCanChargeSMBOnOrderSubmission(
      this.canManageOrders$,
      this.pageOrderConfig.userCanAccessRetailBilling$ ?? of(false),
      orderConfig$,
      retailProvider$,
      retailStatus$,
    );
    this[OrderAction.BypassAdminWorkflowAndActivate] = this.setupBypassAdminWorkflowAndActivate(
      order$,
      this.canManageOrders$,
    );
  }

  public CanView(feature: OrderFeature): Observable<boolean> {
    return this.getFeaturePermissions(feature).pipe(map((permissions) => permissions.view));
  }

  public CanEdit(feature: OrderFeature): Observable<boolean> {
    return this.getFeaturePermissions(feature).pipe(map((permissions) => permissions.edit));
  }

  public CanDoAction(action: OrderAction): Observable<boolean> {
    return this.getActionPermission(action);
  }

  private getFeaturePermissions(feature: OrderFeature): OrderFeaturePermissions[OrderFeature] {
    const permissions = this[feature];
    if (!permissions) {
      throw new Error(`Invalid feature: ${feature}`);
    }
    return permissions;
  }

  private getActionPermission(action: OrderAction): Observable<boolean> {
    const permission = this[action];
    if (!permission) {
      throw new Error(`Invalid action: ${action}`);
    }
    return this[action];
  }

  private setupOrderFormPermissions(
    orderStatus$: Observable<Status>,
    orderConfig$: Observable<ConfigInterface>,
    canManageOrders$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.OrderForms] {
    return combineLatest([orderStatus$, orderConfig$, canManageOrders$]).pipe(
      map(([orderStatus, config, canManageOrders]) => {
        return GetOrderFormPermissions({
          orderStatus,
          workflowStepOptions: config.workflowStepOptions,
          canManageOrders,
        });
      }),
    );
  }

  private setupCanSubmitOrderWithoutRequiredFields(
    canManageOrders$: Observable<boolean>,
    salesPersonConfig$: Observable<SalespersonOptionsInterface>,
  ): OrderActionPermissions[OrderAction.SubmitWithoutRequiredFields] {
    return combineLatest([canManageOrders$, salesPersonConfig$]).pipe(
      map(([canManageOrders, salespersonConfig]) => {
        return GetCanSubmitOrderWithoutRequiredFields({ canManageOrders, salespersonConfig });
      }),
    );
  }

  private setupTagPermissions(
    salespersonOptions$: Observable<SalespersonOptionsInterface>,
    canManageOrders$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.Tags] {
    return combineLatest([salespersonOptions$, canManageOrders$]).pipe(
      map(([salespersonOptions, canManageOrders]) => {
        return GetTagPermissions({ salespersonOptions, canManageOrders });
      }),
    );
  }

  private setupIgnoreAllErrorsOnOrderPermissions(
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.CreateInvoiceFromOrder] {
    return orderStatus$.pipe(map((orderStatus) => GetIgnoreAllErrorsOnOrderPermissions({ orderStatus })));
  }

  private setupCreateInvoiceFromOrderPermissions(
    userCanAccessRetailBilling$: Observable<boolean>,
  ): OrderActionPermissions[OrderAction.CreateInvoiceFromOrder] {
    return userCanAccessRetailBilling$.pipe(
      map((userCanAccessRetailBilling) => GetCreateInvoiceFromOrderPermissions({ userCanAccessRetailBilling })),
    );
  }

  private setupSubmitForCustomerApprovalPermissions(
    orderStatus$: Observable<Status>,
    workflowStepOptions$: Observable<WorkflowStepOptionsInterface>,
    canManageOrders$: Observable<boolean>,
  ): OrderActionPermissions[OrderAction.SubmitForCustomerApproval] {
    return combineLatest([orderStatus$, workflowStepOptions$, canManageOrders$]).pipe(
      map(([orderStatus, workflowStepOptions, canManageOrders]) => {
        return GetSubmitForCustomerApprovalPermissions({ orderStatus, workflowStepOptions, canManageOrders });
      }),
    );
  }

  private setupEditOrderContentsPermissions(
    orderStatus$: Observable<Status>,
    canManageOrders$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.OrderContents] {
    return combineLatest([orderStatus$, canManageOrders$]).pipe(
      map(([orderStatus, canManageOrders]) => GetEditOrderContentsPermissions({ orderStatus, canManageOrders })),
    );
  }

  private setupDuplicateOrderPermissions(
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.DuplicateOrder] {
    return orderStatus$.pipe(map((orderStatus) => GetDuplicateOrderPermissions({ orderStatus })));
  }

  private setupSubscribeToUpdatesPermissions(
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.SubscribeToUpdates] {
    return orderStatus$.pipe(map((orderStatus) => GetSubscribeToUpdatesPermissions({ orderStatus })));
  }

  private setupCancelOrderPermissions(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.CancelOrder] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => GetCancelOrderPermissions({ canManageOrders, orderStatus })),
    );
  }

  private setupRequestToCancelOrderPermissions(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.RequestToCancelOrder] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => GetRequestToCancelOrderPermissions({ canManageOrders, orderStatus })),
    );
  }

  private setupCanReviewCancellationRequest(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ReviewCancellationRequest] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => CanReviewCancellationRequest({ canManageOrders, orderStatus })),
    );
  }

  private setupArchiveOrderPermissions(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ArchiveOrder] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => GetArchiveOrderPermissions({ canManageOrders, orderStatus })),
    );
  }

  private setupActivateOrderPermissions(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ActivateOrder] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => GetActivateOrderPermissions({ canManageOrders, orderStatus })),
    );
  }

  private setupScheduleActivationPermissions(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ScheduleActivation] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => GetScheduleActivationPermissions({ canManageOrders, orderStatus })),
    );
  }

  private setupCanReviewSubmittedOrder(
    canManageOrders$: Observable<boolean>,
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ScheduleActivation] {
    return combineLatest([canManageOrders$, orderStatus$]).pipe(
      map(([canManageOrders, orderStatus]) => CanReviewSubmittedOrder({ canManageOrders, orderStatus })),
    );
  }

  private setupIncludeInInvoicesTogglePermissions(
    canManageOrders$: Observable<boolean>,
    autoGenerateRetailSubscriptions$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.Invoices] {
    return combineLatest([canManageOrders$, autoGenerateRetailSubscriptions$]).pipe(
      map(([canManageOrders, autoGenerateRetailSubscriptions]) =>
        GetInvoicePermissions({ canManageOrders, autoGenerateRetailSubscriptions }),
      ),
    );
  }

  private setupFulfillmentStatusesPermissions(
    orderStatus$: Observable<Status>,
  ): OrderFeaturePermissions[OrderFeature.FulfillmentStatuses] {
    return orderStatus$.pipe(map((orderStatus) => GetFulfillmentStatusesPermissions(orderStatus)));
  }

  private setupCreateOrderDetailsPermissions(
    orderStatus$: Observable<Status>,
    canManageOrders$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.CreateOrderDetails] {
    return combineLatest([orderStatus$, canManageOrders$]).pipe(
      map(([orderStatus, canManageOrders]: [Status, boolean]) => {
        return GetCreateOrderDetailsPermissions(orderStatus, canManageOrders);
      }),
    );
  }

  private setupCondensedOrderDetailsPermissions(
    orderStatus$: Observable<Status>,
  ): OrderFeaturePermissions[OrderFeature.CondensedOrderDetails] {
    return orderStatus$.pipe(
      map((orderStatus) => {
        return GetCondensedOrderDetailsPermissions(orderStatus);
      }),
    );
  }

  private setupActiveItemsPermissions(
    orderStatus$: Observable<Status>,
  ): OrderFeaturePermissions[OrderFeature.ActiveItems] {
    return orderStatus$.pipe(
      map((orderStatus) => {
        return GetActiveItemsPermissions(orderStatus);
      }),
    );
  }

  private setupBusinessHeaderPermissions(
    orderStatus$: Observable<Status>,
  ): OrderFeaturePermissions[OrderFeature.BusinessHeader] {
    return orderStatus$.pipe(
      map((orderStatus) => {
        return GetBusinessHeaderPermissions(orderStatus);
      }),
    );
  }

  private setupCanSelectOrderCurrency(
    order$: Observable<Order>,
  ): OrderActionPermissions[OrderAction.SelectOrderCurrency] {
    return order$.pipe(map((order) => order.partnerId === 'VMF'));
  }

  private setupEditContractStartAndDurationPermissions(
    orderStatus$: Observable<Status>,
    canManageOrders$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.ContractStartAndDuration] {
    return combineLatest([orderStatus$, canManageOrders$]).pipe(
      map(([orderStatus, canManageOrders]) =>
        GetEditOrderContractStartAndDurationPermissions({ orderStatus, canManageOrders }),
      ),
    );
  }

  private setupCanAccessOnlySalespersonAutomations(
    canManageOrders$: Observable<boolean>,
  ): OrderActionPermissions[OrderAction.AccessOnlySalespersonAutomations] {
    return canManageOrders$.pipe(
      map((canManageOrders) => {
        return !canManageOrders;
      }),
    );
  }
  private setupCanRedirectToCompanyProfileAfterSubmittingOrder(
    canManageOrders$: Observable<boolean>,
  ): OrderActionPermissions[OrderAction.RedirectToCompanyProfileAfterSubmitting] {
    return canManageOrders$.pipe(
      map((canManageOrders) => {
        return !canManageOrders;
      }),
    );
  }

  private setupUpdateInvalidContractStartDatePermissions(
    orderStatus$: Observable<Status>,
  ): OrderActionPermissions[OrderAction.ScheduleActivation] {
    return orderStatus$.pipe(map((orderStatus) => CanUpdateInvalidContractStartDatePermissions(orderStatus)));
  }

  private setupInvalidLineItemsBannerPermissions(
    orderStatus$: Observable<Status>,
  ): OrderFeaturePermissions[OrderFeature.InvalidLineItemsBanner] {
    return orderStatus$.pipe(
      map((orderStatus) => {
        return GetInvalidLineItemsBannerPermissions(orderStatus);
      }),
    );
  }

  private setupWholesalePriceColumnPermissions(
    canManageMarketplace$: Observable<boolean>,
  ): OrderFeaturePermissions[OrderFeature.WholesalePriceColumn] {
    return canManageMarketplace$.pipe(
      map((canManageMarketplace) => {
        return GetWholesaleColumnPermissions(canManageMarketplace);
      }),
    );
  }

  private setupCanCollectPaymentFromCustomerPermissions(
    canManageOrders$: Observable<boolean>,
    retailProvider$: Observable<RetailProvider>,
    hasAccessToUnifiedOrders$: Observable<boolean>,
    retailStatus$: Observable<RetailStatus>,
    orderConfig$: Observable<ConfigInterface>,
  ): OrderActionPermissions[OrderAction.CollectPaymentFromCustomer] {
    return combineLatest([
      canManageOrders$,
      retailProvider$,
      hasAccessToUnifiedOrders$,
      retailStatus$,
      orderConfig$,
    ]).pipe(
      map(([canManageOrders, retailProvider, hasAccessToUnifiedOrders, retailStatus, orderConfig]) => {
        return (
          (canManageOrders || orderConfig?.workflowStepOptions?.allowCollectPaymentFromCustomer) &&
          !!retailProvider &&
          hasAccessToUnifiedOrders &&
          retailStatus?.canAcceptPayment
        );
      }),
    );
  }

  private setupCanChargeSMBOnOrderSubmission(
    canManageOrders$: Observable<boolean>,
    userCanAccessRetailBilling$: Observable<boolean>,
    orderConfig$: Observable<ConfigInterface>,
    retailProvider$: Observable<RetailProvider>,
    retailStatus$: Observable<RetailStatus>,
  ): OrderActionPermissions[OrderAction.ChargeSMBOnOrderSubmission] {
    return combineLatest([
      canManageOrders$,
      userCanAccessRetailBilling$,
      orderConfig$,
      retailProvider$,
      retailStatus$,
    ]).pipe(
      map(([canManageOrders, canAccessRetailBilling, orderConfig, retailProvider, retailStatus]) =>
        GetCanChargeSMBOnOrderSubmissionPermissions({
          canManageOrders,
          canAccessRetailBilling,
          orderConfig,
          retailProvider,
          retailStatus,
        }),
      ),
    );
  }
  private setupBypassAdminWorkflowAndActivate(
    order$: Observable<Order>,
    canManageOrders$: Observable<boolean>,
  ): OrderActionPermissions[OrderAction.BypassAdminWorkflowAndActivate] {
    return combineLatest([order$, canManageOrders$]).pipe(
      map(([order, canManageOrders]) => GetCanBypassAdminWorkflowAndActivate(order?.partnerId, canManageOrders)),
    );
  }
}
