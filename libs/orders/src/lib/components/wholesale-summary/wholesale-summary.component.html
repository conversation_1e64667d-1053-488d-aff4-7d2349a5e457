@if (wholesaleSummary$ | async; as wholesaleSummary) {
  <mat-expansion-panel [expanded]="true">
    <mat-expansion-panel-header>
      <mat-panel-title> {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.WHOLESALE_SUMMARY.TITLE' | translate }}</mat-panel-title>
    </mat-expansion-panel-header>
    <div class="pricing-row due-now">
      <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.DUE_NOW' | translate }}</span>
      <span class="pricing">
        <billing-ui-simple-price-display
          [price]="wholesaleSummary.firstPayment"
          [currencyCode]="wholesaleSummary.currencyCode"
        ></billing-ui-simple-price-display>
      </span>
    </div>

    @if (futurePaymentDisplayItems$ | async; as futurePaymentLineItems) {
      @if (futurePaymentLineItems.lineItems && futurePaymentLineItems.lineItems.length > 0) {
        <orders-future-payments-display
          [futurePayments]="futurePaymentLineItems.lineItems"
          [currencyCode]="futurePaymentLineItems.currencyCode"
        />
      }
    }
  </mat-expansion-panel>
}
