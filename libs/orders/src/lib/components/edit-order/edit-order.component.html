@if (
  {
    order: order$ | async,
    invalidForms: invalidForms$ | async,
    unifiedOrderPageEnabled: unifiedOrderPageEnabled$ | async,
    workOrderPersona: workOrderPersona$ | async,
    partnerCurrency: partnerCurrency$ | async,
  };
  as context
) {
  @if (context.unifiedOrderPageEnabled) {
    <mat-tab-group
      [selectedIndex]="selectedTabIndex$ | async"
      (selectedIndexChange)="indexChanged($event)"
      data-testid="order-tabs"
    >
      <mat-tab [label]="'LIB_ORDERS.COMMON.ORDERS.OVERVIEW_TAB_LABEL' | translate">
        <div class="tab-body">
          <ng-container *ngTemplateOutlet="editOrderContent"></ng-container>
        </div>
      </mat-tab>
      <mat-tab [disabled]="(hasOrderForms$ | async) !== true">
        <ng-template mat-tab-label>
          @if (context.invalidForms) {
            <mat-icon class="warning-icon">warning</mat-icon>
          }
          {{ 'LIB_ORDERS.COMMON.ORDERS.ORDER_FORMS_LABEL' | translate }}
        </ng-template>
        <div class="tab-body">
          <ng-container
            [ngTemplateOutlet]="orderFormContent"
            [ngTemplateOutletContext]="{ order: context.order }"
          ></ng-container>
        </div>
      </mat-tab>
      <mat-tab [disabled]="!(workOrderDetails.workOrders$ | async).length">
        <ng-template mat-tab-label>
          @if (workOrderDetails.detailsNeeded$ | async) {
            <mat-icon class="warning-icon">warning</mat-icon>
          }
          {{ 'LIB_ORDERS.COMMON.ORDERS.FULFILLMENT_FORMS_LABEL' | translate }}
        </ng-template>
        <div class="tab-body">
          <work-order-details
            #workOrderDetails
            class="work-orders"
            [partnerId]="business.externalIdentifiers.partnerId"
            [marketId]="business.externalIdentifiers.marketId"
            [businessId]="business.accountGroupId"
            [salesOrderId]="context.order.orderId"
            [users]="users"
            [persona]="context.workOrderPersona"
            [hidePageWrapper]="true"
            [allowUserManagement]="true"
          ></work-order-details>
        </div>
      </mat-tab>
    </mat-tab-group>
  } @else {
    <ng-container *ngTemplateOutlet="editOrderContent"></ng-container>
  }
  @if ((selectedTabIndex$ | async) === 0 && (showFooter$ | async)) {
    <glxy-sticky-footer data-testid="order-footer">
      <div>
        @if ((hasOrderForms$ | async) && invalidActivateAttempt) {
          <div class="submit-form-error-message">
            *
            {{ 'LIB_ORDERS.SALES_ORDERS.ERRORS.FILL_OUT_REQUIRED_FORMS' | translate }}
          </div>
        }
        @if (canScheduleActivation$ | async) {
          <div class="confirm-activate">
            <mat-checkbox color="primary" [(ngModel)]="confirmationCheckbox" />
            <span>
              {{
                'LIB_ORDERS.SALES_ORDERS.I_UNDERSTAND_I_WILL_BE_BILLED'
                  | translate: { businessAppName: 'COMMON.BUSINESS_APP' | translate }
              }}
            </span>
          </div>
        }
        <div class="sticky-footer-actions">
          @if ((canCancelOrder$ | async) === true) {
            <button
              mat-stroked-button
              class="cancel-button"
              color="warn"
              [disabled]="processingAction()"
              (click)="onRequestToCancel()"
            >
              {{ 'LIB_ORDERS.COMMON.ORDERS.REQUEST_TO_CANCEL_ORDER' | translate }}
            </button>
          }
          @if (canScheduleActivation$ | async) {
            <button
              mat-flat-button
              color="primary"
              [disabled]="!confirmationCheckbox || processingAction()"
              (click)="onScheduleBtnPressed(context.order)"
              data-testid="schedule-activation-button"
            >
              {{ 'LIB_ORDERS.COMMON.ACTION_LABELS.SCHEDULE_ACTIVATION' | translate }}
            </button>
          }
          @if (canActivateOrder$ | async) {
            <button
              mat-button
              [ngClass]="
                context.order.status === Scheduled
                  ? 'mat-mdc-unelevated-button'
                  : 'mdc-button--outlined mat-mdc-outlined-button'
              "
              [color]="context.order.status === Scheduled ? 'primary' : null"
              [disabled]="
                context.order.status === Scheduled ? processingAction() : !confirmationCheckbox || processingAction()
              "
              (click)="onActivateBtnPressed(context.order)"
              data-testid="activate-order-button"
            >
              {{ 'LIB_ORDERS.COMMON.ACTION_LABELS.ACTIVATE_NOW' | translate }}
            </button>
          }
          @if ((canReviewCancellationRequest$ | async) === true) {
            <button mat-flat-button color="warn" [disabled]="processingAction()" (click)="approveCancellationRequest()">
              {{ 'LIB_ORDERS.COMMON.ORDERS.APPROVE_CANCELLATION_REQUEST' | translate }}
            </button>
            <button mat-stroked-button [disabled]="processingAction()" (click)="onDeclineCancellationRequest()">
              {{ 'LIB_ORDERS.COMMON.ORDERS.DECLINE_ORDER_CANCELLATION' | translate }}
            </button>
          }
          @if ((canReviewSubmittedOrder$ | async) === true) {
            <button
              mat-flat-button
              color="primary"
              [disabled]="processingAction()"
              (click)="approveSubmittedOrder()"
              data-testid="approve-submitted-order-button"
            >
              {{ 'LIB_ORDERS.COMMON.ORDERS.APPROVE_SUBMITTED_ORDER' | translate }}
            </button>
            <button
              mat-stroked-button
              [disabled]="processingAction()"
              (click)="declineSubmittedOrder()"
              data-testid="decline-submitted-order-button"
            >
              {{ 'LIB_ORDERS.COMMON.ORDERS.DECLINE_SUBMITTED_ORDER' | translate }}
            </button>
          }
          @if ((canArchiveOrder$ | async) === true) {
            <button mat-button class="archive-button" [disabled]="processingAction()" (click)="onArchive()">
              <mat-icon>archive</mat-icon>
              {{ 'LIB_ORDERS.COMMON.ORDERS.ARCHIVE_ORDER' | translate }}
            </button>
          }
        </div>
      </div>
    </glxy-sticky-footer>
  }

  <ng-template #editOrderContent>
    @if (this.invalidOrderLineItems$ | async; as invalidOrderLineItems) {
      @if (
        context.unifiedOrderPageEnabled &&
        (this.canViewInvalidLineItemsBanner$ | async) &&
        invalidOrderLineItems?.length > 0
      ) {
        <orders-line-item-validation-banner />
      }
    }
    @if ((canSubmitWithoutRequiredFields$ | async) !== true && context.invalidForms) {
      <glxy-alert
        type="warning"
        [showAction]="true"
        actionTitle="View"
        (actionClick)="openOrderFormTab()"
        data-testid="invalid-forms-alert"
      >
        <span [innerHtml]="'LIB_ORDERS.COMMON.ORDERS.ORDER_FORMS_MISSING_INFORMATION' | translate"></span>
      </glxy-alert>
    }
    <form [formGroup]="orderDetailsFormGroup" *ngIf="context.order as order">
      <div class="edit-order-container">
        <glxy-alert
          *ngIf="this.shouldShowDetailsNeededBanner$ | async"
          type="warning"
          [showAction]="true"
          actionTitle="{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.VIEW_FORMS' | translate }}"
          [actionRouterLink]="'/sales-orders/' + order.businessId + '/work-order/' + order.orderId"
          data-action="view-forms"
          data-testid="work-order-alert"
        >
          <strong>
            {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.DETAILS_NEEDED_BANNER' | translate }}
          </strong>
          <span>-</span>
          {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.DETAILS_NEEDED_BANNER_DESCRIPTION' | translate }}
        </glxy-alert>
        <div class="row row-gutters">
          <div class="left-col col col-xs-12 col-sm-12 col-md-12 col-lg-9">
            @if (context.unifiedOrderPageEnabled && (canViewBusinessHeader$ | async) === true) {
              <orders-business-header [orderId]="order.orderId" />
            }
            @if ((canViewCreateOrderDetails$ | async) === true) {
              <orders-create-order-details
                [business]="business"
                [formGroup]="orderDetailsFormGroup"
                [orderId]="order.orderId"
                [contractStartDate]="order.requestedActivation"
                [contractDuration]="order.contractDuration"
                [viewOnly]="(canEditOrderContents$ | async) === false"
              />
            }
            @if (context.unifiedOrderPageEnabled) {
              @if ((canEditOrderCurrency$ | async) && (canEditOrderContents$ | async)) {
                <orders-currency-selector
                  (currencySelected)="selectOrderCurrency($event)"
                  [customCurrencies]="currencySelectorOptions"
                  [initialCurrency]="currencyOverride$ | async"
                />
              }

              @if (order.status !== Status.DRAFTED) {
                <orders-order-activity [order$]="order$" />
                @if (fulfillmentStatusCardData$ | async; as statusCardData) {
                  <orders-fulfillment-status-card
                    [productActivations]="statusCardData.productActivations"
                    [workOrders]="statusCardData.workOrders"
                    [partnerId]="order.partnerId"
                    [businessId]="order.businessId"
                    [marketId]="order.marketId"
                    [orderId]="order.orderId"
                    [persona]="context.workOrderPersona"
                    (workOrdersLinkClicked)="navigateToWorkOrdersTab()"
                  />
                }
              }
            }
            <orders-line-items
              #lineItems
              [business]="business"
              [orderMarketId]="order.marketId"
              [specifiedLineItems]="order.lineItems"
              [editable]="true"
              [startEditing]="true"
              [currencyOverride]="currencyOverride$ | async"
              [viewOnly]="(canEditOrderContents$ | async) === false"
              [canEditInvoices]="canEditInvoices$ | async"
              [canSeeWholesaleColumn]="canSeeWholesaleColumn$ | async"
              (lineItemsUpdated)="saveUpdatedLineItems()"
              (updateWholesaleSummary)="saveUpdatedWholesaleSummary($event)"
            />

            @if (context.unifiedOrderPageEnabled && orderConfig$ | async; as orderConfig) {
              <orders-retail-summary
                *ngIf="retailSummary$ | async as summary"
                [summary]="summary"
                [accountGroupId]="order.businessId"
                [partnerId]="order.partnerId"
                [orderId]="order.orderId"
                [canChargeOrder]="canChargeSmbOnOrderSubmission$ | async"
                [orderCustomerRecipient]="order.customerRecipient"
                [orderPaymentMethodToken]="order.paymentMethodToken"
                [canEditPaymentMethod]="canEditOrderContents$ | async"
                [lineItems]="order.lineItems"
                [contractStartDate]="order.requestedActivation"
                [taxOptions]="order.taxOptions"
                (orderCharged)="orderCharged($event)"
              ></orders-retail-summary>

              @if (canManageMarketplace$ | async) {
                <orders-wholesale-summary [wholesaleSummary]="wholesaleSummary$ | async" />
              }
              <div *ngIf="projectTrackerInfo$ | async as trackers" class="order-section">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>
                      {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.FULFILLMENT.PROJECTS' | translate }}
                    </mat-card-title>
                  </mat-card-header>
                  <project-tracker-simple
                    *ngFor="let tracker of trackers"
                    [projectTitle]="tracker.projectTitle"
                    [projectIdentity]="tracker.projectIdentity"
                    [subtasks]="tracker.subtasks"
                    [hideAskQuestion]="true"
                  ></project-tracker-simple>
                </mat-card>
              </div>
            }

            @if (!context.unifiedOrderPageEnabled) {
              <ng-container
                [ngTemplateOutlet]="orderFormContent"
                [ngTemplateOutletContext]="{ order: order }"
              ></ng-container>
            }
          </div>
          <div class="right-col col col-xs-12 col-sm-12 col-md-12 col-lg-3 no-padding-top">
            <ng-container>
              <mat-accordion multi="true" class="sidebar">
                @if (context.unifiedOrderPageEnabled && (canViewCondensedOrderDetails$ | async) === true) {
                  <mat-expansion-panel expanded="true" class="panel">
                    <mat-expansion-panel-header>
                      <div class="order-details-title">
                        <mat-panel-title>{{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.TITLE' | translate }}</mat-panel-title>
                      </div>
                    </mat-expansion-panel-header>
                    <orders-condensed-order-details />
                  </mat-expansion-panel>
                }
                @if (context.unifiedOrderPageEnabled && (canViewActiveItems$ | async) === true) {
                  <mat-expansion-panel [expanded]="order.status === Status.DRAFTED" class="active-items">
                    <mat-expansion-panel-header>
                      <mat-panel-title>{{ 'LIB_ORDERS.SALES_ORDERS.ACTIVE_ITEMS' | translate }}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <inventory-ui-active-items
                      [partnerId]="order.partnerId"
                      [businessId]="order.businessId"
                      [marketId]="order.marketId"
                      [hideTitle]="true"
                    />
                  </mat-expansion-panel>
                }
                @if (context.unifiedOrderPageEnabled) {
                  @if (
                    { products: variablePricesMap$ | async, packages: variablePackagePricesMap$ | async };
                    as priceMaps
                  ) {
                    @if ((priceMaps.products?.size ?? 0) > 0 || (priceMaps.packages?.size ?? 0) > 0) {
                      <mat-expansion-panel expanded="true" class="panel">
                        <mat-expansion-panel-header>
                          <mat-panel-title>
                            {{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.VARIABLE_PRICES_TITLE' | translate }}
                          </mat-panel-title>
                        </mat-expansion-panel-header>
                        <orders-variable-prices
                          #variablePricesComponent
                          [variablePricesMap]="priceMaps.products"
                          [variablePackagePricesMap]="priceMaps.packages"
                          [partnerCurrency]="(currencyOverride$ | async) || context.partnerCurrency"
                          (updateData)="variablePriceChanged($event)"
                        />
                      </mat-expansion-panel>
                    }
                  }
                }

                @if (!hideAttachmentsSection) {
                  <mat-expansion-panel class="panel" [expanded]="!!order.attachments">
                    <mat-expansion-panel-header>
                      <mat-panel-title>{{
                        'LIB_ORDERS.COMMON.ORDER_DETAILS.ADMINISTRATOR_ATTACHMENTS' | translate
                      }}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <orders-admin-attachments
                      [ngClass]="{ 'non-printable': !order.attachments }"
                      [files]="order.attachments"
                      [orderId]="order.orderId"
                      [businessId]="order.businessId"
                      [uploadUrl]="attachmentsUploadUrl"
                    />
                  </mat-expansion-panel>

                  <mat-expansion-panel class="panel" [expanded]="!!order.customerAttachments">
                    <mat-expansion-panel-header>
                      <mat-panel-title>{{
                        'LIB_ORDERS.COMMON.ORDER_DETAILS.CUSTOMER_ATTACHMENTS' | translate
                      }}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <orders-customer-attachments
                      [ngClass]="{ 'non-printable': !order.customerAttachments }"
                      [files]="order.customerAttachments"
                      [orderId]="order.orderId"
                      [businessId]="order.businessId"
                      [uploadUrl]="attachmentsUploadUrl"
                    />
                  </mat-expansion-panel>
                }
                <mat-expansion-panel class="panel" [expanded]="!!order.notes">
                  <mat-expansion-panel-header>
                    <mat-panel-title>{{
                      'LIB_ORDERS.COMMON.ORDER_DETAILS.ADMINISTRATIVE_NOTES_TITLE' | translate
                    }}</mat-panel-title>
                  </mat-expansion-panel-header>
                  <orders-notes
                    *ngIf="!hideNotesSections"
                    [ngClass]="{ 'non-printable': !order.notes }"
                    [existingNotes]="order.notes"
                    [orderId]="order.orderId"
                    [businessId]="order.businessId"
                    [noteType]="'administrator'"
                  />
                </mat-expansion-panel>

                <mat-expansion-panel class="panel" [expanded]="!!order.customerNotes">
                  <mat-expansion-panel-header>
                    <mat-panel-title>{{
                      'LIB_ORDERS.COMMON.ORDER_DETAILS.CUSTOMER_NOTES_TITLE' | translate
                    }}</mat-panel-title>
                  </mat-expansion-panel-header>
                  <orders-notes
                    *ngIf="!hideNotesSections"
                    [ngClass]="{ 'non-printable': !order.customerNotes }"
                    [existingNotes]="order.customerNotes"
                    [orderId]="order.orderId"
                    [businessId]="order.businessId"
                    [noteType]="'customer'"
                  />
                </mat-expansion-panel>

                @if (
                  {
                    canView: canViewTags$ | async,
                    canEdit: canEditTags$ | async,
                  };
                  as tags
                ) {
                  <mat-expansion-panel class="panel" [expanded]="!!order.tags">
                    <mat-expansion-panel-header>
                      <mat-panel-title>{{ 'LIB_ORDERS.COMMON.ORDER_DETAILS.TAGS' | translate }}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <orders-tags
                      *ngIf="tags.canView"
                      [ngClass]="{ 'non-printable': !order.tags }"
                      [existingOrder]="order"
                      [viewOnly]="!tags.canEdit"
                    />
                  </mat-expansion-panel>
                }
              </mat-accordion>
            </ng-container>
          </div>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template #orderFormContent let-order="order">
    <ng-container class="print-page-break" *ngIf="productIds">
      <business-product-activation-prereq-form
        #productPrereqForm
        [businessId]="business.accountGroupId"
        [expanded]="true"
        [productIds]="productIds"
        [orderFormOptions]="orderFormOptions$ | async"
      />
    </ng-container>
    <ng-container class="print-page-break" *ngIf="orderConfig$ | async as orderConfig">
      @if (orderFormOptions$ | async; as orderFormOptions) {
        <orders-form
          #orderForm
          [order]="order"
          [orderConfig]="orderConfig"
          [userOptions]="users"
          [orderFormOptions]="orderFormOptions"
          [fileUploadUrl]="fileUploadUrl"
          [showSelfSaveButton]="!hideOrderFormSaveButton && !orderFormOptions.readOnly"
          [parentForm]="orderFormGroup"
          [partnerId]="order.partnerId"
          [businessId]="order.businessId"
        />
      }
    </ng-container>
  </ng-template>
}
