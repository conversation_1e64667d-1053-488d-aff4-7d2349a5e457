<glxy-confirmation-body>
  <mat-icon glxyConfirmationIcon class="icon" inline="true" color="primary">info</mat-icon>

  <glxy-confirmation-title>{{
    'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.PROCESS_ORDER' | translate
  }}</glxy-confirmation-title>
  <glxy-confirmation-custom-content>
    <div>{{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.ON_CONTRACT_START' | translate }}</div>
    <mat-checkbox class="confirm-checkbox" [(ngModel)]="confirmProcessOrder">{{
      'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.CONFIRM_BILLED' | translate
    }}</mat-checkbox>
  </glxy-confirmation-custom-content>
</glxy-confirmation-body>

<glxy-confirmation-actions>
  <glxy-confirmation-primary-actions>
    <button mat-stroked-button matDialogClose (click)="onCancel()">
      {{ 'LIB_ORDERS.COMMON.ORDERS.CANCEL' | translate }}
    </button>

    <button mat-flat-button color="primary" [disabled]="!confirmProcessOrder || loading" (click)="onConfirm()">
      {{ 'LIB_ORDERS.COMMON.ORDERS.DIALOGS.COLLECT_PAYMENT.CONFIRM' | translate }}
    </button>
  </glxy-confirmation-primary-actions>
</glxy-confirmation-actions>
