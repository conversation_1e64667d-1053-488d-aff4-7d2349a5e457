import { Component, Injector, ViewEncapsulation } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { RetailPaymentData, RetailPaymentService } from '../../../../core/retail-payment.service';
import { Observable } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { CustomerRecipientInterface, Order } from '@vendasta/sales-orders';
import { OrdersSelectPaymentMethodDialogComponent } from '../../../retail-summary/select-payment-method/orders-select-payment-method-dialog/orders-select-payment-method-dialog.component';
import { SelectPaymentMethodDialogData } from '../../../retail-summary/select-payment-method/payment-method';
import { OrderStoreService } from '../../../../core/orders.service';
import { PaymentMethodDisplayComponent } from '@vendasta/smb-invoicing';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProcessOrderDialogComponent } from './process-order-dialog/process-order-dialog.component';
import { OrderAction } from '../../../../core/permissions/permissions';
import { OrderPermissionsService } from '../../../../core/permissions/permissions.service';
import { TranslateModule } from '@ngx-translate/core';

export enum CollectPaymentDialogResult {
  Submitted = 'Submitted',
  Processed = 'Processed',
  Charged = 'Charged',
}

@Component({
  selector: 'orders-collect-payment-dialog',
  templateUrl: './collect-payment-dialog.component.html',
  imports: [
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    AsyncPipe,
    PaymentMethodDisplayComponent,
    MatProgressSpinner,
    TranslateModule,
  ],
  styleUrls: ['./collect-payment-dialog.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class CollectPaymentDialogComponent {
  retailPaymentData$: Observable<RetailPaymentData>;
  order$: Observable<Order>;
  loadingOrder$: Observable<boolean>;
  canCollectPaymentFromCustomer$: Observable<boolean>;

  constructor(
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<CollectPaymentDialogComponent>,
    private readonly retailPaymentService: RetailPaymentService,
    private readonly orderStoreService: OrderStoreService,
    private readonly snackbarService: SnackbarService,
    private readonly injector: Injector,
    private readonly permissionsService: OrderPermissionsService,
  ) {
    this.retailPaymentData$ = this.retailPaymentService.retailPaymentData$;
    this.order$ = this.orderStoreService.order$;
    this.loadingOrder$ = this.orderStoreService.loadingOrder$;
    this.canCollectPaymentFromCustomer$ = this.permissionsService.CanDoAction(OrderAction.CollectPaymentFromCustomer);
  }

  addPaymentMethod(retailPaymentData: RetailPaymentData, order: Order): void {
    this.dialog
      .open(OrdersSelectPaymentMethodDialogComponent, {
        width: '600px',
        data: {
          accountId: retailPaymentData.stripeConnectId,
          merchantId: order.partnerId,
          customerId: order.businessId,
          stripeKey: retailPaymentData.stripeKey,
          paymentMethods: retailPaymentData.paymentMethodMap,
          submitDialogText: 'Save',
          defaultPaymentMethod: retailPaymentData.defaultPaymentMethod?.details?.id,
          orderCustomerRecipient: order.customerRecipient as CustomerRecipientInterface,
          defaultCustomerRecipientId: retailPaymentData?.defaultCustomerRecipientId,
        } as SelectPaymentMethodDialogData,
        autoFocus: false,
        injector: Injector.create({
          providers: [{ provide: OrderStoreService, useValue: this.orderStoreService }],
          parent: this.injector,
        }),
      })
      .afterClosed()
      .subscribe((response) => {
        if (response) {
          return this.retailPaymentService.selectPaymentMethod(response);
        }
      });
  }

  onCharge(retailPaymentData: RetailPaymentData, order: Order): void {
    this.dialogRef.addPanelClass('hidden-dialog');
    this.retailPaymentService.openChargeConfirmationDialog(retailPaymentData, order).subscribe({
      next: (submitted) => {
        if (submitted) {
          this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
          this.dialogRef.close(CollectPaymentDialogResult.Charged);
        } else {
          this.dialogRef.removePanelClass('hidden-dialog');
        }
      },
      error: (err) => {
        this.dialogRef.removePanelClass('hidden-dialog');
        if (err?.message === 'Order has no lineItem') {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ORDERS.ERROR_AT_LEAST_ONE_LINE_ITEM');
        } else {
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
        }
      },
    });
  }

  sendForCustomerApproval() {
    this.dialogRef.close(CollectPaymentDialogResult.Submitted);
  }

  processOrder(order: Order): void {
    this.dialogRef.addPanelClass('hidden-dialog');
    this.dialog
      .open(ProcessOrderDialogComponent, {
        width: '600px',
        data: { order },
        autoFocus: false,
        injector: Injector.create({
          providers: [{ provide: OrderStoreService, useValue: this.orderStoreService }],
          parent: this.injector,
        }),
      })
      .afterClosed()
      .subscribe({
        next: (submitted) => {
          if (submitted) {
            this.snackbarService.openSuccessSnack('LIB_ORDERS.SALES_ORDERS.SUCCESS.ORDER_UPDATED');
            this.dialogRef.close(CollectPaymentDialogResult.Processed);
          } else {
            this.dialogRef.removePanelClass('hidden-dialog');
          }
        },
      });
  }
}
