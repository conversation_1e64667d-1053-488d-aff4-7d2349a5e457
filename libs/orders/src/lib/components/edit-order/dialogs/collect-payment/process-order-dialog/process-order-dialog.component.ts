import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Order, SalesOrdersService } from '@vendasta/sales-orders';
import { take } from 'rxjs';

export interface ProcessOrderDialogComponentData {
  order: Order;
}

@Component({
  selector: 'orders-process-order-dialog',
  templateUrl: './process-order-dialog.component.html',
  styleUrls: ['./process-order-dialog.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    MatIconModule,
    GalaxyConfirmationModalModule,
  ],
})
export class ProcessOrderDialogComponent {
  confirmProcessOrder = false;

  loading = false;

  constructor(
    private dialogRef: MatDialogRef<ProcessOrderDialogComponent>,
    private salesOrdersService: SalesOrdersService,
    @Inject(MAT_DIALOG_DATA) public data: ProcessOrderDialogComponentData,
    private readonly snackbarService: SnackbarService,
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.loading = true;
    this.salesOrdersService
      .scheduleActivation(
        this.data.order.orderId,
        this.data.order.businessId,
        this.data.order.customFields,
        this.data.order.commonFields,
        this.data.order.extraFields,
      )
      .pipe(take(1))
      .subscribe({
        next: () => {
          this.loading = false;
          this.dialogRef.close(true);
        },
        error: () => {
          this.loading = false;
          this.dialogRef.close(false);
          this.snackbarService.openErrorSnack('LIB_ORDERS.COMMON.ERRORS.GENERIC_ERROR');
        },
      });
  }
}
