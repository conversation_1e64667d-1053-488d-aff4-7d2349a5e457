import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Order, SalesOrdersService } from '@vendasta/sales-orders';
import { BehaviorSubject, Observable, of, ReplaySubject, Subscription } from 'rxjs';
import { debounceTime, map, switchMap } from 'rxjs/operators';
import { NewOrderDetails } from './tags.interface';

@Component({
  selector: 'orders-tags',
  templateUrl: 'tags.component.html',
  styleUrls: ['tags.component.scss'],
  standalone: false,
})
export class TagsComponent implements OnInit, OnDestroy {
  // Exactly one of the following inputs is required:
  // newOrderDetails is to be used when there isn't an existing order to pull info from (eg. partner id and market id)
  @Input() newOrderDetails: NewOrderDetails;
  // otherwise the existingOrder can be passed in which has all the info this component needs
  @Input() existingOrder: Order;
  @Input() viewOnly = false;
  @Input() createMode = false;

  @ViewChild('tagsInput') tagsInput: ElementRef<HTMLInputElement>;
  @ViewChild('auto') matAutocomplete: MatAutocomplete;

  partnerId: string;
  marketId: string;

  editingTags: string[] = [];
  tagOptions$: Observable<string[]>;
  updatingTags = false;

  separatorKeysCodes: number[] = [ENTER, COMMA];
  tagsCtrl = new UntypedFormControl();
  loadTags$$ = new BehaviorSubject<boolean>(true);
  newTag$$ = new ReplaySubject<string>(1);
  newTag$ = this.newTag$$.asObservable();
  filteredTags$: Observable<string[]>;
  filteredTags: string[];
  tags: string[] = [];
  availableTags: string[] = [];
  subscriptions: Subscription[] = [];

  constructor(
    private snack: SnackbarService,
    private salesOrderService: SalesOrdersService,
  ) {}

  ngOnInit(): void {
    if (this.newOrderDetails) {
      this.initNewOrderTags();
    } else {
      this.initExistingOrderTags();
    }
    this.tagOptions$ = this.loadTags$$.pipe(
      switchMap(() => {
        return this.salesOrderService.listTags(this.partnerId, { marketIds: [this.marketId] });
      }),
      map((resp) => {
        return resp.tags || [];
      }),
    );

    this.subscriptions.push(
      // init all tags dropdown
      this.tagOptions$.subscribe((tags) => {
        // if we have pre-selected tags
        this.availableTags = tags;
      }),
    );

    this.filteredTags$ = this.tagsCtrl.valueChanges.pipe(
      debounceTime(300),
      map((tag: string | null) => {
        if (!tag) {
          tag = '';
        }
        const filteredTags = this._filter(tag);
        // if no existing tags found, display the new entry as an option:
        if (filteredTags.length === 0) {
          this.newTag$$.next(tag);
          return [];
        }
        this.newTag$$.next('');
        return tag ? filteredTags : this.availableTags.slice();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  // initializes class variables with information provided via an existing order
  private initExistingOrderTags(): void {
    this.editingTags = this.existingOrder.tags;
    if (this.editingTags) {
      this.tags = this.editingTags;
    }
    this.partnerId = this.existingOrder.partnerId;
    this.marketId = this.existingOrder.marketId;
  }

  // initializes class variables with info provided via the details input
  private initNewOrderTags(): void {
    this.partnerId = this.newOrderDetails.partnerId;
    this.marketId = this.newOrderDetails.marketId;
  }

  getTags(): string[] {
    return this.tags || []; // the tag input may not have been toggled
  }

  removeTag(tag: string): void {
    const index = this.tags.findIndex((tagOption) => tagOption === tag);
    if (index >= 0) {
      this.tags.splice(index, 1);
      this.updateTags();
    }
  }

  onAddTag(tag: string): void {
    const index = this.tags.findIndex((tagOption) => tagOption === tag);
    if (index === -1) {
      this.tags.push(tag);
      this.updateTags();
    }
    this.tagsInput.nativeElement.value = '';
    this.tagsCtrl.setValue(null);
  }

  updateTags(): void {
    if (this.createMode) {
      // Skip if component is used in create order.
      return;
    }
    this.updatingTags = true;
    this.subscriptions.push(
      this.salesOrderService
        .updateTags(this.existingOrder.orderId, this.existingOrder.businessId, this.tags)
        .pipe(
          switchMap((order) => {
            return of({
              tags: order.tags || [],
            });
          }),
        )
        .subscribe({
          next: () => {
            this.updatingTags = false;
            this.loadTags$$.next(true);
          },
          error: () => {
            this.snack.openErrorSnack('LIB_ORDERS.COMMON.ORDER_DETAILS.TAGS_UPDATE_ERROR');
            this.updatingTags = false;
          },
        }),
    );
  }

  private _filter(option: string): string[] {
    const filterValue = option.toLowerCase();
    return this.availableTags.filter((tag) => tag.toLowerCase().indexOf(filterValue) === 0);
  }
}
