import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  CommonField,
  ConfigInterface,
  CustomField,
  Field,
  LineItemInterface,
  Order,
  Status,
  UserInterface as User,
} from '@vendasta/sales-orders';
import {
  CommonFormData,
  CustomFieldsAnswer,
  CustomFieldsAnswers,
  OrderFormFieldOptionsType,
  OrderFormInterface,
  OrderFormOptionsInterface,
  ProductInfoInterface,
  OrderFormComponent as StoreOrderFormComponent,
} from '@vendasta/store';
import { BehaviorSubject, Observable, Subject, Subscription, combineLatest, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, startWith, switchMap, take, tap } from 'rxjs/operators';
import { OrderFormService } from './order-form.service';

interface OrderData {
  orderForms: OrderFormInterface[];
  productInfo: ProductInfoInterface[];
  commonData: CommonFormData;
  answers: CustomFieldsAnswers[];
  extraQuestions: OrderFormFieldOptionsType[];
  extraAnswers: CustomFieldsAnswer[];
  users: User[];
  orderFormOptions: OrderFormOptionsInterface;
}

@Component({
  templateUrl: 'order-form.component.html',
  providers: [OrderFormService],
  selector: 'orders-form',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['../../shared/shared-styles.scss', './order-form.component.scss'],
  standalone: false,
})
export class OrderFormComponent implements OnInit, OnDestroy {
  @ViewChild('vaOrderForm')
  public orderFormComponent: StoreOrderFormComponent;

  // The order and its configuration
  @Input() order: Order;
  @Input() orderFormOptions: OrderFormOptionsInterface;
  @Input() orderConfig: ConfigInterface;

  // Optionally used within the create order experience
  @Input() lineItems: LineItemInterface[];

  // Required attributes
  @Input() userOptions: User[];
  @Input() fileUploadUrl = '';
  @Input() showSelfSaveButton = false;
  @Input() answers: CustomFieldsAnswers[] = [];

  private parentForm$$ = new BehaviorSubject<UntypedFormGroup | null>(null);
  @Input() set parentForm(form: UntypedFormGroup) {
    this.parentForm$$.next(form);
  }
  get parentForm(): UntypedFormGroup | null {
    return this.parentForm$$.getValue();
  }

  // External identifiers
  @Input() partnerId: string;
  @Input() businessId: string;

  @Output() loading: EventEmitter<boolean> = new EventEmitter();

  answers$: Observable<CustomFieldsAnswers[]>;
  orderData$: Observable<OrderData>;
  startOpen$: Observable<boolean>;
  submitting = false;
  orderStatus: Status;

  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  loading$: Observable<boolean> = this.loading$$.asObservable();
  isValid$!: Observable<boolean>;

  subscriptions: Subscription[] = [];

  constructor(
    private alertService: SnackbarService,
    private salesOrderFormService: OrderFormService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.salesOrderFormService.initialize(this.order, this.orderFormOptions, this.lineItems, this.orderConfig);

    this.answers$ = this.salesOrderFormService.getAnswers.pipe(
      map((answers) => {
        if (answers.length > 0) {
          return answers;
        }

        return this.answers;
      }),
    );

    this.orderData$ = combineLatest([
      this.salesOrderFormService.getOrderForms,
      this.salesOrderFormService.getProductInfo,
      this.salesOrderFormService.getCommonData,
      this.answers$,
      this.salesOrderFormService.getExtraAnswers,
      this.salesOrderFormService.getExtraQuestions,
      this.salesOrderFormService.orderFormOptions$,
    ]).pipe(
      // OrderFormService emits in a burst when the order re-renders which caused order form issues, debounce helps prevent that (https://vendasta.jira.com/browse/QOF-1472)
      debounceTime(250),
      map(([orderForms, productInfo, commonData, answers, extraAnswers, extraQuestions, orderFormOptions]) => {
        return {
          orderForms: orderForms,
          productInfo: productInfo,
          commonData: commonData,
          answers: answers,
          extraQuestions: extraQuestions,
          extraAnswers: extraAnswers,
          users: this.userOptions,
          orderFormOptions: orderFormOptions,
        };
      }),
      tap(() => this.loading$$.next(false)),
    );

    this.isValid$ = this.parentForm$$.asObservable().pipe(
      filter((form) => form !== null),
      switchMap((form) => {
        return form.statusChanges.pipe(
          startWith(form.status),
          debounceTime(250), // wait for form to stabilize before emitting
          map((status) => status !== 'INVALID'),
        );
      }),
      distinctUntilChanged(),
    );

    this.startOpen$ = this.salesOrderFormService.order$.pipe(
      take(1),
      map(
        (o) =>
          !o ||
          (o.status !== Status.PROCESSING &&
            o.status !== Status.ACTIVATION_ERRORS &&
            o.status !== Status.FULFILLED &&
            o.status !== Status.ARCHIVED),
      ),
    );

    this.subscriptions.push(this.loading$.pipe(tap((loading) => this.loading.emit(loading))).subscribe());
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => {
      sub.unsubscribe();
    });
  }

  /**
   * returns the data from common form fields in a format that is usable by sales orders
   */
  public getCommonFormData(): CommonField[] {
    return this.orderFormComponent.getCommonFormData();
  }

  /**
   * returns the data from custom forms in a format that is usable by sales orders
   */
  public getCustomFormData(): CustomField[] {
    return this.orderFormComponent.getCustomFormData();
  }

  /**
   * returns the data from extra fields in a format that is usable by sales orders
   */
  public getExtraFieldsData(): Field[] {
    return this.orderFormComponent.getExtraFieldsData();
  }

  /**
   * returns true if the order form contains fields that need to be filled out by the partner
   */
  public requiresPartnerInput(): boolean {
    return this.orderFormComponent.orderFormRequiresPartnerInput();
  }

  /**
   * changes the read only state of the sales order form
   */
  public changeReadOnly(readOnly: boolean): void {
    this.orderFormComponent.changeReadOnly(readOnly);
  }

  private setAsTouched(form: UntypedFormGroup): void {
    form.markAsTouched();
    const controls = form.controls;
    for (const i of Object.keys(controls)) {
      if (controls[i] instanceof UntypedFormControl) {
        controls[i].markAsTouched();
      } else if (controls[i] instanceof UntypedFormGroup) {
        this.setAsTouched(controls[i] as UntypedFormGroup);
      }
      controls[i].updateValueAndValidity({ emitEvent: false });
    }
    form.updateValueAndValidity({ emitEvent: false });
  }

  /**
   * returns whether or not the sales order form is valid and touches all controls
   */
  public validateForm(): boolean {
    if (!this.parentForm) {
      return true;
    }
    const valid = this.parentForm.valid;
    if (!valid) {
      this.setAsTouched(this.parentForm);
    }
    return valid;
  }

  public openDropdown(): void {
    this.orderFormComponent.openDropdown();
  }

  updateAnswers(): Observable<boolean> {
    if (this.orderStatus !== Status.DRAFTED && !this.validateForm()) {
      this.alertService.openErrorSnack('FRONTEND.SALES_UI.ERRORS.ERROR_FORM_NOT_VALID');
      return of(false);
    }

    this.submitting = true;
    const customFormData = this.getCustomFormData();
    const extraFieldsData = this.getExtraFieldsData();
    const commonFormData = this.getCommonFormData();

    const result$$ = new Subject<boolean>();
    this.salesOrderFormService
      .updateAnswers(commonFormData, customFormData, extraFieldsData)
      .pipe(take(1))
      .subscribe(
        () => {
          result$$.next(true);
          this.parentForm?.markAsPristine();
          this.submitting = false;
          this.cdr.detectChanges();
          this.alertService.openSuccessSnack('FRONTEND.SALES_UI.ORDER_FORM_ANSWERS_UPDATED');
        },
        () => {
          result$$.next(false);
          this.submitting = false;
          this.cdr.detectChanges();
          this.alertService.openErrorSnack('FRONTEND.SALES_UI.ERRORS.ERROR_UPDATING_ORDER_FORM');
        },
      );
    return result$$.asObservable();
  }

  refresh(o: Order): void {
    this.salesOrderFormService.refresh(o);
    this.cdr.detectChanges();
  }
}
