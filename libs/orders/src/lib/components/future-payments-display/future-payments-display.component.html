<div class="futurePaymentContainer">
  <div>
    <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.FUTURE_PAYMENTS' | translate }}</span>
  </div>
  <div>
    @for (
      futurePayment of futurePayments;
      track trackFuturePayment($index, futurePayment.startDate, futurePayment.duration, futurePayment.frequency)
    ) {
      <div [className]="'futurePaymentItem'">
        <span [className]="'futurePaymentItemPrice'">
          <billing-ui-simple-price-display
            [price]="futurePayment.price"
            [currencyCode]="currencyCode"
            [frequency]="futurePayment.frequency"
          ></billing-ui-simple-price-display>
        </span>
        @if (futurePayment.startDate) {
          @if (futurePayment.duration > 1 || futurePayment.duration === FuturePaymentScheduledTime.RenewIndefinitely) {
            <span>
              {{
                'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.STARTING'
                  | translate: { date: futurePayment.startDate | date: 'mediumDate' }
              }}</span
            >
          } @else {
            <span>
              {{
                'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.ON_DATE'
                  | translate: { date: futurePayment.startDate | date: 'mediumDate' }
              }}</span
            >
          }
        } @else {
          <span>
            {{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.STARTING_IMMEDIATELY' | translate }}</span
          >
        }

        @if (futurePayment.duration && futurePayment.duration > 1) {
          <span>
            {{
              'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.FOR_DURATION_FREQUENCY'
                | translate: { duration: futurePayment.duration }
            }}

            @if (futurePayment.frequency === Frequency.MONTHLY) {
              <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.MONTHS' | translate }}</span>
            } @else if (futurePayment.frequency === Frequency.YEARLY) {
              <span>{{ 'LIB_ORDERS.COMMON.EDIT_ORDERS.RETAIL_SUMMARY.FUTURE_PAYMENTS.YEARS' | translate }}</span>
            }
          </span>
        }
      </div>
    }
  </div>
</div>
