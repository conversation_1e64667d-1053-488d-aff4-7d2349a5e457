import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyConfirmationModalModule } from '@vendasta/galaxy/confirmation-modal';

@Component({
  templateUrl: './submit-for-charge-dialog.component.html',
  styleUrls: ['./submit-for-charge-dialog.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    MatIconModule,
    GalaxyConfirmationModalModule,
  ],
})
export class SubmitForChargeDialogComponent {
  readonly accountGroupId: string;
  readonly partnerId: string;

  confirmRetailCharge = false;
  confirmWholesaleCharge = false;

  constructor(public dialogRef: MatDialogRef<SubmitForChargeDialogComponent>) {}

  chargeAndSubmit() {
    this.dialogRef.close(true);
  }
}
