<div>
  <span class="card" [ngClass]="{ disabled: !isEditable }">
    @if (retailCustomerConfiguration$ | async; as retailCustomerConfiguration) {
      @if (defaultPaymentMethod && (orderCustomerRecipient?.userId || retailCustomerConfiguration?.contactId)) {
        <smb-invoicing-payment-method-display
          [paymentMethod]="defaultPaymentMethod"
        ></smb-invoicing-payment-method-display>
        @if (isEditable) {
          <button class="edit-button" mat-button type="button" color="primary" (click)="openPaymentMethodDialog()">
            {{ editButton }}
          </button>
          <div [ngClass]="'edit-spacer'"></div>
          <mat-hint>{{ hintText }}</mat-hint>
          @if (canChargeOrder) {
            @if (hasAlreadyActiveProducts$ | async) {
              <div
                (mouseenter)="showPopovers(PopoverPositions.Top)"
                (mouseleave)="hidePopovers()"
                [glxyPopover]="popover"
                class="charge-payment-method"
              >
                <button mat-stroked-button (click)="openChargeConfirmationDialog()" disabled="true">
                  {{
                    'LIB_ORDERS.COMMON.ORDERS.DIALOGS.ORDERS_SELECT_PAYMENT_METHOD.CHARGE_PAYMENT_METHOD' | translate
                  }}
                </button>
              </div>
            } @else {
              <div class="charge-payment-method">
                <button mat-stroked-button (click)="openChargeConfirmationDialog()">
                  {{
                    'LIB_ORDERS.COMMON.ORDERS.DIALOGS.ORDERS_SELECT_PAYMENT_METHOD.CHARGE_PAYMENT_METHOD' | translate
                  }}
                </button>
              </div>
            }
          }
        }
      } @else if (isEditable) {
        <div class="col empty-card-layout">
          <button
            type="button"
            class="empty-card-button"
            color="primary"
            mat-stroked-button
            (click)="openPaymentMethodDialog()"
          >
            {{ addCardButton }}
          </button>
        </div>
      }
    }
  </span>
</div>

<glxy-popover #popover [isOpen]="showPopover" [positions]="[this.position]">
  {{ 'LIB_ORDERS.COMMON.ORDERS.ALREADY_ACTIVE_POPOVER' | translate }}
</glxy-popover>
