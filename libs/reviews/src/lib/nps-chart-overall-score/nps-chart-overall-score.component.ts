import { Component, inject, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgApexchartsModule } from 'ng-apexcharts';
import { GalaxyPopoverModule, PopoverPositions } from '@vendasta/galaxy/popover';
import { combineLatest, map, Observable, Subscription } from 'rxjs';

import { StatisticDeltaComponent } from '../statistic-delta/statistic-delta.component';
import { NpsScoreService } from './nps-score.service';

export interface NPSOverallScoreChartOptions {
  plotOptions: ApexPlotOptions;
  dataLabels: ApexDataLabels;
  xaxis: ApexXAxis;
  color: string[];
  series: { data: number[]; name: string }[];
  legend: ApexLegend;
  grid: ApexGrid;
  tooltip: ApexTooltip;
  chart: ApexChart;
  stroke: ApexStroke;
  yaxis: ApexYAxis;
}

export interface NPSScoreOverallData {
  current: { detractors: number; promoters: number; passives: number };
  previous: { detractors: number; promoters: number; passives: number };
}

@Component({
  selector: 'reviews-nps-chart-overall-score',
  imports: [
    CommonModule,
    TranslateModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    NgApexchartsModule,
    GalaxyPopoverModule,
    StatisticDeltaComponent,
  ],
  templateUrl: './nps-chart-overall-score.component.html',
  styleUrl: './nps-chart-overall-score.component.scss',
})
export class NpsChartOverallScoreComponent implements OnInit, OnDestroy {
  @Input() ChartData$: Observable<NPSScoreOverallData>;
  public chartOptions$: Observable<NPSOverallScoreChartOptions>;

  showPopover = false;
  currentScore = 0;
  previousScore = 0;
  difference = 0;
  isChartDataAvailable = false; // Boolean to track if data is valid
  totalNPSRatings = 0; // Store total ratings
  PopoverPositions = PopoverPositions;

  private chartDataSubscription: Subscription;
  private translateService = inject(TranslateService);
  private npsScoreService = inject(NpsScoreService);

  private colors: string[] = ['#388E3C', '#F9A825', '#D32F2F'];

  ngOnInit() {
    this.chartDataSubscription = this.ChartData$.subscribe((chartData) => {
      this.calculateScores(chartData);
    });
    this.chartOptions$ = this.initializeChartOptions();
  }

  ngOnDestroy(): void {
    // Unsubscribe when the component is destroyed
    if (this.chartDataSubscription) {
      this.chartDataSubscription.unsubscribe();
    }
  }

  private initializeChartOptions(): Observable<NPSOverallScoreChartOptions> {
    const translationStream$ = this.translateService.stream([
      'NPS_OVERALL_CHART.NPS_PROMOTER',
      'NPS_OVERALL_CHART.NPS_PASSIVE',
      'NPS_OVERALL_CHART.NPS_DETRACTOR',
    ]);

    return combineLatest([translationStream$, this.ChartData$]).pipe(
      map(([translations, chartdata]) => {
        const total = chartdata.current.promoters + chartdata.current.passives + chartdata.current.detractors;
        const percentages = [
          total ? Math.round((chartdata.current.promoters / total) * 100) : 0,
          total ? Math.round((chartdata.current.passives / total) * 100) : 0,
          total ? Math.round((chartdata.current.detractors / total) * 100) : 0,
        ];
        const individualScore = [chartdata.current.promoters, chartdata.current.passives, chartdata.current.detractors];
        return {
          series: [
            {
              name: 'Ratings',
              data: percentages,
            },
          ],
          chart: {
            height: 100,
            width: '100%',
            type: 'bar',
            zoom: {
              enabled: false,
            },
            toolbar: {
              show: false,
            },
          },
          legend: {
            show: true,
            position: 'right',
            itemMargin: {
              vertical: 5,
            },
            formatter: (seriesName: string, opts) => {
              const percentage = percentages[opts.seriesIndex];
              const score = individualScore[opts.seriesIndex];
              return `
        <div style="display: flex; align-items: center; justify-content: space-between;gap: 6px">
          <span style="font-size: 12px;font-weight:400;color: #616161;min-width: 22px;text-align: right">${score}</span>
          <span style="width: 2px; height: 12px; background-color: #E0E0E0FF;"></span>
          <span style="font-size: 12px;font-weight:400;color: #616161">${percentage}%</span>
        </div>
      `;
            },
            onItemHover: {
              highlightDataSeries: false,
            },
          },
          plotOptions: {
            bar: {
              horizontal: true,
              columnWidth: '100%',
              barHeight: '16px',
              distributed: true,
              colors: {
                backgroundBarColors: ['#F5F5F5FF'],
                backgroundBarOpacity: 0.8,
              },
            },
          },
          tooltip: {
            enabled: false,
          },
          dataLabels: {
            enabled: false,
          },
          stroke: {
            curve: 'smooth',
          },
          grid: {
            padding: {
              top: -25,
            },
            xaxis: {
              lines: {
                show: false,
              },
            },
            yaxis: {
              lines: {
                show: false,
              },
            },
          },
          yaxis: {
            labels: {
              show: true,
              style: {
                colors: ['#616161FF'],
                fontSize: '14px',
              },
            },
            axisTicks: {
              show: false,
            },
            axisBorder: {
              show: false,
            },
          },
          xaxis: {
            categories: [
              translations['NPS_OVERALL_CHART.NPS_PROMOTER'],
              translations['NPS_OVERALL_CHART.NPS_PASSIVE'],
              translations['NPS_OVERALL_CHART.NPS_DETRACTOR'],
            ],
            labels: {
              show: false,
            },
            style: {
              colors: ['#616161', '#616161', '#616161'],
              fontSize: '12px',
              fontFamily: 'Roboto',
              fontWeight: 400,
            },
            axisTicks: {
              show: false,
            },
            axisBorder: {
              show: false,
            },
            min: 0,
            max: 100,
          },
          color: this.colors,
        };
      }),
    );
  }

  calculateScores(chartData: NPSScoreOverallData) {
    this.currentScore = this.npsScoreService.calculateCategoryAndOverallScore(chartData.current);
    this.previousScore = this.npsScoreService.calculateCategoryAndOverallScore(chartData.previous);
    this.difference = this.currentScore - this.previousScore; // Calculate the difference

    // Set the boolean flag to true if the condition is satisfied
    this.isChartDataAvailable =
      chartData.current.promoters > 0 || chartData.current.passives > 0 || chartData.current.detractors > 0;

    // Calculate total ratings and store in totalRatings variable
    this.totalNPSRatings = chartData.current.promoters + chartData.current.passives + chartData.current.detractors;
  }

  showPopovers(): void {
    this.showPopover = true;
  }

  hidePopovers(): void {
    this.showPopover = false;
  }
}
