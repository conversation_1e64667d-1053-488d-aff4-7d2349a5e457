import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInput } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MeetingBotService } from '@vendasta/meetings';
import { MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$ } from '../constants';
import { Observable, switchMap, take } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { AtlasMenuService } from '@galaxy/atlas';

@Component({
  selector: 'meeting-analysis-schedule-bot',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatInput,
    TranslateModule,
    MatButtonModule,
    MatDivider,
    MatIcon,
  ],
  templateUrl: './schedule-bot.component.html',
  styleUrl: './schedule-bot.component.scss',
})
export class ScheduleBotComponent {
  formControl = new FormControl<string>('');

  constructor(
    private readonly meetingBotService: MeetingBotService,
    @Inject(MEETING_ANALYSIS_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
    private readonly alertService: SnackbarService,
    private _: GalaxyAiIconService,
    private menuService: AtlasMenuService,
  ) {}

  joinMeeting(): void {
    const url = (this.formControl.value || '').trim();
    if (url) {
      this.namespace$
        .pipe(
          take(1),
          switchMap((namespace) => this.meetingBotService.scheduleBot(namespace, url)),
        )
        .subscribe({
          next: () => {
            this.formControl.setValue('');
            this.close();
            this.alertService.openSuccessSnack('SCHEDULE_BOT.SUCCESS');
          },
          error: (err) => {
            console.error(err);
            this.alertService.openErrorSnack('SCHEDULE_BOT.ERROR');
          },
        });
    }
  }

  close(): void {
    this.menuService.closeMenu('');
  }
}
