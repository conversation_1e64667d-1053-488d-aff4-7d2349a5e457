import { Component, DestroyRef, effect, forwardRef, inject, input, OnInit, output, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormArray,
  FormBuilder,
  FormControl,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatOption } from '@angular/material/autocomplete';
import { MatSelect } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CdkAccordion, CdkAccordionItem } from '@angular/cdk/accordion';
import { Mat<PERSON><PERSON>, MatCardContent } from '@angular/material/card';
import { FunctionParameterInterface } from '@vendasta/ai-assistants/lib/_internal/interfaces/function.interface';
import { FunctionParameterParameterLocation } from '@vendasta/ai-assistants';

export enum ParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  INTEGER = 'integer',
  OBJECT = 'object',
  ARRAY = 'array',
}

@Component({
  selector: 'ai-function-parameter',
  imports: [
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    MatIcon,
    MatIconButton,
    MatInput,
    MatOption,
    MatSelect,
    CommonModule,
    CdkAccordion,
    CdkAccordionItem,
    MatButton,
    MatCard,
    MatCardContent,
    TranslateModule,
  ],
  templateUrl: './function-parameter.component.html',
  styleUrl: './function-parameter.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FunctionParameterComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => FunctionParameterComponent),
      multi: true,
    },
  ],
})
export class FunctionParameterComponent implements ControlValueAccessor, Validator, OnInit {
  private fb = inject(FormBuilder);
  private destroyRef = inject(DestroyRef);
  @ViewChild('accordionItem') accordionItem?: CdkAccordionItem;

  disableDelete = input(false);
  isArrayItem = input(false);
  isNested = input(false);

  remove = output();

  private onChange: (value: Partial<FunctionParameterInterface>) => void = () => {
    return;
  };
  private onTouched: () => void = () => {
    return;
  };

  parameterTypeOptions = [
    { value: ParameterType.STRING, label: 'String' },
    { value: ParameterType.NUMBER, label: 'Number' },
    { value: ParameterType.BOOLEAN, label: 'Boolean' },
    { value: ParameterType.INTEGER, label: 'Integer' },
    { value: ParameterType.OBJECT, label: 'Object' },
    { value: ParameterType.ARRAY, label: 'Array' },
  ];

  parameterLocationOptions = [
    { value: FunctionParameterParameterLocation.LOCATION_BODY, label: 'Request Body Parameter' },
    { value: FunctionParameterParameterLocation.LOCATION_QUERY_PARAM, label: 'Query Parameter' },
  ];

  form = this.fb.group({
    name: [''],
    type: [<string>ParameterType.STRING, Validators.required],
    description: [''],
    location: [FunctionParameterParameterLocation.LOCATION_BODY, Validators.required],
    properties: this.fb.array([]),
    items: this.fb.control<Partial<FunctionParameterInterface | undefined>>(undefined),
  });

  get name() {
    return this.form.get('name');
  }

  get properties() {
    return this.form.get('properties') as FormArray<FormControl>;
  }

  get type() {
    return this.form.get('type');
  }

  get items() {
    return this.form.get('items');
  }

  get location() {
    return this.form.get('location');
  }

  ngOnInit(): void {
    this.onLocationChange();
  }

  constructor() {
    this.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
      this.onChange(value as Partial<FunctionParameterInterface>);
      this.onTouched();
    });

    effect(() => {
      const isArrayItem = this.isArrayItem();
      if (!isArrayItem) {
        this.name?.setValidators([Validators.required, Validators.pattern(/^[a-zA-Z0-9_-]{1,64}$/)]);
      } else {
        this.name?.clearValidators();
      }
      // Update validity after changing validators
      this.name?.updateValueAndValidity();
      this.form.updateValueAndValidity();

      // mark the form as touched when it is created so it is obvious to the user that they need to fill it out
      Object.values(this.form.controls).forEach((control) => {
        control.markAsTouched();
      });
    });

    this.type?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => {
      if (value === ParameterType.ARRAY) {
        // Create a complete item with all required fields for array
        this.form.setControl(
          'items',
          this.fb.control<Partial<FunctionParameterInterface>>({
            name: '',
            type: ParameterType.STRING,
            description: '',
            location: FunctionParameterParameterLocation.LOCATION_BODY,
            properties: [],
          }),
        );
      } else {
        this.form.setControl('items', this.fb.control<Partial<undefined>>(undefined));
      }
    });
  }

  onTypeChange() {
    const type = this.form.get('type')?.value;

    if (type === ParameterType.OBJECT) {
      this.form.patchValue({
        properties: [],
        items: null,
        location: FunctionParameterParameterLocation.LOCATION_BODY,
      });
      // Lock the location to Body for object type
      this.form.get('location')?.disable();
    } else if (type === ParameterType.ARRAY) {
      // For ARRAY type, don't wipe out items - just create a default if needed
      if (!this.form.get('items')?.value) {
        // Create a default item if none exists
        this.form.setControl(
          'items',
          this.fb.control<Partial<FunctionParameterInterface>>({
            name: '',
            type: ParameterType.STRING,
            description: '',
            location: FunctionParameterParameterLocation.LOCATION_BODY,
            properties: [],
          }),
        );
      }

      // Set location to BODY
      this.form.patchValue({
        properties: undefined,
        location: FunctionParameterParameterLocation.LOCATION_BODY,
      });
      // Lock the location to Body for array type
      this.form.get('location')?.disable();
    } else {
      this.form.patchValue({
        properties: undefined,
        items: undefined,
      });
      const valueControl = this.form.get('value');
      if (valueControl) {
        valueControl.disable();
      }
      // Re-enable location selection for non-nested types
      if (!this.isNested()) {
        this.form.get('location')?.enable();
      }
    }
  }

  onLocationChange(): void {
    const location = this.form.get('location')?.value;
    const type = this.form.get('type')?.value;

    // For nested types, always set location to Body
    if (type === ParameterType.OBJECT || type === ParameterType.ARRAY) {
      this.form.patchValue({ location: FunctionParameterParameterLocation.LOCATION_BODY });
      this.form.get('location')?.disable();
      return;
    }

    if (location === FunctionParameterParameterLocation.LOCATION_QUERY_PARAM) {
      this.form.patchValue({ type: ParameterType.STRING });
      this.form.get('type')?.disable();
    } else {
      this.form.get('type')?.enable();
    }
  }

  createField(property: FunctionParameterInterface) {
    return this.fb.control({
      name: property.name || '',
      type: property.type || ParameterType.STRING,
      description: property.description || '',
      properties: property.properties || [],
      items: property.items,
      location: property.location || FunctionParameterParameterLocation.LOCATION_BODY,
    });
  }

  registerOnChange(fn: (value: FunctionParameterInterface) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  writeValue(value: FunctionParameterInterface | null): void {
    if (!value) {
      this.form.reset();
      return;
    }

    // Reset form to default values
    this.form.patchValue(
      {
        name: '',
        type: ParameterType.STRING,
        description: '',
        location: FunctionParameterParameterLocation.LOCATION_BODY,
      },
      { emitEvent: false },
    );

    // Clear arrays
    this.properties.clear();

    // Patch with incoming values
    this.form.patchValue(
      {
        name: value.name || '',
        type: value.type || ParameterType.STRING,
        description: value.description || '',
        location: value.location || FunctionParameterParameterLocation.LOCATION_BODY,
      },
      { emitEvent: false },
    );

    // Handle properties for object type
    if (value.type === ParameterType.OBJECT && value.properties && value.properties.length > 0) {
      value.properties.forEach((prop) => {
        this.properties.push(this.createField(prop));
      });
    }

    // Handle items for array type
    if (value.type === ParameterType.ARRAY && value.items) {
      // Preserve existing form control structure but update its content
      const formControl = this.form.get('items') as FormControl;
      if (formControl) {
        // When updating existing control, ensure we conform to the expected structure
        const itemValue = {
          name: value.items.name || '',
          type: value.items.type || ParameterType.STRING,
          description: value.items.description || '',
          location: value.items.location || FunctionParameterParameterLocation.LOCATION_BODY,
          properties: value.items.properties || [],
          items: value.items.items,
        };
        formControl.setValue(itemValue, { emitEvent: false });
      } else {
        // If no control exists, create one with our helper method
        const mockItem: FunctionParameterInterface = {
          name: value.items.name || '',
          type: value.items.type || ParameterType.STRING,
          description: value.items.description || '',
          location: value.items.location || FunctionParameterParameterLocation.LOCATION_BODY,
          properties: value.items.properties || [],
          items: value.items.items,
        };
        this.form.setControl('items', this.createField(mockItem));
      }
    }

    // Apply control state based on current values
    this.onTypeChange();
    this.onLocationChange();

    if (this.isNested()) {
      this.form.get('location')?.disable();
    }
  }

  validate(_: AbstractControl): ValidationErrors | null {
    return this.form.valid ? null : { invalidParameter: true };
  }

  addField(property: FunctionParameterInterface): void {
    this.properties.push(this.createField(property));
  }

  removeField(index: number): void {
    this.properties.removeAt(index);
  }

  protected readonly ParameterType = ParameterType;
}
