@use 'design-tokens' as dt;

:host {
  .mat-expansion-panel {
    border: none;
    box-shadow: none;
    background: transparent;
  }

  ::ng-deep .mat-expansion-panel-body {
    border: 0;
  }
}

.panel-header {
  padding: dt.$spacing-3;
  border-bottom: 0;
}

.titles {
  flex-grow: 1;
}

.content {
  display: flex;
  align-items: center;
  width: 100%;
}

.remove-button {
  color: dt.$icon-color;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: dt.$spacing-2;
  gap: dt.$spacing-2;
}
