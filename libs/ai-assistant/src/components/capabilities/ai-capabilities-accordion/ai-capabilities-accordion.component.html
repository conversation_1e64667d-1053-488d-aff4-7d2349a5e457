<mat-card>
  <mat-card-header>
    <mat-card-title>
      {{ 'AI_ASSISTANT.SETTINGS.CAPABILITIES' | translate }}
    </mat-card-title>
  </mat-card-header>
  <mat-accordion displayMode="flat" togglePosition="before" multi>
    @for (capability of capabilities().controls; track capability; let i = $index) {
      <mat-expansion-panel
        class="expansion-panel"
        [expanded]="expandedPanels().includes(i)"
        (opened)="expandPanel(i)"
        (closed)="collapsePanel(i)"
      >
        <mat-expansion-panel-header collapsedHeight="auto" expandedHeight="auto" class="panel-header">
          <div class="content">
            <div class="titles">
              <mat-panel-title>
                {{ capability.controls.name.value }}
              </mat-panel-title>
              <mat-panel-description>
                {{ capability.controls.description.value }}
              </mat-panel-description>
            </div>
            <button mat-icon-button (click)="handleCapabilityRemoveClicked($event, i)" class="remove-button">
              <mat-icon>clear</mat-icon>
            </button>
          </div>
        </mat-expansion-panel-header>
        @if (isMeetingBookingCapability(capability)) {
          <ai-book-me-now-capability-form [configurationForm]="capability.controls.configuration"></ai-book-me-now-capability-form>
        } @else {
          <ai-capability-form
            [capabilityForm]="capability"
            (toolFormDisplay)="
              toolFormDisplay.emit($event !== undefined ? { form: $event, parentCapabilityForm: capability } : undefined)
            "
          />
        }
      </mat-expansion-panel>
      <mat-divider />
    }
  </mat-accordion>
  <mat-card-footer class="card-footer">
    <button mat-button (click)="handleCapabilityAddClicked()">
      <mat-icon>add</mat-icon>
      {{ 'AI_ASSISTANT.SETTINGS.ADD_CAPABILITY' | translate }}
    </button>
  </mat-card-footer>
</mat-card>
