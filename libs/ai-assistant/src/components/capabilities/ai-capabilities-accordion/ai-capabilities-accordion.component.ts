import { Component, inject, input, output, signal } from '@angular/core';
import { AiCapabilityFormComponent } from '../ai-capability-form/ai-capability-form.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatBadgeModule } from '@angular/material/badge';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule, MatIconButton } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { MatDividerModule } from '@angular/material/divider';
import { AiCapabilityForm, AiCapabilityFormArray, AiToolForm } from '../../../core/forms';
import { MatDialog } from '@angular/material/dialog';
import { AiCapabilitySelectComponent } from '../ai-capability-select-dialog/ai-capability-select-dialog.component';
import { GoalInterface, PromptModuleKey } from '@vendasta/ai-assistants';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BookMeNowCapabilityFormComponent } from '../ai-capability-form/book-me-now-capability-form/book-me-now-capability-form.component';

const DEFAULT_MEETING_BOOKING_GOAL_ID = 'BookAppointmentsWithBookMeNow';

@Component({
  selector: 'ai-capabilities-accordion',
  imports: [
    AiCapabilityFormComponent,
    MatBadgeModule,
    MatCardModule,
    MatDividerModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatIconButton,
    TranslateModule,
    BookMeNowCapabilityFormComponent,
  ],
  templateUrl: './ai-capabilities-accordion.component.html',
  styleUrl: './ai-capabilities-accordion.component.scss',
  host: {
    class: 'ai-capabilities-accordion',
  },
})
export class AiCapabilityAccordionComponent {
  readonly capabilities = input.required<AiCapabilityFormArray>();
  readonly toolFormDisplay = output<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>();

  readonly addDialog = inject(MatDialog);
  private readonly assistantService = inject(AiAssistantService);
  private readonly snackbar = inject(SnackbarService);

  readonly expandedPanels = signal<number[]>([]);

  handleCapabilityRemoveClicked(event: Event, i: number) {
    event.stopPropagation();
    this.capabilities().removeAt(i);
  }

  async handleCapabilityAddClicked() {
    const dialogRef = this.addDialog.open(AiCapabilitySelectComponent, {
      data: {
        excludedGoalIds: this.capabilities()
          .controls.map((capabilityForm) => capabilityForm.controls.goalId.value)
          .filter((id) => id != null),
      },
    });

    dialogRef.afterClosed().subscribe(async (result: { goal?: GoalInterface; newGoal: boolean } | '') => {
      if (result === '' || !result) {
        return;
      }

      const { goal: chosenGoal, newGoal } = result;

      if (chosenGoal) {
        let promptModuleContents: Record<string, string> = {};
        const promptModuleKeys =
          chosenGoal?.promptModules?.map((pm) => new PromptModuleKey({ id: pm.id, namespace: pm.namespace })) || [];
        if (promptModuleKeys.length > 0) {
          try {
            promptModuleContents = await this.assistantService.getMultiPromptModuleVersions(promptModuleKeys);
          } catch (err) {
            this.snackbar.openErrorSnack('AI_ASSISTANT.GOALS.ADD_GOAL.ERROR_LOADING_CAPABILITY');
            return;
          }
        }
        this.capabilities().push(new AiCapabilityForm({ goal: chosenGoal, configuration: [] }, promptModuleContents));
      } else if (newGoal) {
        this.capabilities().push(new AiCapabilityForm());
        this.expandPanel(this.capabilities().length - 1);
      }
    });
  }

  expandPanel(index: number) {
    if (this.expandedPanels().includes(index)) {
      return;
    }

    this.expandedPanels.update((panels) => [...panels, index]);
  }

  collapsePanel(index: number) {
    this.expandedPanels.update((panels) => panels.filter((panelIndex) => panelIndex !== index));
  }

  isMeetingBookingCapability(c: AiCapabilityForm): boolean {
    return c.controls?.goalId?.getRawValue() === DEFAULT_MEETING_BOOKING_GOAL_ID;
  }
}
