import { Component, inject, OnInit, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { firstValueFrom } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FunctionInterface } from '@vendasta/ai-assistants';
import { MatIconModule } from '@angular/material/icon';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';

@Component({
  selector: 'ai-tool-select-dialog',
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    GalaxyBadgeModule,
    AiAssistantI18nModule,
    GalaxyLoadingSpinnerModule,
    GalaxyInfiniteScrollTriggerModule,
  ],
  templateUrl: './ai-tool-select-dialog.component.html',
  styleUrls: ['./ai-tool-select-dialog.component.scss', '../../ai-assistant-configuration/ai-assistant-shared.scss'],
})
export class AiToolSelectDialogComponent implements OnInit {
  private dialogRef = inject(MatDialogRef<AiToolSelectDialogComponent>);
  private snackbarService = inject(SnackbarService);
  private aiAssistantService = inject(AiAssistantService);
  private excludedFunctionIds: string[] = inject(MAT_DIALOG_DATA).excludedFunctionIds || [];
  protected readonly functions = signal<FunctionInterface[]>([]);
  protected readonly isLoading = signal(false);
  protected readonly nextCursor = signal('');
  protected readonly hasMore = signal(true);

  ngOnInit() {
    this.loadFunctions();
  }

  async loadFunctions(): Promise<void> {
    if (!this.isLoading() && this.hasMore()) {
      this.isLoading.set(true);
      try {
        const nextFunctions = await firstValueFrom(this.aiAssistantService.listFunctions());

        this.functions.set([
          ...this.functions(),
          ...(nextFunctions?.functions || []).filter(
            (aiFunc) => aiFunc.id && !this.excludedFunctionIds.includes(aiFunc.id) && !aiFunc.managed,
          ),
        ]);
        this.nextCursor.set(nextFunctions?.nextCursor);
        this.hasMore.set(nextFunctions?.hasMore);
        this.isLoading.set(false);
      } catch (error) {
        this.isLoading.set(false);
        this.snackbarService.openErrorSnack('AI_ASSISTANT.FUNCTIONS.ADD_FUNCTION.ERROR_LOADING_FUNCTIONS');
      } finally {
        this.isLoading.set(false);
      }
    }
  }

  chooseFunction(aiFunc: FunctionInterface) {
    this.dialogRef.close({
      functions: aiFunc,
    });
  }

  newFunction() {
    this.dialogRef.close({
      newFunction: true,
    });
  }
}
