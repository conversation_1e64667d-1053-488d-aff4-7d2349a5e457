import { Component, inject, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { AiToolForm, AiToolFormArray } from '../../../core/forms';
import { MatDialog } from '@angular/material/dialog';
import { AiToolSelectDialogComponent } from '../ai-tool-select-dialog/ai-tool-select-dialog.component';
import { FunctionInterface } from '@vendasta/ai-assistants';

@Component({
  selector: 'ai-tools-list',
  imports: [MatListModule, TranslateModule, MatButtonModule, MatIconModule],
  templateUrl: './ai-tools-list.component.html',
  styleUrl: './ai-tools-list.component.scss',
})
export class AiToolsListComponent {
  readonly forms = input.required<AiToolFormArray>();
  readonly showAddButton = input<boolean>(true);
  readonly toolFormDisplay = output<AiToolForm | undefined>();

  readonly addDialog = inject(MatDialog);

  handleToolRemoved(event: Event, idx: number) {
    event.stopPropagation();
    this.forms().removeAt(idx);
  }

  handleToolAdd(event: Event) {
    event.stopPropagation();

    const dialogRef = this.addDialog.open(AiToolSelectDialogComponent, {
      data: {
        excludedFunctionIds: this.forms()
          .controls.map((form) => form.controls.id.value)
          .filter((id) => id != null),
      },
    });

    dialogRef
      .afterClosed()
      .subscribe(async (result: { functions?: FunctionInterface; newFunction: boolean } | null) => {
        if (!result) {
          return;
        }

        const { functions, newFunction } = result;

        if (functions) {
          this.forms().push(new AiToolForm({ func: functions }));
        } else if (newFunction) {
          this.toolFormDisplay.emit(new AiToolForm({ metadata: { isNew: true } }));
        }
      });
  }
}
