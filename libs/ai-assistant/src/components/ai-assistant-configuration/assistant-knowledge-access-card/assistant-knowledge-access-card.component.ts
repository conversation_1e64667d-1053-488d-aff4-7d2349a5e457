import { Component, inject, input, Signal } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatCard, MatCardActions, MatCardContent, MatCardHeader, MatCardTitle } from '@angular/material/card';
import { KnowledgeSource, KnowledgeSourceConfigType } from '@vendasta/embeddings';
import { AssistantKnowledgeAccessFormComponent } from './assistant-knowledge-access-form/assistant-knowledge-access-form.component';
import { AiKnowledgeService, SHOW_BUSINESS_PROFILE_SOURCE_TOKEN } from '@galaxy/ai-knowledge';
import { AiKnowledgeFormArray, KnowledgeForm } from '../../../core/forms';
import { AddKnowledgeDialogComponent } from './add-knowledge-dialog/add-knowledge-dialog.component';
import { firstValueFrom, from } from 'rxjs';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialog } from '@angular/material/dialog';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIcon } from '@angular/material/icon';
import { MatListItemIcon } from '@angular/material/list';

@Component({
  selector: 'ai-assistant-knowledge-access-card',
  imports: [
    MatButton,
    MatCard,
    MatCardActions,
    MatCardContent,
    MatCardHeader,
    MatCardTitle,
    AssistantKnowledgeAccessFormComponent,
    MatMenuModule,
    TranslateModule,
    MatIcon,
    MatListItemIcon,
  ],
  templateUrl: './assistant-knowledge-access-card.component.html',
  styleUrl: './assistant-knowledge-access-card.component.scss',
})
export class AssistantKnowledgeAccessCardComponent {
  private readonly knowledgeService = inject(AiKnowledgeService);
  private readonly dialog = inject(MatDialog);

  readonly knowledgeFormArray = input.required<AiKnowledgeFormArray>();
  readonly allKnowledgeSources: Signal<KnowledgeSource[] | undefined> = toSignal(from(this.listAllKnowledge()));
  private readonly showBusinessProfileSource = toSignal(inject(SHOW_BUSINESS_PROFILE_SOURCE_TOKEN));
  readonly businessProfileKSId = this.knowledgeService.businessProfileKSId;

  getAvailableKnowledgeSources(): KnowledgeSource[] {
    const knowledgeIds =
      this.knowledgeFormArray()
        ?.getRawValue()
        ?.map((knowledge) => knowledge.raw.id) ?? [];
    if (this.showBusinessProfileSource()) {
      const businessProfileSource = this.knowledgeService.buildBusinessProfileKnowledgeSource(true);
      if (businessProfileSource) {
        this.allKnowledgeSources()?.unshift(businessProfileSource);
      }
    }
    return (
      this.allKnowledgeSources()
        ?.filter((knowledgeSource) => !knowledgeIds.some((knowledgeId) => knowledgeId === knowledgeSource.id))
        ?.sort((a, b) => (a.name ?? '').localeCompare(b.name ?? '')) ?? []
    );
  }

  async openAddKnowledgeModal(): Promise<void> {
    const availableKnowledgeSources = this.getAvailableKnowledgeSources();
    const res = await firstValueFrom(
      this.dialog
        .open(AddKnowledgeDialogComponent, {
          width: '900px',
          height: '600px',
          data: { knowledgeSources: availableKnowledgeSources },
        })
        .afterClosed(),
    );

    if (res?.knowledge) {
      this.addKnowledgeForm(res.knowledge);
    }
  }

  addKnowledgeForm(knowledgeSource: KnowledgeSource): void {
    if (knowledgeSource.id === this.businessProfileKSId()) {
      this.knowledgeFormArray().insert(0, new KnowledgeForm(knowledgeSource));
    } else {
      this.knowledgeFormArray().push(new KnowledgeForm(knowledgeSource));
    }
    this.knowledgeFormArray().markAsDirty();
  }

  removeKnowledgeForm(index: number): void {
    this.knowledgeFormArray().removeAt(index);
    this.knowledgeFormArray().markAsDirty();
  }

  getKnowledgePreview(source: KnowledgeSource) {
    return this.knowledgeService.getKnowledgePreview(source);
  }

  private async listAllKnowledge(pageSize = 100) {
    const response = await this.knowledgeService.listKnowledgeSources(
      [
        KnowledgeSourceConfigType.KNOWLEDGE_SOURCE_CONFIG_TYPE_CUSTOM_DATA,
        KnowledgeSourceConfigType.KNOWLEDGE_SOURCE_CONFIG_TYPE_WEBSITE_SCRAPE,
      ],
      { pageSize: pageSize },
    );
    return response.knowledgeSources;
  }
}
