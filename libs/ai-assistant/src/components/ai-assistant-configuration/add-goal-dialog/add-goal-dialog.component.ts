import { Component, inject, OnInit, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { AiGoalService } from '../../../core/services/ai-goal.service';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { combineLatest, firstValueFrom, switchMap } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GoalInterface } from '@vendasta/ai-assistants/lib/_internal/interfaces/goal.interface';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

@Component({
  selector: 'ai-add-goal-dialog',
  imports: [
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    GalaxyBadgeModule,
    AiAssistantI18nModule,
    GalaxyLoadingSpinnerModule,
    GalaxyInfiniteScrollTriggerModule,
  ],
  templateUrl: './add-goal-dialog.component.html',
  styleUrls: ['./add-goal-dialog.component.scss', '../ai-assistant-shared.scss'],
})
export class AddGoalDialogComponent implements OnInit {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly aiGoalService = inject(AiGoalService);
  private readonly dialogRef = inject(MatDialogRef<AddGoalDialogComponent>);
  private readonly excludedGoalIds: string[] = inject(MAT_DIALOG_DATA).excludedGoalIds || [];
  private readonly snackbar = inject(SnackbarService);

  protected readonly isLoading = signal(false);
  protected readonly goals = signal<GoalInterface[]>([]);
  protected readonly nextCursor = signal('');
  protected readonly hasMore = signal(true);

  ngOnInit() {
    this.loadGoals();
  }

  protected async loadGoals(): Promise<void> {
    if (!this.isLoading() && this.hasMore()) {
      this.isLoading.set(true);
      try {
        const nextGoals = await firstValueFrom(
          combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
            switchMap(([accountGroupId, partnerId]) => {
              return this.aiGoalService.getGoals(accountGroupId, partnerId, this.nextCursor());
            }),
          ),
        );

        this.goals.set([
          ...this.goals(),
          ...(nextGoals?.goals || []).filter(
            (goal) => goal.id && !this.excludedGoalIds.includes(goal.id) && !goal.managed,
          ),
        ]);
        this.nextCursor.set(nextGoals?.nextCursor);
        this.hasMore.set(nextGoals?.hasMore);
        this.isLoading.set(false);
      } catch (error) {
        this.isLoading.set(false);
        this.snackbar.openErrorSnack('AI_ASSISTANT.GOALS.ADD_GOAL.ERROR_LOADING_CAPABILITIES');
        return;
      }
    }
  }

  chooseGoal(goal: GoalInterface) {
    this.dialogRef.close({
      goal,
    });
  }

  newGoal() {
    this.dialogRef.close({
      newGoal: true,
    });
  }
}
