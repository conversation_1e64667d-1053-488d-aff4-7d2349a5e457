import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { AiAssistantFormV2Component } from './ai-assistant-form-v2.component';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { AssistantApiService, AssistantType, VendorModel } from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { ACCOUNT_GROUP_ID_TOKEN, AI_DEFAULT_WORKFORCE_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AiAssistant, AiConnection, ConnectionType } from '../../../core/interfaces/ai-assistant.interface';
import { ConversationApiService } from '@vendasta/conversation';
import { AiConnectionsForm } from '../../../core/forms';
import { AiKnowledgeService } from '@galaxy/ai-knowledge';
import { voiceFamilies } from '../../../core/services/ai-assistant-utils';

jest.mock('../../../core/services/ai-assistant.service');
jest.mock('@vendasta/ai-assistants');
jest.mock('@vendasta/galaxy/snackbar-service');
jest.mock('@vendasta/galaxy/page/src/page.service');

const ACCOUNT_GROUP_ID = 'AG-1234';
const PARTNER_ID = 'ABC';
describe('AiAssistantFormV2Component', () => {
  let component: AiAssistantFormV2Component;
  let aiAssistantService: jest.Mocked<AiAssistantService>;
  let assistantApiService: jest.Mocked<AssistantApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let pageService: jest.Mocked<PageService>;
  let MockAiKnowledgeService: jest.Mocked<AiKnowledgeService>;
  beforeEach(async () => {
    const mockSMSConnection: AiConnection = {
      connection: {
        id: 'test-sms-connection-id',
        namespace: {
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        },
        name: 'Test SMS Connection',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: {
              accountGroupNamespace: {
                accountGroupId: ACCOUNT_GROUP_ID,
              },
            },
          },
        ],
        connectionType: ConnectionType.SMS,
        connectionTypeName: 'SMS',
        supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
      },
    };

    const mockWebchatConnection: AiConnection = {
      connection: {
        id: 'test-webchat-connection-id',
        namespace: {
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        },
        name: 'Test Webchat Connection',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: {
              accountGroupNamespace: {
                accountGroupId: ACCOUNT_GROUP_ID,
              },
            },
          },
        ],
        connectionType: ConnectionType.WebchatWidget,
        connectionTypeName: 'Web Chat',
        supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
        isConnectionLocked: true,
      },
    };

    const mockAssistant: AiAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: {
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        },
        name: 'Test Assistant',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        avatarUrl: 'test-assistant-avatar-url',
      },
    };

    aiAssistantService = {
      buildAssistantConfigurationUrl: jest.fn().mockReturnValue('/assistant/configuration'),
      getAssistant: jest.fn().mockReturnValue(of(mockAssistant)),
      hydrateAssistantWithDefaultInfo: jest.fn().mockReturnValue(mockAssistant),
      listConnectionsForAssistant: jest.fn().mockReturnValue(of([mockSMSConnection, mockWebchatConnection])),
    } as unknown as jest.Mocked<AiAssistantService>;

    assistantApiService = {
      upsertAssistant: jest.fn(),
      setAssistantConnections: jest.fn(),
    } as unknown as jest.Mocked<AssistantApiService>;

    snackbarService = {
      openErrorSnack: jest.fn(),
      openSuccessSnack: jest.fn(),
    } as unknown as jest.Mocked<SnackbarService>;

    conversationApiService = {
      getMultiWidget: jest.fn().mockReturnValue(
        of({
          widgets: [],
        }),
      ),
    } as unknown as jest.Mocked<ConversationApiService>;

    pageService = {
      navigateToPrevious: jest.fn(),
    } as unknown as jest.Mocked<PageService>;

    MockAiKnowledgeService = {
      businessProfileKSId: jest.fn().mockReturnValue('businessProfileKSId'),
      listAllKnowledgeSourcesForApp: jest.fn().mockResolvedValue([]),
    } as unknown as jest.Mocked<AiKnowledgeService>;

    await TestBed.configureTestingModule({
      imports: [AiAssistantI18nModule, HttpClientTestingModule, LexiconModule.forRoot(), AiAssistantFormV2Component],
      providers: [
        FormBuilder,
        { provide: PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of({}) },
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: AssistantApiService, useValue: assistantApiService },
        { provide: SnackbarService, useValue: snackbarService },
        { provide: PageService, useValue: pageService },
        { provide: ConversationApiService, useValue: conversationApiService },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: (key: string) => (key === 'assistantId' ? 'test-assistant-id' : null),
            }),
          },
        },
        { provide: TranslateService, useValue: { setDefaultLang: jest.fn(), use: jest.fn() } },
        { provide: AiKnowledgeService, useValue: MockAiKnowledgeService },
      ],
    }).compileComponents();

    const fixture = TestBed.createComponent(AiAssistantFormV2Component);

    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', () => {
    expect(aiAssistantService.getAssistant).toHaveBeenCalledWith(PARTNER_ID, ACCOUNT_GROUP_ID, 'test-assistant-id');
    expect(aiAssistantService.hydrateAssistantWithDefaultInfo).toHaveBeenCalledWith(
      {
        assistant: {
          id: 'test-assistant-id',
          name: 'Test Assistant',
          type: AssistantType.ASSISTANT_TYPE_INBOX,
          avatarUrl: 'test-assistant-avatar-url',
          namespace: {
            accountGroupNamespace: {
              accountGroupId: ACCOUNT_GROUP_ID,
            },
          },
        },
        connections: [
          {
            connection: {
              assistantKeys: [
                {
                  id: 'test-assistant-id',
                  namespace: {
                    accountGroupNamespace: {
                      accountGroupId: 'AG-1234',
                    },
                  },
                },
              ],
              connectionType: 'SMS',
              connectionTypeName: 'SMS',
              id: 'test-sms-connection-id',
              name: 'Test SMS Connection',
              namespace: {
                accountGroupNamespace: {
                  accountGroupId: 'AG-1234',
                },
              },
              supportedAssistantTypes: [2],
            },
          },
          {
            connection: {
              assistantKeys: [
                {
                  id: 'test-assistant-id',
                  namespace: {
                    accountGroupNamespace: {
                      accountGroupId: 'AG-1234',
                    },
                  },
                },
              ],
              connectionType: 'WebchatWidget',
              connectionTypeName: 'Web Chat',
              id: 'test-webchat-connection-id',
              isConnectionLocked: true,
              name: 'Test Webchat Connection',
              namespace: {
                accountGroupNamespace: {
                  accountGroupId: 'AG-1234',
                },
              },
              supportedAssistantTypes: [2],
            },
          },
        ],
      },
      expect.anything(),
      expect.anything(),
    );

    expect(component.assistantForm()?.getRawValue()).toEqual({
      name: 'Test Assistant',
      avatarUrl: 'test-assistant-avatar-url',
      capabilities: [],
      knowledge: [],
      voiceConfig: {
        voiceFamily: voiceFamilies[0],
        modelConfig: {
          deepgramConfig: {
            voice: 'aura-asteria-en',
          },
          openAIRealtimeConfig: {
            voice: 'alloy',
            turnDetection: {
              prefixPadding: 300,
              silenceDuration: 500,
              threshold: 0.75,
            },
          },
        },
        vendorModel: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
      },
      connections: expect.any(Array),
    });

    // Get the connections as AiConnectionsForm
    const connectionsForm = component.assistantForm().get('connections') as AiConnectionsForm;
    expect(connectionsForm.controls.length).toBe(2);

    // Find connections by their metadata IDs
    const smsConnection = connectionsForm.controls.find((control) => control.metadata.id === 'test-sms-connection-id');
    const webchatConnection = connectionsForm.controls.find(
      (control) => control.metadata.id === 'test-webchat-connection-id',
    );

    // Check that connections exist
    expect(smsConnection).toBeTruthy();
    expect(webchatConnection).toBeTruthy();

    // Check connection values
    expect(smsConnection?.get('connected')?.value).toBe(true);
    expect(webchatConnection?.get('connected')?.value).toBe(true);

    // Check disabled states
    expect(smsConnection?.get('connected')?.disabled).toBeFalsy();
    expect(webchatConnection?.get('connected')?.disabled).toBeTruthy();
  });

  it('should submit the expected assistant data', async () => {
    component.assistantForm().controls['name'].setValue('Updated Assistant Name');
    component.assistantForm().controls['avatarUrl'].setValue('updated-avatar-url');

    // Get the connections FormArray and set the connected value to false for the SMS connection
    const connectionsArray = component.assistantForm().get('connections') as AiConnectionsForm;
    const smsConnectionForm = connectionsArray.controls.find(
      (control) => control.metadata.id === 'test-sms-connection-id',
    );
    smsConnectionForm?.get('connected')?.setValue(false);
    smsConnectionForm?.markAsDirty();

    component.assistantForm().markAsDirty();

    assistantApiService.upsertAssistant.mockReturnValue(of({}));

    await component.submit();

    // TODO: check this when we're actually calling it
    // expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
    //   assistant: expect.objectContaining({
    //     id: 'test-assistant-id',
    //     name: 'Updated Assistant Name',
    //     namespace: {
    //       accountGroupNamespace: {
    //         accountGroupId: ACCOUNT_GROUP_ID,
    //       },
    //     },
    //     avatarUrl: 'updated-avatar-url',
    //   }),
    //   options: { applyDefaults: false },
    // });

    expect(assistantApiService.setAssistantConnections).toHaveBeenCalledWith({
      assistantKey: {
        id: 'test-assistant-id',
        namespace: {
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        },
      },
      associationStates: [
        {
          connectionKey: {
            id: 'test-sms-connection-id',
            namespace: {
              accountGroupNamespace: {
                accountGroupId: ACCOUNT_GROUP_ID,
              },
            },
            connectionType: ConnectionType.SMS,
          },
          isAssociated: false,
        },
      ],
    });
  });

  it('should update the form when the image changes', () => {
    component.onImageChanged('new-image-url');
    expect(component.assistantForm().get('avatarUrl')?.value).toBe('new-image-url');
    expect(component.assistantForm().get('avatarUrl')?.dirty).toBe(true);
  });

  it('should navigate back when back() is called', () => {
    component.back();
    expect(pageService.navigateToPrevious).toHaveBeenCalled();
  });
});
