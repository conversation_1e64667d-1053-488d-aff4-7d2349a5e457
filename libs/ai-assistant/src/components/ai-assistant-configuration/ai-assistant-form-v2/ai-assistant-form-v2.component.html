<glxy-page>
  @let form = assistantForm();
  @let toolForm = activeToolForm();
  @if (!toolForm) {
    <glxy-page-toolbar>
      <glxy-page-nav>
        <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
      </glxy-page-nav>
      <glxy-page-title>
        @if (!aiAssistant()?.assistant?.id) {
          {{ 'AI_ASSISTANT.SETTINGS.CREATE_CUSTOM_ASSISTANT_TITLE' | translate }}
        } @else {
          {{ 'AI_ASSISTANT.SETTINGS.TITLE' | translate: { assistantName: aiAssistant()?.assistant?.name } }}
        }
        @if (showBetaBadge()) {
          <glxy-badge [color]="'green'" class="beta-badge">{{ 'AI_ASSISTANT.SHARED.BETA' | translate }}</glxy-badge>
        }
      </glxy-page-title>
    </glxy-page-toolbar>
  }
  <glxy-page-wrapper #pageContent>
    @if (loading()) {
      <glxy-loading-spinner class="avatar-loading-spinner"></glxy-loading-spinner>
    } @else if (form) {
      @if (toolForm) {
        <ai-tool-form [form]="toolForm.form" />
        <ai-sticky-footer
          (submitClick)="handleToolFormSubmit()"
          (cancelClick)="activeToolForm.set(undefined)"
          [submitOverride]="'AI_ASSISTANT.FUNCTIONS.EDIT_FUNCTION.DONE' | translate"
          [hideCancel]="toolForm.form.disabled"
        />
      }
      <form [formGroup]="form" [attr.class]="toolForm ? 'form-wrapper hidden' : 'form-wrapper'">
        <!-- Avatar -->
        <div class="avatar">
          <ai-image-upload
            (imageUrlChanged)="onImageChanged($event)"
            [imageUrl]="form.controls.avatarUrl.value"
            [defaultSvg]="aiAssistant()?.decoration?.defaultAvatarIcon ?? defaultAvatarIcon"
          />
        </div>
        <!-- Name -->
        <glxy-form-field>
          <glxy-label>
            {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME' | translate }}
            <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME_TOOLTIP' | translate">
              info_outline
            </mat-icon>
          </glxy-label>
          <input
            matInput
            id="assistant-name"
            formControlName="name"
            placeholder="{{ 'AI_ASSISTANT.SETTINGS.DEFAULT_ASSISTANT_NAME' | translate }}"
          />
        </glxy-form-field>

        <div class="form-cards">
          <!-- Voice Usage Information -->
          @if (isVoiceAssistant()) {
            <glxy-alert type="info">
              <div>
                <strong>Important</strong>
                &mdash; {{ 'AI_ASSISTANT.SETTINGS.VOICE_BETA_BANNER_MESSAGE' | translate }}
              </div>
              <ai-assistant-voice-usage
                class="voice-usage"
                [namespace]="aiAssistant()?.assistant?.namespace"
              ></ai-assistant-voice-usage>
            </glxy-alert>
          }
          <!-- Capabilities -->
          @let capabilitiesForm = form?.controls?.capabilities;
          @if (capabilitiesForm) {
            <ai-capabilities-accordion
              [capabilities]="capabilitiesForm"
              (toolFormDisplay)="handleToolFormDisplay($event)"
            />
          }

          <!-- Connections -->
          @let connectionsForm = form?.controls?.connections;
          @if (connectionsForm && aiConnections()) {
            <ai-connections-form [connectionForms]="connectionsForm"></ai-connections-form>
          }

          <!-- Voice Configuration -->
          @if (isVoiceAssistant()) {
            <ai-voice-configuration-form [voiceConfigForm]="form.controls.voiceConfig" />
          }

          @let knowledgeForm = form?.controls?.knowledge;
          @if (knowledgeForm) {
            <ai-assistant-knowledge-access-card
              [knowledgeFormArray]="knowledgeForm"
            ></ai-assistant-knowledge-access-card>
          }
        </div>
      </form>
      <ai-sticky-footer
        [attr.class]="toolForm ? 'hidden' : ''"
        (submitClick)="submit()"
        (cancelClick)="back()"
        [disabled]="saving()"
      />
    }
  </glxy-page-wrapper>
</glxy-page>
