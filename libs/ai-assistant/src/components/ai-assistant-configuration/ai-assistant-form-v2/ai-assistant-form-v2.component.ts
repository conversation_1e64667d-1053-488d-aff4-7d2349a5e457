import { Component, computed, inject, signal } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Assistant, AssistantType } from '@vendasta/ai-assistants';
import { ACCOUNT_GROUP_ID_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute, Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { ImageUploadComponent } from '../image-upload/image-upload.component';
import { StickyFooterComponent } from '../../sticky-footer/sticky-footer.component';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';
import { DEFAULT_AVATAR_SVG_ICON } from '../../../core/ai-assistant.constants';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyAiIconService } from '@vendasta/galaxy/ai-icon';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { CanComponentDeactivate } from '../../../core/services/unsaved-changes-v2.guard';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { AssistantKnowledgeAccessCardComponent } from '../assistant-knowledge-access-card/assistant-knowledge-access-card.component';

import { AiCapabilityAccordionComponent } from '../../capabilities/ai-capabilities-accordion/ai-capabilities-accordion.component';
import { getNamespace } from '../../../core/services/ai-assistant-utils';
import { AiCapabilityForm, AiToolForm } from '../../../core/forms';
import { AiToolFormComponent } from '../../tools/ai-tool-form/ai-tool-form.component';
import { AiConnectionsFormComponent } from '../../ai-connections-form/ai-connections-form.component';
import { AiVoiceConfigurationFormComponent } from '../../ai-voice-configuration-form/ai-voice-configuration-form.component';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { AiAssistantVoiceUsageComponent } from '../../ai-assistant-voice-usage/ai-assistant-voice-usage.component';

const FALLBACK_BACK_URL = '../..';

const DEFAULT_MEETING_BOOKING_GOAL_ID = 'BookAppointmentsWithBookMeNow';

const BETA_ASSISTANT_TYPES = [AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST, AssistantType.ASSISTANT_TYPE_CUSTOM];

@Component({
  selector: 'ai-ai-assistant-form-v2',
  imports: [
    CommonModule,
    MatInputModule,
    MatCardModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    GalaxyFormFieldModule,
    GalaxyLoadingSpinnerModule,
    AiAssistantI18nModule,
    ImageUploadComponent,
    StickyFooterComponent,
    GalaxyPageModule,
    GalaxyAvatarModule,
    GalaxyTooltipModule,
    MatIconModule,
    GalaxyBadgeModule,
    AssistantKnowledgeAccessCardComponent,
    AiConnectionsFormComponent,
    AiCapabilityAccordionComponent,
    AssistantKnowledgeAccessCardComponent,
    AiVoiceConfigurationFormComponent,
    GalaxyAlertModule,
    AiAssistantVoiceUsageComponent,
    AiToolFormComponent,
  ],
  providers: [AiAssistantFormV2Service],
  templateUrl: './ai-assistant-form-v2.component.html',
  styleUrl: './ai-assistant-form-v2.component.scss',
})
export class AiAssistantFormV2Component implements CanComponentDeactivate {
  private readonly accountGroupId$ = inject(ACCOUNT_GROUP_ID_TOKEN);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);

  private readonly aiAssistantService = inject(AiAssistantService);
  private readonly route = inject(ActivatedRoute);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pageService = inject(PageService);
  private readonly aiIconService = inject(GalaxyAiIconService);
  private readonly router = inject(Router);
  private readonly aiAssistantFormV2Service = inject(AiAssistantFormV2Service);

  readonly defaultAvatarIcon = DEFAULT_AVATAR_SVG_ICON;

  protected readonly assistantForm = toSignal(this.aiAssistantFormV2Service.form$);

  protected readonly aiAssistant = toSignal(this.aiAssistantFormV2Service.aiAssistant$);
  protected readonly activeToolForm = signal<{ parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined>(
    undefined,
  );
  protected readonly aiConnections = toSignal(this.aiAssistantFormV2Service.aiConnections$);
  protected readonly appId = computed(() => 'APP-AI' + this.aiAssistant()?.assistant.id);

  protected readonly loading = computed(() => {
    return this.assistantForm() === undefined;
  });

  protected readonly saving = signal(false);

  protected readonly showBetaBadge = computed(() => {
    const assistantType = this.aiAssistant()?.assistant?.type;
    return assistantType && BETA_ASSISTANT_TYPES.includes(assistantType);
  });

  protected readonly isVoiceAssistant = computed(() => {
    return this.aiAssistant()?.assistant?.type === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
  });

  constructor() {
    // Make sure the default AI icon is registered
    this.aiIconService.dummyInit();
  }

  protected onImageChanged(imageUrl: string): void {
    this.assistantForm()?.controls.avatarUrl.setValue(imageUrl);
    this.assistantForm()?.controls.avatarUrl.markAsDirty();
  }

  protected handleToolFormDisplay(event: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm } | undefined) {
    const { parentCapabilityForm, form: toolForm } = event || {};

    this.activeToolForm.set({
      parentCapabilityForm: parentCapabilityForm,
      form: toolForm?.clone() ?? new AiToolForm({ metadata: { isNew: true } }),
    });
  }

  protected handleToolFormClosed(_toolForm: AiToolForm | undefined) {
    this.activeToolForm.set(undefined);
  }

  protected handleToolFormSubmit() {
    const submittedToolForm = this.activeToolForm();
    const assistantForm = this.assistantForm();
    if (!submittedToolForm || !assistantForm || submittedToolForm.form.disabled) {
      this.activeToolForm.set(undefined);
      return;
    }

    if (submittedToolForm.form?.metadata?.isNew) {
      const matchedCapabilityForm = assistantForm.controls.capabilities.controls.find(
        (capabilityForm) =>
          capabilityForm.controls.goalId.getRawValue() ===
          submittedToolForm.parentCapabilityForm?.controls.goalId.getRawValue(),
      );
      if (matchedCapabilityForm) {
        matchedCapabilityForm.controls.tools.controls.push(submittedToolForm.form);
      }
    } else {
      assistantForm.controls.capabilities.controls.forEach((capabilityForm) => {
        const matchedTool = capabilityForm.controls.tools.controls.find(
          (tool) => tool.controls.id.getRawValue() === submittedToolForm.form.controls.id.getRawValue(),
        );
        if (matchedTool) {
          matchedTool.merge(submittedToolForm.form);
        }
      });
    }

    this.activeToolForm.set(undefined);
  }

  protected async submit(): Promise<void> {
    const form = this.assistantForm();
    if (!form) {
      return;
    }

    try {
      this.saving.set(true);

      const aiAssistant = await firstValueFrom(this.aiAssistantFormV2Service.aiAssistant$);
      let assistant = aiAssistant?.assistant || new Assistant();

      const createNewAssistant = !assistant?.id;

      if (!form.dirty && !createNewAssistant) {
        return;
      }

      if (!form.valid) {
        this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_VALIDATION_ERROR');
        return;
      }

      if (createNewAssistant) {
        // Create a new default assistant first
        const defaultAiAssistant = await firstValueFrom(this.aiAssistantFormV2Service.upsertAssistant(assistant, true));
        assistant = defaultAiAssistant.assistant;
      }

      // Handle base assistant updates (name and avatar)
      if (form.controls.name.dirty || form.controls.avatarUrl.dirty) {
        assistant.name = form.value.name || undefined;
        assistant.avatarUrl = form.value.avatarUrl || undefined;

        await firstValueFrom(this.aiAssistantFormV2Service.upsertAssistant(assistant));
      }

      if (form.controls.voiceConfig.dirty) {
        console.warn('Updating voice config not yet implemented');
      }

      // Handle capability updates
      const capabilities = form.controls.capabilities;
      const namespace = getNamespace(await firstValueFrom(this.accountGroupId$), await firstValueFrom(this.partnerId$));

      if (!namespace) {
        throw new Error('Namespace is required for updating prompt modules');
      }

      for (let i = 0; i < capabilities.length; i++) {
        const capabilityForm = capabilities.at(i);
        if (!capabilityForm.dirty) continue;

        const promptModuleForms = capabilityForm.controls.promptModules;
        const goalId = capabilityForm.controls.goalId.value;

        // Handle meeting booking capability
        // TODO: Could map all values into the goal config object for all goals by default...
        if (goalId === DEFAULT_MEETING_BOOKING_GOAL_ID && capabilityForm.controls.configuration.dirty) {
          const goalConfig = capabilityForm.controls.configuration.value;
          if ((goalConfig?.length ?? 0) > 0) {
            // TODO: Update the goal configuration with the calendar and meeting type
            console.warn('Goal configuration update not yet implemented');
          }
        }

        const promptModuleUpsertPromises: Promise<void>[] = [];
        for (const promptModuleForm of promptModuleForms.controls) {
          if (promptModuleForm.dirty) {
            const promptModuleId = promptModuleForm.controls.id.getRawValue();
            const newPrompt = promptModuleForm.controls.instructions.value;

            const content = promptModuleForm.controls.instructions.dirty
              ? (promptModuleForm.controls.instructions.value ?? undefined)
              : undefined;

            if (promptModuleId && newPrompt && assistant.id) {
              promptModuleUpsertPromises.push(
                this.aiAssistantService.upsertPromptModule(
                  {
                    id: promptModuleId,
                    name: capabilityForm.controls.name.value ?? undefined,
                    description: capabilityForm.controls.description.value ?? undefined,
                  },
                  content,
                ),
              );
            }
          }
        }
        Promise.all(promptModuleUpsertPromises);

        // Handle other capability updates (name, description, functionIds)
        if (
          capabilityForm.controls.goalId.dirty ||
          capabilityForm.controls.name.dirty ||
          capabilityForm.controls.description.dirty ||
          capabilityForm.controls.tools.dirty
        ) {
          // TODO: Implement goal updates once the API is available
          console.warn('Goal updates not yet implemented');
        }
      }

      if (form.controls.connections.dirty) {
        firstValueFrom(
          this.aiAssistantFormV2Service.updateConnections(
            assistant,
            this.aiConnections() || [],
            this.assistantForm()?.controls.connections.controls.map((connectionForm) => ({
              connectionId: connectionForm.metadata.id,
              enabled: connectionForm.get('connected')?.value ?? false,
            })) || [],
          ),
        );
      }

      if (form.controls.knowledge.dirty) {
        await this.aiAssistantFormV2Service.updateAssistantKnowledge(
          this.appId(),
          this.aiAssistant()?.assistant.name || '',
          form.controls.knowledge,
        );
      }

      form.markAsPristine();

      if (createNewAssistant) {
        const accountGroupId = await firstValueFrom(this.accountGroupId$);
        // Navigate to the edit page
        const url = this.aiAssistantService.buildAssistantConfigurationUrl(accountGroupId || '', assistant.id || '');
        this.router.navigateByUrl(url, { replaceUrl: true });
      }
      this.snackbarService.openSuccessSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');
    } catch (e) {
      console.error(e);
      this.snackbarService.openErrorSnack('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATE_ERROR');
    } finally {
      this.saving.set(false);
    }
  }

  protected back(): void {
    this.pageService.navigateToPrevious(FALLBACK_BACK_URL, this.route);
  }

  canDeactivate(): boolean {
    const form = this.assistantForm();
    return !(form && form.dirty);
  }
}
