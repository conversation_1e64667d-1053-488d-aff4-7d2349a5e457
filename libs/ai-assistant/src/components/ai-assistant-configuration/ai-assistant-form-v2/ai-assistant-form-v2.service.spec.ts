import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { AssistantApiService, AssistantType } from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ActivatedRoute } from '@angular/router';
import { HttpResponse } from '@angular/common/http';
import { ACCOUNT_GROUP_ID_TOKEN, AI_DEFAULT_WORKFORCE_TOKEN, PARTNER_ID_TOKEN } from '../../../core/tokens';
import { ConversationApiService, GetMultiWidgetResponse } from '@vendasta/conversation';
import { AiAssistant, AiConnection, ConnectionType } from '../../../core/interfaces/ai-assistant.interface';
import { AiKnowledgeService } from '@galaxy/ai-knowledge';

const ACCOUNT_GROUP_ID = 'AG-1234';
const PARTNER_ID = 'ABC';
describe('AiAssistantFormV2Service', () => {
  let service: AiAssistantFormV2Service;
  let aiAssistantService: jest.Mocked<AiAssistantService>;
  let assistantApiService: jest.Mocked<AssistantApiService>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let MockAiKnowledgeService: jest.Mocked<AiKnowledgeService>;

  beforeEach(() => {
    aiAssistantService = {
      getAssistant: jest.fn(),
      hydrateAssistantWithDefaultInfo: jest.fn(),
      listConnectionsForAssistant: jest.fn(),
    } as unknown as jest.Mocked<AiAssistantService>;

    conversationApiService = {
      getMultiWidget: jest.fn(),
    } as unknown as jest.Mocked<ConversationApiService>;

    assistantApiService = {
      upsertAssistant: jest.fn(),
      setAssistantConnections: jest.fn(),
    } as unknown as jest.Mocked<AssistantApiService>;

    snackbarService = {
      openErrorSnack: jest.fn(),
    } as unknown as jest.Mocked<SnackbarService>;

    MockAiKnowledgeService = {
      businessProfileKSId: jest.fn().mockReturnValue('businessProfileKSId'),
      listAllKnowledgeSourcesForApp: jest.fn().mockResolvedValue([]),
    } as unknown as jest.Mocked<AiKnowledgeService>;

    TestBed.configureTestingModule({
      providers: [
        { provide: PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of({}) },
        { provide: ConversationApiService, useValue: conversationApiService },
        AiAssistantFormV2Service,
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: AssistantApiService, useValue: assistantApiService },
        { provide: SnackbarService, useValue: snackbarService },
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: (key: string) => (key === 'assistantId' ? 'test-assistant-id' : null),
            }),
          },
        },
        { provide: AiKnowledgeService, useValue: MockAiKnowledgeService },
      ],
    });

    service = TestBed.inject(AiAssistantFormV2Service);
  });

  it('should initialize aiConnections$ with the expected value', (done) => {
    const mockAssistant: AiAssistant = {
      assistant: {
        id: 'test-assistant',
        namespace: {
          accountGroupNamespace: {
            accountGroupId: ACCOUNT_GROUP_ID,
          },
        },
        name: 'test assistant',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        avatarUrl:
          'https://lh3.googleusercontent.com/hqc0uF1anrBxASyKpV4zOOqnjeeumBGBx9sUMpt8juosmUSuNdjSO5R9Y_ts-EJaKODTcnpSb3BbFbkF7hiH74bbsy425QUAW3bzegCu',
      },
    };

    aiAssistantService.getAssistant.mockReturnValue(of(mockAssistant));
    aiAssistantService.hydrateAssistantWithDefaultInfo.mockReturnValue(mockAssistant);

    const mockConnections = [
      {
        connection: {
          id: 'webchat-widget-id',
          namespace: {
            accountGroupNamespace: {
              accountGroupId: ACCOUNT_GROUP_ID,
            },
          },
          name: 'Webchat',
          assistantKeys: [
            {
              id: 'test-assistant-id',
              namespace: {
                accountGroupNamespace: {
                  accountGroupId: ACCOUNT_GROUP_ID,
                },
              },
            },
          ],
          connectionType: ConnectionType.WebchatWidget,
          connectionTypeName: 'Web Chat',
          iconUrl: 'https://vstatic-prod.apigateway.co/business-center-client/assets/webchat-icon.svg',
          isConnectionLocked: true,
          supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
        },
      },
      {
        connection: {
          id: 'CONNECTION-sms',
          namespace: {
            accountGroupNamespace: {
              accountGroupId: ACCOUNT_GROUP_ID,
            },
          },
          name: 'SMS',
          configurationUrl: '/account/location/AG-TPMX3ZGWRV/administration/inbox/phone-configuration',
          assistantKeys: [
            {
              id: 'test-assistant-id',
              namespace: {
                accountGroupNamespace: {
                  accountGroupId: ACCOUNT_GROUP_ID,
                },
              },
            },
          ],
          connectionType: ConnectionType.SMS,
          connectionTypeName: 'SMS',
          iconUrl: 'https://vstatic-prod.apigateway.co/business-center-client/assets/phone.svg',
          supportedAssistantTypes: [AssistantType.ASSISTANT_TYPE_INBOX],
        },
      },
    ];

    aiAssistantService.listConnectionsForAssistant.mockReturnValue(of(mockConnections));

    conversationApiService.getMultiWidget.mockReturnValue(
      of(
        new GetMultiWidgetResponse({
          widgets: [
            {
              widgetId: 'webchat-widget-id',
              name: 'My Webchat Widget',
            },
          ],
        }),
      ),
    );

    const expectedConnections = [
      {
        connection: {
          ...mockConnections[0].connection,
          name: 'My Webchat Widget',
        },
      },
      mockConnections[1],
    ];

    service.aiConnections$.subscribe((connections) => {
      expect(connections).toEqual(expectedConnections);
      done();
    });
  });

  it('should call upsertAssistant with the correct parameters', (done) => {
    const mockResponse = { id: 'new-assistant-id' };
    assistantApiService.upsertAssistant.mockReturnValue(of(mockResponse));

    service.upsertAssistant({ id: 'test-assistant-id' }).subscribe((response) => {
      expect(response).toEqual(mockResponse);
      expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
        assistant: { id: 'test-assistant-id' },
        options: { applyDefaults: false },
      });
      done();
    });
  });

  it('should call updateConnections with the correct payload', (done) => {
    const assistant = { id: 'test-assistant-id', namespace: 'test-namespace' } as any;
    const connections = [
      { connection: { id: 'conn1', namespace: 'ns1', connectionType: 'type1', isConnectionLocked: false } },
      { connection: { id: 'conn2', namespace: 'ns2', connectionType: 'type2', isConnectionLocked: true } },
    ] as AiConnection[];
    const connectionStates = [
      { connectionId: 'conn1', enabled: true },
      { connectionId: 'conn2', enabled: false },
    ];

    assistantApiService.setAssistantConnections.mockReturnValue(of(new HttpResponse({ status: 200 })));

    service.updateConnections(assistant, connections, connectionStates).subscribe(() => {
      expect(assistantApiService.setAssistantConnections).toHaveBeenCalledWith({
        assistantKey: { id: 'test-assistant-id', namespace: 'test-namespace' },
        associationStates: [
          {
            connectionKey: { id: 'conn1', namespace: 'ns1', connectionType: 'type1' },
            isAssociated: true,
          },
        ],
      });
      done();
    });
  });
});
