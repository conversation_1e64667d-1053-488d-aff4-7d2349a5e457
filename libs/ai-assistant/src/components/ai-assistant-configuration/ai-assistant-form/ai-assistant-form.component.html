<glxy-page [isLoading]="$isLoading()">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
    </glxy-page-nav>

    <glxy-page-title>
      @if (isCreateMode()) {
        {{ 'AI_ASSISTANT.SETTINGS.CREATE_CUSTOM_ASSISTANT_TITLE' | translate }}
      } @else {
        {{ 'AI_ASSISTANT.SETTINGS.TITLE' | translate: { assistantName: aiAssistant()?.assistant?.name } }}
      }
      @if ($showBetaBadge()) {
        &nbsp;
        <glxy-badge [color]="'green'">{{ 'AI_ASSISTANT.SHARED.BETA' | translate }}</glxy-badge>
      }
    </glxy-page-title>
    @if (isCustomAssistant()) {
      <glxy-page-actions>
        <button mat-stroked-button (click)="openDeleteAssistantConfirmationModal()">
          {{ 'AI_ASSISTANT.SHARED.DELETE' | translate }}
        </button>
      </glxy-page-actions>
    }
  </glxy-page-toolbar>
  <glxy-page-wrapper widthPreset="default">
    @if (!!error()) {
      <ng-container
        [ngTemplateOutlet]="errorTemplate"
        [ngTemplateOutletContext]="{ errorMessage: error() }"
      ></ng-container>
    } @else {
      <div class="form-wrapper">
        <div class="persona-column">
          <!-- Assistant Appearance -->
          <form [formGroup]="assistantForm">
            <!-- Avatar -->
            <div class="avatar">
              @if ($isLoading()) {
                <glxy-loading-spinner class="avatar-loading-spinner"></glxy-loading-spinner>
              } @else {
                <ai-image-upload
                  (imageUrlChanged)="onImageChanged($event)"
                  [imageUrl]="assistantForm.get('assistantAvatarUrl')?.value"
                  [defaultSvg]="aiAssistant()?.decoration?.defaultAvatarIcon ?? defaultAvatarIcon"
                ></ai-image-upload>
              }
            </div>

            <!-- Name -->
            <glxy-form-field>
              <glxy-label class="assistant-name">
                {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME' | translate }}
                <mat-icon
                  class="info-icon"
                  [glxyTooltip]="'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME_TOOLTIP' | translate"
                  [tooltipPositions]="[PopoverPositions.TopLeft, PopoverPositions.BottomLeft]"
                >
                  info_outline
                </mat-icon>
              </glxy-label>
              <input
                matInput
                id="assistant-name"
                formControlName="name"
                placeholder="{{ 'AI_ASSISTANT.SETTINGS.DEFAULT_ASSISTANT_NAME' | translate }}"
              />
            </glxy-form-field>
          </form>
        </div>

        <div class="config-cards">
          @if (showConnections()) {
            <form [formGroup]="connectionForm">
              <div class="config-cards">
                @if ($isVoiceAssistant()) {
                  <glxy-alert type="info">
                    <strong>Important</strong>
                    &mdash; {{ 'AI_ASSISTANT.SETTINGS.VOICE_BETA_BANNER_MESSAGE' | translate }}
                    <ai-assistant-voice-usage
                      class="voice-usage"
                      [namespace]="assistant()?.namespace"
                    ></ai-assistant-voice-usage>
                  </glxy-alert>
                }
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>{{ 'AI_ASSISTANT.SETTINGS.CHANNELS.TITLE' | translate }}</mat-card-title>
                    <mat-card-subtitle>{{ 'AI_ASSISTANT.SETTINGS.CHANNELS.SUBTITLE' | translate }}</mat-card-subtitle>
                  </mat-card-header>
                  <mat-card-content formArrayName="connections">
                    @if ($isLoading()) {
                      <glxy-loading-spinner></glxy-loading-spinner>
                    } @else {
                      @let aiConnections = connections$ | async;
                      @if (aiConnections?.length === 0) {
                        <glxy-empty-state [size]="'small'">
                          <glxy-empty-state-hero>
                            <mat-icon>forum</mat-icon>
                          </glxy-empty-state-hero>
                          <glxy-empty-state-title
                            >{{ 'AI_ASSISTANT.SETTINGS.CHANNELS.NO_CHANNELS_AVAILABLE' | translate }}
                          </glxy-empty-state-title>
                          <!--TODO:  MEGA-1748 - Add contact us button -->
                        </glxy-empty-state>
                      } @else {
                        @let widgetConnections = widgetConnections$ | async;
                        @for (aiConnection of aiConnections; track i; let i = $index) {
                          <glxy-form-field bottomSpacing="none">
                            <mat-checkbox [formControlName]="i" class="connections-checkbox">
                              <div class="checkbox-icon-and-label-wrapper">
                                <img [src]="aiConnection.connection.iconUrl" alt="avatar" class="connection-icon" />
                                <div>
                                  <div>
                                    {{ getConnectionName(aiConnection.connection, widgetConnections || []) }}
                                  </div>
                                  <!-- preventDefault stops the checkbox from being toggled -->
                                  @if (aiConnection.cta?.action?.showButton) {
                                    <a
                                      (click)="triggerAction(aiConnection); $event.preventDefault()"
                                      class="connections-checkbox-settings-link"
                                    >
                                      @if (aiConnection.isDefault && !aiConnection.cta?.label) {
                                        {{
                                          'AI_ASSISTANT.SETTINGS.SETUP_CONNECTIONS'
                                            | translate: { connectionName: aiConnection?.connection.name }
                                        }}
                                      } @else {
                                        {{ aiConnection.cta?.label || 'AI_ASSISTANT.SETTINGS.SETTINGS' | translate }}
                                      }
                                    </a>
                                  }
                                </div>
                              </div>
                            </mat-checkbox>
                          </glxy-form-field>
                        }
                      }
                    }
                  </mat-card-content>
                </mat-card>
              </div>
            </form>
          }
          @if ($isVoiceAssistant()) {
            <form [formGroup]="voiceConfigForm">
              <div class="config-cards">
                <mat-card>
                  <mat-card-header>
                    <mat-card-title>{{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.VOICE_CONFIG' | translate }}</mat-card-title>
                  </mat-card-header>
                  <mat-card-content>
                    <glxy-form-field>
                      <glxy-label>
                        {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY' | translate }}
                      </glxy-label>
                      <mat-select formControlName="family">
                        @for (family of voiceFamilies; track family) {
                          <mat-option [value]="family.name">
                            <div>{{ family.name }}</div>
                          </mat-option>
                        }
                      </mat-select>
                      @let selectedVoiceFamily = voiceFamily();
                      @if (selectedVoiceFamily) {
                        <glxy-hint>
                          {{ selectedVoiceFamily.description_key | translate }}
                        </glxy-hint>
                      }
                    </glxy-form-field>
                    <div class="voice-select-container">
                      <div class="voice-select-label">
                        {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.VOICE' | translate }}
                      </div>
                      <div class="voice-select-and-preview">
                        <glxy-form-field [bottomSpacing]="'none'" class="select-form">
                          <mat-select formControlName="voice">
                            <ng-template #voiceDisplay let-voice>
                              {{ voice.name }} ({{ voice.region | translate }}, {{ voice.gender | translate }})
                              @if (voice.recommended) {
                                <glxy-badge color="green" size="small">{{
                                  'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.RECOMMENDED' | translate
                                }}</glxy-badge>
                              }
                            </ng-template>

                            <mat-select-trigger>
                              <ng-container
                                *ngTemplateOutlet="voiceDisplay; context: { $implicit: selectedAiVoice }"
                              ></ng-container>
                            </mat-select-trigger>

                            @for (voice of availableVoices(); track voice) {
                              <mat-option [value]="voice.value">
                                <ng-container
                                  *ngTemplateOutlet="voiceDisplay; context: { $implicit: voice }"
                                ></ng-container>
                              </mat-option>
                            }
                          </mat-select>
                        </glxy-form-field>
                        <button mat-icon-button class="preview-player" (click)="togglePreview()">
                          @if (currentlyPlayingPreview()) {
                            <mat-icon>stop</mat-icon>
                          } @else {
                            <mat-icon>play_arrow</mat-icon>
                          }
                        </button>
                      </div>
                    </div>
                    @if (voiceFamily()?.provider === vendorModels.VENDOR_MODEL_OPEN_AI_REALTIME) {
                      <cdk-accordion>
                        <cdk-accordion-item #accordionItem="cdkAccordionItem">
                          <a (click)="accordionItem.toggle()">
                            <mat-icon class="accordian-expansion-icon"
                              >{{ accordionItem.expanded ? 'expand_less' : 'expand_more' }}
                            </mat-icon>
                            {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.ADVANCED' | translate }}
                          </a>
                          @if (accordionItem.expanded) {
                            <div class="advanced-voice-options" formGroupName="advanced">
                              <glxy-form-field>
                                <glxy-label>
                                  {{ 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.THRESHOLD.LABEL' | translate }}
                                </glxy-label>
                                <mat-slider min="0" max="1" step="0.01" discrete>
                                  <input matSliderThumb formControlName="turnDetectionThreshold" />
                                </mat-slider>
                                <glxy-hint
                                  >{{
                                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.THRESHOLD.DESCRIPTION'
                                      | translate
                                  }}
                                </glxy-hint>
                              </glxy-form-field>
                              <glxy-form-field>
                                <glxy-label>
                                  {{
                                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.PREFIX_PADDING.LABEL' | translate
                                  }}
                                </glxy-label>
                                <mat-slider min="0" max="1000" step="1" discrete>
                                  <input matSliderThumb formControlName="turnDetectionPrefixPadding" />
                                </mat-slider>
                                <glxy-hint
                                  >{{
                                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.PREFIX_PADDING.DESCRIPTION'
                                      | translate
                                  }}
                                </glxy-hint>
                              </glxy-form-field>
                              <glxy-form-field>
                                <glxy-label>
                                  {{
                                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.SILENCE_DURATION.LABEL'
                                      | translate
                                  }}
                                </glxy-label>
                                <mat-slider min="0" max="1000" step="1" discrete>
                                  <input matSliderThumb formControlName="turnDetectionSilenceDuration" />
                                </mat-slider>
                                <glxy-hint
                                  >{{
                                    'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.TURN_DETECTION.SILENCE_DURATION.DESCRIPTION'
                                      | translate
                                  }}
                                </glxy-hint>
                              </glxy-form-field>
                            </div>
                          }
                        </cdk-accordion-item>
                      </cdk-accordion>
                    }
                  </mat-card-content>
                </mat-card>
              </div>
            </form>
          }

          <form [formGroup]="assistantForm">
            <div class="config-cards">
              <mat-card>
                <mat-card-header>
                  <mat-card-title>{{ 'AI_ASSISTANT.SETTINGS.GOALS_INSTRUCTIONS' | translate }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  @if ($showPrimaryGoalCheckbox()) {
                    <h3>{{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_PRIMARY_GOAL' | translate }}</h3>
                    <glxy-form-field>
                      <mat-checkbox formControlName="enableLeadCapture">
                        {{ 'AI_ASSISTANT.SETTINGS.ENABLE_LEAD_CAPTURE.DESCRIPTION' | translate }}
                        <extended>
                          {{ 'AI_ASSISTANT.SETTINGS.ENABLE_LEAD_CAPTURE.INFORMATION' | translate }}
                          <a style="white-space: nowrap" (click)="openEnableLeadCaptureDialog($event)">
                            {{ 'AI_ASSISTANT.SETTINGS.LEARN_MORE' | translate }}
                          </a>
                        </extended>
                      </mat-checkbox>
                    </glxy-form-field>
                    @if ($isMeetingBookingEnabled()) {
                      <glxy-form-field>
                        <mat-checkbox formControlName="enableMeetingBooking">
                          {{ 'AI_ASSISTANT.SETTINGS.ENABLE_MEETING_BOOKING.DESCRIPTION' | translate }}
                          <extended>
                            {{ 'AI_ASSISTANT.SETTINGS.ENABLE_MEETING_BOOKING.INFORMATION' | translate }}
                            <a style="white-space: nowrap" (click)="openEnableMeetingBookingDialog($event)">
                              {{ 'AI_ASSISTANT.SETTINGS.LEARN_MORE' | translate }}
                            </a>
                          </extended>
                        </mat-checkbox>
                        @if (assistantForm?.get('enableMeetingBooking')?.value) {
                          <glxy-form-field class="meeting-config">
                            <glxy-label>
                              {{ 'AI_ASSISTANT.SETTINGS.ENABLE_MEETING_BOOKING.LABEL' | translate }}
                            </glxy-label>
                            <mat-select formControlName="calendarAndMeetingType" [compareWith]="compareBookingOptions">
                              @for (calendar of calendarsWithTypes$ | async; track calendar) {
                                @if (calendar.meetingTypes.length > 0) {
                                  <mat-optgroup [label]="calendar.calendarName" class="calendar-optgroup">
                                    @for (type of calendar.meetingTypes; track type) {
                                      <mat-option [value]="type">
                                        @if (calendar.isPersonal) {
                                          <mat-icon>person</mat-icon>
                                        } @else {
                                          <mat-icon>group</mat-icon>
                                        }
                                        {{ type.name }}
                                      </mat-option>
                                    }
                                  </mat-optgroup>
                                }
                              }
                              <mat-option [value]="null" (click)="goToMeetings()" class="create-new-calendar">
                                <mat-icon>open_in_new</mat-icon>
                                <span class="option-text">
                                  {{ 'AI_ASSISTANT.SETTINGS.ENABLE_MEETING_BOOKING.MANAGE_EVENT_LINKS' | translate }}
                                </span>
                              </mat-option>
                            </mat-select>
                          </glxy-form-field>
                        }
                      </glxy-form-field>
                    }
                  }
                  @if (!showCustomGoals()) {
                    <h3>
                      {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_ADDITIONAL_INSTRUCTIONS' | translate }}
                      <glxy-badge color="green" size="small">
                        {{ 'AI_ASSISTANT.SHARED.BETA' | translate }}
                      </glxy-badge>
                    </h3>
                    <glxy-form-field>
                      <glxy-label>
                        {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_ADDITIONAL_INSTRUCTIONS_DESCRIPTION' | translate }}
                        <a style="white-space: nowrap" (click)="openAdditionalInstructionsDialog()">
                          {{ 'AI_ASSISTANT.SETTINGS.LEARN_MORE' | translate }}
                        </a>
                      </glxy-label>
                      <textarea
                        matInput
                        cdkTextareaAutosize
                        cdkAutosizeMinRows="5"
                        cdkAutosizeMaxRows="20"
                        formControlName="additionalPrompt"
                        [placeholder]="
                          'AI_ASSISTANT.SETTINGS.ASSISTANT_ADDITIONAL_INSTRUCTIONS_DESCRIPTION' | translate
                        "
                        #additionalPromptSoftLimit
                      ></textarea>
                      <glxy-hint *ngIf="additionalPromptSoftLimit.value?.length <= additionalInstructionsLimit">
                        {{ additionalPromptSoftLimit.value?.length || 0 }}/{{ additionalInstructionsLimit }}
                      </glxy-hint>
                      <glxy-error
                        align="end"
                        *ngIf="additionalPromptSoftLimit.value?.length > additionalInstructionsLimit"
                      >
                        {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_ADDITIONAL_INSTRUCTIONS_LIMIT_EXCEEDED' | translate }}
                        &nbsp; {{ additionalPromptSoftLimit.value?.length || 0 }} /{{ additionalInstructionsLimit }}
                      </glxy-error>
                    </glxy-form-field>
                  } @else {
                    <h3>{{ 'AI_ASSISTANT.SETTINGS.CUSTOM_CAPABILITIES' | translate }}</h3>
                    <ai-goals-list
                      [goals]="customGoals()"
                      (goalClicked)="editGoal($event)"
                      (goalRemoved)="removeGoal($event)"
                    ></ai-goals-list>
                    <mat-card class="add-button" (click)="openGoalsModal()">
                      <mat-card-content>{{ 'AI_ASSISTANT.SETTINGS.ADD_CAPABILITY' | translate }}</mat-card-content>
                    </mat-card>
                  }
                </mat-card-content>
              </mat-card>
              <mat-card>
                <mat-card-header>
                  <mat-card-title>{{ 'AI_ASSISTANT.SETTINGS.KNOWLEDGE_SOURCES' | translate }}</mat-card-title>
                  <mat-card-subtitle
                    >{{ 'AI_ASSISTANT.SETTINGS.KNOWLEDGE_SOURCES_DESCRIPTION' | translate }}
                  </mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  @if ($isLoading()) {
                    <glxy-loading-spinner class="avatar-loading-spinner"></glxy-loading-spinner>
                  } @else {
                    <ai-knowledge-application-knowledge
                      [appId]="knowledgeForm.controls?.knowledgeAppId?.value"
                      (selectedSourcesChange)="updateSelectedSources($event)"
                    ></ai-knowledge-application-knowledge>
                  }
                </mat-card-content>
              </mat-card>
            </div>
          </form>
        </div>
      </div>
      <ng-content></ng-content>

      <ai-sticky-footer
        (submitClick)="submit()"
        [disabled]="(!$canSave() && !newGoalToSave()) || $isSaving()"
        (cancelClick)="back()"
      ></ai-sticky-footer>
    }
  </glxy-page-wrapper>
</glxy-page>

<ng-template #errorTemplate let-errorMessage="errorMessage">
  <glxy-alert type="error">
    <strong>{{ 'AI_ASSISTANT.SETTINGS.SOMETHING_WENT_WRONG' | translate }}</strong>
    <glxy-alert-extended>
      {{ errorMessage }}
    </glxy-alert-extended>
  </glxy-alert>
</ng-template>
