import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AiAssistantFormComponent } from './ai-assistant-form.component';
import { LexiconModule } from '@galaxy/lexicon';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatDialog } from '@angular/material/dialog';
import {
  ACCOUNT_GROUP_ID_TOKEN as KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN,
  AiKnowledgeService,
  BUSINESS_PROFILE_URL_TOKEN,
  MANAGE_KNOWLEDGE_URL_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN as KNOWLEDGE_PARTNER_ID_TOKEN,
  SHOW_BUSINESS_PROFILE_SOURCE_TOKEN,
  WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN,
} from '@galaxy/ai-knowledge';
import { of } from 'rxjs';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { AccountGroupService } from '@galaxy/account-group';
import { ActivatedRoute, Params, Router } from '@angular/router';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  CURRENT_USER_ID_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../core/tokens';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import { BookingConfigService } from '../../../core/services/booking-config.service';
import { Namespace } from '@vendasta/ai-assistants';
import { AiConnection } from '../../../core/interfaces/ai-assistant.interface';
import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';

describe('AssistantFormComponent', () => {
  let component: TestAiAssistantFormComponent;
  let fixture: ComponentFixture<TestAiAssistantFormComponent>;
  let router: Router;
  let route: ActivatedRoute;

  // Create a test class that extends the component to access protected methods
  @Component({
    standalone: true,
    template: '',
  })
  class TestAiAssistantFormComponent extends AiAssistantFormComponent {
    public testBuildConnectionActionCallback(aiConnection: AiConnection): (() => void) | undefined {
      return this.buildConnectionActionCallback(aiConnection);
    }

    // Expose protected properties for testing
    public get testAssistantForm() {
      return this.assistantForm;
    }
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        LexiconModule.forRoot(),
        TranslateTestingModule.withTranslations({}),
        TestAiAssistantFormComponent,
      ],
      providers: [
        { provide: MatDialog, useValue: {} },
        { provide: AiKnowledgeService, useValue: {} },
        { provide: AccountGroupService, useValue: {} },
        {
          provide: AiAssistantService,
          useValue: {
            buildAppId: () => jest.fn(),
            getAssistant: () => of({}),
            getAssistantWithDefaultInfo: () => of({}),
            listConnections: () => of([]),
            listConnectionsForAssistant: () => of([]),
            $isAssistantGoalsEnabled: () => true,
            $isAIMeetingBookingEnabled: () => false,
          },
        },
        {
          provide: BookingConfigService,
          useValue: {
            getCalendarsWithTypes: () => of([]),
          },
        },
        { provide: PARTNER_ID_TOKEN, useValue: of('ABC') },
        { provide: KNOWLEDGE_PARTNER_ID_TOKEN, useValue: of('ABC') },
        { provide: MARKET_ID_TOKEN, useValue: of('default') },
        { provide: KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN, useValue: of('AG-123') },
        { provide: MANAGE_KNOWLEDGE_URL_TOKEN, useValue: of('some-url') },
        { provide: BUSINESS_PROFILE_URL_TOKEN, useValue: of('some-url') },
        { provide: SHOW_BUSINESS_PROFILE_SOURCE_TOKEN, useValue: of(false) },
        { provide: WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN, useValue: of(['some-url']) },
        { provide: ActivatedRoute, useValue: {} },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useExisting: KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN },
        { provide: CURRENT_USER_ID_TOKEN, useValue: of('user-id') },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              params: { ['assistantId']: 'assistant-id' } as Params,
            },
            paramMap: of({
              get: () => 'assistant-id',
            }),
            queryParams: of({}),
          },
        },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of([]) },
        {
          provide: Router,
          useValue: {
            navigate: jest.fn(),
          },
        },
        {
          provide: Location,
          useValue: {
            back: jest.fn(),
          },
        },
        {
          provide: PageService,
          useValue: {},
        },
        {
          provide: UnsavedChangesGuard,
          useValue: {
            canDeactivate: () => true,
            notifyStateChanged: jest.fn(),
          },
        },
      ],
    }).compileComponents();

    router = TestBed.inject(Router);
    route = TestBed.inject(ActivatedRoute);

    fixture = TestBed.createComponent(TestAiAssistantFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should not require a name', () => {
    component.testAssistantForm?.setValue({
      name: '',
      assistantId: '',
      enableLeadCapture: false,
      enableMeetingBooking: false,
      calendarAndMeetingType: {
        calendarId: '',
        meetingTypeId: '',
      },
      additionalPrompt: '',
      assistantAvatarUrl: '',
      namespace: {} as Namespace,
    });

    expect(component.testAssistantForm?.valid).toEqual(true);
  });

  it('should invalidate prompt over limit', () => {
    component.testAssistantForm?.setValue({
      name: 'immaname',
      assistantId: 'widgetId',
      namespace: {} as Namespace,
      enableLeadCapture: true,
      calendarAndMeetingType: {
        calendarId: '',
        meetingTypeId: '',
      },
      enableMeetingBooking: false,
      additionalPrompt: `
Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.

Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.
Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.

Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.
Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.

Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.
Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.

Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.

Alas were stairs Gundabad charm upset guarding oaths defense. Attacked ever-watchful uttered brigands caverns couldn't base whether river known Dunland clever. Nerves shaken inbreds joke Glóin pillage track following? Misty Mountain beautiful distant misery liege tempted thieves enchanting. Grievances Maggot's arrangements demanding safety playing. Winds sausages Witch-king uncle board clothing field lived lthilien and. Today is my 111th birthday! Fire-breathing halt shine valley something's Haldir obvious beard strangers shh. Let's spell deposit longer trousers enchanting Grubbs walk other they'll then. Caradhras only you'll fishermen.

Coward reaction thin serve force use admire now Rohirrim province antique aloft. Destroying only panic challenge raised hunting butter store griping Mirkwood. Gandalf's death was not in vain. Nor would he have you give up hope. Emyn power mission. Stead scare mountains goodbye distances firestorm Dory food alongside tired fully. Scales then underground cater villain Wizards defiling contend schemes sour wit shields. Beauty Brandybucks eavesdropping bestow. Told Angmar's 200 rat starving tomb blunt flank exceeding.
`,
      assistantAvatarUrl: '',
    });

    expect(component.testAssistantForm?.invalid).toEqual(true);
  });

  describe('buildConnectionActionCallback', () => {
    it('should return undefined when no cta or action.showButton is false', () => {
      const aiConnection: AiConnection = {
        connection: {} as any,
        isDefault: false,
      };
      const result = (component as TestAiAssistantFormComponent).testBuildConnectionActionCallback(aiConnection);
      expect(result).toBeUndefined();
    });

    it('should return inbox navigation function when url contains inbox and has partner namespace', () => {
      const aiConnection: AiConnection = {
        connection: {
          namespace: {
            partnerNamespace: {},
          },
        } as any,
        cta: {
          action: {
            url: '/inbox/settings',
            showButton: true,
            relativeRoute: true,
          },
        },
      };
      const result = (component as TestAiAssistantFormComponent).testBuildConnectionActionCallback(aiConnection);
      expect(result).toBeDefined();
      result?.();
      expect(router.navigate).toHaveBeenCalledWith(['/inbox/settings'], { relativeTo: route });
    });

    it('should return relative navigation function when url and relativeRoute are present', () => {
      const aiConnection: AiConnection = {
        connection: {} as any,
        cta: {
          action: {
            url: '/test/url',
            relativeRoute: true,
            showButton: true,
          },
        },
      };
      const result = (component as TestAiAssistantFormComponent).testBuildConnectionActionCallback(aiConnection);
      expect(result).toBeDefined();
      result?.();
      expect(router.navigate).toHaveBeenCalledWith(['/test/url'], { relativeTo: route });
    });

    it('should return navigation function without relativeTo when relativeRoute is false', () => {
      const aiConnection: AiConnection = {
        connection: {} as any,
        cta: {
          action: {
            url: 'http://test.com',
            relativeRoute: false,
            showButton: true,
          },
        },
      };
      const result = (component as TestAiAssistantFormComponent).testBuildConnectionActionCallback(aiConnection);
      expect(result).toBeDefined();
      result?.();
      expect(router.navigate).toHaveBeenCalledWith(['http://test.com'], undefined);
    });

    it('should return pathCommands navigation function when present', () => {
      const aiConnection: AiConnection = {
        connection: {} as any,
        cta: {
          action: {
            pathCommands: ['/path', 'to', 'navigate'],
            showButton: true,
          },
        },
      };
      const result = (component as TestAiAssistantFormComponent).testBuildConnectionActionCallback(aiConnection);
      expect(result).toBeDefined();
      result?.();
      expect(router.navigate).toHaveBeenCalledWith(['/path', 'to', 'navigate']);
    });
  });
});
