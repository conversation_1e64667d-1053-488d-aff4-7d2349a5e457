import { Observable } from 'rxjs';
import { Action, AiAssistant, AiConnection } from './ai-assistant.interface';
import { VendorModel } from '@vendasta/ai-assistants';

export interface AssistantInfo {
  assistantId: string;
  iconUrl?: string;
  cardDisabled?: boolean;
  cta?: Action;
  hideCard?: boolean;
}

export interface AiAssistantConfig {
  // Current partner ID.
  partnerId$: Observable<string | undefined | null>;

  // Optional: Current market ID
  marketId$: Observable<string | undefined | null>;

  // Optional: Current account group ID.
  accountGroupId$: Observable<string | undefined | null>;

  // Optional: default ai assistant list.
  defaultAIWorkforce$: Observable<AiAssistant[] | undefined | null>;

  // Optional: default connection list.
  defaultConnections$: Observable<AiConnection[] | undefined | null>;

  // Optional: current user ID.
  currentUserId$: Observable<string | undefined | null>;

  // Optional: whether the voice AI feature is available.
  voiceAIAvailable$: Observable<boolean | undefined | null>;
}

export interface AiVoice {
  name: string;
  gender: string;
  region: string;
  value: string;
  previewAudioUrl?: string;
  recommended?: boolean;
}

export interface AiVoiceFamily {
  name: string;
  description_key: string;
  voices: AiVoice[];
  provider: VendorModel;
}

export interface MeetingType {
  name: string;
  meetingTypeId: string;
  calendarId: string;
}

export interface CalendarWithMeetingTypes {
  calendarName: string;
  calendarId: string;
  isPersonal: boolean;
  meetingTypes: MeetingType[];
}
