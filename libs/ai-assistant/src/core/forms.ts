import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import {
  ConfigurableGoalInterface,
  ConfigVoiceConfigInterface,
  DeepgramConfigInterface,
  FunctionHeaderInterface,
  FunctionInterface,
  FunctionParameterInterface,
  FunctionParameterParameterLocation,
  KeyValuePair,
  ModelConfigInterface,
  OpenAIRealtimeConfigInterface,
  PromptModuleInterface,
  VendorModel,
} from '@vendasta/ai-assistants';
import { AiAssistant, AiConnection, CTAction } from './interfaces/ai-assistant.interface';
import { KnowledgeSource, WebsiteScrapeConfigCrawlMode } from '@vendasta/embeddings';
import { urlValidator } from '@vendasta/forms';
import { AiVoiceFamily } from './interfaces/config.interface';
import { familyFromVoice, voiceFamilies } from './services/ai-assistant-utils';

// Assistant
export type AiAssistantFormControls = {
  name: FormControl<string | null>;
  avatarUrl: FormControl<string | null>;
  capabilities: AiCapabilityFormArray;
  connections: AiConnectionsForm;
  knowledge: AiKnowledgeFormArray;
  voiceConfig: AiVoiceConfigurationForm;
};

export class AiAssistantForm extends FormGroup<AiAssistantFormControls> {
  constructor(
    assistant?: AiAssistant,
    promptModuleContents?: Record<string, string>,
    knowledgeSources?: KnowledgeSource[],
  ) {
    const goals = assistant?.assistant.configurableGoals?.filter((goal) => goal !== undefined) || [];
    super({
      name: new FormControl(assistant?.assistant.name || ''),
      avatarUrl: new FormControl(assistant?.assistant.avatarUrl || ''),
      capabilities: new AiCapabilityFormArray(goals, promptModuleContents || {}),
      connections: new AiConnectionsForm(assistant?.connections || [], assistant?.assistant.id || ''),
      knowledge: new AiKnowledgeFormArray(knowledgeSources ?? []),
      voiceConfig: new AiVoiceConfigurationForm(assistant?.assistant?.config?.voiceConfig),
    });
  }
}

export type CapabilityConfigurationFormControl = FormControl<KeyValuePair[] | null>;

// Capabilities
export type AiCapabilityFormControls = {
  goalId: FormControl<string | null>;
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  promptModules: AiPromptModuleFormArray;
  tools: AiToolFormArray;
  configuration: CapabilityConfigurationFormControl;
};

export class AiCapabilityForm extends FormGroup<AiCapabilityFormControls> {
  constructor(goal?: ConfigurableGoalInterface, promptModuleContents?: Record<string, string>) {
    const capability = goal?.goal ?? null;
    const keyValuePairs = goal?.configuration
      ?.map((v): KeyValuePair => {
        if (v instanceof KeyValuePair) {
          return v;
        }
        return new KeyValuePair(v);
      })
      ?.filter((v) => Boolean(v.key && v.value));
    super({
      goalId: new FormControl({ value: capability?.id ?? null, disabled: true }),
      name: new FormControl(capability?.name ?? null, Validators.required),
      description: new FormControl(capability?.description ?? null),
      promptModules: new AiPromptModuleFormArray({
        promptModules: capability?.promptModules || [],
        contents: promptModuleContents || {},
      }),
      tools: new AiToolFormArray(capability?.functions || []),
      configuration: new FormControl<KeyValuePair[] | null>(keyValuePairs ?? null),
    });
    if (capability?.managed || capability?.namespace?.globalNamespace) {
      this.disable();
    }
    this.controls.configuration.enable();
  }
}

export class AiKnowledgeFormArray extends FormArray<KnowledgeForm> {
  constructor(knowledgeSources: KnowledgeSource[]) {
    super(knowledgeSources.map((knowledgeSource) => new KnowledgeForm(knowledgeSource)));
  }
}

export class AiCapabilityFormArray extends FormArray<AiCapabilityForm> {
  constructor(capabilities: ConfigurableGoalInterface[], promptModuleContents: Record<string, string>) {
    super(capabilities.map((capability) => new AiCapabilityForm(capability, promptModuleContents)));
  }
}

// Prompt Module
export type AiPromptModuleControls = {
  id: FormControl<string | null>;
  instructions: FormControl<string | null>;
};

export class AiPromptModuleForm extends FormGroup<AiPromptModuleControls> {
  constructor(opts: { promptModule?: PromptModuleInterface; contents?: string }) {
    const { promptModule, contents } = opts;
    super({
      id: new FormControl({ value: promptModule?.id ?? null, disabled: true }),
      instructions: new FormControl(contents ?? null),
    });

    if (promptModule?.managed || promptModule?.namespace?.globalNamespace) {
      this.disable();
    }
  }
}

export class AiPromptModuleFormArray extends FormArray<AiPromptModuleForm> {
  constructor(opts: { promptModules: PromptModuleInterface[]; contents: Record<string, string> }) {
    const { promptModules, contents } = opts;
    super(
      promptModules.map((pm) => {
        const content = pm.id ? contents[pm.id] : undefined;
        return new AiPromptModuleForm({ promptModule: pm, contents: content });
      }),
    );
  }
}

// Tools
type AiToolFormMetadata = {
  isNew: boolean;
};

export type AiToolFormControls = {
  id: FormControl<string | null>;
  generatesAnswer: FormControl<boolean | null>;
  description: FormControl<string | null>;
  method: FormControl<string | null>;
  url: FormControl<string | null>;
  headers: AiToolHeaderFormArray;
  parameters: FormArray<AiToolParameterForm>;
};

type AiToolFormOptions = {
  func?: FunctionInterface;
  metadata?: AiToolFormMetadata;
};

export class AiToolForm extends FormGroup<AiToolFormControls> {
  metadata: AiToolFormMetadata;

  constructor(opts?: AiToolFormOptions) {
    const { func, metadata = { isNew: false } } = opts ?? {};

    super({
      id: new FormControl({ value: func?.id ?? null, disabled: !metadata.isNew }),
      generatesAnswer: new FormControl({ value: func?.generatesAnswer ?? false, disabled: true }),
      description: new FormControl(func?.description ?? null, Validators.required),
      method: new FormControl(func?.methodType ?? null),
      url: new FormControl(func?.url ?? null),
      headers: new AiToolHeaderFormArray(func?.headers ?? []),
      parameters: new AiToolParameterFormArray(func?.functionParameters ?? []),
    });
    this.metadata = metadata;

    if (func?.managed || func?.namespace?.globalNamespace) {
      this.disable();
    }
  }

  clone(): AiToolForm {
    const clonedForm = new AiToolForm({ metadata: this.metadata });

    // You can only patch values into FormArrays if the length of the receiving array is the same as the length of values you are trying to patch
    clonedForm.controls.headers.controls.push(
      ...(this.controls.headers.controls.map(() => new AiToolHeaderForm()) ?? []),
    );
    clonedForm.controls.parameters.controls.push(
      ...(this.controls.parameters.controls.map((paramForm) => paramForm.clone()) ?? []),
    );

    clonedForm.patchValue(this.getRawValue());

    if (this.disabled) {
      clonedForm.disable();
    }
    if (this.controls.id.disabled) {
      clonedForm.controls.id.disable();
    }

    return clonedForm;
  }

  merge(other: AiToolForm) {
    this.controls.headers.clear();
    other.controls.headers.controls.forEach((otherHeaderForm) => this.controls.headers.push(otherHeaderForm));

    this.controls.parameters.clear();
    other.controls.parameters.controls.forEach((otherParamForm) => this.controls.parameters.push(otherParamForm));

    this.metadata = { ...other.metadata };
    this.patchValue(other.getRawValue());
  }
}

export class AiToolFormArray extends FormArray<AiToolForm> {
  constructor(functions: FunctionInterface[]) {
    super(functions.map((func) => new AiToolForm({ func })));
  }
}

// Tool Headers
export type AiToolHeaderFormControls = {
  name: FormControl<string | null>;
  value: FormControl<string | null>;
};

export class AiToolHeaderForm extends FormGroup<AiToolHeaderFormControls> {
  constructor(header?: FunctionHeaderInterface) {
    super({
      name: new FormControl(header?.key ?? null),
      value: new FormControl(header?.value ?? null),
    });
  }
}

export class AiToolHeaderFormArray extends FormArray<AiToolHeaderForm> {
  constructor(headers: FunctionHeaderInterface[]) {
    super(headers.map((header) => new AiToolHeaderForm(header)));
  }
}

// Tool Parameters
export type AiToolParameterFormControls = {
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  type: FormControl<string | null>;
  location: FormControl<FunctionParameterParameterLocation | null>;
  automaticallyFilled: FormControl<boolean | null>;
  nestedParameters: AiToolParameterFormArray;
};

export class AiToolParameterForm extends FormGroup<AiToolParameterFormControls> {
  constructor(toolParameter?: FunctionParameterInterface) {
    const nestedParameters =
      toolParameter?.type?.toLocaleLowerCase() === 'object'
        ? toolParameter?.properties
        : toolParameter?.type?.toLowerCase() === 'array'
          ? [toolParameter?.items].filter((item) => item !== undefined)
          : [];
    super({
      name: new FormControl(toolParameter?.name ?? null),
      description: new FormControl(toolParameter?.description ?? null, Validators.required),
      type: new FormControl(toolParameter?.type ?? null, Validators.required),
      location: new FormControl(
        toolParameter?.location ?? FunctionParameterParameterLocation.LOCATION_BODY,
        Validators.required,
      ),
      automaticallyFilled: new FormControl(false),
      nestedParameters: new AiToolParameterFormArray(nestedParameters ?? []),
    });
  }

  clone(): AiToolParameterForm {
    const clone = new AiToolParameterForm();

    clone.controls.nestedParameters.controls.push(
      ...(this.controls.nestedParameters.controls.map((nestedForm) => nestedForm.clone()) ?? []),
    );

    clone.patchValue(this.getRawValue());
    return clone;
  }

  merge(other: AiToolParameterForm) {
    this.controls.nestedParameters.clear();
    other.controls.nestedParameters.controls.forEach((otherParamForm) =>
      this.controls.nestedParameters.push(otherParamForm),
    );
    this.patchValue(other.getRawValue());
  }
}

export class AiToolParameterFormArray extends FormArray<AiToolParameterForm> {
  constructor(parameters: FunctionParameterInterface[]) {
    super(parameters.map((param) => new AiToolParameterForm(param)));
  }
}

export interface ConnectionMetadata {
  id: string;
  name: string;
  iconUrl: string;
  isDefault: boolean;
  cta?: CTAction;
}

interface ConnectionFormControls {
  connected: FormControl<boolean | null>;
}

class ConnectionForm extends FormGroup<ConnectionFormControls> {
  constructor(
    public readonly metadata: ConnectionMetadata,
    connected: boolean,
    disabled: boolean,
  ) {
    super({
      connected: new FormControl({ value: connected, disabled }),
    });
  }
}

export class AiConnectionsForm extends FormArray<ConnectionForm> {
  constructor(aiConnections: AiConnection[], assistantId: string) {
    const connectionForms = aiConnections
      .map((aiConnection) => {
        if (!aiConnection?.connection?.id) {
          return null;
        }

        const metadata: ConnectionMetadata = {
          id: aiConnection.connection.id,
          name: aiConnection.connection.name || '',
          iconUrl: aiConnection.connection.iconUrl || '',
          isDefault: aiConnection.isDefault || false,
          cta: aiConnection.cta,
        };

        const connected =
          aiConnection.connection.assistantKeys?.some((assistantKey) => assistantKey.id === assistantId) || false;

        const disabled = aiConnection.connection.isConnectionLocked || aiConnection.isDefault || false;

        return new ConnectionForm(metadata, connected, disabled);
      })
      .filter((form): form is ConnectionForm => form !== null);

    super(connectionForms);
  }
}

export type DeepgramConfigFormControls = {
  voice: FormControl<string | null>;
};

export class DeepgramConfigForm extends FormGroup<DeepgramConfigFormControls> {
  constructor(deepgramConfig?: DeepgramConfigInterface) {
    super({
      voice: new FormControl(deepgramConfig?.voice ?? 'aura-asteria-en'),
    });
  }
}

export type OpenAIRealtimeConfigFormControls = {
  voice: FormControl<string | null>;
  turnDetection: FormGroup<{
    threshold: FormControl<number | null>;
    prefixPadding: FormControl<number | null>;
    silenceDuration: FormControl<number | null>;
  }>;
};

export class OpenAIRealtimeConfigForm extends FormGroup<OpenAIRealtimeConfigFormControls> {
  constructor(openAIRealtimeConfig?: OpenAIRealtimeConfigInterface) {
    super({
      voice: new FormControl(openAIRealtimeConfig?.voice ?? 'alloy'),
      turnDetection: new FormGroup({
        threshold: new FormControl(openAIRealtimeConfig?.turnDetection?.threshold ?? 0.75),
        prefixPadding: new FormControl(openAIRealtimeConfig?.turnDetection?.prefixPadding ?? 300),
        silenceDuration: new FormControl(openAIRealtimeConfig?.turnDetection?.silenceDuration ?? 500),
      }),
    });
  }
}

export type ModelConfigFormControls = {
  openAIRealtimeConfig: OpenAIRealtimeConfigForm;
  deepgramConfig: DeepgramConfigForm;
};

export class ModelConfigForm extends FormGroup<ModelConfigFormControls> {
  constructor(modelConfig?: ModelConfigInterface) {
    super({
      openAIRealtimeConfig: new OpenAIRealtimeConfigForm(modelConfig?.openaiRealtimeConfig),
      deepgramConfig: new DeepgramConfigForm(modelConfig?.deepgramConfig),
    });
  }
}

export type AiVoiceConfigFormControls = {
  vendorModel: FormControl<VendorModel | null>;
  voiceFamily: FormControl<AiVoiceFamily | null>;
  modelConfig: ModelConfigForm;
};

export class AiVoiceConfigurationForm extends FormGroup<AiVoiceConfigFormControls | null> {
  constructor(voiceConfig?: ConfigVoiceConfigInterface) {
    let voice = 'alloy';
    switch (voiceConfig?.vendorModel) {
      case VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME:
        voice = voiceConfig?.modelConfig?.openaiRealtimeConfig?.voice ?? '';
        break;
      case VendorModel.VENDOR_MODEL_DEEPGRAM:
        voice = voiceConfig?.modelConfig?.deepgramConfig?.voice ?? '';
        break;
    }
    const voiceFamily = familyFromVoice(voice);

    super({
      vendorModel: new FormControl(voiceConfig?.vendorModel ?? VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME),
      voiceFamily: new FormControl(voiceFamily ?? voiceFamilies[0]),
      modelConfig: new ModelConfigForm(voiceConfig?.modelConfig),
    });
  }
}

export type KnowledgeFormData = {
  name: FormControl<string>;
  content: FormControl<string>;
  url: FormControl<string>;
  crawlMode: FormControl<WebsiteScrapeConfigCrawlMode>;
  raw: FormControl<KnowledgeSource>;
};

export class KnowledgeForm extends FormGroup<KnowledgeFormData> {
  constructor(knowledge?: KnowledgeSource) {
    const isWebsite = !!knowledge?.config?.websiteScrapeConfig;
    const isCustomData = !!knowledge?.config?.customDataConfig;

    super({
      name: new FormControl(knowledge?.name || '', {
        nonNullable: true,
        validators: isCustomData ? [Validators.required] : [],
      }),
      content: new FormControl(knowledge?.config?.customDataConfig?.text || '', {
        nonNullable: true,
        validators: isCustomData ? [Validators.required] : [],
      }),
      url: new FormControl(knowledge?.config?.websiteScrapeConfig?.url || '', {
        nonNullable: true,
        validators: isWebsite ? [urlValidator, Validators.required] : [],
      }),
      crawlMode: new FormControl(
        knowledge?.config?.websiteScrapeConfig?.crawlMode ||
          WebsiteScrapeConfigCrawlMode.WEBSITE_SCRAPE_CONFIG_CRAWL_MODE_SINGLE,
        { nonNullable: true },
      ),
      raw: new FormControl(knowledge!, { nonNullable: true }),
    });
  }
}
