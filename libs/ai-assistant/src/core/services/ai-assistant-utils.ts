import {
  AssistantType,
  FunctionInterface,
  FunctionParameterInterface,
  Namespace,
  NamespaceInterface,
  VendorModel,
} from '@vendasta/ai-assistants';

import {
  ASSISTANT_ID_CHAT_RECEPTIONIST,
  ASSISTANT_ID_CONTENT_CREATOR,
  ASSISTANT_ID_REVIEW_MANAGER,
  ASSISTANT_ID_SALES_COACH,
  ASSISTANT_ID_SEO_ANALYST,
  ASSISTANT_ID_VOICE_RECEPTIONIST,
} from '../ai-assistant.constants';
import { AiVoiceFamily } from '../interfaces/config.interface';
import {
  FunctionAuthStrategyInterface,
  FunctionHeaderInterface,
} from '@vendasta/ai-assistants/lib/_internal/interfaces/function.interface';

export function convertAssistantTypeToDefaultID(type: AssistantType): string {
  switch (type) {
    case AssistantType.ASSISTANT_TYPE_INBOX:
      return ASSISTANT_ID_CHAT_RECEPTIONIST;
    case AssistantType.ASSISTANT_TYPE_SOCIAL_MARKETING:
      return ASSISTANT_ID_CONTENT_CREATOR;
    case AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST:
      return ASSISTANT_ID_VOICE_RECEPTIONIST;
    default:
      return 'unspecified';
  }
}

export function AssistantIdToAssistantType(id: string): AssistantType {
  switch (id) {
    case ASSISTANT_ID_CHAT_RECEPTIONIST:
      return AssistantType.ASSISTANT_TYPE_INBOX;
    case ASSISTANT_ID_CONTENT_CREATOR:
      return AssistantType.ASSISTANT_TYPE_SOCIAL_MARKETING;
    case ASSISTANT_ID_VOICE_RECEPTIONIST:
      return AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
    default:
      return AssistantType.ASSISTANT_TYPE_CUSTOM;
  }
}

export function AssistantIdToDefaultAssistantName(id: string): string {
  switch (id) {
    case ASSISTANT_ID_CHAT_RECEPTIONIST:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CHAT_RECEPTIONIST.TITLE';
    case ASSISTANT_ID_CONTENT_CREATOR:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.CONTENT_CREATOR.TITLE';
    case ASSISTANT_ID_VOICE_RECEPTIONIST:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.VOICE_RECEPTIONIST.TITLE';
    case ASSISTANT_ID_REVIEW_MANAGER:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.REVIEW_MANAGER.TITLE';
    case ASSISTANT_ID_SEO_ANALYST:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.SEO_ANALYST.TITLE';
    case ASSISTANT_ID_SALES_COACH:
      return 'AI_ASSISTANT.WORKFORCE.DEFAULT_ASSISTANTS.SALES_COACH.TITLE';
    default:
      return '';
  }
}

export const voiceFamilies: AiVoiceFamily[] = [
  {
    name: 'OpenAI GPT-4o Realtime (Sep. 17 Preview)',
    provider: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
    description_key: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY_DESCRIPTION.GPT_RT_PREVIEW_SEPT_2024',
    voices: [
      {
        name: 'Alloy',
        value: 'alloy',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/alloy.wav',
      },
      {
        name: 'Echo',
        value: 'echo',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/echo.wav',
      },
      {
        name: 'Shimmer',
        value: 'shimmer',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/shimmer.wav',
      },
    ],
  },
  {
    name: 'OpenAI GPT-4o Realtime (Dec. 17 Preview)',
    provider: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
    description_key: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY_DESCRIPTION.GPT_RT_PREVIEW_DEC_2024',
    voices: [
      {
        name: 'Ash',
        value: 'ash',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/ash.wav',
      },
      {
        name: 'Ballad',
        value: 'ballad',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.UK',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/ballad.wav',
      },
      {
        name: 'Coral',
        value: 'coral',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/coral.wav',
      },
      {
        name: 'Sage',
        value: 'sage',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/sage.wav',
      },
      {
        name: 'Verse',
        value: 'verse',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl: 'https://storage.googleapis.com/voice-receptionist-preview-audios/Openai%20audios/verse.wav',
      },
    ],
  },
  {
    name: 'Deepgram Aura-1',
    provider: VendorModel.VENDOR_MODEL_DEEPGRAM,
    description_key: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY_DESCRIPTION.DEEPGRAM_AURA',
    voices: [
      {
        name: 'Asteria',
        value: 'aura-asteria-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Asteria.mp3',
      },
      {
        name: 'Luna',
        value: 'aura-luna-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Luna.mp3',
      },
      {
        name: 'Stella',
        value: 'aura-stella-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Stella.mp3',
      },
      {
        name: 'Athena',
        value: 'aura-athena-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.UK',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Athena.mp3',
      },
      {
        name: 'Hera',
        value: 'aura-hera-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Hera.mp3',
      },
      {
        name: 'Orion',
        value: 'aura-orion-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Orion.mp3',
      },
      {
        name: 'Arcas',
        value: 'aura-arcas-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Arcas.mp3',
      },
      {
        name: 'Perseus',
        value: 'aura-perseus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Perseus.mp3',
      },
      {
        name: 'Angus',
        value: 'aura-angus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.IRELAND',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Angus.mp3',
      },
      {
        name: 'Orpheus',
        value: 'aura-orpheus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Orpheus.mp3',
      },
      {
        name: 'Helios',
        value: 'aura-helios-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.UK',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Helios.mp3',
      },
      {
        name: 'Zeus',
        value: 'aura-zeus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Zeus.mp3',
      },
    ],
  },
  {
    name: 'Deepgram Aura-2',
    provider: VendorModel.VENDOR_MODEL_DEEPGRAM,
    description_key: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.FAMILY_DESCRIPTION.DEEPGRAM_AURA_2',
    voices: [
      {
        name: 'Thalia',
        value: 'aura-2-thalia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Thalia.mp3',
        recommended: true,
      },
      {
        name: 'Andromeda',
        value: 'aura-2-andromeda-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Andromeda.mp3',
        recommended: true,
      },
      {
        name: 'Helena',
        value: 'aura-2-helena-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Helena.mp3',
        recommended: true,
      },
      {
        name: 'Apollo',
        value: 'aura-2-apollo-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Apollo.mp3',
        recommended: true,
      },
      {
        name: 'Arcas',
        value: 'aura-2-arcas-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Arcas.mp3',
        recommended: true,
      },
      {
        name: 'Aries',
        value: 'aura-2-aries-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Aries.mp3',
        recommended: true,
      },
      {
        name: 'Amalthea',
        value: 'aura-2-amalthea-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.PHILIPPINES',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Amalthea.mp3',
      },
      {
        name: 'Asteria',
        value: 'aura-2-asteria-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Asteria.mp3',
      },
      {
        name: 'Athena',
        value: 'aura-2-athena-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Athena.mp3',
      },
      {
        name: 'Atlas',
        value: 'aura-2-atlas-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Atlas.mp3',
      },
      {
        name: 'Aurora',
        value: 'aura-2-aurora-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Aurora.mp3',
      },
      {
        name: 'Callista',
        value: 'aura-2-callista-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Callista.mp3',
      },
      {
        name: 'Cora',
        value: 'aura-2-cora-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Cora.mp3',
      },
      {
        name: 'Cordelia',
        value: 'aura-2-cordelia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Cordelia.mp3',
      },
      {
        name: 'Delia',
        value: 'aura-2-delia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Delia.mp3',
      },
      {
        name: 'Draco',
        value: 'aura-2-draco-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.UK',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Draco.mp3',
      },
      {
        name: 'Electra',
        value: 'aura-2-electra-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Electra.mp3',
      },
      {
        name: 'Harmonia',
        value: 'aura-2-harmonia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Harmonia.mp3',
      },
      {
        name: 'Hera',
        value: 'aura-2-hera-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Hera.mp3',
      },
      {
        name: 'Hermes',
        value: 'aura-2-hermes-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Hermes.mp3',
      },
      {
        name: 'Hyperion',
        value: 'aura-2-hyperion-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.AUSTRALIA',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Hyperion.mp3',
      },
      {
        name: 'Iris',
        value: 'aura-2-iris-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Iris.mp3',
      },
      {
        name: 'Janus',
        value: 'aura-2-janus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Janus.mp3',
      },
      {
        name: 'Juno',
        value: 'aura-2-juno-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Juno.mp3',
      },
      {
        name: 'Jupiter',
        value: 'aura-2-jupiter-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Jupiter.mp3',
      },
      {
        name: 'Luna',
        value: 'aura-2-luna-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Luna.mp3',
      },
      {
        name: 'Mars',
        value: 'aura-2-mars-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Mars.mp3',
      },
      {
        name: 'Minerva',
        value: 'aura-2-minerva-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Minerva.mp3',
      },
      {
        name: 'Neptune',
        value: 'aura-2-neptune-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Neptune.mp3',
      },
      {
        name: 'Odysseus',
        value: 'aura-2-odysseus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Odysseus.mp3',
      },
      {
        name: 'Ophelia',
        value: 'aura-2-ophelia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Ophelia.mp3',
      },
      {
        name: 'Orion',
        value: 'aura-2-orion-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Orion.mp3',
      },
      {
        name: 'Orpheus',
        value: 'aura-2-orpheus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Orpheus.mp3',
      },
      {
        name: 'Pandora',
        value: 'aura-2-pandora-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.UK',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Pandora.mp3',
      },
      {
        name: 'Phoebe',
        value: 'aura-2-phoebe-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Phoebe.mp3',
      },
      {
        name: 'Pluto',
        value: 'aura-2-pluto-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Pluto.mp3',
      },
      {
        name: 'Saturn',
        value: 'aura-2-saturn-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Saturn.mp3',
      },
      {
        name: 'Selene',
        value: 'aura-2-selene-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Selene.mp3',
      },
      {
        name: 'Theia',
        value: 'aura-2-theia-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.AUSTRALIA',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Theia.mp3',
      },
      {
        name: 'Vesta',
        value: 'aura-2-vesta-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.FEMALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Vesta.mp3',
      },
      {
        name: 'Zeus',
        value: 'aura-2-zeus-en',
        gender: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.GENDER.MALE',
        region: 'AI_ASSISTANT.SETTINGS.VOICE_CONFIG.REGION.US',
        previewAudioUrl:
          'https://storage.googleapis.com/voice-receptionist-preview-audios/Deepgram%20audios/Deepgram%20Aura2%20Zeus.mp3',
      },
    ],
  },
];

export function getVoiceFamilies(deepgramEnabled: boolean): AiVoiceFamily[] {
  const results: AiVoiceFamily[] = [];
  for (const family of voiceFamilies) {
    if (family.provider === VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME) {
      results.push(family);
    }
    if (family.provider === VendorModel.VENDOR_MODEL_DEEPGRAM && deepgramEnabled) {
      results.push(family);
    }
  }
  return results;
}

export function familyFromVoice(voice?: string) {
  return voiceFamilies.find((family) => family.voices.find((v) => v.value === voice));
}

export function voicesForFamily(family: string) {
  return voiceFamilies.find((f) => f.name === family)?.voices || [];
}

export function previewUrlFromVoice(voice: string) {
  for (const family of voiceFamilies) {
    const foundVoice = family.voices.find((v) => v.value === voice);
    if (foundVoice) {
      return foundVoice.previewAudioUrl;
    }
  }
  return undefined;
}

export function getNamespace(accountGroupId: string | undefined, partnerId: string | undefined): Namespace | undefined {
  if (accountGroupId) {
    return new Namespace({
      accountGroupNamespace: {
        accountGroupId: accountGroupId,
      },
    });
  } else if (partnerId) {
    return new Namespace({
      partnerNamespace: {
        partnerId: partnerId,
      },
    });
  }
  return;
}

export class AIFunction implements FunctionInterface {
  id?: string;
  namespace?: NamespaceInterface;
  description?: string;
  url?: string;
  methodType?: string;
  functionParameters?: FunctionParameterInterface[];
  headers?: FunctionHeaderInterface[];
  managed?: boolean;
  generatesAnswer?: boolean;
  updated?: Date;
  authStrategy?: FunctionAuthStrategyInterface;
}
