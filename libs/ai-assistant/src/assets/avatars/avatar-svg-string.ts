export const ChatReceptionistAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 60C46.569 60 60 46.569 60 30C60 13.431 46.569 0 30 0C13.431 0 0 13.431 0 30C0 46.569 13.431 60 30 60Z" fill="url(#paint0_linear_0_1)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M45.786 30.0868C45.786 37.9528 38.784 44.3308 30.15 44.3308C21.516 44.3308 14.514 37.9528 14.514 30.0868C14.514 22.2208 21.516 15.8428 30.15 15.8428C38.784 15.8428 45.786 22.2208 45.786 30.0868ZM30.414 21.5158C30.339 21.4528 30.243 21.4198 30.147 21.4168H30.144C30.048 21.4168 29.952 21.4528 29.877 21.5158C29.802 21.5788 29.754 21.6658 29.736 21.7618C29.328 24.1858 28.521 25.9618 27.348 27.2278C26.175 28.4908 24.597 29.2828 22.584 29.6818C22.491 29.6998 22.404 29.7508 22.344 29.8258C22.284 29.8978 22.251 29.9908 22.251 30.0868C22.251 30.1828 22.284 30.2758 22.344 30.3478C22.404 30.4228 22.491 30.4738 22.584 30.4918C26.526 31.2778 28.929 34.4068 29.739 38.4238C29.757 38.5168 29.808 38.6008 29.883 38.6608C29.958 38.7208 30.051 38.7538 30.147 38.7538C30.243 38.7538 30.336 38.7208 30.411 38.6608C30.486 38.6008 30.537 38.5168 30.555 38.4238C31.362 34.4068 33.765 31.2748 37.71 30.4918C37.803 30.4738 37.89 30.4228 37.95 30.3478C38.01 30.2728 38.043 30.1828 38.043 30.0868C38.043 29.9908 38.01 29.8978 37.95 29.8258C37.89 29.7508 37.803 29.6998 37.71 29.6818C35.697 29.2828 34.125 28.4908 32.949 27.2278C31.773 25.9618 30.966 24.1858 30.558 21.7618C30.543 21.6658 30.492 21.5788 30.417 21.5158H30.414Z" fill="white"/>
<path d="M35.067 41.9368L39.39 44.8738C39.915 45.2308 40.602 44.7868 40.479 44.1688L38.934 36.5188C38.895 36.3298 38.649 36.2788 38.541 36.4348L35.004 41.6398C34.938 41.7358 34.965 41.8708 35.064 41.9398L35.067 41.9368Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.761 12.4019C13.815 12.4019 13.866 12.4199 13.905 12.4559C13.944 12.4889 13.974 12.5369 13.983 12.5909C14.112 13.6889 14.562 14.7239 15.279 15.5669C15.972 16.2809 16.881 16.7489 17.862 16.9049C17.913 16.9169 17.961 16.9409 17.994 16.9799C18.027 17.0189 18.045 17.0699 18.045 17.1209C18.045 17.1719 18.027 17.2229 17.994 17.2619C17.961 17.3009 17.916 17.3309 17.862 17.3369C15.723 17.7659 14.418 19.4699 13.98 21.6599C13.968 21.7109 13.944 21.7559 13.902 21.7919C13.863 21.8249 13.809 21.8459 13.761 21.8459C13.713 21.8459 13.659 21.8279 13.617 21.7919C13.578 21.7589 13.548 21.7139 13.539 21.6599C13.098 19.4699 11.799 17.7629 9.657 17.3369C9.606 17.3249 9.558 17.3009 9.525 17.2619C9.492 17.2229 9.474 17.1719 9.474 17.1209C9.474 17.0699 9.492 17.0189 9.525 16.9799C9.558 16.9409 9.603 16.9109 9.657 16.9049C10.641 16.7489 11.547 16.2809 12.24 15.5669C12.957 14.7239 13.407 13.6889 13.536 12.5909C13.545 12.5369 13.572 12.4919 13.614 12.4559C13.653 12.4229 13.707 12.4019 13.758 12.4019H13.764H13.761Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_0_1" x1="0" y1="0" x2="60" y2="57" gradientUnits="userSpaceOnUse">
<stop offset="0.09" stop-color="#22C0CA"/>
<stop offset="0.915" stop-color="#19E8C2"/>
</linearGradient>
</defs>
</svg>
`;

export const VoiceReceptionistAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1_30)">
<path d="M29.9994 59.9939C46.5681 59.9939 59.9988 46.5632 59.9988 29.9945C59.9988 13.4258 46.5681 -0.00488281 29.9994 -0.00488281C13.4307 -0.00488281 0 13.4258 0 29.9945C0 46.5632 13.4307 59.9939 29.9994 59.9939Z" fill="url(#paint0_linear_1_30)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M41.585 26C41.63 26 41.672 26.015 41.705 26.045C41.738 26.075 41.762 26.114 41.768 26.156C41.876 27.071 42.251 27.935 42.848 28.637C43.424 29.231 44.183 29.624 45.002 29.753C45.044 29.762 45.083 29.783 45.113 29.816C45.14 29.849 45.158 29.891 45.158 29.936C45.158 29.981 45.143 30.023 45.113 30.056C45.083 30.089 45.047 30.113 45.002 30.119C43.22 30.476 42.131 31.898 41.765 33.722C41.756 33.764 41.735 33.803 41.699 33.833C41.666 33.863 41.621 33.878 41.579 33.878C41.537 33.878 41.492 33.863 41.459 33.833C41.426 33.803 41.402 33.767 41.393 33.722C41.027 31.898 39.941 30.476 38.156 30.119C38.111 30.11 38.075 30.089 38.045 30.056C38.015 30.023 38 29.981 38 29.936C38 29.891 38.015 29.849 38.045 29.816C38.072 29.783 38.111 29.759 38.156 29.753C38.975 29.624 39.734 29.234 40.31 28.637C40.907 27.935 41.282 27.071 41.39 26.156C41.396 26.111 41.42 26.072 41.453 26.045C41.486 26.015 41.531 26 41.573 26H41.579H41.585Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.887 12C31.947 12 32.007 12.021 32.052 12.06C32.097 12.099 32.13 12.153 32.139 12.213C32.286 13.464 32.799 14.643 33.615 15.603C34.404 16.416 35.439 16.95 36.558 17.127C36.615 17.139 36.669 17.169 36.708 17.214C36.747 17.259 36.768 17.316 36.768 17.376C36.768 17.436 36.747 17.493 36.708 17.538C36.669 17.583 36.618 17.616 36.558 17.625C34.122 18.114 32.637 20.055 32.136 22.548C32.124 22.605 32.094 22.659 32.046 22.698C32.001 22.737 31.941 22.758 31.884 22.758C31.827 22.758 31.767 22.737 31.722 22.698C31.677 22.659 31.644 22.608 31.632 22.548C31.131 20.055 29.649 18.111 27.21 17.625C27.15 17.613 27.099 17.583 27.06 17.538C27.021 17.493 27 17.436 27 17.376C27 17.316 27.021 17.259 27.06 17.214C27.099 17.169 27.15 17.136 27.21 17.127C28.329 16.95 29.364 16.416 30.153 15.603C30.969 14.643 31.482 13.464 31.629 12.213C31.638 12.153 31.671 12.099 31.716 12.06C31.761 12.021 31.821 12 31.881 12H31.887Z" fill="white"/>
<path d="M41.35 38.6333C39.3 38.6333 37.3167 38.3 35.4667 37.7C34.8833 37.5 34.2333 37.65 33.7833 38.1L31.1667 41.3833C26.45 39.1333 22.0333 34.8833 19.6833 30L22.9333 27.2333C23.3833 26.7667 23.5167 26.1167 23.3333 25.5333C22.7167 23.6833 22.4 21.7 22.4 19.65C22.4 18.75 21.65 18 20.75 18H14.9833C14.0833 18 13 18.4 13 19.65C13 35.1333 25.8833 48 41.35 48C42.5333 48 43 46.95 43 46.0333V40.2833C43 39.3833 42.25 38.6333 41.35 38.6333Z" fill="white"/>
</g>
<defs>
<linearGradient id="paint0_linear_1_30" x1="0" y1="-0.00488281" x2="59.9988" y2="59.9939" gradientUnits="userSpaceOnUse">
<stop offset="0.135" stop-color="#6D13FF"/>
<stop offset="0.845" stop-color="#E013FF"/>
</linearGradient>
<clipPath id="clip0_1_30">
<rect width="60" height="60" fill="white"/>
</clipPath>
</defs>
</svg>
`;

export const ContentWriterAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29.9994 59.9939C46.5681 59.9939 59.9988 46.5632 59.9988 29.9945C59.9988 13.4258 46.5681 -0.00488281 29.9994 -0.00488281C13.4307 -0.00488281 0 13.4258 0 29.9945C0 46.5632 13.4307 59.9939 29.9994 59.9939Z" fill="url(#paint0_linear_2848_5423)"/>
<path d="M43.7723 24.2049C44.9303 23.0589 44.9423 21.1929 43.7963 20.0349L40.5893 16.7889C39.4433 15.6309 37.5773 15.6189 36.4193 16.7649L34.6973 18.4659L42.1043 25.8519L43.7723 24.2049Z" fill="white"/>
<path d="M16.3707 36.5824L15.3597 43.9773C15.2847 44.5353 15.7587 45.0153 16.3167 44.9463L23.7235 44.0193L40.3522 27.5826L32.9453 20.1968L16.3707 36.5824Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M41.8492 38.0554C41.8942 38.0554 41.9362 38.0704 41.9692 38.1004C42.0022 38.1304 42.0262 38.1694 42.0322 38.2114C42.1402 39.1264 42.5152 39.9904 43.1122 40.6924C43.6882 41.2864 44.4472 41.6794 45.2662 41.8084C45.3082 41.8174 45.3472 41.8384 45.3772 41.8714C45.4042 41.9044 45.4222 41.9464 45.4222 41.9914C45.4222 42.0364 45.4072 42.0784 45.3772 42.1114C45.3472 42.1444 45.3112 42.1684 45.2662 42.1744C43.4842 42.5314 42.3952 43.9534 42.0292 45.7774C42.0202 45.8194 41.9992 45.8584 41.9632 45.8884C41.9302 45.9184 41.8852 45.9334 41.8432 45.9334C41.8012 45.9334 41.7562 45.9184 41.7232 45.8884C41.6902 45.8584 41.6662 45.8224 41.6572 45.7774C41.2912 43.9534 40.2052 42.5314 38.4202 42.1744C38.3752 42.1654 38.3392 42.1444 38.3092 42.1114C38.2792 42.0784 38.2642 42.0364 38.2642 41.9914C38.2642 41.9464 38.2792 41.9044 38.3092 41.8714C38.3362 41.8384 38.3752 41.8144 38.4202 41.8084C39.2392 41.6794 39.9982 41.2894 40.5742 40.6924C41.1712 39.9904 41.5462 39.1264 41.6542 38.2114C41.6602 38.1664 41.6842 38.1274 41.7172 38.1004C41.7502 38.0704 41.7952 38.0554 41.8372 38.0554H41.8432H41.8492Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.4667 14.0859C19.5267 14.0859 19.5867 14.1069 19.6317 14.1459C19.6767 14.1849 19.7097 14.2389 19.7187 14.2989C19.8657 15.5499 20.3787 16.7289 21.1947 17.6889C21.9837 18.5019 23.0187 19.0359 24.1377 19.2129C24.1947 19.2249 24.2487 19.2549 24.2877 19.2999C24.3267 19.3449 24.3477 19.4019 24.3477 19.4619C24.3477 19.5219 24.3267 19.5789 24.2877 19.6239C24.2487 19.6689 24.1977 19.7019 24.1377 19.7109C21.7017 20.1999 20.2167 22.1409 19.7157 24.6339C19.7037 24.6909 19.6737 24.7449 19.6257 24.7839C19.5807 24.8229 19.5207 24.8439 19.4637 24.8439C19.4067 24.8439 19.3467 24.8229 19.3017 24.7839C19.2567 24.7449 19.2237 24.6939 19.2117 24.6339C18.7107 22.1409 17.2287 20.1969 14.7897 19.7109C14.7297 19.6989 14.6787 19.6689 14.6397 19.6239C14.6007 19.5789 14.5797 19.5219 14.5797 19.4619C14.5797 19.4019 14.6007 19.3449 14.6397 19.2999C14.6787 19.2549 14.7297 19.2219 14.7897 19.2129C15.9087 19.0359 16.9437 18.5019 17.7327 17.6889C18.5487 16.7289 19.0617 15.5499 19.2087 14.2989C19.2177 14.2389 19.2507 14.1849 19.2957 14.1459C19.3407 14.1069 19.4007 14.0859 19.4607 14.0859H19.4667Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_2848_5423" x1="0" y1="-0.00488281" x2="59.9988" y2="59.9939" gradientUnits="userSpaceOnUse">
<stop offset="0.135" stop-color="#0097EC"/>
<stop offset="0.845" stop-color="#00D1FF"/>
</linearGradient>
</defs>
</svg>`;

export const ReviewManagerAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M29.43 59.9947C46.4494 60.3097 60.3097 46.4494 59.9947 29.43C59.6977 13.3916 46.6114 0.302273 30.57 0.00526534C13.5506 -0.309742 -0.309742 13.5506 0.00526534 30.57C0.302273 46.6114 13.3916 59.6977 29.43 59.9947Z" fill="url(#paint0_linear_2848_5351)"/>
<path d="M30.0634 17.1963L34.4677 25.7298C34.4677 25.7298 34.483 25.7481 34.4952 25.7481L44.0319 27.3172C44.0625 27.3233 44.0716 27.3569 44.0533 27.3783L37.2602 34.2509C37.2602 34.2509 37.248 34.2693 37.251 34.2815L38.7069 43.8335C38.71 43.864 38.6824 43.8855 38.6549 43.8702L30.0175 39.5331C30.0175 39.5331 29.9961 39.527 29.9838 39.5331L21.3464 43.8702C21.3189 43.8824 21.2883 43.861 21.2944 43.8335L22.7503 34.2815C22.7503 34.2815 22.7503 34.2601 22.7411 34.2509L15.9481 27.3783C15.9267 27.3569 15.9389 27.3233 15.9695 27.3172L25.5061 25.7481C25.5061 25.7481 25.5275 25.7389 25.5336 25.7298L29.9716 17.1443C29.9838 17.1168 30.0236 17.1168 30.0358 17.1443L30.0634 17.1963Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.9587 17.6749L30.9726 17.6481C30.9681 17.6572 30.9634 17.6661 30.9587 17.6749ZM29.0389 17.6562L29.055 17.6865C29.0495 17.6765 29.0442 17.6664 29.0389 17.6562ZM30.0033 19.5232L33.5282 26.3526L33.6066 26.4468L34.4677 25.7298C33.6066 26.4468 33.6068 26.4469 33.6069 26.4471L33.6077 26.4481L33.6089 26.4495L33.6112 26.4523L33.6162 26.4581C33.6196 26.4621 33.6233 26.4664 33.6273 26.4709C33.6353 26.4799 33.6446 26.4903 33.6554 26.5017C33.6755 26.5231 33.7067 26.5546 33.7474 26.5895C33.7845 26.6213 33.8533 26.6766 33.949 26.7289C34.0143 26.7645 34.148 26.83 34.3314 26.8568L41.7817 28.0825L36.3877 33.5397L36.328 33.6291L37.2602 34.2509C36.328 33.6291 36.3279 33.6293 36.3278 33.6295L36.327 33.6307L36.326 33.6323L36.3238 33.6355L36.3193 33.6424C36.3163 33.6471 36.313 33.6522 36.3096 33.6576C36.3029 33.6684 36.2953 33.681 36.2871 33.6952C36.2717 33.722 36.25 33.7621 36.228 33.8122C36.2276 33.8131 36.227 33.8144 36.2263 33.816C36.208 33.8566 36.0898 34.1185 36.1446 34.459L37.2829 41.9274L30.4263 38.4845L30.3251 38.4556L30.0175 39.5331C30.3251 38.4556 30.3249 38.4556 30.3247 38.4555L30.3243 38.4554L30.3234 38.4551L30.3216 38.4546L30.3179 38.4536L30.3102 38.4515C30.3049 38.45 30.2992 38.4485 30.2932 38.447C30.2812 38.4439 30.2673 38.4406 30.2519 38.4373C30.2224 38.4309 30.1801 38.4228 30.1289 38.4171C30.1278 38.417 30.1264 38.4168 30.1249 38.4167C30.0713 38.4105 29.7885 38.378 29.4827 38.5309L29.481 38.5317L22.7184 41.9274L23.8709 34.3662L23.8709 34.2811L22.7503 34.2815C23.8709 34.2811 23.8709 34.2808 23.8709 34.2806V34.2801L23.8709 34.2792L23.8708 34.2773L23.8708 34.2733L23.8707 34.265C23.8707 34.2594 23.8705 34.2533 23.8703 34.2469C23.8699 34.2341 23.8693 34.2194 23.8682 34.2031C23.8661 34.1721 23.8619 34.1275 23.8526 34.0746C23.8524 34.0734 23.8521 34.072 23.8519 34.0704C23.8427 34.0168 23.7916 33.7197 23.5381 33.4632C23.5379 33.4631 23.5378 33.4629 23.5376 33.4628L18.2196 28.0825L25.8225 26.8316L25.9477 26.7779L25.5061 25.7481C25.9477 26.7779 25.9476 26.778 25.9477 26.7779L25.9483 26.7777L25.9499 26.777L25.9521 26.7761L25.9567 26.774L25.9671 26.7694C25.9744 26.7661 25.983 26.7622 25.9924 26.7577C26.0103 26.7491 26.0365 26.7361 26.0671 26.7189C26.0951 26.7031 26.1437 26.6745 26.1992 26.6328C26.2406 26.6018 26.3606 26.5093 26.466 26.3513L26.5005 26.2996L30.0033 19.5232ZM24.7888 24.7305L28.9687 16.6443C29.3888 15.7919 30.6104 15.7895 31.0351 16.637L31.0564 16.6773L35.213 24.7306L44.2328 26.2146L44.2516 26.2184C45.1816 26.4044 45.4398 27.4826 44.904 28.1076L44.8782 28.1378L38.4407 34.6506L39.819 43.6932L39.8219 43.722C39.8598 44.1011 39.7088 44.514 39.3535 44.7737C38.9884 45.0406 38.5209 45.06 38.1461 44.8685L30.0007 40.7785L21.8257 44.8834L21.8015 44.8941C21.4122 45.0672 20.9814 45.0072 20.6629 44.7831C20.3482 44.5616 20.1049 44.1394 20.1892 43.6482L21.5606 34.6506L15.152 28.1669C14.8332 27.8452 14.7377 27.3775 14.8833 26.9673C15.0244 26.5695 15.3627 26.2958 15.7497 26.2184L15.7686 26.2146L24.7888 24.7305ZM22.4007 44.0122L22.4041 43.9899C22.403 43.9974 22.4019 44.0048 22.4007 44.0122Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.7287 12.4016C18.7827 12.4016 18.8337 12.4196 18.8727 12.4556C18.9117 12.4886 18.9417 12.5366 18.9507 12.5906C19.0797 13.6886 19.5297 14.7237 20.2468 15.5667C20.9398 16.2807 21.8488 16.7487 22.8298 16.9047C22.8808 16.9167 22.9288 16.9407 22.9618 16.9797C22.9948 17.0187 23.0128 17.0697 23.0128 17.1207C23.0128 17.1717 22.9948 17.2227 22.9618 17.2617C22.9288 17.3007 22.8838 17.3307 22.8298 17.3367C20.6908 17.7657 19.3857 19.4698 18.9477 21.6598C18.9357 21.7108 18.9117 21.7558 18.8697 21.7918C18.8307 21.8248 18.7767 21.8458 18.7287 21.8458C18.6807 21.8458 18.6267 21.8278 18.5847 21.7918C18.5457 21.7588 18.5157 21.7138 18.5067 21.6598C18.0657 19.4698 16.7667 17.7627 14.6246 17.3367C14.5736 17.3247 14.5256 17.3007 14.4926 17.2617C14.4596 17.2227 14.4416 17.1717 14.4416 17.1207C14.4416 17.0697 14.4596 17.0187 14.4926 16.9797C14.5256 16.9407 14.5706 16.9107 14.6246 16.9047C15.6086 16.7487 16.5147 16.2807 17.2077 15.5667C17.9247 14.7237 18.3747 13.6886 18.5037 12.5906C18.5127 12.5366 18.5397 12.4916 18.5817 12.4556C18.6207 12.4226 18.6747 12.4016 18.7257 12.4016H18.7317H18.7287Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_2848_5351" x1="0" y1="0" x2="51" y2="52" gradientUnits="userSpaceOnUse">
<stop offset="0.12" stop-color="#FF846C"/>
<stop offset="0.845" stop-color="#F7CB16"/>
</linearGradient>
</defs>
</svg>
`;

export const SalesCoachAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 60C46.569 60 60 46.569 60 30C60 13.431 46.569 0 30 0C13.431 0 0 13.431 0 30C0 46.569 13.431 60 30 60Z" fill="#E0E0E0"/>
<path d="M32.9411 39.1556C31.7891 40.3106 29.9171 40.3106 28.7621 39.1556L24.0041 34.3976C23.7731 34.1666 23.3981 34.1666 23.1671 34.3976L16.7471 40.8236C16.5161 41.0546 16.1411 41.0546 15.9101 40.8236L12.6551 37.5686C12.4241 37.3376 12.4241 36.9626 12.6551 36.7316L21.4991 27.8906C22.6511 26.7356 24.5231 26.7356 25.6781 27.8906L30.4361 32.6486C30.6671 32.8796 31.0421 32.8796 31.2731 32.6486L40.1261 23.7956C40.3571 23.5646 40.7291 23.5646 40.9631 23.7956L44.2151 27.0506C44.4461 27.2816 44.4461 27.6566 44.2151 27.8876L32.9441 39.1586L32.9411 39.1556Z" fill="white"/>
<path d="M47.5181 20.3396H31.7389C31.4302 20.3396 31.1809 20.5889 31.1809 20.8976V25.2425C31.1809 25.5512 31.4302 25.8004 31.7389 25.8004H47.5181V20.3396Z" fill="white"/>
<path d="M47.5153 20.3396H42.0545V36.1188C42.0545 36.4276 42.3065 36.6768 42.6124 36.6768H46.9573C47.266 36.6768 47.5153 36.4276 47.5153 36.1188V20.3396Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.467 14.0911C19.527 14.0911 19.587 14.1121 19.632 14.1511C19.677 14.1901 19.71 14.2441 19.719 14.3041C19.866 15.5551 20.379 16.7341 21.195 17.6941C21.984 18.5071 23.019 19.0411 24.138 19.2181C24.195 19.2301 24.249 19.2601 24.288 19.3051C24.327 19.3501 24.348 19.4071 24.348 19.4671C24.348 19.5271 24.327 19.5841 24.288 19.6291C24.249 19.6741 24.198 19.7071 24.138 19.7161C21.702 20.2051 20.217 22.1461 19.716 24.6391C19.704 24.6961 19.674 24.7501 19.626 24.7891C19.581 24.8281 19.521 24.8491 19.464 24.8491C19.407 24.8491 19.347 24.8281 19.302 24.7891C19.257 24.7501 19.224 24.6991 19.212 24.6391C18.711 22.1461 17.229 20.2021 14.79 19.7161C14.73 19.7041 14.679 19.6741 14.64 19.6291C14.601 19.5841 14.58 19.5271 14.58 19.4671C14.58 19.4071 14.601 19.3501 14.64 19.3051C14.679 19.2601 14.73 19.2271 14.79 19.2181C15.909 19.0411 16.944 18.5071 17.733 17.6941C18.549 16.7341 19.062 15.5551 19.209 14.3041C19.218 14.2441 19.251 14.1901 19.296 14.1511C19.341 14.1121 19.401 14.0911 19.461 14.0911H19.467Z" fill="white"/>
</svg>
`;

export const SEOAnalystAvatar = `<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 60C46.569 60 60 46.569 60 30C60 13.431 46.569 0 30 0C13.431 0 0 13.431 0 30C0 46.569 13.431 60 30 60Z" fill="#E0E0E0"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16 12.0032C16.0511 12.0032 16.0994 12.0226 16.1364 12.058C16.1733 12.0935 16.1989 12.145 16.2074 12.1998C16.3267 13.3632 16.7472 14.4589 17.4176 15.3516C18.0653 16.1057 18.9119 16.602 19.8295 16.7664C19.8778 16.776 19.9205 16.805 19.9517 16.8469C19.983 16.8888 20 16.9436 20 16.9984C20 17.0532 19.983 17.108 19.9517 17.1499C19.9205 17.1918 19.8778 17.2208 19.8295 17.2304C17.8324 17.6848 16.6165 19.4863 16.2074 21.8066C16.1989 21.8614 16.1733 21.9098 16.1335 21.9452C16.0966 21.9807 16.0483 22 16 22C15.9517 22 15.9034 21.9807 15.8665 21.9452C15.8295 21.9098 15.804 21.8614 15.7926 21.8066C15.3835 19.4895 14.1676 17.6848 12.1705 17.2304C12.1222 17.2208 12.0795 17.1918 12.0483 17.1499C12.017 17.108 12 17.0532 12 16.9984C12 16.9436 12.017 16.8888 12.0483 16.8469C12.0795 16.805 12.1222 16.776 12.1705 16.7664C13.0881 16.602 13.9347 16.1057 14.5824 15.3516C15.25 14.4589 15.6705 13.3632 15.7926 12.1998C15.8011 12.145 15.8267 12.0935 15.8636 12.058C15.9006 12.0226 15.9489 12 16 12V12.0032Z" fill="white"/>
<path d="M30.5015 44C23.0562 44 17 37.7188 17 30C17 22.2812 23.0562 16 30.5015 16C37.9468 16 44 22.2812 44 30C44 37.7188 37.9438 44 30.5015 44ZM30.5015 19.0118C24.66 19.0118 19.907 23.9414 19.907 30C19.907 36.0586 24.66 40.9882 30.5015 40.9882C36.3431 40.9882 41.093 36.0586 41.093 30C41.093 23.9414 36.34 19.0118 30.5015 19.0118Z" fill="white"/>
<path d="M40.3464 37.2267L38.2268 39.3457C37.9244 39.6447 37.9244 40.1322 38.2268 40.4345L42.5669 44.7733C42.866 45.0756 43.3537 45.0756 43.656 44.7733L45.7757 42.6543C46.0748 42.3553 46.0748 41.8678 45.7757 41.5655L41.4355 37.2267C41.1332 36.9244 40.6455 36.9244 40.3464 37.2267Z" fill="white"/>
<mask id="mask0_2890_7231" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="21" y="20" width="19" height="20">
<path d="M37.2661 36.5123C39.0709 34.7076 39.9701 32.4706 39.9701 29.8045C39.9701 27.1385 39.0677 24.9015 37.2661 23.0967C35.4582 21.292 33.2213 20.3896 30.5583 20.3896C27.8954 20.3896 25.6553 21.2889 23.8505 23.0967C22.0427 24.9015 21.1403 27.1385 21.1403 29.8045C21.1403 32.4706 22.0427 34.7076 23.8505 36.5123C25.6553 38.3171 27.8923 39.2194 30.5583 39.2194C33.2244 39.2194 35.4582 38.3202 37.2661 36.5123Z" fill="white"/>
</mask>
<g mask="url(#mask0_2890_7231)">
<path d="M20.8497 40.3396H25.98C26.1978 40.3396 26.3744 40.163 26.3744 39.9452V31.3854C26.3744 31.1675 26.1978 30.991 25.98 30.991H20.8497C20.6319 30.991 20.4553 31.1675 20.4553 31.3854V39.9452C20.4553 40.163 20.6319 40.3396 20.8497 40.3396Z" fill="white"/>
<path d="M27.9901 39.3899H33.1203C33.3381 39.3899 33.5147 39.2133 33.5147 38.9955V23.8919C33.5147 23.6741 33.3381 23.4975 33.1203 23.4975H27.9901C27.7722 23.4975 27.5957 23.6741 27.5957 23.8919V38.9955C27.5957 39.2133 27.7722 39.3899 27.9901 39.3899Z" fill="white"/>
<path d="M35.1336 40.655H40.2638C40.4816 40.655 40.6582 40.4785 40.6582 40.2606V28.4289C40.6582 28.2111 40.4816 28.0345 40.2638 28.0345H35.1336C34.9158 28.0345 34.7392 28.2111 34.7392 28.4289V40.2606C34.7392 40.4785 34.9158 40.655 35.1336 40.655Z" fill="white"/>
</g>
</svg>
`;

export const webchatIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" fill="none" preserveAspectRatio="xMidYMid meet" focusable="false">
  <rect width="40" height="40" fill="#F5F5F5" rx="20"></rect>
  <circle cx="20" cy="20" r="20" fill="#22C0CA"></circle>
  <path fill="#fff" d="M30.37 8.395H9.63c-1.426 0-2.593 1.111-2.593 2.47v22.221l5.185-4.938H30.37c1.426 0 2.593-1.11 2.593-2.469V10.864c0-1.358-1.167-2.469-2.593-2.469Zm0 17.284H12.222L9.63 28.15V10.863H30.37V25.68Z"></path>
  <path fill="#fff" fill-rule="evenodd" d="M8.407 8.89a1 1 0 0 0-1 1v17.259a1 1 0 0 0 1 1h23.186a1 1 0 0 0 1-1V9.889a1 1 0 0 0-1-1H8.407Zm16.63 2.407H9.926v2.407h15.111v-2.407ZM9.926 16.11h20.148v9.63H9.926v-9.63Zm20.148-4.814h-2.518v2.407h2.518v-2.407Z" clip-rule="evenodd"></path>
</svg>`;

export const smsIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fit="" height="100%" width="100%" preserveAspectRatio="xMidYMid meet" focusable="false"><path d="M0 0h24v24H0V0z" fill="#30bf56"></path><path d="M14.606 4H9.394c-.977 0-1.777.8-1.777 1.778v12.344c0 .978.8 1.778 1.777 1.778h5.211c.978 0 1.778-.8 1.778-1.778V5.778c0-.978-.8-1.778-1.777-1.778zm-2.512 14.947a1.08 1.08 0 1 1 .001-2.161 1.08 1.08 0 1 1-.001 2.161zm3.15-4.442c0 .546-.435.993-.967.993H9.711a.982.982 0 0 1-.967-.993V6.591c0-.546.435-.993.967-.993h4.565c.532 0 .967.447.967.993l.001 7.914z" fill="#fff"></path>
</svg>`;

export const voiceIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60" fit="" height="100%" width="100%" preserveAspectRatio="xMidYMid meet" focusable="false">
  <rect x="0" y="0" width="60" height="60" fill="#30bf56"></rect>x
  <g clip-path="url('/account/location/AG-XNSZC2RWXL/ai/assistants#clip0_1_30')">
    <path d="M43.35 36.6333C41.3 36.6333 39.3167 36.3 37.4667 35.7C36.8833 35.5 36.2333 35.65 35.7833 36.1L33.1667 39.3833C28.45 37.1333 24.0333 32.8833 21.6833 28L24.9333 25.2333C25.3833 24.7667 25.5167 24.1167 25.3333 23.5333C24.7167 21.6833 24.4 19.7 24.4 17.65C24.4 16.75 23.65 16 22.75 16H16.9833C16.0833 16 15 16.4 15 17.65C15 33.1333 27.8833 46 43.35 46C44.5333 46 45 44.95 45 44.0333V38.2833C45 37.3833 44.25 36.6333 43.35 36.6333Z" fill="white"></path>
  </g>
</svg>`;

export const whatsappIcon = `<svg width="664" height="664" viewBox="0 0 664 664" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_49_2)">
<rect width="664" height="664" fill="#25D366"/>
<path d="M486.9 175.1C445 133.1 389.2 110 329.9 110C207.5 110 107.9 209.6 107.9 332C107.9 371.1 118.1 409.3 137.5 443L106 558L223.7 527.1C256.1 544.8 292.6 554.1 329.8 554.1H329.9C452.2 554.1 554 454.5 554 332.1C554 272.8 528.8 217.1 486.9 175.1ZM329.9 516.7C296.7 516.7 264.2 507.8 235.9 491L229.2 487L159.4 505.3L178 437.2L173.6 430.2C155.1 400.8 145.4 366.9 145.4 332C145.4 230.3 228.2 147.5 330 147.5C379.3 147.5 425.6 166.7 460.4 201.6C495.2 236.5 516.6 282.8 516.5 332.1C516.5 433.9 431.6 516.7 329.9 516.7ZM431.1 378.5C425.6 375.7 398.3 362.3 393.2 360.5C388.1 358.6 384.4 357.7 380.7 363.3C377 368.9 366.4 381.3 363.1 385.1C359.9 388.8 356.6 389.3 351.1 386.5C318.5 370.2 297.1 357.4 275.6 320.5C269.9 310.7 281.3 311.4 291.9 290.2C293.7 286.5 292.8 283.3 291.4 280.5C290 277.7 278.9 250.4 274.3 239.3C269.8 228.5 265.2 230 261.8 229.8C258.6 229.6 254.9 229.6 251.2 229.6C247.5 229.6 241.5 231 236.4 236.5C231.3 242.1 217 255.5 217 282.8C217 310.1 236.9 336.5 239.6 340.2C242.4 343.9 278.7 399.9 334.4 424C369.6 439.2 383.4 440.5 401 437.9C411.7 436.3 433.8 424.5 438.4 411.5C443 398.5 443 387.4 441.6 385.1C440.3 382.6 436.6 381.2 431.1 378.5Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_49_2">
<rect width="664" height="664" fill="white"/>
</clipPath>
</defs>
</svg>
`;
