import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  CRMFieldLayoutApiService,
  CRMFieldSchemaApiService,
  CRMMultiLocationFieldLayoutApiService,
  CRMMultiLocationFieldSchemaApiService,
  CrmObjectInterface,
  FieldGroupInterface as CrmFieldGroupInterface,
  FieldSchema,
  FieldSchemaInterface as CrmFieldSchemaInterface,
  FieldType,
  FieldValueInterface as CrmFieldValueInterface,
  ListFieldGroupsResponseInterface,
  ListFieldSchemaResponseInterface,
  ObjectProjectionFilterInterface,
  PagedRequestOptionsInterface,
  ValidateCrmObjectResponseValidationErrorInterface,
  ValidateCrmObjectResponseValidationErrorSeverity as ErrorSeverity,
} from '@vendasta/crm';
import {
  combineLatest,
  EMPTY,
  expand,
  filter,
  forkJoin,
  map,
  Observable,
  of,
  reduce,
  shareReplay,
  switchMap,
  take,
} from 'rxjs';
import { CrmObjectService } from './crm-object-service/crm-object.service';
import { TranslateByObjectTypeService } from '../../i18n/translate-by-object-type/translate-by-object-type.service';
import {
  CrmDependencies,
  CrmInjectionToken,
  CrmMultiLocationDependencies,
  CrmMultilocationInjectionToken,
  FieldGroupInterface,
  FieldInterface,
  FieldSchemaInterface,
  FieldValueInterface,
  MultiLocationContext,
  ObjectType,
} from '../../tokens-and-interfaces';
import { isModifiedFieldValue } from './crm-form.utils';

const MAX_SERVER_PAGE_SIZE = 200;

export interface ValidateErrorsWithFieldSchemas {
  errors: ValidateCrmObjectResponseValidationErrorInterface[];
  fieldSchemaMap: Map<string, FieldSchemaInterface>;
}

@Injectable()
export class CrmFormService {
  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    @Inject(CrmMultilocationInjectionToken) private multiLocationConfig: CrmMultiLocationDependencies,
    private readonly crmObjectService: CrmObjectService,
    private readonly crmFieldSchemaApiService: CRMFieldSchemaApiService,
    private readonly crmMultiLocationFieldSchemaService: CRMMultiLocationFieldSchemaApiService,
    private readonly crmFieldLayoutApiService: CRMFieldLayoutApiService,
    private readonly crmMultiLocationFieldLayoutService: CRMMultiLocationFieldLayoutApiService,
    private readonly translationService: TranslateService,
    private readonly translateByObjectTypeService: TranslateByObjectTypeService,
  ) {}

  private handleValidationErrors(objectType: ObjectType, validationResponse: ValidateErrorsWithFieldSchemas): void {
    const invalidSeverityErrors = (validationResponse.errors ?? []).filter(
      (e) => e.severity === ErrorSeverity.VALIDATION_ERROR_SEVERITY_INVALID,
    );
    // only display first error for now
    if (invalidSeverityErrors.length) {
      const firstError = invalidSeverityErrors[0];
      const fieldId = firstError.details?.uniquenessCheck?.fieldId || '';
      const firstErrorSchema = validationResponse.fieldSchemaMap.get(fieldId);
      if (firstError.message) {
        throw new Error(
          this.translateByObjectTypeService.getTranslationByObjectType(objectType, firstError?.message, {
            label: firstErrorSchema?.fieldName?.toLowerCase() || '',
          }),
        );
      }
      throw new Error();
    }
  }

  /*
   * TODO: this will probably start to lag when there's a lot of fields.
   * We should probably change to using tabs for different sets of fields later.
   */
  listAllObjectFields$(objectType: ObjectType, objectSubtype?: string): Observable<CrmFieldSchemaInterface[]> {
    return of({
      fieldSchemas: [],
      pagingMetadata: {
        nextCursor: '',
        hasMore: true,
      },
    }).pipe(
      expand((resp: ListFieldSchemaResponseInterface) => {
        if (!resp?.pagingMetadata?.hasMore) {
          return EMPTY;
        }
        return this.listObjectFieldsNextPage$(
          objectType,
          {
            cursor: resp.pagingMetadata.nextCursor,
            pageSize: MAX_SERVER_PAGE_SIZE,
          },
          objectSubtype,
        );
      }),
      reduce((acc: CrmFieldSchemaInterface[], val) => [...acc, ...(val?.fieldSchemas || [])], []),
    );
  }

  private listObjectFieldsNextPage$(
    objectType: ObjectType,
    paging: PagedRequestOptionsInterface,
    objectSubtype?: string,
  ): Observable<ListFieldSchemaResponseInterface> {
    // listing for a specific namespace will include standard fields as well
    const multiLocationContext$ = this.multiLocationConfig.multiLocationContext$ ?? of({} as MultiLocationContext);
    return combineLatest([this.config.namespace$, multiLocationContext$]).pipe(
      take(1),
      switchMap(([namespace, context]) => {
        const request = {
          crmObjectType: objectType,
          pagingOptions: paging,
          crmObjectSubtype: objectSubtype || '',
        };
        if (namespace === '' && context.groupId && context.accountGroupsIds && context.accountGroupsIds.length > 0) {
          return this.crmMultiLocationFieldSchemaService.listMultiLocationFieldSchema({
            namespaces: context.accountGroupsIds,
            ...request,
          });
        } else {
          return this.crmFieldSchemaApiService.listFieldSchema({ namespace: namespace, ...request });
        }
      }),
    );
  }

  /*
   * TODO: this will probably start to lag when there's a lot of fields.
   * We should probably change to using tabs for different sets of fields later.
   */
  listAllObjectFieldGroups$(objectType: ObjectType, objectSubtype?: string): Observable<CrmFieldGroupInterface[]> {
    return of({
      fieldSchemas: [],
      pagingMetadata: {
        nextCursor: '',
        hasMore: true,
      },
    }).pipe(
      expand((resp: ListFieldGroupsResponseInterface) => {
        if (!resp?.pagingMetadata?.hasMore) {
          return EMPTY;
        }
        return this.listObjectFieldGroupsNextPage$(
          objectType,
          {
            cursor: resp.pagingMetadata.nextCursor,
            pageSize: MAX_SERVER_PAGE_SIZE,
          },
          objectSubtype,
        );
      }),
      reduce((acc: CrmFieldGroupInterface[], val) => {
        return [...acc, ...(val?.fieldGroups || [])];
      }, []),
    );
  }

  private listObjectFieldGroupsNextPage$(
    objectType: ObjectType,
    paging: PagedRequestOptionsInterface,
    objectSubtype?: string,
  ): Observable<ListFieldGroupsResponseInterface> {
    const multiLocationContext$ = this.multiLocationConfig.multiLocationContext$ ?? of({} as MultiLocationContext);
    return combineLatest([this.config.namespace$, multiLocationContext$]).pipe(
      take(1),
      switchMap(([namespace, context]) => {
        const request = {
          crmObjectType: objectType,
          pagingOptions: paging,
          crmObjectSubtype: objectSubtype || '',
        };
        if (namespace === '' && context.groupId && context.accountGroupsIds && context.accountGroupsIds.length > 0) {
          return this.crmMultiLocationFieldLayoutService.listMultiLocationFieldGroups({
            namespaces: context.accountGroupsIds,
            ...request,
          });
        } else {
          return this.crmFieldLayoutApiService.listFieldGroups({ namespace: namespace, ...request });
        }
      }),
    );
  }

  private crmFieldGroupToFormFieldGroup(crmFieldGroup: CrmFieldGroupInterface): FieldGroupInterface {
    return {
      description: crmFieldGroup.description,
      fieldIds: crmFieldGroup.fieldIds || [],
    };
  }

  listAllObjectFormFieldGroups$(
    objectType: ObjectType,
    objectSubtype?: Observable<string>,
  ): Observable<FieldGroupInterface[]> {
    return (objectSubtype ?? of('')).pipe(
      switchMap((subtype) => this.listAllObjectFieldGroups$(objectType, subtype)),
      map((crmFieldGroups) => crmFieldGroups.map(this.crmFieldGroupToFormFieldGroup)),
    );
  }

  private crmFieldSchemasToFormFieldSchemas(crmFieldSchemas: CrmFieldSchemaInterface[]): FieldSchemaInterface[] {
    return crmFieldSchemas.map((crmFieldSchema) => {
      return {
        fieldId: crmFieldSchema.fieldId || '',
        fieldType: crmFieldSchema.fieldType || FieldType.FIELD_TYPE_INVALID,
        fieldName: crmFieldSchema.fieldName || '',
        fieldDescription: crmFieldSchema.fieldDescription || '',
        dropdownOptions: crmFieldSchema.dropdownOptions || [],
        currencyCode: crmFieldSchema.currencyCode || '',
        value: null,
        readOnly: crmFieldSchema.readonly,
      } as FieldSchemaInterface;
    });
  }

  listAllFormFields$(objectType: ObjectType, objectSubtype?: string): Observable<FieldSchemaInterface[]> {
    return this.listAllObjectFields$(objectType, objectSubtype).pipe(
      map((crmFieldSchemas) => this.crmFieldSchemasToFormFieldSchemas(crmFieldSchemas)),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  private getCrmFieldValueToFormValue(crmFieldValue: CrmFieldValueInterface, fieldType: FieldType): any {
    switch (fieldType) {
      case FieldType.FIELD_TYPE_INTEGER:
        return crmFieldValue.integerValue;
      case FieldType.FIELD_TYPE_CURRENCY:
        return crmFieldValue.currencyValue;
      case FieldType.FIELD_TYPE_STRING:
      case FieldType.FIELD_TYPE_DROPDOWN:
      case FieldType.FIELD_TYPE_EMAIL:
        return crmFieldValue.stringValue;
      case FieldType.FIELD_TYPE_PHONE:
        if (crmFieldValue.phoneFieldValues) {
          return crmFieldValue.phoneFieldValues;
        }
        return crmFieldValue.stringValue;
      case FieldType.FIELD_TYPE_BOOLEAN:
        return crmFieldValue.booleanValue;
      case FieldType.FIELD_TYPE_TAG:
      case FieldType.FIELD_TYPE_STRING_LIST:
        return crmFieldValue.stringValues?.values || [];
      case FieldType.FIELD_TYPE_DATETIME:
      case FieldType.FIELD_TYPE_DATE:
        return crmFieldValue.dateValue;
      case FieldType.FIELD_TYPE_FLOAT:
        return crmFieldValue.floatValue;
      default:
        return null;
    }
  }

  isModifiedFieldValue(
    currentValue: CrmFieldValueInterface,
    modifiedValue: CrmFieldValueInterface,
    fieldType: FieldType,
  ): boolean {
    return isModifiedFieldValue(currentValue, modifiedValue, fieldType);
  }

  private initializeFormFieldSchemaWithCrmObject(
    fieldSchema: FieldSchemaInterface,
    crmObject: CrmObjectInterface,
  ): void {
    if (!fieldSchema?.fieldId || !crmObject?.fields) {
      return;
    }

    const crmObjectFieldValue = crmObject.fields.find((crmObjectField) => {
      return crmObjectField.fieldId === fieldSchema.fieldId;
    });

    if (!crmObjectFieldValue) {
      return;
    }

    fieldSchema.value = this.getCrmFieldValueToFormValue(crmObjectFieldValue, fieldSchema.fieldType);
  }

  listAllFormFieldsInitializedWithCrmObject$(
    objectType: ObjectType,
    crmObjectId: string,
    objectSubtype?: string,
  ): Observable<FieldSchemaInterface[]> {
    return forkJoin([
      this.crmObjectService.getMultiObject(objectType, [crmObjectId], [
        { filterType: 1, fromFieldType: FieldType.FIELD_TYPE_PHONE, toFieldType: FieldType.FIELD_TYPE_PHONE },
      ] as ObjectProjectionFilterInterface[]),
      this.listAllFormFields$(objectType, objectSubtype),
    ]).pipe(
      map(([getMultiResponse, fieldSchemas]) => {
        if (getMultiResponse?.crmObjects?.length === 0 || !getMultiResponse?.crmObjects?.[0].crmObjectId) {
          throw new Error(this.translationService.instant('CRM_FORM_SERVICE.RESOURCE_NOT_FOUND_ERROR'));
        } else {
          const crmObject = getMultiResponse.crmObjects[0];
          fieldSchemas.forEach((field) => {
            this.initializeFormFieldSchemaWithCrmObject(field, crmObject);
          });
        }
        return fieldSchemas;
      }),
    );
  }

  validateCrmObject$(
    objectType: ObjectType,
    crmObject: CrmObjectInterface,
  ): Observable<ValidateErrorsWithFieldSchemas> {
    const validationErrors$ = this.crmObjectService
      .validateCrmObject(objectType, crmObject)
      .pipe(map((response) => response.errors || []));
    const fieldSchemaMap$ = this.listAllFormFields$(objectType, crmObject.crmObjectSubtype).pipe(
      map((schemas) => {
        const schemaMap = new Map<string, FieldSchemaInterface>();
        for (const schema of schemas) {
          schemaMap.set(schema.fieldId, schema);
        }
        return schemaMap;
      }),
    );
    return forkJoin([validationErrors$, fieldSchemaMap$]).pipe(
      map(([validationErrors, fieldSchemaMap]) => {
        return {
          errors: validationErrors,
          fieldSchemaMap: fieldSchemaMap,
        };
      }),
    );
  }

  validateAndCreateCrmObject$(
    objectType: ObjectType,
    fields: FieldInterface[],
    objectSubtype?: string,
  ): Observable<CrmObjectInterface | null> {
    return of(null).pipe(
      map(() => fields.filter((field) => field.hasModification()).map((field) => field.fieldValue())),
      map((fields) =>
        fields.filter((fieldValue) => {
          return fieldValue?.stringValue !== '';
        }),
      ),
      switchMap((fieldValues) => {
        const crmObject: CrmObjectInterface = {
          fields: fieldValues,
          crmObjectSubtype: objectSubtype,
        };
        const validationResponse$ = this.validateCrmObject$(objectType, crmObject);
        return forkJoin([of(fieldValues), validationResponse$]);
      }),
      switchMap(([fieldValues, validationResponse]) => {
        this.handleValidationErrors(objectType, validationResponse);
        if (!fieldValues?.length) {
          return of(null);
        }
        const crmObject: CrmObjectInterface = {
          fields: fieldValues,
          crmObjectSubtype: objectSubtype,
        };

        return this.crmObjectService.createCrmObject(objectType, crmObject).pipe(
          map((resp) => {
            crmObject.crmObjectId = resp.crmObjectId;
            return crmObject;
          }),
        );
      }),
    );
  }

  validateAndUpdateCrmObject$(
    objectType: ObjectType,
    crmObjectId: string,
    fields: FieldInterface[],
    objectSubtype?: string,
  ): Observable<CrmObjectInterface | null> {
    return this.validateCrmObject$(objectType, {
      crmObjectId: crmObjectId,
      fields: fields.map((field) => field.fieldValue()),
      crmObjectSubtype: objectSubtype,
    }).pipe(
      switchMap((validationResponse) => {
        this.handleValidationErrors(objectType, validationResponse);

        const modifiedFieldValues = fields
          .filter((field) => field.hasModification())
          .map((field) => field.fieldValue());
        if (!modifiedFieldValues?.length) {
          return of(null);
        }

        return this.crmObjectService
          .updateCrmObject(objectType, {
            crmObjectId: crmObjectId,
            fields: modifiedFieldValues,
            crmObjectSubtype: objectSubtype,
          })
          .pipe(
            map(() => ({
              crmObjectId: crmObjectId,
              fields: fields.map((field) => field.fieldValue()),
              crmObjectSubtype: objectSubtype,
            })),
          );
      }),
    );
  }

  validateAndUpdateCrmObjectFields$(
    objectType: ObjectType,
    crmObjectId: string,
    modifiedValues: FieldValueInterface[] = [],
    objectSubtype?: string,
  ): Observable<CrmObjectInterface | null> {
    const fieldSchemaMap$: Observable<Map<string, FieldSchema>> = this.config.namespace$.pipe(
      take(1),
      switchMap((namespace) =>
        this.crmFieldSchemaApiService.getMultiFieldSchema({
          namespace: namespace,
          crmObjectType: objectType,
          crmObjectSubtype: objectSubtype,
          fieldId: modifiedValues.map((f) => f.fieldId),
        }),
      ),
      map((response) => !!response?.fieldSchemas && response?.fieldSchemas),
      map((schemas) => {
        const schemaMap = new Map<string, FieldSchema>();
        for (const s of schemas) {
          if (!s?.fieldId) {
            continue;
          }
          schemaMap.set(s.fieldId, s);
        }
        return schemaMap;
      }),
      shareReplay(1),
    );
    const currentCrmObject$ = this.crmObjectService
      .getMultiObject(objectType, [crmObjectId], [
        { filterType: 1, fromFieldType: FieldType.FIELD_TYPE_PHONE, toFieldType: FieldType.FIELD_TYPE_PHONE },
      ] as ObjectProjectionFilterInterface[])
      .pipe(
        take(1),
        map((response) => {
          const crmObjects = response.crmObjects || [];
          if (crmObjects.length === 0 || !crmObjects[0].crmObjectId) {
            return null;
          }
          return crmObjects[0];
        }),
        filter((crmObject) => !!crmObject),
        shareReplay(1),
      );
    const values$ = combineLatest([currentCrmObject$, fieldSchemaMap$]).pipe(
      map(([currentObject, fieldSchemaMap]) => {
        const currentObjectFields = currentObject?.fields || [];
        const fieldsToModify: { fieldValue: CrmFieldValueInterface; indexToModify: number }[] = [];
        for (const modifiedValue of modifiedValues) {
          if (!modifiedValue?.fieldId) {
            continue;
          }
          const currentValueIndex = currentObjectFields.findIndex(
            (fieldValue: CrmFieldValueInterface) => fieldValue?.fieldId === modifiedValue.fieldId,
          );
          const currentValue =
            currentValueIndex !== -1 ? currentObjectFields[currentValueIndex] : { fieldId: modifiedValue.fieldId };
          const fieldSchema = fieldSchemaMap.get(modifiedValue.fieldId);
          if (!fieldSchema) {
            continue;
          }
          if (this.isModifiedFieldValue(currentValue, modifiedValue, fieldSchema.fieldType)) {
            fieldsToModify.push({
              fieldValue: modifiedValue,
              indexToModify: currentValueIndex,
            });
          }
        }
        if (fieldsToModify.length === 0) {
          return {};
        }
        for (const fieldToModify of fieldsToModify) {
          if (fieldToModify.indexToModify === -1) {
            currentObjectFields.push(fieldToModify.fieldValue);
          } else {
            currentObjectFields[fieldToModify.indexToModify] = fieldToModify.fieldValue;
          }
        }
        return {
          combinedFieldForValidation: [...currentObjectFields],
          fieldsToModify: [...fieldsToModify],
        };
      }),
      shareReplay(1),
    );
    const validationResponse$ = values$.pipe(
      take(1),
      switchMap(({ combinedFieldForValidation, fieldsToModify }) => {
        if (!fieldsToModify?.length) {
          return of(null);
        }

        return this.validateCrmObject$(objectType, {
          crmObjectId: crmObjectId,
          fields: combinedFieldForValidation,
          crmObjectSubtype: objectSubtype,
        });
      }),
    );

    return combineLatest([validationResponse$, values$]).pipe(
      take(1),
      switchMap(([validationResponse, { combinedFieldForValidation, fieldsToModify }]) => {
        if (validationResponse?.errors?.length || !fieldsToModify?.length) {
          return of(null);
        }

        const crmObject: CrmObjectInterface = {
          crmObjectId: crmObjectId,
          fields: fieldsToModify.map((f) => f.fieldValue),
          crmObjectSubtype: objectSubtype,
        };
        return this.crmObjectService.updateCrmObject(objectType, crmObject).pipe(
          map(() => {
            crmObject.fields = combinedFieldForValidation;
            return crmObject;
          }),
        );
      }),
    );
  }
}
