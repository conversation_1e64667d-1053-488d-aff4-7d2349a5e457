import { Inject, Injectable } from '@angular/core';
import { CrmObjectInterface, FieldSchemaInterface } from '@vendasta/crm';
import { CrmFieldService, StandardIds, SystemFieldIds } from '../crm-services/field.service';
import { ColumnDataInterface, TableCustomizationService } from './_abstract-table-customization.service';
import { GalaxyFilterDefinitionInterface, GalaxyFilterInterface } from '@vendasta/galaxy/filter/chips';
import { Observable, of } from 'rxjs';
import { GalaxyColumnDef } from '@vendasta/galaxy/table';
import { map, take } from 'rxjs/operators';
import { PAGE_ROUTES } from '../../constants';
import { CrmDependencies, CrmInjectionToken } from '../../tokens-and-interfaces';
import { CrmObjectDisplayService } from '../crm-object-display.service';

@Injectable({ providedIn: 'root' })
export class CustomObjectTableCustomizationService extends TableCustomizationService {
  private profileLink$: Observable<string>;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    protected override readonly crmFieldService: CrmFieldService,
    private readonly objectDisplayService: CrmObjectDisplayService,
  ) {
    super(crmFieldService);
    this.profileLink$ = this.config.routePrefix$.pipe(
      map((prefix) => {
        const viewRoute = `${PAGE_ROUTES.CUSTOM_OBJECT.ROOT}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.PROFILE}`;
        return prefix ? `${prefix}/${viewRoute}` : '';
      }),
    );
  }

  getRowFullName(crmObject: CrmObjectInterface): string {
    return this.objectDisplayService.customObjectDisplayName(crmObject);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getAssociationInfo(crmObject: CrmObjectInterface): string {
    return '';
  }

  customizeRowData(
    crmObject: CrmObjectInterface,
    schemaMap: { [schemaId: string]: FieldSchemaInterface },
    columnData: ColumnDataInterface,
  ): void {
    this.addCustomObjectProfileLink(crmObject, columnData);
  }

  private addCustomObjectProfileLink(crmObject: CrmObjectInterface, columnData: ColumnDataInterface): void {
    const customObjectNameField = this.crmFieldService.getFieldId(StandardIds.CustomObjectName);
    this.profileLink$.pipe(take(1)).subscribe((profileLink) => {
      profileLink = profileLink.replace(':customObjectTypeID', crmObject.crmObjectSubtype || '');
      profileLink = profileLink.replace(':crmObjectId', crmObject.crmObjectId || '');
      columnData[customObjectNameField] = {
        cellType: 'text',
        value: this.getRowFullName(crmObject),
        link: profileLink,
      };
    });
  }

  customizeColumns(columns: GalaxyColumnDef[], _isMobile: boolean): GalaxyColumnDef[] {
    const customizedColumns = this.hideIDColumns(columns);
    return [...customizedColumns];
  }

  private hideIDColumns(columns: GalaxyColumnDef[]): GalaxyColumnDef[] {
    const columnIdentifiersToHide = [
      SystemFieldIds.CustomObjectID,
      SystemFieldIds.CustomObjectExternalID,
      SystemFieldIds.CustomObjectTypeID,
      SystemFieldIds.CustomObjectOwnerID,
    ];

    for (const identifier of columnIdentifiersToHide) {
      const index = columns.findIndex((c) => c.id === identifier);
      if (index >= 0) {
        columns[index].hidden = true;
      }
    }

    for (const nameCol of columns.filter((c) => c.id === StandardIds.CustomObjectName)) {
      nameCol.sticky = true;
    }

    return columns;
  }

  customizeFilters(filters: GalaxyFilterDefinitionInterface[]): Observable<GalaxyFilterDefinitionInterface[]> {
    return of(filters);
  }

  getInitialFilters(
    _filterDefinitions$: Observable<GalaxyFilterDefinitionInterface[]>,
  ): Promise<GalaxyFilterInterface[]> {
    return Promise.resolve([]);
  }
}
