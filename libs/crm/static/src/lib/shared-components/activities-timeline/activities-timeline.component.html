@let objectType = activityObjectType();

<div #scrollableContainerRef class="activity-timeline-container">
  @if (objectType) {
    <crm-activity-logger
      [crmObjectId]="crmObjectId()"
      [objectType]="objectType"
      (activityCreated)="addActivity($event)"
    ></crm-activity-logger>
  }

  @if (enableAISummary() && !hasTimelineActivitiesFeatureFlag()) {
    <crm-ai-summary [crmObjectId]="crmObjectId()" [crmObjectType]="objectType!"></crm-ai-summary>
  }

  <div class="search-and-filter-container">
    <div class="search-and-filter-row">
      <div class="filter-search-group">
        <button
          mat-stroked-button
          class="filters-toggle"
          data-action="clicked-filters-toggle"
          [ngClass]="{ on: showFiltersOpen }"
          (click)="toggleFilterBar()"
        >
          <mat-icon class="filters-toggle--icon">
            filter_list
            <span *ngIf="filtersApplied()" class="filters-toggle--indicator"></span>
          </mat-icon>
          <span class="filters-toggle--label">{{ 'GALAXY.TABLE.FILTERS.TOGGLE_BUTTON' | translate }}</span>
        </button>

        <glxy-form-field
          class="searchbar"
          suffixIcon="search"
          bottomSpacing="none"
          [ngClass]="{ 'darker-border': hasTimelineActivitiesFeatureFlag() }"
        >
          <input type="text" [placeholder]="'ACTIONS.SEARCH' | translate" matInput [formControl]="searchControl" />
        </glxy-form-field>
      </div>
      <crm-filter-view
        class="filter-view-button"
        [filters]="filters()"
        (filtersChange)="updateFilters($event, true)"
        [keys]="keys"
        [presetFilters]="presetFilters()"
      ></crm-filter-view>
    </div>
    <div class="filter-bar" [@openClose]="showFiltersOpen ? 'open' : 'closed'">
      <div class="filter-bar--inner-space">
        <glxy-filter-chips
          #filterChipRef
          [addFiltersHeader]="'ACTIVITY.TITLE' | translate"
          (filtersChanged)="updateFilters($event, false)"
          filters-area
        ></glxy-filter-chips>
      </div>
    </div>
  </div>

  <crm-timeline
    #crmTimelineRef
    [crmObjectId]="crmObjectId()"
    [objectType]="objectType!"
    (activitiesChanged)="handleActivitiesChanged($event)"
    [showCopyLink]="true"
  ></crm-timeline>
  <!-- this must be predicated on the first load already being done so more items will load if the page does not initially overflow -->
  <glxy-infinite-scroll-trigger
    *ngIf="showLoadMore"
    [visiblilityMargin]="60"
    (isVisible)="loadMoreActivities()"
  ></glxy-infinite-scroll-trigger>
</div>
