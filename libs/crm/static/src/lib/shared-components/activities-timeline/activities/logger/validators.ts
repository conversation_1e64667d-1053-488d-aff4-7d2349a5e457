import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function requiredFieldsIf(predicate: () => boolean, fields: AbstractControl[]): ValidatorFn {
  return (): ValidationErrors | null => {
    const when = predicate();
    if (when) {
      fields.forEach((field) => validateRequired(field));
    } else {
      fields.forEach((field) => clearRequired(field));
    }
    return null;
  };
}

export function requiredFormGroupFieldsIf(predicate: () => boolean, fieldNames: string[]): ValidatorFn {
  return (formGroup: AbstractControl): ValidationErrors | null => {
    const when = predicate();
    const controls = fieldNames.map((name) => formGroup.get(name)).filter((c): c is AbstractControl => Boolean(c));
    if (when) {
      controls.forEach((c) => validateRequired(c));
    } else {
      controls.forEach((c) => clearRequired(c));
    }

    return null;
  };
}

function validateRequired(control: AbstractControl): void {
  if (control.value) {
    if (control.hasError('required')) {
      delete control.errors?.['required'];
    }
  } else {
    control.setErrors({ required: true });
  }
}

function clearRequired(control: AbstractControl): void {
  if (control.hasError('required')) {
    delete control.errors?.['required'];
  }
}
