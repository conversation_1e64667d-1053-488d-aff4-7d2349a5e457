<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="call-rte"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['loggedCallNote']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      <ng-container *ngIf="showErrors">
        <glxy-error class="rich-text-error" *ngIf="formGroup.controls.loggedCallNote.hasError('required')">
          {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_DESCRIPTION' | translate }}
        </glxy-error>
      </ng-container>
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <glxy-form-row class="detail-form-row" [class.mobile]="mobile">
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        size="default"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'date_range'"
      >
        <input
          formControlName="loggedCallDate"
          type="datetime-local"
          matInput
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_CALL.DATE_PLACEHOLDER' | translate }}"
        />
        <mat-datepicker #callDate></mat-datepicker>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedCallDate.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_CALL_DATE' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'compare_arrows'"
      >
        <mat-select
          formControlName="loggedCallDirection"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_CALL.DIRECTION_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ ActivityDirection.Outbound }}">{{ 'ACTIVITY.OUTBOUND' | translate }}</mat-option>
          <mat-option value="{{ ActivityDirection.Inbound }}">{{ 'ACTIVITY.INBOUND' | translate }}</mat-option>
        </mat-select>
        <ng-container *ngIf="showErrors">
          <glxy-error *ngIf="formGroup.controls.loggedCallDirection.hasError('required')">
            {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_CALL_DIRECTION' | translate }}
          </glxy-error>
        </ng-container>
      </glxy-form-field>
    </glxy-form-row>
    <glxy-form-row>
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'check'"
      >
        <mat-select
          formControlName="loggedCallStatus"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_CALL.STATUS_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ CallStatus.Connected }}">{{ 'ACTIVITY.CALL.CONNECTED' | translate }}</mat-option>
          <mat-option value="{{ CallStatus.Busy }}">{{ 'ACTIVITY.CALL.BUSY' | translate }}</mat-option>
          <mat-option value="{{ CallStatus.Rejected }}">{{ 'ACTIVITY.CALL.REJECTED' | translate }}</mat-option>
          <mat-option value="{{ CallStatus.Voicemail }}">
            {{ 'ACTIVITY.CALL.LEFT_VOICEMAIL' | translate }}
          </mat-option>
          <mat-option value="{{ CallStatus.NoAnswer }}">{{ 'ACTIVITY.CALL.NO_ANSWER' | translate }}</mat-option>
          <mat-option value="{{ CallStatus.NotInService }}">
            {{ 'ACTIVITY.CALL.NOT_IN_SERVICE' | translate }}
          </mat-option>
          <mat-option value="{{ CallStatus.WrongNumber }}">
            {{ 'ACTIVITY.CALL.WRONG_NUMBER' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
      <glxy-form-field
        bottomSpacing="small"
        [showLabel]="false"
        class="detail-form-field"
        [class.mobile]="mobile"
        [prefixIcon]="'feedback'"
      >
        <mat-select
          formControlName="loggedCallOutcome"
          placeholder="{{ 'ACTIVITY_LOGGER.LOGGED_CALL.OUTCOME_PLACEHOLDER' | translate }}"
        >
          <mat-option value="{{ ActivityOutcome.NoInterest }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.NO_INTEREST' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Interested }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.INTERESTED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.QualityConnect }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.QUALITY_CONNECT' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Demo }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.DEMO_SCHEDULED' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Timeline }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.TIMELINE_6_12_MONTHS' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Rescheduling }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.RESCHEDULING' | translate }}
          </mat-option>
          <mat-option value="{{ ActivityOutcome.Referred }}">
            {{ 'ACTIVITY.OUTCOME_OPTIONS.REFERRED' | translate }}
          </mat-option>
        </mat-select>
      </glxy-form-field>
    </glxy-form-row>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
