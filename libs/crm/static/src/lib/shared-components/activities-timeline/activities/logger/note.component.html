<form [formGroup]="formGroup" novalidate #form="ngForm">
  <div>
    <glxy-form-row>
      <glxy-rich-text-editor
        class="rich-text-editor"
        elementId="note-rte"
        [statusbar]="true"
        [enableThemes]="true"
        [formControl]="formGroup.controls['noteBody']"
        [allowResize]="true"
        [plugins]="DEFAULT_RICH_TEXT_PLUGINS"
        [toolbar]="DEFAULT_RICH_TEXT_TOOLBAR"
      ></glxy-rich-text-editor>
      <ng-container *ngIf="showErrors">
        <glxy-error class="rich-text-error" *ngIf="formGroup.controls.noteBody.hasError('required')">
          {{ 'ACTIVITY_LOGGER.ERROR_REQUIRE_DESCRIPTION' | translate }}
        </glxy-error>
      </ng-container>
    </glxy-form-row>
  </div>
  <ng-content select="[crmLoggerContent='after-description']"></ng-content>
  <div>
    <ng-content select="[crmLoggerContent='fields']"></ng-content>
  </div>
</form>
