import { Inject, Injectable } from '@angular/core';
import { ActivityAssociationInterface, ActivityInterface, FieldValueInterface } from '@vendasta/crm';
import { IAMService, UserIdentifier } from '@vendasta/iamv2';
import { Observable, of } from 'rxjs';
import { distinctUntilChanged, map, shareReplay, switchMap, tap } from 'rxjs/operators';
import { UserWithFullName } from './logger/interfaces';
import { CrmInjectionToken, ActivityType, CrmDependencies } from '../../../tokens-and-interfaces';
import { ActivityService, CRMTrackingService } from '../../../shared-services';

@Injectable()
export class ActivityLoggerService {
  user$ = new Observable<UserWithFullName>();

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private activityService: ActivityService,
    private readonly iamService: IAMService,
    private readonly trackingService: CRMTrackingService,
  ) {
    this.user$ = this.config.currentUserId$.pipe(
      distinctUntilChanged(),
      switchMap((currentUserId) => {
        return this.iamService.getUser(new UserIdentifier({ userId: currentUserId }));
      }),
      map((user) => ({
        ...user,
        fullName: `${user.firstName} ${user.lastName}`.trim(),
      })),
      shareReplay(1),
    );
  }

  addActivity(
    activityType: ActivityType,
    fields: FieldValueInterface[],
    associations: ActivityAssociationInterface[],
    ownerId?: string,
  ): Observable<string> {
    const cleanAssociations = associations.map(
      (association) =>
        ({
          crmObjectId: association.crmObjectId,
          crmObjectType: association.crmObjectType,
          crmObjectSubtype: association.crmObjectSubtype,
        }) as ActivityAssociationInterface,
    );
    const activity = {
      crmObjectSubtype: activityType,
      fields: fields,
      associations: cleanAssociations,
      ownerId: ownerId,
    } as ActivityInterface;

    return this.activityService.createActivity(activity).pipe(
      tap(() => {
        this.trackingService.trackEvent('Activity', 'added-crm-activity', {
          activityType: activityType,
        });
      }),
    );
  }

  getActivity(activityId: string): Observable<ActivityInterface> {
    return this.activityService.getMultiActivities([activityId]).pipe(map((activities) => activities[0]));
  }

  updateActivity(
    activityId: string,
    activityType: ActivityType,
    fields: FieldValueInterface[],
    associations: ActivityAssociationInterface[],
    ownerId?: string,
    fieldMask?: string[],
  ): Observable<string> {
    const cleanAssociations = [] as ActivityAssociationInterface[];
    associations.forEach((association) => {
      cleanAssociations.push({
        crmObjectId: association.crmObjectId,
        crmObjectType: association.crmObjectType,
        crmObjectSubtype: association.crmObjectSubtype,
      } as ActivityAssociationInterface);
    });
    const activity = {
      crmObjectId: activityId,
      crmObjectSubtype: activityType,
      fields: fields,
      associations: cleanAssociations,
      ownerId: ownerId,
    } as ActivityInterface;

    if (!fieldMask?.length) {
      return of(activityId);
    }
    return this.activityService.updateActivity(activity, { paths: fieldMask }).pipe(
      map(() => activityId),
      tap(() => {
        this.trackingService.trackEvent('Activity', 'updated-crm-activity', {
          activityType: activityType,
          activityId: activityId,
        });
      }),
    );
  }
}
