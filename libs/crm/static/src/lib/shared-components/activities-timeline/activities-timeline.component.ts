import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  AfterViewChecked,
  Component,
  computed,
  DestroyRef,
  ElementRef,
  inject,
  Inject,
  input,
  OnDestroy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  CrmDependencies,
  CrmInjectionToken,
  ObjectType,
  PrimaryAccountGroupIDForCrmObject,
  tableFiltersInjectionTokenGenerator,
} from '../../tokens-and-interfaces';
import { SEARCH_DEBOUNCE_MS } from '../../constants';
import { TranslationModule } from '../../i18n/translation-module';
import {
  combineLatest,
  debounceTime,
  filter,
  map,
  of,
  shareReplay,
  skipWhile,
  Subscription,
  switchMap,
  tap,
} from 'rxjs';
import { CrmActivityLoggerComponent } from './activities/activity-logger.component';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CrmTimelineComponent } from './timeline/timeline.component';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyFilterChipsComponent } from '@vendasta/galaxy/filter/chips/src/galaxy-filter-chips.component';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterChipInjectionToken,
  GalaxyFilterInterface,
  GalaxyFilterOperator,
} from '@vendasta/galaxy/filter/chips';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { ActivityInterface } from '@vendasta/crm';
import { RxState } from '@rx-angular/state';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { decodeFilters, encodeFilters, validateFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { ActivitiesTimelineService } from './timeline/timeline.service';
import { FilterViewComponent } from '../filter-view/filter-view.component';
import { SavedFilters } from '../filter-view/interfaces';
import { CrmFieldService, CrmObjectService, PlatformExtensionFieldIds, StandardIds } from '../../shared-services';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { CrmAISummaryComponent } from '../ai-summary';

@Component({
  selector: 'crm-activities-timeline',
  templateUrl: './activities-timeline.component.html',
  styleUrls: ['./activities-timeline.component.scss'],
  providers: [
    RxState,
    ActivitiesTimelineService,
    {
      provide: GalaxyFilterChipInjectionToken,
      useFactory: tableFiltersInjectionTokenGenerator('Activity'),
    },
    {
      provide: PrimaryAccountGroupIDForCrmObject,
      useFactory: (
        comp: CrmActivitiesTimelineComponent,
        objectService: CrmObjectService,
        fieldService: CrmFieldService,
      ) => {
        const crmObjectId$ = toObservable(comp.crmObjectId);
        const crmObjectType$ = toObservable(comp.activityObjectType).pipe(filter(Boolean));
        return combineLatest([crmObjectId$, crmObjectType$]).pipe(
          switchMap((v: [string, ObjectType]) => {
            const crmObjectId = v[0];
            const crmObjectType = v[1];
            return objectService.getMultiObject(crmObjectType, [crmObjectId]);
          }),
          map((response) => response?.crmObjects?.[0]),
          map((crmObject) => {
            return (
              fieldService.getFieldValueFromCrmObject(crmObject!, PlatformExtensionFieldIds.AccountGroupID)
                ?.stringValue ?? null
            );
          }),
          filter(Boolean),
          shareReplay(1),
        );
      },
      deps: [CrmActivitiesTimelineComponent, CrmObjectService, CrmFieldService],
    },
  ],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
          opacity: 1,
        }),
      ),
      state(
        'closed',
        style({
          height: '0px',
          opacity: 0,
        }),
      ),
      transition('open <=> closed', [animate('0.2s')]),
    ]),
  ],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    CrmActivityLoggerComponent,
    CrmTimelineComponent,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    GalaxyFilterChipsComponent,
    MatIconModule,
    GalaxyInfiniteScrollTriggerModule,
    FilterViewComponent,
    GalaxyAlertModule,
    CrmAISummaryComponent,
  ],
})
export class CrmActivitiesTimelineComponent implements OnDestroy, OnInit, AfterContentInit, AfterViewChecked {
  @ViewChild('crmTimelineRef', { static: true }) timeline!: CrmTimelineComponent;
  @ViewChild('scrollableContainerRef', { static: true }) scrollableContainer!: ElementRef;
  @ViewChild('filterChipRef') filterChipRef?: GalaxyFilterChipsComponent;

  crmObjectId = input<string>('');

  showFiltersOpen = false;

  activityObjectType = input<ObjectType | null>(null);
  enableAISummary = input<boolean>(false);

  hasTimelineActivitiesFeatureFlag = toSignal(this.config?.hasTimelineActivitiesFeatureFlag$ ?? of(false));
  presetFilters = computed(() => {
    const presets: SavedFilters = [
      { name: this.translateService.instant('VIEWS.PRESET_FILTERS.ALL'), filters: [] },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.NOTES'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'notes-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Note' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.EMAILS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'emails-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Email' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.CALLS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'calls-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Call' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.MEETINGS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'meetings-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Meeting' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.TASKS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'tasks-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Task' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.COMMUNICATIONS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'communications-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Communication' }],
          },
        ],
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.EXCLUDE_CAMPAIGNS'),
        filters: [
          {
            fieldId: StandardIds.ActivityCampaignID,
            filterId: 'exclude-campaign-preset-campaign-id',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY,
          },
          {
            fieldId: StandardIds.ActivityCommunicationType,
            filterId: 'exclude-campaign-preset-comm-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT,
            values: [{ string: 'Campaign SMS' }],
          },
        ],
        toggle: this.hasTimelineActivitiesFeatureFlag(),
      },
    ];
    if (!this.crmObjectId()) {
      return presets.filter((filter) => filter.name !== this.translateService.instant('VIEWS.PRESET_FILTERS.TASKS'));
    }
    return presets;
  });

  objectType: ObjectType = 'Activity';

  private readonly translateService = inject(TranslateService);
  private readonly destroyRef = inject(DestroyRef);

  protected readonly filters = signal<GalaxyFilterInterface[]>([]);
  protected readonly filtersApplied = computed(() => this.filters().length > 0);

  searchControl = new FormControl<string>('');
  subscriptions: Subscription[] = [];
  showLoadMore = false;
  private scrollTop = 0;

  private initialSearchTerm = '';
  private initialSearchTermSet = false;
  private initialFilters: GalaxyFilterInterface[] = [];
  keys: string[];

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly activitiesTimelineService: ActivitiesTimelineService,
    @Inject(GalaxyFilterChipInjectionToken)
    private readonly activitiesTimelineFilterService: GalaxyFilterChipDependencies,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
  ) {
    const filterParam = this.activatedRoute.snapshot.queryParamMap.get('filter');
    let filters: GalaxyFilterInterface[] = [];
    if (filterParam) {
      const f = decodeFilters(filterParam);
      if (validateFilters(f) && f.length > 0) {
        filters = f;
      }
    } else {
      const campaignPreset = this.presetFilters().find(
        (f) => f.name === this.translateService.instant('VIEWS.PRESET_FILTERS.EXCLUDE_CAMPAIGNS'),
      );
      if (campaignPreset?.filters) {
        filters = campaignPreset.filters;
      }
    }
    this.initialFilters = filters;
    this.activitiesTimelineFilterService.setInitialAppliedFilters?.(filters);

    const keys: string[] = [];
    if (this.config?.appID) {
      keys.push('crm:' + this.config.appID + ':activities-timeline:views');
    }
    keys.push('crm:activities-timeline:views');
    this.keys = keys;
  }

  ngOnInit() {
    const searchParam = this.activatedRoute.snapshot.queryParamMap.get('search');
    if (searchParam) {
      this.initialSearchTerm = decodeURI(searchParam);
    }
    this.activitiesTimelineService.initializeState(this.initialSearchTerm, this.initialFilters);

    this.subscriptions.push(
      this.searchControl.valueChanges
        .pipe(
          skipWhile((term) => term !== this.initialSearchTerm),
          debounceTime(SEARCH_DEBOUNCE_MS),
          map((value) => value?.trim() ?? ''),
          tap((searchTerm) => {
            let searchParam: string | null = null;
            if (searchTerm) {
              searchParam = encodeURI(searchTerm);
            }
            this.router.navigate([], {
              relativeTo: this.activatedRoute,
              queryParams: { search: searchParam },
              queryParamsHandling: 'merge',
            });
            this.timeline.setSearchTerm(searchTerm);
          }),
        )
        .subscribe(),
    );
  }

  ngAfterViewChecked(): void {
    if (!this.initialSearchTermSet) {
      this.searchControl.setValue(this.initialSearchTerm);
      this.searchControl.updateValueAndValidity();
      this.initialSearchTermSet = true;
    }
  }

  ngAfterContentInit(): void {
    this.timeline.filters$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((filters) => this.filters.set(filters));
  }

  addActivity(activity: ActivityInterface): void {
    this.timeline.addActivity(activity);
  }

  handleActivitiesChanged(activities: ActivityInterface[]): void {
    this.scrollableContainer.nativeElement.scrollTop = this.scrollTop;
    this.showLoadMore = activities?.length > 0;
  }

  loadMoreActivities(): void {
    this.scrollTop = this.scrollableContainer?.nativeElement?.scrollTop ?? 0;
    this.timeline?.loadMoreActivities();
  }

  ngOnDestroy() {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  toggleFilterBar(): void {
    this.showFiltersOpen = !this.showFiltersOpen;
  }

  updateFilters($event: GalaxyFilterInterface[], updateChipList?: boolean): void {
    if (!this.timeline) {
      return;
    }
    let filterParam: string | null = null;
    if ($event.length > 0) {
      const readyFilters = $event.filter(
        (f) => !(f.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS && f.values?.length === 0),
      );
      if (readyFilters.length > 0) {
        filterParam = encodeFilters(readyFilters);
      }
    }
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filter: filterParam },
      queryParamsHandling: 'merge',
    });
    this.timeline.setFilters($event);
    if (updateChipList) {
      this.filterChipRef?.filters.set($event);
    }
  }
}
