import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  effect,
  EventEmitter,
  inject,
  Inject,
  input,
  Input,
  OnDestroy,
  OnInit,
  Output,
  signal,
  ViewChild,
  WritableSignal,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatTableModule } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { RxState } from '@rx-angular/state';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxySnackbarModule, SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyDataSource, GalaxyTableModule, Row } from '@vendasta/galaxy/table';
import { GalaxyColumnDef } from '@vendasta/galaxy/table/src/table.interface';
import {
  BehaviorSubject,
  combineLatest,
  firstValueFrom,
  forkJoin,
  Observable,
  of,
  ReplaySubject,
  Subscription,
} from 'rxjs';
import {
  ActivityType,
  CrmDependencies,
  CrmInjectionToken,
  CrmMultiLocationDependencies,
  CrmMultilocationInjectionToken,
  CrmObjectDependencies,
  CrmObjectInjectionToken,
  CRMRowObject,
  ListObjectView,
  MultiRowAction,
  ObjectType,
  SelectAllAction,
  SingleRowAction,
} from '../../tokens-and-interfaces';
import { LIST_OBJECTS_DEFAULT_PAGE_SIZE, LIST_OBJECTS_PAGE_SIZE_OPTIONS, PAGE_ROUTES } from '../../constants';
import { TranslationModule } from '../../i18n/translation-module';
import { ListObjectsTableService } from './list-objects-table.service';
import { bufferTime, catchError, filter, map, startWith, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { SidepanelComponent } from '../side-panel/side-panel.component';
import { FilterVisibleActionsPipe } from '../../shared-pipes';
import { CrmTableStateService } from '../../shared-services/crm-services/crm-object-service/crm-table-state.service';
import { GalaxyFilterChipsModule, GalaxyFilterInterface, GalaxyFilterOperator } from '@vendasta/galaxy/filter/chips';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { CrmFiltersService } from '../../shared-services/crm-services/crm-filters.service';
import { decodeFilters, encodeFilters, validateFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { FilterViewComponent, PresetFilters, SavedFilter, SavedFilters } from '../filter-view';
import { GalaxyFilterChipsComponent } from '@vendasta/galaxy/filter/chips/src/galaxy-filter-chips.component';
import { CustomRowStyle } from '../../shared-services/table-customization/_abstract-table-customization.service';
import { FeatureFlagsApiService } from '@vendasta/partner';
import { ContactCustomColumns, CRMTrackingService, Pipeline, StandardIds } from '../../shared-services';
import { FadeIn, LoadingBehavior } from '@galaxy/crm/components/animations';
import {
  CrmBoardCardContentDirective,
  CrmBoardColumnHeaderDirective,
  CrmBoardComponent,
  CrmBoardDatasourceInterface,
  CrmBoardItemClickedEvent,
  CrmBoardItemClickedToken,
  CrmBoardItemCompareToken,
} from '@galaxy/crm/components/board';
import { ListObjectsBoardColumnHeaderComponent } from './board/column-header.component';
import { ListObjectsBoardCardContentComponent } from './board/card-content.component';
import { PipelineSelectorComponent } from '../pipeline-selector';
import { GalaxyTableSelectionCustomization, GalaxyTableSelectionCustomizationToken } from '@vendasta/galaxy/table/';
import { ValueSummary, ValueSummaryService } from './value-summary.service';
import { TranslateForCrmObjectPipe, TranslateForCrmObjectService } from '../../i18n';
import { TabGroup, ViewTabsComponent } from '@galaxy/crm/components/view-tabs';
import { TranslateService } from '@ngx-translate/core';
import { v4 as uuidv4 } from 'uuid';

@Component({
  selector: 'crm-list-objects-table',
  templateUrl: `./list-objects-table.component.html`,
  styleUrls: ['./list-objects-table.component.scss'],
  imports: [
    CommonModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    GalaxyTableModule,
    MatTableModule,
    MatIconModule,
    MatMenuModule,
    MatButtonModule,
    TranslationModule,
    MatDialogModule,
    GalaxySnackbarModule,
    GalaxyBadgeModule,
    GalaxyPageModule,
    SidepanelComponent,
    FilterVisibleActionsPipe,
    GalaxyFilterChipsModule,
    MatSortModule,
    FilterViewComponent,
    CrmBoardComponent,
    CrmBoardColumnHeaderDirective,
    CrmBoardCardContentDirective,
    ListObjectsBoardColumnHeaderComponent,
    ListObjectsBoardCardContentComponent,
    PipelineSelectorComponent,
    TranslateForCrmObjectPipe,
    ViewTabsComponent,
  ],
  animations: [LoadingBehavior, FadeIn],
  providers: [
    ListObjectsTableService,
    CrmTableStateService,
    RxState,
    {
      provide: CrmBoardItemCompareToken,
      useValue: (a: Row, b: Row) => a.id === b.id,
    },
    {
      provide: CrmBoardItemClickedToken,
      useValue: (r: Row): CrmBoardItemClickedEvent => {
        return { objectId: r.id };
      },
    },
    {
      provide: GalaxyTableSelectionCustomizationToken,
      useValue: {
        rowsSelectionLimit: 100,
      } as GalaxyTableSelectionCustomization,
    },
  ],
})
export class ListObjectsTableComponent implements OnInit, OnDestroy {
  protected readonly view: WritableSignal<'grid' | 'list'> = signal('list');
  protected readonly selectedPipeline: WritableSignal<Pipeline | undefined> = signal(undefined);
  protected readonly initialPipeline: WritableSignal<string> = signal('');
  protected readonly isBoardView = computed(() => {
    const view = this.view();
    return view === 'grid';
  });
  private readonly isBoardView$ = toObservable(this.isBoardView);
  protected readonly isOnBoardFeatureFlag$: Observable<boolean>;
  protected readonly showBoardEmptyState = signal(false);
  protected readonly isMultiLocation$: Observable<boolean>;

  protected readonly boardColumnLayout = computed(() =>
    this.service.defaultBoardColumns(this._objectType(), this.activityType, this.selectedPipeline()),
  );

  @Input() rowStyle: CustomRowStyle | undefined;

  private readonly multiActions$$ = new BehaviorSubject<MultiRowAction[]>([]);

  @Input() set multiActions(actions: MultiRowAction[]) {
    this.multiActions$$.next(actions);
  }

  private readonly singleActions$$ = new BehaviorSubject<SingleRowAction[]>([]);

  @Input() set singleActions(actions: SingleRowAction[]) {
    this.singleActions$$.next(actions);
  }

  private readonly crmObjectDependencies: CrmObjectDependencies | null = inject(CrmObjectInjectionToken, {
    optional: true,
  });
  private readonly objectSubtype = toSignal(this.crmObjectDependencies?.objectSubtype$ ?? of(''));
  private readonly _objectType = signal<ObjectType>('Contact');
  protected readonly objectType$ = toObservable(this._objectType);
  @Input({ required: true }) set objectType(o: ObjectType) {
    this._objectType.set(o);
    this.tableStateService.setLocalStorageKey(this._objectType(), this.objectSubtype());
    this.service.setObjectType(this._objectType());
    this.summaryService.setObjectType(this._objectType());
    this.loadFilterBarState();
  }

  get objectType(): ObjectType {
    return this._objectType();
  }

  get objectTypeForTranslation(): ObjectType | ActivityType {
    return this.activityType ?? this.objectType;
  }

  get totalDataMembers(): number {
    return this.dataSource.state.totalDataMembers ?? 0;
  }

  readOnlyMode$$ = new BehaviorSubject(false);
  isReadOnlyMode = toSignal(this.readOnlyMode$$);
  readonly filterBarOpen = signal(false);
  readonly showFiltersOpen = computed(() => !this.isReadOnlyMode() && this.filterBarOpen());

  @Input() set readOnlyMode(value: boolean) {
    this.readOnlyMode$$.next(value ?? false);
  }

  filtersAreForced = false;

  @Input() set forceFilters(filters: GalaxyFilterInterface[]) {
    if (filters && filters.length > 0) {
      this.filtersAreForced = true;
      this.updateFilters([...filters], true);
    }
  }

  protected readonly multiRowActions$: Observable<MultiRowAction[]> = this.objectType$.pipe(
    switchMap((objectType) => {
      return this.createMultiRowActions(objectType);
    }),
  );

  protected readonly selectAllActions$: Observable<SelectAllAction[]> = this.objectType$.pipe(
    switchMap((objectType) => {
      return this.createSelectAllActions(objectType);
    }),
  );

  protected readonly singleRowActions$: Observable<SingleRowAction[]> = this.objectType$.pipe(
    switchMap((objectType) => {
      return this.createSingleRowActions(objectType);
    }),
  );
  protected readonly showMultiRowActions$: Observable<boolean> = combineLatest([
    this.multiRowActions$,
    this.isBoardView$,
    this.readOnlyMode$$,
  ]).pipe(map(([actions, isBoardView, readOnlyMode]) => actions.length > 0 && !isBoardView && !readOnlyMode));

  private _activityType: ActivityType | undefined;
  @Input() set activityType(o: ActivityType | undefined) {
    if (o) {
      this._activityType = o;
      this.service.setActivityType(this._activityType);
    }
  }

  get activityType(): ActivityType | undefined {
    return this._activityType;
  }

  presetFilters = input<PresetFilters>();
  @Input() refreshRow$?: Observable<string>;

  @Output() editClick: EventEmitter<CRMRowObject> = new EventEmitter<CRMRowObject>();
  @Output() selected: EventEmitter<Row[]> = new EventEmitter<Row[]>();
  @Output() pipelineSelected = new EventEmitter<boolean>();
  @Output() currentView = new EventEmitter<ListObjectView>();

  @ViewChild('filterChipRef') filterChipRef?: GalaxyFilterChipsComponent;

  protected readonly dataSource: GalaxyDataSource<Row, GalaxyFilterInterface, MatSort> = this.service.dataSource;
  protected readonly boardSource: CrmBoardDatasourceInterface<Row, GalaxyFilterInterface, MatSort> =
    this.service.boardSource;
  protected readonly columns$: Observable<GalaxyColumnDef[]> = combineLatest([
    this.service.columns$,
    this.readOnlyMode$$,
  ]).pipe(
    map(([columns, isReadOnlyMode]) => {
      if (isReadOnlyMode) {
        return columns.filter((c) => c.id !== 'actions');
      }
      return columns;
    }),
  );
  protected readonly groupConfigs$: Observable<GalaxyColumnDef[]> = this.service.groupConfigs$;
  protected readonly showEmptyState$: Observable<boolean> = combineLatest([
    this.service.showEmptyState$,
    this.isBoardView$,
  ]).pipe(map(([showEmptyState, isBoardView]) => showEmptyState && !isBoardView && !this.filtersAreForced));
  protected selectedRows: Row[] = [];

  protected readonly filters = signal<GalaxyFilterInterface[]>([]);
  protected readonly showFiltersApplied$: Observable<boolean> = combineLatest([
    this.dataSource.filtersApplied$,
    toObservable(this.filters),
  ]).pipe(
    map(
      ([filtersApplied, filters]) =>
        filtersApplied &&
        filters.filter(
          (f) =>
            !!f.values?.length ||
            f.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_EMPTY ||
            f.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY,
        ).length > 0,
    ),
  );

  protected readonly pageSizeOptions: number[] = LIST_OBJECTS_PAGE_SIZE_OPTIONS;
  protected readonly pageSize: number = LIST_OBJECTS_DEFAULT_PAGE_SIZE;

  private readonly confirmationModalService = inject(OpenConfirmationModalService);

  protected tableId = '';

  private readonly subscriptions: Subscription[] = [];

  protected readonly useSelectAll$ = this.dataSource.selectAll$;

  protected readonly hasSelectAllActions$ = combineLatest([this.isBoardView$, this.selectAllActions$]).pipe(
    map(([isBoardView, actions]) => actions.length > 0 && !isBoardView),
  );
  protected readonly initialLoadingCompleted$ = this.service.initialLoadingCompleted$;

  protected readonly boardValueSummary$: Observable<ValueSummary> = this.summaryService.valueSummary$;

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    @Inject(CrmMultilocationInjectionToken) private readonly multiLocationConfig: CrmMultiLocationDependencies,
    @Inject(FeatureFlagsApiService) private readonly featureFlagService: FeatureFlagsApiService,
    private readonly service: ListObjectsTableService,
    private readonly router: Router,
    private readonly snackService: SnackbarService,
    protected readonly dialog: MatDialog,
    private readonly tableStateService: CrmTableStateService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly crmFiltersService: CrmFiltersService,
    private readonly trackingService: CRMTrackingService,
    private readonly summaryService: ValueSummaryService,
    private readonly translateForCrmObjectService: TranslateForCrmObjectService,
    private readonly translate: TranslateService,
  ) {
    effect(() => {
      const viewTabKey = this.viewTabKey();
      this.userSavedTabs$$.next(this.loadSavedFilters(viewTabKey));
    });
    effect(() => {
      const defaultsKey = this.hideDefaultsViewKey();
      this.hiddenDefaultTabs$$.next(this.loadSavedStringList(defaultsKey));
    });
    effect(() => {
      const viewOrderKey = this.viewTabOrderKey();
      this.tabOrder$$.next(this.loadSavedStringList(viewOrderKey));
    });

    this.isMultiLocation$ = this.multiLocationConfig.isMultiLocation$ ?? of(false);
    this.activatedRoute.data
      .pipe(
        map((data) => (data?.['view'] === 'board' ? 'grid' : 'list')),
        take(1),
      )
      .subscribe((routeView: 'grid' | 'list') => {
        this.view.set(routeView);
      });
    this.isOnBoardFeatureFlag$ = combineLatest([
      this.config.namespace$,
      this.config.parentNamespace$,
      this.objectType$,
    ]).pipe(
      switchMap(([namespace, parentNamespace, objectType]) => {
        if (objectType === 'Opportunity') {
          return of(true);
        }
        if (objectType === 'Company' || objectType === 'Contact') {
          return this.featureFlagService
            .batchGetStatus({
              featureIds: ['crm_contact_company_board_view'],
              partnerId: (parentNamespace as string) || (namespace as string),
            })
            .pipe(
              map((response) => {
                return response?.hasAccess.length > 0 && response?.hasAccess[0];
              }),
            );
        }
        return this.featureFlagService
          .batchGetStatus({
            featureIds: ['crm_object_board_view'],
            partnerId: (parentNamespace as string) || (namespace as string),
          })
          .pipe(
            map((response) => {
              return response?.hasAccess.length > 0 && response?.hasAccess[0];
            }),
          );
      }),
      startWith(false),
    );
    this.dataSource.clearSelectionOnChange = true;
    const filterParam = this.activatedRoute.snapshot.queryParamMap.get('filter');
    let filters: GalaxyFilterInterface[] = [];
    if (filterParam) {
      const f = decodeFilters(filterParam);
      if (validateFilters(f) && f.length > 0) {
        filters = f;
      }
    }

    const pipelineId = this.activatedRoute.snapshot.queryParamMap.get('pipelineId');
    if (pipelineId) {
      const decodedPipelineId = decodeURIComponent(pipelineId);
      this.initialPipeline.set(decodedPipelineId);
    }

    if (this.isBoardView()) {
      const pipelineFieldIds = [StandardIds.ActivityTaskStatus, StandardIds.OpportunityPipelineID];
      const pipelineFilterIndex = filters.findIndex((filter) => pipelineFieldIds.includes(filter.fieldId));
      if (pipelineFilterIndex > -1) {
        filters.splice(pipelineFilterIndex, 1);
      }
    } else {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { pipelineId: null },
        queryParamsHandling: 'merge',
      });
    }
    this.crmFiltersService.setInitialFilters(filters);
    this.boardSource.setFilters(filters);

    const sub = combineLatest([this.activatedRoute.queryParams, this.dataSource.state$])
      .pipe(
        tap(([queryParams, state]) => {
          const newQueryParams = { ...queryParams };
          let filterParam: string | null = null;
          if (state.filters && state.filters.length > 0) {
            const readyFilters = state.filters.filter(
              (filter) => !(filter.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS && filter.values?.length === 0),
            );
            if (readyFilters.length > 0) {
              filterParam = encodeFilters(readyFilters);
              newQueryParams['filter'] = filterParam;
            } else {
              delete newQueryParams['filter'];
            }
          } else {
            delete newQueryParams['filter'];
          }
          // Note: `queryParamsHandling: 'merge'` doesn't work when multiple shared-components are adjusting the query params
          // at the same time. Without this other shared-components may not be able to write to the query params.
          if (queryParams['filter'] === newQueryParams['filter']) {
            return;
          }
          if (this.filtersAreForced) {
            return;
          }
          this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: newQueryParams,
          });
        }),
      )
      .subscribe();

    this.subscriptions.push(sub);

    const currentView = this.isBoardView$.subscribe((isBoardView) => {
      const view = isBoardView ? 'board' : 'table';
      this.currentView.emit(view);
    });

    this.subscriptions.push(currentView);
    effect(() => {
      const sortingChanged = this.dataSource.sortingChanged();
      sortingChanged.forEach((sort) => {
        this.trackingService.trackEvent(this.objectType, 'sorted-crm-object-table', {
          columnId: sort.active,
          direction: sort.direction,
          objectType: this.objectType,
        });
      });
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  ngOnInit() {
    if (this.crmObjectDependencies) {
      if (this.crmObjectDependencies.baseColumnIds && this.crmObjectDependencies.baseColumnIds.length > 0) {
        this.service.setBaseColumns(this.crmObjectDependencies.baseColumnIds);
      }
    } else {
      switch (this.objectType) {
        case 'Contact':
          if (this.config.contact?.baseColumnIds && this.config.contact.baseColumnIds.length > 0) {
            this.service.setBaseColumns(this.config.contact.baseColumnIds);
          }
          break;
        case 'Company':
          if (this.config.company?.baseColumnIds && this.config.company.baseColumnIds.length > 0) {
            this.service.setBaseColumns(this.config.company.baseColumnIds);
          }
          break;
        case 'Activity':
          switch (this.activityType) {
            case 'Task':
              if (this.config.task?.baseColumnIds && this.config.task.baseColumnIds.length > 0) {
                this.service.setBaseColumns(this.config.task.baseColumnIds);
              }
              break;
          }
          break;
        case 'Opportunity':
          if (this.config.opportunity?.baseColumnIds && this.config.opportunity.baseColumnIds.length > 0) {
            this.service.setBaseColumns(this.config.opportunity.baseColumnIds);
          }
          break;
      }
    }

    this.tableId = this.config.appID + '-' + this.objectType;

    if (this.refreshRow$) {
      this.subscriptions.push(
        this.refreshRow$
          .pipe(
            bufferTime(1000),
            map((rows) => rows.filter((row) => Boolean(row))),
            filter((rows) => rows.length > 0),
          )
          .subscribe((rowsToRefresh) => {
            this.service.refreshRows(rowsToRefresh);
          }),
      );
    }
  }

  protected editObject(row: Row): void {
    const namespace = row?.data['namespace']?.value ?? '';
    this.editClick.emit({
      namespace: namespace,
      objectId: row.id,
      objectType: this.objectType,
    });

    const routePrefix$ = this.isMultiLocation$.pipe(
      switchMap((isMultiLocation) => {
        return isMultiLocation ? this.multiLocationConfig.routePrefix$ : this.config.routePrefix$;
      }),
    );
    combineLatest([routePrefix$, this.crmObjectDependencies?.defaultPageUrl$ ?? of(''), this.isMultiLocation$])
      .pipe(take(1))
      .subscribe(([routePrefix, defaultPageUrl, isMultiLocation]) => {
        let editRouteTemplate;
        if (this.crmObjectDependencies) {
          editRouteTemplate = `${defaultPageUrl}/${PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.EDIT}`;
        } else {
          switch (this.objectType) {
            case 'Contact':
              if (isMultiLocation) {
                editRouteTemplate = `${routePrefix}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.ROOT}/${PAGE_ROUTES.MULTI_LOCATION.CONTACTS.SUBROUTES.EDIT}`;
              } else {
                editRouteTemplate = `${routePrefix}/${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.EDIT}`;
              }
              break;
            case 'Company':
              editRouteTemplate = `${routePrefix}/${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.EDIT}`;
              break;
            case 'Opportunity':
              editRouteTemplate = `${routePrefix}/${PAGE_ROUTES.OPPORTUNITY.ROOT}/${PAGE_ROUTES.OPPORTUNITY.SUBROUTES.EDIT}`;
              break;
          }
        }
        if (editRouteTemplate) {
          const editRoute = editRouteTemplate.replace(':crmObjectId', row.id).replace(':namespace', namespace);
          this.router.navigateByUrl(editRoute);
        }
      });
  }

  protected deleteObject(row: Row): void {
    let name: string;
    switch (this.objectTypeForTranslation) {
      case 'Company':
        name = (row?.data?.[StandardIds.CompanyName]?.value as string) || 'company';
        break;
      case 'Contact':
        name = (row?.data?.[ContactCustomColumns.FullName]?.value as string) || 'contact';
        break;
      case 'Task':
        name = (row?.data?.[StandardIds.ActivityName]?.value?.name as string) || 'task';
        break;
      default:
        name = '';
        break;
    }

    combineLatest([
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE.TITLE',
        { name: name },
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE.CONFIRMATION_MESSAGE',
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CONFIRM',
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CANCEL',
      ),
    ])
      .pipe(
        switchMap(([title, message, confirmationButtonText, cancelButtonText]) => {
          return this.confirmationModalService.openModal({
            type: 'warn',
            title: title,
            message: message,
            confirmButtonText: confirmationButtonText,
            cancelButtonText: cancelButtonText,
          });
        }),
        withLatestFrom(
          this.translateForCrmObjectService.getTranslationForCrmObject(
            this.objectTypeForTranslation,
            'LIST_OBJECTS_TABLE.ACTIONS.DELETE.SUCCESS',
          ),
          this.translateForCrmObjectService.getTranslationForCrmObject(
            this.objectTypeForTranslation,
            'LIST_OBJECTS_TABLE.ACTIONS.DELETE.ERROR',
          ),
        ),
      )
      .subscribe(([confirmed, success, error]: [boolean, string, string]) => {
        if (confirmed) {
          this.service.deleteObject(row.data['namespace']?.value ?? '', row.id).subscribe({
            next: () => {
              this.snackService.openSuccessSnack(success);
            },
            error: (err) => {
              console.error(err);
              this.snackService.openErrorSnack(error);
            },
          });
        }
      });
  }

  protected viewObject(objectId: string): void {
    if (!objectId) {
      console.error('ObjectID required when navigating to a profile page');
    }
    this.config.routePrefix$.subscribe((routePrefix) => {
      let viewRoute;
      switch (this.objectType) {
        case 'Contact':
          viewRoute = `${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE}`;
          break;
        case 'Company':
          viewRoute = `${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.PROFILE}`;
          break;
        case 'Opportunity':
          viewRoute = `${PAGE_ROUTES.OPPORTUNITY.ROOT}/${PAGE_ROUTES.OPPORTUNITY.SUBROUTES.PROFILE}`;
          break;
      }

      if (viewRoute) {
        viewRoute = viewRoute.replace(':crmObjectId', objectId);
        this.router.navigateByUrl(`${routePrefix}/${viewRoute}`);
      }
    });
  }

  protected async selectViewFilters(filters: GalaxyFilterInterface[]): Promise<void> {
    const initialFilters = await this.crmFiltersService.getInitialAppliedFilters$(
      this.objectType,
      this.crmObjectDependencies ?? undefined,
    );
    const mergedFilters: GalaxyFilterInterface[] = [];
    initialFilters.forEach((filter) => {
      const fieldId = filter.fieldId;
      const values = filter.values ?? [];
      if (values.length > 0 || filter.operator !== 3) {
        //maintain defaults
        mergedFilters.push(filter);
      } else {
        const override = filters.find((filter) => filter.fieldId === fieldId);
        if (!override) {
          //override empty filter bubbles
          mergedFilters.push(filter);
        }
      }
    });
    mergedFilters.push(...filters);
    this.updateFilters(mergedFilters, true);
  }

  pipelineChanged(pipeline: Pipeline | null) {
    if (pipeline) {
      this.selectedPipeline.set(pipeline);
      this.summaryService.setPipelineId(pipeline.id);
      this.updatePipelineQueryParam(pipeline.id);
      this.pipelineSelected.emit(true);
    } else {
      this.setStateForNoPipelineSelected();
    }
  }

  setStateForNoPipelineSelected() {
    this.showBoardEmptyState.set(true);
    this.pipelineSelected.emit(false);
  }

  updatePipelineQueryParam(pipelineId: string) {
    let encodedPipelineId = null;
    if (pipelineId) {
      encodedPipelineId = encodeURIComponent(pipelineId);
    }
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { pipelineId: encodedPipelineId },
      queryParamsHandling: 'merge',
    });
  }

  protected updateFilters(filters: GalaxyFilterInterface[], updateChipList?: boolean): void {
    this.dataSource.setFilters(filters);
    this.boardSource.setFilters(filters);
    this.filters.set(filters);
    if (updateChipList) {
      this.filterChipRef?.filters.set(filters);
    }
    this.trackingService.trackFilterUsage(this.objectType, filters);
  }

  protected selectionChanged(rows: Row[]): void {
    this.selectedRows = rows;
    this.selected.emit(rows);
  }

  protected columnsChanged(columns: GalaxyColumnDef[]): void {
    // there's a select column at the start of every table execution with an advanced selector
    // the columns shouldn't be saved if only the select column is available
    // in the list of columns
    const columnCount = columns.filter((c) => c.id !== 'select').length;
    if (columnCount > 0) {
      this.tableStateService.setLocalStorageColumns(columns);
    }
  }

  protected searchTermChanged(searchTerm: string): void {
    this.boardSource.setSearchTerm(searchTerm);
  }

  private createSingleRowActions(objectType: ObjectType): Observable<SingleRowAction[]> {
    let typeSingleActions$: Observable<SingleRowAction[]>;

    switch (objectType) {
      case 'Contact':
        typeSingleActions$ = this.config.contact?.singleRowTableActions$ ?? of([]);
        break;
      case 'Company':
        typeSingleActions$ = this.config.company?.singleRowTableActions$ ?? of([]);
        break;
      case 'Activity':
        typeSingleActions$ = this.config.task?.singleRowTableActions$ ?? of([]);
        break;
      case 'Opportunity':
        typeSingleActions$ = this.config.opportunity?.singleRowTableActions$ ?? of([]);
        break;
      default:
        typeSingleActions$ = of([]);
        break;
    }

    return combineLatest([typeSingleActions$, this.singleActions$$]).pipe(
      map(([typeActions, singleActions]) => [...typeActions, ...singleActions]),
    );
  }

  private deleteRows(rows: Row[]): void {
    combineLatest([
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.TITLE',
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CONFIRMATION_MESSAGE',
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CONFIRM',
      ),
      this.translateForCrmObjectService.getTranslationForCrmObject(
        this.objectTypeForTranslation,
        'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.CANCEL',
      ),
    ])
      .pipe(
        switchMap(([title, message, confirmationButtonText, cancelButtonText]) => {
          return this.confirmationModalService.openModal({
            type: 'warn',
            title: title,
            message: message,
            confirmButtonText: confirmationButtonText,
            cancelButtonText: cancelButtonText,
          });
        }),
      )
      .subscribe((confirmed) => {
        if (confirmed) {
          const objectKeys = rows.map((row) => ({ id: row.id, namespace: row.data['namespace']?.value }));
          forkJoin(
            objectKeys.map((key) => {
              return this.service.deleteObject(key.namespace, key.id).pipe(
                map(() => ''),
                catchError(() => {
                  return of(key.id);
                }),
              );
            }),
          )
            .pipe(
              withLatestFrom(
                this.translateForCrmObjectService.getTranslationForCrmObject(
                  this.objectTypeForTranslation,
                  'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.SUCCESS',
                ),
                this.translateForCrmObjectService.getTranslationForCrmObject(
                  this.objectTypeForTranslation,
                  'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.ERROR',
                ),
              ),
            )
            .subscribe(([resp, success, error]: [string[], string, string]) => {
              const failedIDs = resp.filter((address) => address !== '');
              if (failedIDs.length === 0) {
                this.snackService.openSuccessSnack(success);
              } else {
                console.error('Failed to delete IDs: ', failedIDs);
                this.snackService.openErrorSnack(error);
              }
              if (resp.length - failedIDs.length > 0) {
                // If anything has been deleted, clear the selection
                this.dataSource.clearSelection();
              }
            });
        }
      });
  }

  private createMultiRowActions(objectType: ObjectType): Observable<MultiRowAction[]> {
    let typeMultiActions$: Observable<MultiRowAction[]>;

    switch (objectType) {
      case 'Contact':
        typeMultiActions$ = this.config.contact?.multiRowTableActions$ ?? of([]);
        break;
      case 'Company':
        typeMultiActions$ = this.config.company?.multiRowTableActions$ ?? of([]);
        break;
      case 'Activity':
        typeMultiActions$ = this.config.task?.multiRowTableActions$ ?? of([]);
        break;
      case 'Opportunity':
        typeMultiActions$ = this.config.opportunity?.multiRowTableActions$ ?? of([]);
        break;
      default:
        typeMultiActions$ = of([]);
        break;
    }

    const deleteRowsAction$ = this.translateForCrmObjectService
      .getTranslationForCrmObject(this.objectTypeForTranslation, 'LIST_OBJECTS_TABLE.ACTIONS.DELETE_SELECTED.ACTION')
      .pipe(
        map((label) => {
          return {
            label: label,
            callback: (rows: Row[]) => this.deleteRows(rows),
            selectAllVisible: false,
          };
        }),
      );

    return combineLatest([typeMultiActions$, this.multiActions$$, deleteRowsAction$]).pipe(
      map(([typeActions, multiActions, deleteRowsAction]) => [...typeActions, ...multiActions, deleteRowsAction]),
    );
  }

  private createSelectAllActions(objectType: ObjectType): Observable<SelectAllAction[]> {
    let selectAllActions$: Observable<SelectAllAction[]>;

    switch (objectType) {
      case 'Contact':
        selectAllActions$ = this.config.contact?.selectAllTableActions$ ?? of([]);
        break;
      case 'Company':
        selectAllActions$ = this.config.company?.selectAllTableActions$ ?? of([]);
        break;
      case 'Activity':
        selectAllActions$ = this.config.task?.selectAllTableActions$ ?? of([]);
        break;
      case 'Opportunity':
        selectAllActions$ = this.config.opportunity?.selectAllTableActions$ ?? of([]);
        break;
      default:
        selectAllActions$ = of([]);
        break;
    }

    return selectAllActions$;
  }

  protected handleSelectAll() {
    this.dataSource.toggleSelectAll();
  }

  protected async changeDisplay(display: 'grid' | 'list'): Promise<void> {
    const routeDisplay = display === 'grid' ? 'board' : display;
    let routeObject;
    if (this.objectType === 'Contact') {
      routeObject = `${PAGE_ROUTES.CONTACT.ROOT}`;
    } else if (this.objectType === 'Company') {
      routeObject = `${PAGE_ROUTES.COMPANY.ROOT}`;
    } else if (this.objectType === 'Activity' && this._activityType === 'Task') {
      routeObject = `${PAGE_ROUTES.TASK.ROOT}`;
    } else if (this.objectType === 'Opportunity') {
      routeObject = `${PAGE_ROUTES.OPPORTUNITY.ROOT}`;
    } else {
      throw new Error(`Invalid object type: ${this.objectType}`);
    }
    const routePrefix = await firstValueFrom(this.config.routePrefix$);
    await this.router.navigate([`${routePrefix}/${routeObject}/${routeDisplay}`], {
      queryParamsHandling: 'merge',
    });
  }

  protected boardItemUpdateFailed(): void {
    this.snackService.openErrorSnack('ERRORS.GENERIC_EDIT_MESSAGE');
  }

  protected boardItemClicked(event: CrmBoardItemClickedEvent): void {
    this.viewObject(event.objectId);
  }

  protected handleFilterOpenChange(open: boolean): void {
    if (!this.isReadOnlyMode()) {
      // save filter bar state to local storage
      const key = this.tableStateService.tableState().filterBarOpenStorageKey;
      if (key) {
        localStorage.setItem(key, open ? 'open' : 'closed');
      }
    }
    this.filterBarOpen.set(open);
  }

  private loadFilterBarState(): void {
    const key = this.tableStateService.tableState().filterBarOpenStorageKey;
    if (key) {
      const open = localStorage.getItem(key) !== 'closed';
      this.filterBarOpen.set(open);
    } else {
      this.filterBarOpen.set(true);
    }
  }

  openAndSetupPipelineSettings(): void {
    this.config.routePrefix$.pipe(take(1)).subscribe((routePrefix) => {
      const settingPage = `${routePrefix}/${PAGE_ROUTES.PIPELINE_SETTINGS.ROOT}`;
      this.router.navigateByUrl(this.router.createUrlTree([settingPage], { queryParams: { create: true } }));
    });
  }

  //View tabs
  protected readonly hasViewTabsFeatureFlag$ = combineLatest([
    this.config.namespace$,
    this.config.parentNamespace$,
  ]).pipe(
    switchMap(([namespace, parentNamespace]) => {
      return this.featureFlagService.batchGetStatus({
        featureIds: ['crm_table_view_tabs'],
        partnerId: parentNamespace || namespace,
      });
    }),
    map((response) => {
      return response?.hasAccess.length > 0 && response?.hasAccess[0];
    }),
  );

  protected readonly newViewTemplate = computed(() => {
    return {
      name: this.translate.instant('LIST_OBJECTS_TABLE.VIEW_TABS.NEW_VIEW'),
      filters: this.filters(), //based on current filters
      preset: false,
    } as SavedFilter;
  });

  protected readonly viewTabKey = computed(() => {
    const objectType = this._objectType();
    return this.config.appID
      ? 'crm:' + this.config.appID + ':list-' + objectType + ':table'
      : 'crm:list-' + objectType + ':table';
  });

  // preset views are not saved locally so we need to handle hiding them differently
  private readonly hideDefaultsViewKey = computed(() => {
    return this.viewTabKey() + ':hide-defaults';
  });

  private readonly viewTabOrderKey = computed(() => {
    return this.viewTabKey() + ':order';
  });

  private readonly presetTabs$ = toObservable(this.presetFilters);
  private readonly userSavedTabs$$ = new ReplaySubject<SavedFilter[]>(1);
  private readonly hiddenDefaultTabs$$ = new ReplaySubject<string[]>(1);
  protected readonly tabOrder$$ = new ReplaySubject<string[]>(1);

  protected readonly viewTabs$ = combineLatest([this.presetTabs$, this.userSavedTabs$$, this.hiddenDefaultTabs$$]).pipe(
    map(([presets, userSaved, hiddenDefaults]) => {
      const presetFilters =
        presets?.map((f) => ({ ...f, preset: true, hidden: hiddenDefaults?.includes(f.id) }) as SavedFilter) ?? [];

      userSaved.forEach((userSaved) => {
        if (!userSaved.id) {
          //handles custom views saved without an id (ie through the old view dropdown)
          userSaved.id = uuidv4();
        }
      });

      const customFilterGroup = {
        name: this.translate.instant('LIST_OBJECTS_TABLE.VIEW_TABS.CUSTOM_VIEWS_TAB_GROUP'),
        id: 'custom',
        tabs: userSaved,
        canModify: true,
      } as TabGroup<SavedFilter>;

      return presetFilters.length > 0
        ? [
            {
              name: this.translate.instant('LIST_OBJECTS_TABLE.VIEW_TABS.DEFAULT_VIEWS_TAB_GROUP'),
              id: 'default',
              tabs: presetFilters,
              canAddMore: false,
            },
            customFilterGroup,
          ]
        : ([customFilterGroup] as TabGroup<SavedFilter>[]);
    }),
    startWith([] as TabGroup<SavedFilter>[]),
  );

  protected async changeViewFilters($event: SavedFilter) {
    const filterCopy: GalaxyFilterInterface[] = JSON.parse(JSON.stringify($event.filters));
    await this.selectViewFilters(filterCopy);
  }

  private sortFilters(filters: SavedFilters): SavedFilters {
    return filters.sort((a, b) => a.name.toLocaleLowerCase().localeCompare(b.name.toLocaleLowerCase()));
  }

  private loadSavedFilters(key: string): SavedFilter[] {
    const savedFilters = key ? localStorage.getItem(key) : undefined;
    if (savedFilters) {
      try {
        const filters = JSON.parse(savedFilters);
        return this.sortFilters(filters);
      } catch {
        localStorage.removeItem(key);
      }
    }
    return [];
  }

  private loadSavedStringList(key: string): string[] {
    const hiddenDefaults = localStorage.getItem(key);
    if (hiddenDefaults) {
      try {
        return JSON.parse(hiddenDefaults);
      } catch {
        localStorage.removeItem(key);
      }
    }
    return [];
  }

  saveTabChanges($event: TabGroup<SavedFilter>[]) {
    $event.forEach((group) => {
      if (group.id === 'default') {
        const hiddenTabs = group.tabs
          .filter((tab) => tab.hidden && tab.id)
          .map((tab) => {
            return tab.id;
          });
        try {
          localStorage.setItem(this.hideDefaultsViewKey(), JSON.stringify(hiddenTabs));
          this.hiddenDefaultTabs$$.next(hiddenTabs);
          //force reload
        } catch {
          this.snackService.openErrorSnack('LIST_OBJECTS_TABLE.VIEW_TABS.ERRORS.FAILED_TO_SAVE_DEFAULT_VIEWS');
        }
      }
      if (group.id === 'custom') {
        try {
          const tabsAsString = JSON.stringify(group.tabs);
          localStorage.setItem(this.viewTabKey(), tabsAsString);
          this.userSavedTabs$$.next(group.tabs);
        } catch {
          this.snackService.openErrorSnack('LIST_OBJECTS_TABLE.VIEW_TABS.ERRORS.FAILED_TO_SAVE_CUSTOM_VIEWS');
        }
        return;
      }
    });
  }

  saveTabOrder($event: string[]) {
    try {
      localStorage.setItem(this.viewTabOrderKey(), JSON.stringify($event));
      this.tabOrder$$.next($event);
    } catch {
      this.snackService.openErrorSnack('LIST_OBJECTS_TABLE.VIEW_TABS.ERRORS.FAILED_TO_SAVE_VIEW_ORDER');
    }
    return;
  }
}
