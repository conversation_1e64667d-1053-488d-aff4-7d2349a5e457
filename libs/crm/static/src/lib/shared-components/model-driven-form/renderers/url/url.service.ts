export const linkedInUrlValidator = (url: string): string | false => {
  if (!url) {
    return url;
  }
  const normalizedUrl = normalizeUrl(url);
  const cleanedUrl = cleanUrl(normalizedUrl);

  const parsedUrl = isValidUrl(cleanedUrl);
  if (!parsedUrl || !urlHostEndsWith(parsedUrl, 'linkedin.com') || !urlPathNotEmpty(parsedUrl)) {
    return false;
  }

  return cleanedUrl;
};

export const facebookUrlValidator = (url: string): string | false => {
  if (!url) {
    return url;
  }
  const normalizedUrl = normalizeUrl(url);
  let cleanedUrl = normalizedUrl;
  if (!url.includes('?id=') && !url.includes('&id=')) {
    cleanedUrl = cleanUrl(normalizedUrl);
  }

  const parsedUrl = isValidUrl(cleanedUrl);
  if (!parsedUrl || !urlHostEquals(parsedUrl, 'facebook.com') || !urlPathNotEmpty(parsedUrl)) {
    return false;
  }

  return cleanedUrl;
};

export const pinterestUrlValidator = (url: string): string | false => {
  if (!url) {
    return url;
  }
  const normalizedUrl = normalizeUrl(url);
  const cleanedUrl = cleanUrl(normalizedUrl);

  const parsedUrl = isValidUrl(cleanedUrl);
  if (
    !parsedUrl ||
    !urlHostEndsWithOneOf(parsedUrl, ['pinterest.com', 'pinterest.co.uk', 'pinterest.ca', 'pinterest.com.au']) ||
    !urlPathNotEmpty(parsedUrl)
  ) {
    return false;
  }

  return cleanedUrl;
};

export const tikTokUrlValidator = (url: string): string | false => {
  //currently no backend validation for tiktok
  return url;
};

export const xUrlValidator = (url: string): string | false => {
  if (!url) {
    return url;
  }
  if (url.includes('/#!/')) {
    url = url.replace('/#!/', '/');
  }
  const normalizedUrl = normalizeUrl(url);
  const cleanedUrl = cleanUrl(normalizedUrl);

  const parsedUrl = isValidUrl(cleanedUrl);
  if (!parsedUrl || !urlHostEqualsOneOf(parsedUrl, ['twitter.com', 'x.com']) || !urlPathNotEmpty(parsedUrl)) {
    return false;
  }

  return cleanedUrl;
};

export const instagramUrlValidator = (url: string): string | false => {
  if (!url) {
    return url;
  }
  const normalizedUrl = normalizeUrl(url);
  const cleanedUrl = cleanUrl(normalizedUrl);

  const parsedUrl = isValidUrl(cleanedUrl);
  if (!parsedUrl || !urlHostEquals(parsedUrl, 'instagram.com') || !urlPathNotEmpty(parsedUrl)) {
    return false;
  }

  return cleanedUrl;
};

function normalizeUrl(url: string): string {
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return 'http://' + url;
  }
  return url;
}

function cleanUrl(url: string): string {
  try {
    const parsedUrl = new URL(url);
    const scheme = parsedUrl.protocol;
    const host = parsedUrl.host;
    const path = normalizeUrlPath(parsedUrl.pathname);
    return `${scheme}//${host}${path}`;
  } catch (e) {
    return url;
  }
}

function normalizeUrlPath(path: string): string {
  const re = /\/{2,}/g;
  return path.replace(re, '/');
}

function isValidUrl(url: string): URL | null {
  try {
    const newUrl = new URL(url);
    return newUrl;
  } catch (e) {
    return null;
  }
}

function urlHostEquals(url: URL, hostValue: string): boolean {
  return url.host.toLowerCase().replace(/^www\./, '') === hostValue.toLowerCase();
}

function urlHostEqualsOneOf(url: URL, hostValues: string[]): boolean {
  const host = url.host.toLowerCase().replace(/^www\./, '');
  return hostValues.some((h) => h === host);
}

function urlHostEndsWith(url: URL, hostValue: string): boolean {
  return url.host.toLowerCase().endsWith(hostValue);
}

function urlHostEndsWithOneOf(url: URL, hostValues: string[]): boolean {
  const host = url.host.toLowerCase();
  return hostValues.some((h) => host.endsWith(h.toLowerCase()));
}

function urlPathNotEmpty(url: URL): boolean {
  return !(url.pathname === '' || (url.pathname === '/' && url.hash === ''));
}
