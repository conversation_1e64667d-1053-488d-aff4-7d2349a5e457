import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  Inject,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { TranslateModule } from '@ngx-translate/core';
import {
  AbstractControl,
  ReactiveFormsModule,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
} from '@angular/forms';
import {
  FormInputComponent,
  FieldValueInterface,
  FieldOptionsInjectionToken,
  FieldOptionsDependencies,
} from '../../../../tokens-and-interfaces';
import { InlineEditService } from '../../inline-edit.service';
import { StandardIds } from '../../../../shared-services';
import { SyncIconComponent } from '../accounts/sync-icon/sync-icon.component';
import { SEARCH_DEBOUNCE_MS } from '../../../../constants';
import {
  facebookUrlValidator,
  linkedInUrlValidator,
  pinterestUrlValidator,
  tikTokUrlValidator,
  xUrlValidator,
  instagramUrlValidator,
} from './url.service';

import {
  BehaviorSubject,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  Observable,
  of,
  skipWhile,
  startWith,
  switchMap,
  tap,
} from 'rxjs';

import { stopChromeAutofill } from '../stop-chrome-autofill';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FieldWarningService } from '../model-driven-form-error/field-warning.service';
import { provideAnimations } from '@angular/platform-browser/animations';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { AutoFill } from '@galaxy/crm/components/animations';
import { UpdaterService } from '../updater.service';

@Component({
  templateUrl: './social-media-url.html',
  styleUrls: ['../renderers.scss'],
  imports: [
    CommonModule,
    MatInputModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    MatAutocompleteModule,
    SyncIconComponent,
    MatIcon,
    MatIconButton,
  ],
  providers: [FieldWarningService, provideAnimations()],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [AutoFill],
})
export class SocialMediaUrlComponent extends FormInputComponent {
  private validUrl = '';

  filteredOptions$: Observable<string[]> = of([]);
  initialized$ = new BehaviorSubject<boolean>(false);

  _fieldId = '';
  set fieldId(fieldId: string) {
    this._fieldId = fieldId;
  }
  get fieldId(): string {
    return this._fieldId;
  }
  _label = '';
  set label(label: string) {
    this._label = label;
  }
  get label(): string {
    return this._label;
  }

  _control!: UntypedFormControl;
  set control(formControl: UntypedFormControl) {
    this._control = formControl;
    this._control.clearValidators();
    this.setUrlValidator();
    const controlChanges$ = this._control.valueChanges.pipe(
      startWith(this._control.value),
      debounceTime(SEARCH_DEBOUNCE_MS),
    );
    const initialized$ = this.initialized$.pipe(distinctUntilChanged());
    this.filteredOptions$ = combineLatest([controlChanges$, initialized$]).pipe(
      takeUntilDestroyed(this.destroyRef),
      skipWhile(([, initialized]: [string, boolean]) => !initialized),
      switchMap(([value]: [string, boolean]) => {
        return this.fieldOptionsDependencies.getFieldOptions(this.fieldId, value);
      }),
    );
  }
  get control(): UntypedFormControl {
    return this._control;
  }

  disableAutoComplete: string = stopChromeAutofill();
  autoFillState = 'default';
  private isInlineEdit = toSignal(this.inlineEditService.isInlineEdit$);

  constructor(
    @Inject(FieldOptionsInjectionToken) private readonly fieldOptionsDependencies: FieldOptionsDependencies,
    readonly destroyRef: DestroyRef,
    private changeDetRef: ChangeDetectorRef,
    private updaterService: UpdaterService,
    private readonly inlineEditService: InlineEditService,
  ) {
    super();
    this.updaterService.clearedField$
      .pipe(
        takeUntilDestroyed(),
        filter((f: FieldValueInterface) => f.fieldId === this.fieldId),
      )
      .subscribe((_) => this.clear());

    this.updaterService.updatedField
      .pipe(
        takeUntilDestroyed(),
        tap((data) => {
          if (data.stringValue?.trim().length && data.fieldId === this.fieldId) {
            this.autoFillState = 'highlight';
            setTimeout(() => {
              this.autoFillState = 'default';
              this.changeDetRef.detectChanges();
            }, 1200);
            this.control.setValue(data.stringValue);
            this.control.markAsDirty();
            this.changeDetRef.detectChanges();
          }
        }),
      )
      .subscribe();
  }

  clear(): void {
    this.control.reset();
    this.control.markAsDirty();
    if (this.isInlineEdit()) {
      this.saveField();
    }
  }

  initializeField(): void {
    this.initialized$.next(true);
  }

  set formGroup(group: UntypedFormGroup) {
    // do nothing
  }

  get hasModification(): boolean {
    return this.control.dirty;
  }

  get value(): FieldValueInterface {
    return {
      fieldId: this.fieldId,
      stringValue: this.control.value,
    };
  }

  saveField(): void {
    if (this.control.valid) {
      this.inlineEditService.saveInlineEdit(this.objectType, [
        {
          fieldId: this.fieldId,
          stringValue: this.validUrl,
        },
      ]);
    }
  }

  private setUrlValidator() {
    switch (this.fieldId) {
      case StandardIds.CompanyLinkedInURL:
        this._control.addValidators(this.isValidLinkedInUrl.bind(this));
        break;
      case StandardIds.CompanyFacebookURL:
        this._control.addValidators(this.isValidFacebookUrl.bind(this));
        break;
      case StandardIds.CompanyPinterestURL:
        this._control.addValidators(this.isValidPinterestInUrl.bind(this));
        break;
      case StandardIds.CompanyTikTokURL:
        this._control.addValidators(this.isValidTikTokUrl.bind(this));
        break;
      case StandardIds.CompanyXURL:
        this._control.addValidators(this.isValidXUrl.bind(this));
        break;
      case StandardIds.CompanyInstagramURL:
        this._control.addValidators(this.isValidInstagramUrl.bind(this));
        break;
    }
  }

  private isValidLinkedInUrl(control: AbstractControl): ValidationErrors | null {
    const linkedInUrl = control.value;
    const validLinkedInUrl = linkedInUrlValidator(linkedInUrl);
    if (validLinkedInUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validLinkedInUrl;
    return null;
  }

  private isValidFacebookUrl(control: AbstractControl): ValidationErrors | null {
    const facebookUrl = control.value;
    const validFacebookUrl = facebookUrlValidator(facebookUrl);
    if (validFacebookUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validFacebookUrl;
    return null;
  }

  private isValidPinterestInUrl(control: AbstractControl): ValidationErrors | null {
    const pinterestUrl = control.value;
    const validPinterestUrl = pinterestUrlValidator(pinterestUrl);
    if (validPinterestUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validPinterestUrl;
    return null;
  }

  private isValidTikTokUrl(control: AbstractControl): ValidationErrors | null {
    const tikTokUrl = control.value;
    const validTikTokUrl = tikTokUrlValidator(tikTokUrl);
    if (validTikTokUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validTikTokUrl;
    return null;
  }

  private isValidXUrl(control: AbstractControl): ValidationErrors | null {
    const xUrl = control.value;
    const validXUrl = xUrlValidator(xUrl);
    if (validXUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validXUrl;
    return null;
  }

  private isValidInstagramUrl(control: AbstractControl): ValidationErrors | null {
    const instagramUrl = control.value;
    const validInstagramUrl = instagramUrlValidator(instagramUrl);
    if (validInstagramUrl === false) {
      return {
        invalidSocialMediaUrl: true,
      };
    }
    this.validUrl = validInstagramUrl;
    return null;
  }
}
