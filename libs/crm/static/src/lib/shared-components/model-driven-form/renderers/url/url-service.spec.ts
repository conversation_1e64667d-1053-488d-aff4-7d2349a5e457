import {
  linkedInUrlValidator,
  facebookUrlValidator,
  pinterestUrlValidator,
  tikTokUrlValidator,
  xUrlValidator,
  instagramUrlValidator,
} from './url.service';

describe('Social URLs Validation', () => {
  const testCases = [
    {
      input: { FacebookURL: 'https://www.facebook.com/company' },
      expectedResult: 'https://www.facebook.com/company',
      description: 'Test that the most basic URLs pass.',
    },
    {
      input: { FacebookURL: 'https://facebook.com/company?var=value' },
      expectedResult: 'https://facebook.com/company',
      description: 'Test URLs without www.',
    },
    {
      input: { FacebookURL: 'https://www.facebook/company' },
      expectedResult: false,
      description: 'Test that URLs without a proper ending fail.',
    },
    {
      input: { FacebookURL: 'https://www.facebook.ca/company' },
      expectedResult: false,
      description: 'Test that URLs without the correct ending fail.',
    },
    {
      input: { FacebookURL: 'https://www.twitter.com/company' },
      expectedResult: false,
      description: 'Test that URLs without the correct host fail.',
    },
    {
      input: { FacebookURL: 'https://facebook.com/theGoldenItch' },
      expectedResult: 'https://facebook.com/theGoldenItch',
      description: 'Test that url with a valid path succeeds',
    },
    {
      input: { FacebookURL: 'https://facebook.com/' },
      expectedResult: false,
      description: 'Test that url with a slash for path fails',
    },
    {
      input: { FacebookURL: 'https://facebook.com' },
      expectedResult: false,
      description: 'Test that url with empty path fails',
    },
    {
      input: { XURL: 'https://twitter.com/theGoldenItch' },
      expectedResult: 'https://twitter.com/theGoldenItch',
      description: 'Test that url with a valid path succeeds',
    },
    {
      input: { XURL: 'https://x.com/excitingAccountName' },
      expectedResult: 'https://x.com/excitingAccountName',
      description: 'Test that the domain host X succeeds',
    },
    {
      input: { XURL: 'https://x.com/' },
      expectedResult: false,
      description: 'Test that url with domain host X and a slash for path fails',
    },
    {
      input: { XURL: 'https://x.com' },
      expectedResult: false,
      description: 'Test that url with domain host X empty path fails',
    },
    {
      input: { XURL: 'https://twitter.com/' },
      expectedResult: false,
      description: 'Test that url with a slash for path fails',
    },
    {
      input: { XURL: 'https://twitter.com' },
      expectedResult: false,
      description: 'Test that url with empty path fails',
    },
    {
      input: { InstagramURL: 'https://instagram.com/theGoldenItch' },
      expectedResult: 'https://instagram.com/theGoldenItch',
      description: 'Test that url with a valid path succeeds',
    },
    {
      input: { InstagramURL: 'https://instagram.com/' },
      expectedResult: false,
      description: 'Test that url with a slash for path fails',
    },
    {
      input: { InstagramURL: 'https://instagram.com' },
      expectedResult: false,
      description: 'Test that url with empty path fails',
    },
    {
      input: { PinterestURL: 'https://pinterest.com/theGoldenItch' },
      expectedResult: 'https://pinterest.com/theGoldenItch',
      description: 'Test that url with a valid path succeeds',
    },
    {
      input: { PinterestURL: 'https://pinterest.com/' },
      expectedResult: false,
      description: 'Test that url with a slash for path fails',
    },
    {
      input: { PinterestURL: 'https://pinterest.com' },
      expectedResult: false,
      description: 'Test that url with empty path fails',
    },
    {
      input: { LinkedInURL: 'https://linkedin.com/theGoldenItch' },
      expectedResult: 'https://linkedin.com/theGoldenItch',
      description: 'Test that url with a valid path succeeds',
    },
    {
      input: { LinkedInURL: 'https://linkedin.com/' },
      expectedResult: false,
      description: 'Test that url with a slash for path fails',
    },
    {
      input: { LinkedInURL: 'https://linkedin.com' },
      expectedResult: false,
      description: 'Test that url with empty path fails',
    },
    {
      input: { LinkedInURL: 'http://ca.linkedin.com/company/124673' },
      expectedResult: 'http://ca.linkedin.com/company/124673',
      description: 'Test that Canadian linkedin URL succeeds',
    },
    {
      input: { InstagramURL: 'http://Instagram.com/bathurstrsl' },
      expectedResult: 'http://instagram.com/bathurstrsl',
      description: 'Test that case in hostname is ignored',
    },
    {
      input: { PinterestURL: 'https://au.pinterest.com/thestyleco/' },
      expectedResult: 'https://au.pinterest.com/thestyleco/',
      description: 'Test that country-specific pinterest domain succeeds',
    },
    {
      input: { PinterestURL: 'https://pinterest.co.uk/thestyleco/' },
      expectedResult: 'https://pinterest.co.uk/thestyleco/',
      description: 'Test that country-specific pinterest domain(.co.uk) succeeds',
    },
    {
      input: { PinterestURL: 'https://au.pinterest.co.uk/thestyleco/' },
      expectedResult: 'https://au.pinterest.co.uk/thestyleco/',
      description: 'Test that country-specific pinterest domain (both prefix and .co.uk) succeeds',
    },
    {
      input: { PinterestURL: 'https://pinterest.ca/thestyleco/' },
      expectedResult: 'https://pinterest.ca/thestyleco/',
      description: 'Test that country-specific pinterest domain(.ca) succeeds',
    },
    {
      input: { PinterestURL: 'https://pinterest.com.au/thestyleco/' },
      expectedResult: 'https://pinterest.com.au/thestyleco/',
      description: 'Test that country-specific pinterest domain(.com.au) succeeds',
    },
    {
      input: { FacebookURL: 'Www.facebook.com/company' },
      expectedResult: 'http://www.facebook.com/company',
      description: 'Test that capitalized www domain cleaned up and passes',
    },
    {
      input: { TikTokUrl: 'hi' },
      expectedResult: 'hi',
      description: 'any tiktok url passes',
    },
  ];

  testCases.forEach(({ input, expectedResult, description }) => {
    test(description, () => {
      const validators = {
        FacebookURL: facebookUrlValidator,
        XURL: xUrlValidator,
        LinkedInURL: linkedInUrlValidator,
        PinterestURL: pinterestUrlValidator,
        InstagramURL: instagramUrlValidator,
        TikTokUrl: tikTokUrlValidator,
      };

      Object.keys(input).forEach((key) => {
        const control = { value: input[key] };
        const validator = validators[key];
        const result = validator(control.value);

        expect(result).toEqual(expectedResult);
      });
    });
  });
});
