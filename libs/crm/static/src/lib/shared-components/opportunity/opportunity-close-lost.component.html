<div mat-dialog-title>{{ 'OPPORTUNITY.PROFILE_PAGE.CLOSE_LOST_MODAL.ACTION' | translate }}</div>
<mat-dialog-content>
  <div>
    <glxy-form-field [hideRequiredLabel]="true">
      <glxy-label-hint>
        {{ 'OPPORTUNITY.PROFILE_PAGE.CLOSE_LOST_MODAL.HINT' | translate }}
      </glxy-label-hint>
      <mat-select
        placeholder="{{ 'OPPORTUNITY.PROFILE_PAGE.CLOSE_LOST_MODAL.SELECT_PLACEHOLDER' | translate }}"
        [formControl]="closedLostReason"
      >
        @for (reason of closedLostReasons; track reason) {
          <mat-option [value]="reason | translate">
            {{ reason | translate }}
          </mat-option>
        }
      </mat-select>
    </glxy-form-field>
    <glxy-form-field>
      <textarea
        matInput
        placeholder="{{ 'OPPORTUNITY.PROFILE_PAGE.CLOSE_LOST_MODAL.NOTES_PLACEHOLDER' | translate }}"
        cdkTextareaAutosize
        cdkAutosizeMinRows="3"
        cdkAutosizeMaxRows="10"
        [formControl]="closedLostReasonDescription"
      ></textarea>
    </glxy-form-field>
  </div>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-stroked-button type="button" matDialogClose>{{ 'ACTIONS.CANCEL' | translate }}</button>
  <button mat-flat-button type="submit" color="primary" [disabled]="!closedLostReason.valid" (click)="closeDialog()">
    {{ 'OPPORTUNITY.PROFILE_PAGE.CLOSE_LOST_MODAL.ACTION' | translate }}
  </button>
</mat-dialog-actions>
