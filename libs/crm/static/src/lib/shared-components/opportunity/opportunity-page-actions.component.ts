import { Component, computed, effect, inject, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { CrmInjectionToken, PROFILE_PAGE_DATA_TOKEN } from '../../tokens-and-interfaces';
import { CrmFieldService, OpportunityService, StandardIds } from '../../shared-services';
import { CrmObjectInterface, FieldValue, PipelinesApiService, Stage } from '@vendasta/crm';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { debounceTime, filter, firstValueFrom } from 'rxjs';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';
import { UpdaterService } from '../model-driven-form';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog } from '@angular/material/dialog';
import { CrmOpportunityCloseLostComponent, CloseLostOpportunityData } from './opportunity-close-lost.component';

type OpportunityStatus = 'Open' | 'Closed Won' | 'Closed Lost';

@Component({
  selector: 'crm-opportunity-page-actions',
  templateUrl: './opportunity-page-actions.component.html',
  styleUrls: ['./opportunity-page-actions.component.scss'],
  imports: [CommonModule, MatButtonModule, MatProgressSpinnerModule, TranslateModule],
})
export class CrmOpportunityPageActionsComponent {
  private readonly config = inject(CrmInjectionToken);
  private readonly data = inject(PROFILE_PAGE_DATA_TOKEN);
  private readonly fieldService = inject(CrmFieldService);
  private readonly opportunityService = inject(OpportunityService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly pipelineService = inject(PipelinesApiService);
  private readonly updaterService = inject(UpdaterService);
  private readonly dialog = inject(MatDialog);

  protected readonly status = signal<string>(this.getStatus(this.data.crmObject));
  protected readonly loading = signal<boolean>(false);
  protected readonly pipelineId = signal<string>(this.getPipelineId(this.data.crmObject));
  private readonly stages = signal<Stage[]>([]);
  protected readonly wonStage = computed(() => this.getStage(this.stages(), 'Closed Won'));
  protected readonly lostStage = computed(() => this.getStage(this.stages(), 'Closed Lost'));
  protected readonly firstOpenStage = computed(() => this.getStage(this.stages(), 'Open'));

  constructor() {
    this.updaterService.updatedField
      .pipe(
        takeUntilDestroyed(),
        filter((field) => field.fieldId === StandardIds.OpportunityStatus),
        debounceTime(100),
      )
      .subscribe((field) => this.status.set(field.stringValue ?? 'Open'));
    effect(async () => {
      const pipelineId = this.pipelineId();
      const namespace = await firstValueFrom(this.config.namespace$);
      const pipelines = await firstValueFrom(
        this.pipelineService.getMultiPipeline({ namespace, pipelineIds: [pipelineId] }),
      );
      const pipeline = pipelines.pipelines?.[0];
      this.stages.set(pipeline?.stages ?? []);
    });
  }

  private getStage(stages: Stage[], status: OpportunityStatus): Stage | undefined {
    return stages.find((stage) =>
      stage.mutations.find((m) => m.fieldId === StandardIds.OpportunityStatus && m.stringValue === status),
    );
  }

  private getStatus(crmObject?: CrmObjectInterface): string {
    if (crmObject) {
      return this.fieldService.getFieldValueFromCrmObject(crmObject, StandardIds.OpportunityStatus)?.stringValue || '';
    }
    return '';
  }

  private getPipelineId(crmObject?: CrmObjectInterface): string {
    if (crmObject) {
      return (
        this.fieldService.getFieldValueFromCrmObject(crmObject, StandardIds.OpportunityPipelineID)?.stringValue || ''
      );
    }
    return '';
  }

  async updateStage(stage?: Stage): Promise<void> {
    let closeLostData: CloseLostOpportunityData | undefined;
    if (this.data.crmObject && stage) {
      if (stage.stageId === this.lostStage()?.stageId) {
        closeLostData = await firstValueFrom(
          this.dialog
            .open(CrmOpportunityCloseLostComponent, {
              minWidth: '360px',
            })
            .afterClosed(),
        );
        if (!closeLostData) {
          return;
        }
      }
      this.loading.set(true);
      try {
        const namespace = await firstValueFrom(this.config.namespace$);
        const paths = stage.mutations.map((m) => m.fieldId);
        stage.mutations.push(
          closeLostData?.reason ??
            new FieldValue({ fieldId: StandardIds.OpportunityClosedLostReason, stringValue: '' }),
        );
        paths.push(StandardIds.OpportunityClosedLostReason);
        stage.mutations.push(
          closeLostData?.reasonDescription ??
            new FieldValue({ fieldId: StandardIds.OpportunityClosedLostReasonDescription, stringValue: '' }),
        );
        paths.push(StandardIds.OpportunityClosedLostReasonDescription);

        await firstValueFrom(
          this.opportunityService.updateCrmObject({
            namespace,
            crmObject: {
              crmObjectId: this.data.crmObject.crmObjectId,
              fields: stage.mutations,
            },
            fieldMask: { paths },
          }),
        );
        this.snackbarService.openSuccessSnack('OPPORTUNITY.EDIT_PAGE.SAVE.SUCCESS');
        this.syncChanges(stage, closeLostData);
      } catch {
        this.snackbarService.openErrorSnack('OPPORTUNITY.EDIT_PAGE.SAVE.FAIL');
      } finally {
        this.loading.set(false);
      }
    }
  }

  private syncChanges(stage: Stage, closeLost?: CloseLostOpportunityData): void {
    if (!this.data?.crmObject?.fields) {
      return;
    }
    // merge status into data object
    const otherFields = this.data.crmObject.fields.filter((f) => !stage.mutations.find((m) => m.fieldId === f.fieldId));
    this.data.crmObject = {
      ...this.data.crmObject,
      fields: [...otherFields, ...stage.mutations],
    };
    // emit changes
    stage.mutations.forEach((mutation) => this.updaterService.updateFieldValue(mutation));
    this.updaterService.updateFieldValue({
      fieldId: StandardIds.OpportunityCalculatedStageID,
      stringValue: stage.stageId,
    });
    this.updaterService.updateStringField(
      StandardIds.OpportunityClosedLostReason,
      closeLost?.reason?.stringValue ?? '',
      true,
    );
    this.updaterService.updateStringField(
      StandardIds.OpportunityClosedLostReasonDescription,
      closeLost?.reasonDescription?.stringValue ?? '',
      true,
    );
    this.data.onChangeCallback?.(this.data.crmObject);
  }
}
