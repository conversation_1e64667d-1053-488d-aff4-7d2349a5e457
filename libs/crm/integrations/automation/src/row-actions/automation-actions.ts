import { SingleRowAction } from '@galaxy/crm/static';
import { AutomationActionsService } from './automation-actions.service';
import { CRMSelectAllOptions, MultiRowAction, SelectAllAction } from '@galaxy/crm/static';
import { Row } from '@vendasta/galaxy/table';

export function AutomationSingleRowAction(
  namespace: string,
  service: AutomationActionsService,
  objectType: string,
  label: string,
): SingleRowAction {
  return {
    label: label,
    callback: (row) => {
      service.openStartManualAutomationDialog(namespace, objectType, [row], false);
    },
  };
}

export function AutomationMultiRowAction(
  namespace: string,
  service: AutomationActionsService,
  objectType: string,
  label: string,
): MultiRowAction {
  return {
    label: label,
    callback: (rows) => {
      service.openStartManualAutomationDialog(namespace, objectType, rows, false);
    },
  };
}

export function AutomationSelectAllRowAction(
  namespace: string,
  service: AutomationActionsService,
  objectType: string,
  label: string,
): SelectAllAction {
  return {
    label: label,
    callback: (rows: Row[], options?: CRMSelectAllOptions) => {
      service.openSelectAllStartManualAutomationDialog(namespace, objectType, options);
    },
  };
}
