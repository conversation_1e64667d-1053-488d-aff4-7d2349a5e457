import { CommonModule } from '@angular/common';
import {
  Component,
  effect,
  ElementRef,
  EventEmitter,
  forwardRef,
  inject,
  Input,
  Output,
  signal,
  ViewChild,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import {
  GalaxyAbstractFilterInput,
  GalaxyFilterOperator,
  GalaxyFilterValueInterface,
} from '@vendasta/galaxy/filter/chips';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatChipGrid, MatChipInput, MatChipInputEvent, MatChipRemove, MatChipRow } from '@angular/material/chips';
import { MatIcon } from '@angular/material/icon';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { CrmInjectionToken, SimplifiedUser } from '@galaxy/crm/static';
import { debounceTime, firstValueFrom, Observable, of, switchMap } from 'rxjs';

@Component({
  selector: 'crm-user-filter-input',
  template: `
    @if (singleInput()) {
      <glxy-form-field bottomSpacing="none">
        <input
          type="text"
          [placeholder]="'TABLE_FILTERS.SELECT_EXPRESSION_PLACEHOLDER' | translate"
          matInput
          [formControl]="formControl"
          [matAutocomplete]="auto"
          data-testid="filter-user-input"
        />
        <mat-autocomplete
          #auto="matAutocomplete"
          (optionSelected)="selected($event)"
          [displayWith]="displayUserFn.bind(this)"
        >
          <mat-option *ngFor="let option of availableOptions$ | async" [value]="option">
            {{ option?.displayName }}
          </mat-option>
        </mat-autocomplete>
      </glxy-form-field>
    } @else {
      <glxy-form-field bottomSpacing="none">
        <mat-chip-grid #userGrid aria-label="User selection">
          <mat-chip-row *ngFor="let user of selectedUsers$ | async" (removed)="remove(user.userId)">
            {{ user.displayName }}
            <button matChipRemove [attr.aria-label]="'remove ' + user.displayName">
              <mat-icon>cancel</mat-icon>
            </button>
          </mat-chip-row>
          <input
            matInput
            #userInput
            [placeholder]="'TABLE_FILTERS.SELECT_EXPRESSION_PLACEHOLDER' | translate"
            [formControl]="formControl"
            [matChipInputFor]="userGrid"
            [matAutocomplete]="auto"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputAddOnBlur]="false"
            (matChipInputTokenEnd)="add($event)"
            data-testid="filter-multi-user-input"
          />
        </mat-chip-grid>
        <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
          <mat-option *ngFor="let option of availableOptions$ | async" [value]="option">
            {{ option?.displayName }}
          </mat-option>
        </mat-autocomplete>
      </glxy-form-field>
    }
  `,
  providers: [
    {
      provide: GalaxyAbstractFilterInput,
      useExisting: forwardRef(() => CrmUserFilterInputComponent),
    },
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    MatInputModule,
    GalaxyFormFieldModule,
    MatAutocompleteModule,
    MatChipGrid,
    MatChipInput,
    MatChipRemove,
    MatChipRow,
    MatIcon,
  ],
})
export class CrmUserFilterInputComponent implements GalaxyAbstractFilterInput {
  @Input() set initialValue(value: GalaxyFilterValueInterface[]) {
    const userIds = (value ?? []).map((v) => v.string ?? '');
    this.setInitialValue(userIds);
  }

  _operator?: GalaxyFilterOperator;
  @Input() set operator(value: GalaxyFilterOperator) {
    this._operator = value;
    const isMultiInput =
      this._operator == GalaxyFilterOperator.FILTER_OPERATOR_IS_ANY ||
      this._operator == GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT_ANY;
    if (isMultiInput) {
      this.formControl.setValue('');
    } else {
      const selectedUsers = this.selectedUserIDs();
      if (selectedUsers.length > 0) {
        this.formControl.setValue(selectedUsers[0]);
        if (selectedUsers.length > 1) {
          this.selectedUserIDs.set([selectedUsers[0]]);
        }
      }
    }
    this.singleInput.set(!isMultiInput);
  }

  @Output() valueChanges = new EventEmitter<GalaxyFilterValueInterface[]>();

  @ViewChild('userInput') userInput?: ElementRef<HTMLInputElement>;

  private readonly userService = inject(CrmInjectionToken).services?.userService;

  protected readonly separatorKeysCodes: number[] = [ENTER, COMMA];
  protected readonly formControl = new FormControl<SimplifiedUser | string>('', { nonNullable: true });
  protected readonly singleInput = signal(true);
  protected readonly availableOptions$ = this.setupAvailableOptions();
  protected readonly selectedUserIDs = signal<string[]>([]);
  protected readonly selectedUsers$ = toObservable(this.selectedUserIDs).pipe(
    takeUntilDestroyed(),
    switchMap((userIds) => this.userService?.getMultiUsers?.(userIds) ?? of([])),
  );

  constructor() {
    effect(() => {
      const selectedUserIDs = this.selectedUserIDs();
      this.valueChanges.emit(selectedUserIDs.map((s) => ({ string: s })));
    });
  }

  clearValue() {
    this.formControl.setValue('');
  }

  displayUserFn(user?: SimplifiedUser): string {
    return user?.displayName || '';
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    const singleInput = this.singleInput();
    const currentUsers = this.selectedUserIDs();
    const userId = event.option.value?.userId ?? '';
    const hasUser = currentUsers.some((u) => u === userId);
    if (userId && !hasUser) {
      if (singleInput) {
        this.selectedUserIDs.set([userId]);
      } else {
        this.selectedUserIDs.set([...currentUsers, userId]);
      }
    }
    if (!singleInput && this.userInput?.nativeElement) {
      this.userInput.nativeElement.value = '';
      this.clearValue();
    }
  }

  add(event: MatChipInputEvent) {
    const currentUsers = this.selectedUserIDs();
    const hasUser = currentUsers.some((u) => u === event?.value);
    if (event?.value && !hasUser) {
      this.selectedUserIDs.set([...currentUsers, event.value]);
    }
    event.chipInput?.clear();
    this.clearValue();
  }

  remove(userId: string) {
    const currentUsers = this.selectedUserIDs();
    const listWithoutSelectedUser = currentUsers.filter((u) => u !== userId);
    this.selectedUserIDs.set([...listWithoutSelectedUser]);
  }

  private setupAvailableOptions(): Observable<SimplifiedUser[]> {
    return this.formControl.valueChanges.pipe(
      takeUntilDestroyed(),
      debounceTime(250),
      switchMap((searchTerm) => {
        const filterTerm = typeof searchTerm === 'string' ? searchTerm : searchTerm?.userId;
        return this.userService?.searchUsers?.(filterTerm || '') ?? [];
      }),
    );
  }

  private async setInitialValue(userIds: string[]): Promise<void> {
    this.selectedUserIDs.set(userIds);
    const singleInput = this.singleInput();
    if (this.userService && userIds.length > 0 && singleInput) {
      const users = await firstValueFrom(this.userService.getMultiUsers(userIds));
      this.formControl.setValue(users?.[0]);
    }
  }
}
