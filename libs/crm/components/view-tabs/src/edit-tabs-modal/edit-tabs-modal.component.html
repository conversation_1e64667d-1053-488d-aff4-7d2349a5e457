<mat-dialog-content>
  @for (tabGroup of groupedTabs; track tabGroup; let groupIdx = $index) {
    <div class="tab-group">
      <div class="tab-group-name">
        {{ tabGroup.name }}
      </div>
      @for (tab of tabGroup.tabs; track tab; let idx = $index) {
        <div class="tab-form">
          <div class="tab-form-name-and-toggle">
            <mat-checkbox [checked]="!tab.hidden" (change)="tab.hidden = !tab.hidden"></mat-checkbox>
            @if (tabGroup.canModify) {
              <glxy-form-field [bottomSpacing]="'none'" class="tab-form-name">
                <input type="text" [(ngModel)]="tab.name" />
              </glxy-form-field>
            } @else {
              {{ tab.name }}
            }
          </div>
          @if (tabGroup.canModify) {
            <button mat-icon-button type="button" (click)="deleteTab(groupIdx, idx)">
              <mat-icon class="pipeline-stages-button">close</mat-icon>
            </button>
          }
        </div>
      }
      @if (tabGroup.canModify) {
        <div class="add-button-container">
          <button mat-button class="add-button" color="primary" (click)="addTabToGroup(groupIdx)">+ Add</button>
        </div>
      }
    </div>
  }
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-stroked-button (click)="cancel()">{{ 'ACTIONS.CANCEL' | translate }}</button>
  <button mat-flat-button color="primary" (click)="saveChanges()">{{ 'ACTIONS.SAVE' | translate }}</button>
</mat-dialog-actions>
