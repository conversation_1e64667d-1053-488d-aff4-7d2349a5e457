@use 'design-tokens' as *;

.tab-group {
  display: flex;
  flex-direction: column;
}

.tab-group-name {
  @include text-preset-4--bold;
}

.tab-form {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: $spacing-2 0;
}

.tab-form-name-and-toggle {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-grow: 1;
  @include text-preset-4;
}

.tab-form-name {
  flex-grow: 1;
}

.add-button-container {
  display: flex;
}

.add-button {
  align-self: flex-start;
}
