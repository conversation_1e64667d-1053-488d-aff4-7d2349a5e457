<div class="view-container">
  <nav
    mat-tab-nav-bar
    [tabPanel]="tabPanel"
    animationDuration="0"
    cdkDropList
    cdkDropListOrientation="horizontal"
    (cdkDropListDropped)="drop($event)"
    cdkDropListElementContainer=".mat-mdc-tab-links"
    class="example-drag-tabs"
  >
    @for (tab of visibleTabs(); track tab; let idx = $index) {
      @if (!tab.hidden) {
        <a
          mat-tab-link
          (click)="loadTab(idx)"
          [active]="selectedIndex() === idx"
          cdkDrag
          cdkDragRootElement=".mat-tab-link"
          cdkDragPreviewClass="example-drag-tabs-preview"
        >
          {{ tab.name }}
        </a>
      }
    }
    <a mat-tab-link (click)="modifyTabs()">
      <span class="open-modal-tab">{{ this.editTabLabel() }}</span>
    </a>
  </nav>
  <mat-tab-nav-panel #tabPanel class="view-contents">
    <ng-content />
  </mat-tab-nav-panel>
</div>
