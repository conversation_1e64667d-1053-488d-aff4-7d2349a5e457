import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { GalaxyFilterChipInjectionToken } from '@vendasta/galaxy/filter/chips';
import {
  ObjectType,
  PAGE_ROUTES,
  PageAnalyticsInjectionToken,
  pageAnalyticsInjectionTokenGenerator,
  tableFiltersInjectionTokenGenerator,
  FieldOptionsInjectionToken as ModelDrivenFormFieldOptionsInjectionToken,
  modelDrivenFormFieldOptionsInjectionTokenGenerator,
  CrmObjectInjectionToken,
  CrmDependencies,
  CrmInjectionToken,
  crmObjectDependenciesTokenGenerator,
  CustomObjectTableCustomizationService,
  TranslateForCrmObjectService,
} from '@galaxy/crm/static';
import { FeatureFlagGuard } from '../feature-flag-guard.service';

const OBJECT_TYPE: ObjectType = 'CustomObject';
const OBJECT_URL_ROOT = 'custom-object';

const OBJECT_ROUTES: Routes = [
  {
    path: '',
    redirectTo: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.LIST,
    pathMatch: 'full',
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.LIST,
    loadComponent: () => import('./list.component').then((m) => m.ListCustomObjectsPageComponent),
    data: { view: 'list' },
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.BOARD,
    loadComponent: () => import('./list.component').then((m) => m.ListCustomObjectsPageComponent),
    canActivate: [FeatureFlagGuard],
    data: { featureFlag: 'crm_object_board_view', view: 'board' },
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.CREATE,
    loadComponent: () => import('./create.component').then((m) => m.CrmCreateCustomObjectComponent),
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.EDIT,
    loadComponent: () => import('./edit.component').then((m) => m.CrmEditCustomObjectComponent),
  },
  {
    path: PAGE_ROUTES.CUSTOM_OBJECT.SUBROUTES.PROFILE,
    loadComponent: () => import('./custom-object-profile.component').then((m) => m.CrmCustomObjectProfilePageComponent),
  },
];

// this module must be lazy loaded or the injection tokens will overwrite each other for the different object types
@NgModule({
  imports: [RouterModule.forChild(OBJECT_ROUTES)],
  exports: [RouterModule],
  providers: [
    CustomObjectTableCustomizationService,
    TranslateForCrmObjectService,
    {
      provide: PageAnalyticsInjectionToken,
      useFactory: pageAnalyticsInjectionTokenGenerator(OBJECT_TYPE),
    },
    {
      provide: ModelDrivenFormFieldOptionsInjectionToken,
      useFactory: modelDrivenFormFieldOptionsInjectionTokenGenerator(OBJECT_TYPE),
    },
    {
      provide: GalaxyFilterChipInjectionToken,
      useFactory: tableFiltersInjectionTokenGenerator(OBJECT_TYPE),
    },
    {
      provide: CrmObjectInjectionToken,
      useFactory: (
        crm: CrmDependencies,
        customObjectTableCustomizationService: CustomObjectTableCustomizationService,
      ) =>
        crmObjectDependenciesTokenGenerator(
          crm,
          customObjectTableCustomizationService,
          OBJECT_URL_ROOT,
          crm.customObject,
        ),
      deps: [CrmInjectionToken, CustomObjectTableCustomizationService],
    },
  ],
})
export class CrmCustomObjectModule {}
