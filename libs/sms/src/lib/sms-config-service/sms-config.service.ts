import { inject, Injectable } from '@angular/core';
import { SmsContextToken } from '../token';
import { catchError, combineLatest, distinctUntilChanged, map, Observable, of, switchMap } from 'rxjs';
import {
  ConfigApiService,
  ConversationApiService,
  PhoneNumberConfig,
  PhoneNumberConfigInterface,
} from '@vendasta/smsv2';
import { HttpResponse } from '@angular/common/http';
import { GetPhoneNumberConfigResponse } from '@vendasta/smsv2/lib/_internal/objects';

@Injectable({
  providedIn: 'root',
})
export class SmsConfigService {
  private readonly context = inject(SmsContextToken);
  private readonly configApiService = inject(ConfigApiService);
  private readonly conversationAPI = inject(ConversationApiService);
  public readonly phoneNumberConfig$: Observable<PhoneNumberConfig>;
  public readonly usesBroadlyTwilio$ = this.context.sendsViaBroadly$;
  public readonly excludedFromVoiceAI$: Observable<boolean>;
  // BREW-1431: NBLY Voice Receptionist POC
  private readonly voiceAINBLYAccountGroups = [
    'AG-6R772G8ZJ6',
    'AG-R6VN65LK8H',
    'AG-JQB5ZTP4X8',
    'AG-2BPQCT27GH',
    'AG-4T2TR48LM2',
    'AG-N42V33GCVZ',
    'AG-G4PHF8MR2F',
    'AG-8PFGWWLM6Z',
    'AG-DKL4G3STWT',
    'AG-H4V5C6NWX3',
    'AG-K5BM4K87TC',
    'AG-4TW6XV2NGS',
    'AG-8B7PMHHMXB',
    'AG-G3X6QF3RQK',
    'AG-S8FGCZHRHK',
    'AG-KT6B555PK6',
    'AG-GLH3JJKCDB',
  ];

  constructor() {
    this.phoneNumberConfig$ = this.context.ownerId$.pipe(
      switchMap((ownerId) =>
        this.configApiService.getPhoneNumberConfig({ ownerId: ownerId, ownerType: this.context.ownerType }).pipe(
          catchError(() => {
            return of({ phoneNumberConfig: {} } as GetPhoneNumberConfigResponse);
          }),
        ),
      ),
      map((response) => response.phoneNumberConfig),
    );
    this.excludedFromVoiceAI$ = combineLatest([this.context.featureFlagInfo$, this.context.ownerId$]).pipe(
      distinctUntilChanged(),
      map(([featureFlagInfo, ownerId]) => {
        return featureFlagInfo.partnerId === 'NBLY' && !this.voiceAINBLYAccountGroups.includes(ownerId);
      }),
    );
  }

  public upsertPhoneNumberConfig(config: PhoneNumberConfigInterface): Observable<HttpResponse<null>> {
    return this.context.ownerId$.pipe(
      switchMap((ownerId) => {
        const payload = {
          ownerId: ownerId,
          ownerType: this.context.ownerType,
          phoneNumberConfig: config,
        };

        return this.configApiService.upsertPhoneNumberConfig(payload);
      }),
    );
  }

  public claimPhoneNumber(): Observable<HttpResponse<null>> {
    return this.context.ownerId$.pipe(
      switchMap((ownerId) => {
        const payload = {
          ownerId: ownerId,
          ownerType: this.context.ownerType,
        };

        return this.conversationAPI.claimPhoneNumber(payload);
      }),
    );
  }
}
