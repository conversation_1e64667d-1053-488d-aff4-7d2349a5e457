import { Component, computed, inject, OnInit, signal, Signal } from '@angular/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { FormControl, FormControlStatus, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormGroup } from '@angular/forms';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { CommonModule } from '@angular/common';
import { debounceTime, filter, map, shareReplay, startWith, take, tap } from 'rxjs/operators';
import { AccountGroupService, ProjectionFilter } from '@galaxy/account-group';
import { combineLatest, lastValueFrom, Observable, of, switchMap } from 'rxjs';
import { MultiLocationService } from '@vendasta/multi-location';
import { PARTNER_ID_TOKEN } from '../../tokens';
import { MatButtonModule } from '@angular/material/button';
import {
  ApplyConfigurationRequest,
  ConfigurationManagementApiService,
  ConfigurationTargetContainer,
  ObjectApplicationSelection,
  ObjectApplicationStrategy,
  TargetContainerNamespaceType,
} from '@vendasta/configuration-management';
import { ConfigOption } from '../../config-editor/config-options/options.component';
import { ConfigOptionsComponent } from '../../config-editor/config-options/options.component';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { IAMService, SortDirection, UserSortField } from '@vendasta/iam';

interface TargetContainer {
  id: string;
  name: string;
}

interface ServiceAccountUser {
  userId: string;
  name: string;
}

@Component({
  selector: 'config-applier-dialog',
  templateUrl: './config-applier-dialog.component.html',
  styleUrls: ['./config-applier-dialog.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    MatDialogModule,
    MatSelectModule,
    MatInputModule,
    GalaxyFormFieldModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    CommonModule,
    MatButtonModule,
    ConfigOptionsComponent,
  ],
})
export class ConfigApplierDialogComponent implements OnInit {
  private readonly dialogRef = inject(MatDialogRef<ConfigApplierDialogComponent>);
  private readonly accountGroupService = inject(AccountGroupService);
  private readonly multiLocationService = inject(MultiLocationService);
  private readonly partnerId$ = inject(PARTNER_ID_TOKEN);
  private readonly configService = inject(ConfigurationManagementApiService);
  private readonly snackBarService = inject(SnackbarService);
  private readonly iamService = inject(IAMService);

  private currentConfigOptions = signal<ConfigOption[]>([]);

  serviceAccountSearchResults$: Observable<ServiceAccountUser[]> = of([]);
  selectedServiceAccount: ServiceAccountUser | undefined;

  targetContainerSearchResults$: Observable<TargetContainer[]> = of([]);
  selectedTargetContainer: TargetContainer | undefined;

  isApplyEnabled = computed(() => {
    const hasObject = this.currentConfigOptions().some((opt) => opt.subOptions?.some((subOpt) => subOpt.enabled));
    return this.formStatusSignal() === 'VALID' && hasObject;
  });

  data: { configurationId: string; configOptions: ConfigOption[] } = inject(MAT_DIALOG_DATA);

  form = new FormGroup({
    targetContainerType: new FormControl<string>('', [Validators.required]),
    targetContainer: new FormControl<string>({ value: '', disabled: true }, [Validators.required]),
    serviceAccountSelected: new FormControl<string>('', [Validators.required]),
  });
  formStatusSignal: Signal<FormControlStatus | undefined>;

  constructor() {
    this.currentConfigOptions.set(this.data.configOptions);
    this.formStatusSignal = toSignal(this.form.statusChanges);
  }

  ngOnInit(): void {
    const serviceAccounts$ = this.partnerId$.pipe(
      switchMap((partnerId) =>
        this.iamService.listUsers(
          partnerId,
          '',
          '',
          999,
          ['partner_service_account'],
          [],
          [
            { direction: SortDirection.SORT_DIRECTION_ASCENDING, field: UserSortField.USER_SORT_FIELD_FIRST_NAME },
            { direction: SortDirection.SORT_DIRECTION_ASCENDING, field: UserSortField.USER_SORT_FIELD_LAST_NAME },
          ],
        ),
      ),
      map((resp) => resp?.users || []),
      map((users) => {
        return users.map((user) => {
          const namePart = `${user.firstName || ''} ${user.lastName || ''}`.trim();
          const emailIdPart = user.email.split('@')[0];
          return {
            userId: user.userId,
            name: `${namePart} (${emailIdPart})`,
          };
        });
      }),
    );

    const serviceAccountSearchTerm$ = (this.form.get('serviceAccountSelected')?.valueChanges ?? of('')).pipe(
      startWith(''),
      debounceTime(300),
      map((searchTerm) => searchTerm ?? ''),
    );

    this.serviceAccountSearchResults$ = combineLatest([serviceAccounts$, serviceAccountSearchTerm$]).pipe(
      map(([serviceAccounts, searchTerm]) => {
        return searchTerm
          ? serviceAccounts.filter((serviceAccount) =>
              serviceAccount.name.toLowerCase().includes(searchTerm.toLowerCase()),
            )
          : serviceAccounts;
      }),
    );

    const targetContainerType$ = this.form.get('targetContainerType')!.valueChanges.pipe(
      startWith(''),
      map((value) => (value ? value : '')),
      tap((value) => {
        value ? this.form.get('targetContainer')?.enable() : this.form.get('targetContainer')?.disable();
        this.form.get('targetContainer')?.reset();
      }),
      shareReplay(1),
    );

    const targetContainerSearchTerm$ = this.form.get('targetContainer')!.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      map((value) => value ?? ''),
      shareReplay(1),
    );

    const filteredAccountGroups$: Observable<TargetContainer[]> = combineLatest([
      targetContainerSearchTerm$,
      targetContainerType$,
    ]).pipe(
      filter(([, type]) => type === 'account-group'),
      switchMap(([searchTerm]) =>
        this.accountGroupService.lookup(
          new ProjectionFilter().enableAll(),
          undefined,
          searchTerm,
          undefined,
          10,
          undefined,
        ),
      ),
      filter((accountGroups) => !!accountGroups),
      map((accountGroups) =>
        accountGroups.map((accountGroup) => {
          return {
            id: accountGroup.accountGroupId,
            name: accountGroup.napData.companyName,
          };
        }),
      ),
    );

    const brands$: Observable<TargetContainer[]> = this.partnerId$.pipe(
      switchMap((partnerId) => this.multiLocationService.listBrandsMetadata(partnerId)),
      map((brands) =>
        brands.map((brand) => {
          return {
            id: brand.groupId,
            name: brand.name,
          };
        }),
      ),
    );

    const filteredBrands$: Observable<TargetContainer[]> = combineLatest([
      brands$,
      targetContainerSearchTerm$,
      targetContainerType$,
    ]).pipe(
      filter(([, , type]) => type === 'group') &&
        map(([brands, searchTerm]) =>
          searchTerm ? brands.filter((brand) => brand.name.toLowerCase().includes(searchTerm.toLowerCase())) : brands,
        ),
    );

    this.targetContainerSearchResults$ = combineLatest([
      targetContainerType$,
      filteredAccountGroups$,
      filteredBrands$,
    ]).pipe(map(([type, accounts, brands]) => (type === 'account-group' ? accounts : brands)));
  }

  onOptionsChange(options: ConfigOption[]) {
    this.currentConfigOptions.set(options);
  }

  async apply() {
    const formValue = this.form.value;
    const objectApplicationSelections: ObjectApplicationSelection[] = [];
    this.currentConfigOptions().forEach((opt) => {
      opt.subOptions?.forEach((subOpt) => {
        subOpt.enabled &&
          objectApplicationSelections.push(
            new ObjectApplicationSelection({
              configurationObjectId: subOpt.id,
              strategy: ObjectApplicationStrategy.OBJECT_APPLICATION_STRATEGY_SKIP_EXISTING,
            }),
          );
      });
    });

    if (!this.selectedTargetContainer || !formValue.targetContainerType) {
      return;
    }

    try {
      await lastValueFrom(
        this.configService
          .applyConfiguration(
            new ApplyConfigurationRequest({
              configurationId: this.data.configurationId,
              configurationTargetContainers: [
                new ConfigurationTargetContainer({
                  targetContainerNamespace: this.selectedTargetContainer.id,
                  targetContainerNamespaceType: this.targetContainerTypeToEnum(formValue.targetContainerType),
                }),
              ],
              objectApplicationSelections: objectApplicationSelections,
              runUsingServiceAccountUserId: this.selectedServiceAccount?.userId,
            }),
          )
          .pipe(take(1)),
      );

      this.snackBarService.openSuccessSnack('Application process started');
      this.dialogRef.close();
    } catch (e) {
      this.snackBarService.openErrorSnack('Error applying configuration');
    }
  }

  targetContainerTypeToEnum(type?: string): TargetContainerNamespaceType {
    if (!type) {
      return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_UNSPECIFIED;
    }
    switch (type) {
      case 'account-group':
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_ACCOUNT_GROUP;
      case 'group':
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_GROUP;
      default:
        return TargetContainerNamespaceType.TARGET_CONTAINER_NAMESPACE_UNSPECIFIED;
    }
  }

  onServiceAccountSelect(event: MatAutocompleteSelectedEvent) {
    const value = event.option.value;
    this.selectedServiceAccount = value;
    this.form.get('serviceAccountSelected')?.setValue(value.name);
  }

  onTargetContainerSelect(event: MatAutocompleteSelectedEvent) {
    const value = event.option.value;
    this.selectedTargetContainer = value;
    this.form.get('targetContainer')?.setValue(value.name);
  }
}
