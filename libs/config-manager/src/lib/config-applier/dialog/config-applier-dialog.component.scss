@use 'design-tokens' as *;

.dialog-container {
  width: 660px;
}

mat-dialog-content {
  max-height: 500px;
  height: 500px;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
}

.selection-title {
  color: var(--primary-text-color);
}

.object-selection-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: $spacing-3;
  height: 100%;
  overflow-y: auto;
}

.config-options-container {
  height: 100%;
  overflow-y: auto;
  padding: $spacing-3 0;
}
