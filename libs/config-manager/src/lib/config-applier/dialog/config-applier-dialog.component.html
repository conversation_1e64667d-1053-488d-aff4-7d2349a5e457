<div class="dialog-container">
  <h2 mat-dialog-title>{{ 'CONFIG_APPLIER.TITLE' | translate }}</h2>
  <mat-dialog-content>
    <div class="target-selection-container">
      <p class="selection-title">Select Target</p>
      <form [formGroup]="form">
        <div class="row row-gutters">
          <div class="col col-xs-12 col-sm-4">
            <glxy-form-field [hideRequiredLabel]="true">
              <mat-select
                placeholder="{{ 'CONFIG_APPLIER.TARGET_TYPE_PLACEHOLDER' | translate }}"
                formControlName="targetContainerType"
              >
                <mat-option value="account-group">{{
                  'CONFIG_APPLIER.CONTAINER_TYPE.ACCOUNT_GROUP' | translate
                }}</mat-option>
                <mat-option value="group">{{
                  'CONFIG_APPLIER.CONTAINER_TYPE.MULTI_LOCATION_GROUP' | translate
                }}</mat-option>
              </mat-select>
            </glxy-form-field>
          </div>
          <div class="col col-xs-12 col-sm-8">
            <glxy-form-field [hideRequiredLabel]="true">
              <input
                matInput
                type="text"
                placeholder="{{ 'CONFIG_APPLIER.TARGET_NAME_PLACEHOLDER' | translate }}"
                formControlName="targetContainer"
                [matAutocomplete]="targetContainerSelector"
              />
              <mat-autocomplete
                #targetContainerSelector="matAutocomplete"
                (optionSelected)="onTargetContainerSelect($event)"
              >
                <ng-container *ngFor="let option of targetContainerSearchResults$ | async">
                  <mat-option [value]="option">{{ option.name }} ({{ option.id }})</mat-option>
                </ng-container>
              </mat-autocomplete>
            </glxy-form-field>
          </div>
        </div>
        <div class="row row-gutters">
          <div class="col">
            <glxy-form-field [hideRequiredLabel]="true">
              <glxy-label>{{ 'CONFIG_APPLIER.SERVICE_ACCOUNT_SELECTOR_LABEL' | translate }}</glxy-label>
              <input
                matInput
                type="text"
                placeholder="{{ 'CONFIG_APPLIER.SERVICE_ACCOUNT_SELECTOR_PLACEHOLDER' | translate }}"
                formControlName="serviceAccountSelected"
                [matAutocomplete]="serviceAccountSelector"
              />
              <mat-autocomplete
                #serviceAccountSelector="matAutocomplete"
                (optionSelected)="onServiceAccountSelect($event)"
              >
                <ng-container *ngFor="let option of serviceAccountSearchResults$ | async">
                  <mat-option [value]="option">{{ option.name }}</mat-option>
                </ng-container>
              </mat-autocomplete>
            </glxy-form-field>
          </div>
        </div>
      </form>
    </div>
    <div class="object-selection-container">
      <p class="selection-title" style="margin-bottom: 0">Select Objects to Apply</p>
      <div class="config-options-container">
        <config-options (dataChange)="onOptionsChange($event)" [configOptions]="data.configOptions"></config-options>
      </div>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions>
    <button mat-button mat-dialog-close>{{ 'COMMON.CANCEL' | translate }}</button>
    <button mat-raised-button color="primary" [disabled]="!isApplyEnabled()" (click)="apply()">
      {{ 'COMMON.APPLY' | translate }}
    </button>
  </mat-dialog-actions>
</div>
