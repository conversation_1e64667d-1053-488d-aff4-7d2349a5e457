import { Component, input, model, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { FieldConfig, FieldType, GetConnectionResponse } from '@vendasta/platform-integrations';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'platform-integration-labelfield',
  imports: [TranslateModule, CommonModule],
  templateUrl: './label.component.html',
  styleUrls: ['./label.component.scss'],
})
export class LabelFieldComponent implements OnInit {
  connection = input.required<GetConnectionResponse>();
  field = model<FieldConfig>();
  protected readonly fieldType = FieldType;
  ngOnInit(): void {
    if (this.field()?.hintText.includes('{connectionid}')) {
      const updatedHintText = this.field()?.hintText.replaceAll(
        '{connectionid}',
        this.connection().connectionId || 'connectionid',
      );
      const updatedField = { ...this.field(), hintText: String(updatedHintText) };
      this.field.set(new FieldConfig(updatedField));
    }
  }
}
