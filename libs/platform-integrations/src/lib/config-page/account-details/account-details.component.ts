import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';

@Component({
  selector: 'platform-integration-account-details',
  imports: [MatCardModule, CommonModule, TranslateModule, GalaxyPipesModule],
  templateUrl: './account-details.component.html',
  styleUrls: ['./account-details.component.scss'],
})
export class AccountDetailsComponent {
  @Input() accountDetails!: AccountDetails;

  constructor(private translate: TranslateService) {}
}

export interface AccountDetails {
  accountName: string;
  requestedBy?: string;
  statusLastUpdated?: Date;
  connectionStatus: string;
  dataLastReceived?: Date;
  profileURL?: string;
  isLocationVerified?: string;
}
