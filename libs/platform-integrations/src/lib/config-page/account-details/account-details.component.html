<ng-container *ngIf="accountDetails">
  <mat-card appearance="outlined" class="account-details-card">
    <mat-card-content>
      <table>
        <tr *ngIf="accountDetails.accountName">
          <td class="title">{{ 'ACCOUNT_DETAILS.ACCOUNT_NAME' | translate }}</td>
          <td class="align-td-value">{{ accountDetails.accountName }}</td>
        </tr>
        <tr *ngIf="accountDetails.requestedBy">
          <ng-container *ngIf="accountDetails.connectionStatus === 'PRECONNECTED'; else otherStatus">
            <td class="title">
              {{ 'ACCOUNT_DETAILS.REQUESTED_BY' | translate }}
            </td>
          </ng-container>
          <ng-template #otherStatus>
            <td class="title">
              {{ 'ACCOUNT_DETAILS.CONNECTED_BY' | translate }}
            </td>
          </ng-template>
          <td class="align-td-value">{{ accountDetails.requestedBy }}</td>
        </tr>
        <tr *ngIf="accountDetails.profileURL">
          <td class="title">{{ 'ACCOUNT_DETAILS.ACCOUNT_URL' | translate }}</td>
          <td class="align-td-value">
            <a href="{{ accountDetails.profileURL }}" target="_blank">{{ accountDetails.profileURL | truncateUrl }}</a>
          </td>
        </tr>
        <tr *ngIf="accountDetails.isLocationVerified">
          <td class="title">{{ 'ACCOUNT_DETAILS.LOCATION' | translate }}</td>
          <td class="align-td-value">{{ accountDetails.isLocationVerified }}</td>
        </tr>
        <tr *ngIf="accountDetails.statusLastUpdated">
          <td class="title" *ngIf="accountDetails.connectionStatus === 'CONNECTED'">
            {{ 'ACCOUNT_DETAILS.CONNECTED' | translate }}
          </td>
          <td class="title" *ngIf="accountDetails.connectionStatus === 'DISCONNECTED'">
            {{ 'ACCOUNT_DETAILS.PROBLEM_DETECTED' | translate }}
          </td>
          <td class="title" *ngIf="accountDetails.connectionStatus === 'PRECONNECTED'">
            {{ 'ACCOUNT_DETAILS.REQUESTED' | translate }}
          </td>
          <td class="align-td-value">{{ accountDetails.statusLastUpdated | glxyDate }}</td>
        </tr>
        <tr *ngIf="accountDetails.dataLastReceived">
          <td class="title">{{ 'ACCOUNT_DETAILS.DATA_LAST_RECEIVED' | translate }}</td>
          <td class="align-td-value">{{ accountDetails.dataLastReceived | glxyDate }}</td>
        </tr>
      </table>
    </mat-card-content>
  </mat-card>
</ng-container>
