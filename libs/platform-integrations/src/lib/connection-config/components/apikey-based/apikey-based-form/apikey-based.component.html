<div>
  <ng-container>
    <h2 mat-dialog-title class="model-title">{{ preconnectFormFields.title }}</h2>
    <mat-dialog-content>
      <div *ngFor="let field of preconnectFormFields?.fields">
        <div [formGroup]="syncSettingsForm" class="custom-input">
          <ng-container
            *ngIf="!field.hidden && field.fieldType === fieldType.FIELD_TYPE_CHECKBOX"
            appearance="outlined"
            class="mat-card-margin"
          >
            <mat-card-content>
              <div class="mat-card-content">
                <mat-checkbox [formControlName]="field.id"></mat-checkbox>
                <div class="mat-card-content-column">
                  <div class="title">
                    {{ field.label }}
                  </div>
                  <div class="sub-title">
                    {{ field.hintText }}
                  </div>
                </div>
              </div>
            </mat-card-content>
          </ng-container>
          <ng-container *ngIf="!field.hidden && field.fieldType === fieldType.FIELD_TYPE_TEXT">
            <div class="field-label">
              {{ field.label }}
              <span class="required-chip"> Required </span>
            </div>
            <glxy-form-field [hideRequiredLabel]="true">
              <input
                [type]="field.fieldType"
                [placeholder]="field.placeHolder"
                [formControlName]="field.id"
                [value]="field.defaultValue"
                [required]="field.required"
                [ngClass]="{
                  'invalid-input':
                    syncSettingsForm.get(field.id)?.hasError('required') && syncSettingsForm.get(field.id)?.touched,
                }"
                matInput
              />
              <div class="field-hint">
                {{ field.hintText }}
              </div>
            </glxy-form-field>
          </ng-container>
        </div>
      </div>
    </mat-dialog-content>
    <mat-card>
      <mat-dialog-content class="description-content" [innerHTML]="preconnectFormFields.description">
      </mat-dialog-content>
    </mat-card>
    <mat-dialog-actions>
      <platform-integration-action-type [actions]="preconnectFormFields.actions" (actionClicked)="handleAction($event)">
      </platform-integration-action-type>
    </mat-dialog-actions>
  </ng-container>
</div>
