@use 'design-tokens' as *;

.mat-card-content {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.mat-card-content-title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.mat-card-content-column {
  margin: 4px 0px 0px 10px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.model-title {
  font-family: $default-font-family;
  font-size: $font-preset-3-size;
  font-style: normal;
  font-weight: 400;
  line-height: 19px;
  color: $primary-font-color;
}

.content-title {
  margin-bottom: 10px;
  font-family: $default-font-family;
  font-size: $font-preset-4-size;
  font-style: normal;
  font-weight: 400;
  line-height: 19.5px;
  color: #616161;
}

.title {
  font-family: $default-font-family;
  font-size: $font-preset-4-size;
  font-style: normal;
  font-weight: 400;
  line-height: 19.5px;
  color: $primary-font-color;
}

.sub-title {
  margin-top: 5px;
  margin-right: 10px;
  font-family: $default-font-family;
  font-size: $font-preset-4-size;
  font-style: normal;
  font-weight: 400;
  line-height: 19.5px;
  color: $secondary-text-color;
}

.mat-card-margin {
  margin-bottom: 20px;
}

.lock-icon {
  color: grey;
  padding-top: 0px;
}
.title-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.unlockButton {
  margin-top: 10px;
}

.disabled-title {
  color: grey;
}

.unlockButton span {
  padding: 0px 15px;
}

.radio-button-column {
  margin-top: 5px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.input-title {
  font-family: $default-font-family;
  font-size: $font-preset-4-size;
  font-style: normal;
  font-weight: 500;
  line-height: 19.5px;
  color: $primary-font-color;
}
.divider {
  margin: 15px 0px;
}

.field-label {
  font-family: $default-font-family;
  font-size: $font-preset-3-size;
  font-style: normal;
  font-weight: 400;
  line-height: 19px;
  color: $primary-font-color;
  margin-left: $spacing-2;
}

.description-content {
  margin-left: $spacing-1;
}
.custom-input {
  margin-top: 10px;
  width: 98%;
  margin-left: $spacing-2;
}

mat-dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: $spacing-3;
  gap: $spacing-2;
}

.field-hint {
  margin-top: $spacing-1;
  font-size: $font-preset-5-size;
  color: $secondary-font-color;
}

.invalid-input {
  border: 1px solid red;
}

.required-chip {
  background-color: #ffe6e6;
  color: $glxy-red-700;
  border-radius: 8px;
  font-size: $font-preset-5-size;
  padding: 2px 10px;
  margin-left: 8px;
  display: inline-block;
  vertical-align: middle;
}
