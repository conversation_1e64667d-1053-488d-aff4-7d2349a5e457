import { Component, inject, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Location } from '@angular/common';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogActions, MatDialogClose, MatDialogContent } from '@angular/material/dialog';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { EditionUpgradeAction } from '@galaxy/marketplace-apps';
import { Router } from '@angular/router';
import { CardDataService } from '../card-data.service';
import { PlatformIntegrationsI18nModule } from '@galaxy/platform-integrations/shared';

@Component({
  selector: 'platform-integration-unlock-dialogue-box.component',
  templateUrl: './unlock-dialogue-box.component.html',
  styleUrls: ['./unlock-dialogue-box.component.scss'],
  imports: [
    CommonModule,
    MatDialogModule,
    MatD<PERSON>ogActions,
    MatDialog<PERSON>lose,
    MatDialogContent,
    MatButtonModule,
    TranslateModule,
    PlatformIntegrationsI18nModule,
  ],
})
export class DialogBoxComponent implements OnInit {
  dialogueBoxDetails: any;
  private readonly translate = inject(TranslateService);
  private readonly cardService = inject(CardDataService);
  private readonly location = inject(Location);
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private router: Router,
  ) {}

  unlockModelLogoURL = 'https://storage.googleapis.com/galaxy-libs-public-images/reputation_management_logo.png';
  unlockModelSubtitle = 'Available on Reputation Management Premium';

  setUnlockBoxLogo() {
    if (this.data['rmAppSettings']?.branding?.iconUrl) {
      this.unlockModelLogoURL = this.data['rmAppSettings']?.branding?.iconUrl;
    }
  }
  setUnlockBoxSubtitle() {
    if (this.data['rmAppSettings']?.branding?.name) {
      this.unlockModelSubtitle = this.data['rmAppSettings']?.branding?.name;
    }
  }

  ngOnInit() {
    this.setUnlockBoxLogo();
    this.setUnlockBoxSubtitle();
    this.dialogueBoxDetails = {
      Description:
        this.data['rmAppSettings']?.editionChange?.editionUpgradeAction ===
        EditionUpgradeAction.EDITION_UPGRADE_ACTION_CONTACT_SALES
          ? this.translate.instant('CONNECTION_SYNC_SETTINGS.CONTACT_SALES_DESCRIPTION')
          : this.translate.instant('CONNECTION_SYNC_SETTINGS.BUY_DESCRIPTION'),
      ButtonText:
        this.data['rmAppSettings']?.editionChange?.editionUpgradeAction ===
        EditionUpgradeAction.EDITION_UPGRADE_ACTION_CONTACT_SALES
          ? this.translate.instant('CONNECTION_SYNC_SETTINGS.CONTACT_SALES')
          : this.translate.instant('CONNECTION_SYNC_SETTINGS.BUY'),
    };
  }

  redirectToSales() {
    const editionId = this.cardService.getEditionId();
    const upgradePath = 'edition-upgrades';
    const nextURL = encodeURIComponent(this.location.path());
    const itemsJSON = encodeURIComponent(
      JSON.stringify([{ appId: this.data['rmAppSettings'].appId, editionId: editionId }]),
    );
    const url = `/account/location/${this.data['namespace']}/${upgradePath}?items=${itemsJSON}&nextUrl=${nextURL}&trackingCode=integration-review-button:${this.data['integrationId']}:upgrade`;
    this.router.navigateByUrl(url);
  }
}
