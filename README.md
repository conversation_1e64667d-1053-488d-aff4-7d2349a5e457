<img src="./docs/images/Galaxy_Full.png" alt="Galaxy framework logo" height="96px" />

The Vendasta Galaxy consists of both the Galaxy mono-repo, and
the [Galaxy front-end framework](https://galaxy.vendasta.com/). To learn more about the front-end framework,
please [read the RFC](https://vendasta.jira.com/wiki/spaces/RD/pages/282296377/Galaxy+Frontend+Framework)
, [explore the code](https://github.com/vendasta/galaxy/tree/master/libs/galaxy)
or [view the components in Figma](https://www.figma.com/file/gq7p0ZTQdqBYzb94dHJ6vl/Galaxy-Design-System-Shared-Library?node-id=0%3A1)


<br>

## New to the Monorepo?

The mono-repo requires one of the following versions of **Node** and **NPM**:

* **Node v20.x** and **NPM v10.x**

We use `nx`, the Angular CLI `ng`, and `husky` as tools to manage the mono-repo.

Please read the [Installation and Initial Setup](./docs/getting-started--initial-setup.md) instructions.

<br>

## Common Commands

<br>

#### Running Apps

```shell
# Run your app in DEMO (default)
npm run start <your-project-name>

# Run your app in PROD
npm run start:prod <your-project-name>
```

Note: your app needs to be [specifically configured to run in prod](./docs/configure-proxy.md)

```shell
# Run the for a specific PID in Demo (default)
PID=ABC PROXY_ENV=demo npm start <your-project-name>

# Run the app for a specific PID in Prod
PID=ABC npm run start:prod <your-project-name>
```

Note: If the following command fails with an error: `Cannot find configuration 'fast' for project,` you will need
to [add a `fast` build configuration](https://github.com/vendasta/galaxy/pull/8762) to package.json for your project.

```shell
# Run the app in a mode optimized for build speed. 
PID=ABC npm run start:fast <your-project-name>
```

Note: If the following command fails with an error: `Cannot find configuration 'profile' for project,` you will need
to [add a `profile` build configuration](https://github.com/vendasta/galaxy/pull/14257) to package.json for your project.

```shell
# Run the app in a mode to profile build speed. 
PID=ABC npm run start:profile <your-project-name>
```

Note: Specific PIDs also need to [be manually configured per app](./docs/configure-pid.md)

<br>

#### Local Testing with a Mobile Device

```shell
# Local Testing with a Mobile Device
npm run start:mobile-testing <your-project-name>
```

Note: please read [this document about setting up mobile testing](./docs/getting-started--mobile-testing.md)

<br>

#### Testing (Testing Library and Jest)

```shell
# Run a specific test file
nx test <your-project-name> --test-file=apps/path/to/file.component.spec.ts --skip-nx-cache

# Test what changed since last commit
nx test <your-project-name> --only-changed

# Test a full project
nx test <your-project-name>

# Interactively run jest tests
nx test <your-project-name> --watch

# Only runs tests that are affected by your changes
nx affected:test

# Test everything in galaxy
nx run-many --all --target=test

# Code coverage - Access the code coverage reports in `coverage/apps/<your-project-name>`
nx run <your-project-name>:test --code-coverage

# Clear Jest cache
npx jest --clearCache

# Rebuild all snapshots in your current project
nx test <your-project-name> --updateSnapshot
```

<br>

#### Linting

```shell
# Only runs linter on files that you modified
nx affected:lint

# Lint your project
nx lint <your-project-name>

# Lint everything in galaxy
nx run-many --all --target=lint

# Lint the NX workspace
nx workspace-lint 
```

<br>

#### End to End Testing (Cypress)

```shell
# Interactively run e2e tests with auto-reloading
npm run e2e <your-project-e2e-name> -- --watch

# Only runs e2e tests on projects affected
npm run affected:e2e

# Runs e2e tests on a specific project
npm run test:e2e --project=<your-project-name(proposal-builder, business-center, etc.)>

# Interactively run component tests
nx component-test <your-library-name> --watch

# Run component tests on projects affected
nx affected --target=component-test 
```

Set up tests to run for your builds [here](docs/cypress.md).

<br>

#### Generate Files

Nx will generate files same as angular cli. `cd` into the root of your client/lib and run

````shell
nx generate <component|service|pipe|etc> <file-path-and-name>
````

All generated components/directives/pipes are now standalone by default. Standalone components are faster to write, require less boilerplate, and help with treeshaking. It is easier to reason which imports are used in a component when you can see what the component uses directly, than managing imports in a shared module that handles imports for multiple components. Want to learn more? [Official angular standalone guide](https://angular.io/guide/standalone-components) and [a Net Basal article](https://netbasal.com/angular-standalone-components-welcome-to-a-world-without-ngmodule-abd3963e89c5)

<br>

#### Building

```shell
# Build a project
nx run <your-project-name>:build:production
```

<br>

#### Formatting

```shell
# Format uncommitted files
npm run format:write -- --uncommitted
```

<br>

#### Bundle Analysis

Run bundle analysis locally! This uses the same library as Mission Control, but hopefully provides a quicker feedback
loop.

Turning on bundle analysis in Mission Control is highly recommended as you'll be able to see
how your bundle changes over time. To do this add `"sourceMap": true`
to `targets -> build -> configurations -> production` in your client's `project.json` file

```shell
PROJECT=<your-project> npm run bundle-analyzer
```

<br>

#### Troubleshooting

Run those commands in the base of the galaxy repo, not your home directory

```shell
# Reset node modules and caches
rm -rf node_modules
rm -rf .angular
npm cache clean --force
npm install 

# Delete just the NX Cache
rm -rf ./node_modules/.cache/nx

# Delete just the angular cache
rm -rf .angular

```

##### Out of Memory or Javascript Heap Errors

If the `npm run` commands fail with any out of memory or javascript heap errors, you need to bump the default settings
allocated to node:

```bash
export NODE_OPTIONS=--max_old_space_size=12000
```

If you used the [setup-new-computer.sh script](https://github.com/vendasta/LABS/tree/master/setup-new-computer-script)
then this is added automatically. If not, it is recommended that you add this to your computer startup script (.bashrc,
.zshrc, etc).

<br>

## Full Docs Links

### Getting Started

- [Installation and Initial Setup](./docs/getting-started--initial-setup.md)
- [IDE and Editor Tips](./docs/getting-started--ide-editor-tips.md)
- [Install local dev SSL cert (optional)](./docs/getting-started--ssl.md)
- [Local Testing with a Mobile Device](./docs/getting-started--mobile-testing.md)

### Creating New Apps and Libs

- [Create a new App](./docs/create-new-app.md)
- [Create a new Library](./docs/create-new-lib.md)
- [Create a new SDK build](./docs/create-new-sdk-build.md)
- [Create a new storybook](./docs/create-new-storybook.md)

### Bringing in pre-existing Apps and Libs

- [Move an app to the mono-repo](./docs/move-existing-app-to-monorepo.md)
- [Move a lib to the mono-repo](./docs/move-existing-lib-to-monorepo.md)

### Configuing your Apps

- [Enviroments and Proxies](./docs/configure-proxy.md)
- [PID Mapping](./docs/configure-pid.md)

### Internationalization & Localization (I18N)

- [Adding I18N to your App and Library](./docs/internationalization.md).

### Working with legacy apps

- [Using Galaxy in legacy apps](./docs/testing-galaxy-lib-in-legacy-app.md)

### Contributing

- [Contributing to the Galaxy](./docs/CONTRIBUTING.md)

### Update and Upgrade

- [Update Angular, NX, and Node/NPM](./docs/upgrading-nx-angular-and-node.md)

<br>

## Apps

| App Name                                                                                                                                                          | Links                                                        |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------|
| Billing Client <br> ``` npm run start billing-client```                                                                                                           | [Files](./apps/billing-client)                               |
| **Business App** <br> ``` npm run start business-center-client```  <br> If you need to select the partner like: <br> ```PID=ABC npm run start business-center-client``` | [Files](./apps/business-center-client)         |
| **Task Manager Client** <br> ``` npm run start concierge-client```                                                                                                | [Files and README.md](./apps/concierge-client)               |
| Customer Voice Client <br> ``` npm run start customer-voice-client```                                                                                             | [Files and README.md](./apps/customer-voice-client)          |
| Digital Ads Client <br> ``` npm run start digital-ads-client```                                                                                                   | [Files](./apps/digital-ads-client)                           |
| Invoice Client <br> ``` npm run start invoice-client```                                                                                                           | [Files](./apps/invoice-client)                               |
| Listing Builder Client <br> ``` npm run start listing-builder-client```                                                                                           | [Files and README.md**](./apps/listing-builder-client)       |
| Matchcraft Client <br> ``` npm run start matchcraft-client```                                                                                                     | [Files](./apps/matchcraft-client)                            |
| Meeting Scheduler Client <br> ``` npm run start shell-meeting-scheduler-client```                                                                                 | [Files and README.md](./apps/shell-meeting-scheduler-client) |
| Mission Control Client <br> ``` npm run start mission-control-client```                                                                                           | [Files and README.md](./apps/mission-control-client)         |
| Galaxy Observatory <br> ``` npm run start observatory```                                                                                                          | [Files](./apps/observatory)                                  |
| Office Library Client <br> ``` npm run start office-library-client```                                                                                             | [Files and README.md](./apps/office-library-client)          |
| **Partner Center Client** <br> ``` npm run start partner-center-client```                                                                                         | [Files](./apps/partner-center-client)                        |
| Partner Signup Client <br> ``` npm run start partner-self-signup-client```                                                                                        | [Files](./apps/partner-self-signup-client)                   |
| Proposal Builder Client <br> ``` npm run start proposal-builder-client```                                                                                         | [Files](./apps/proposal-builder-client)                      |
| Public Store <br> ``` npm run start public-store```                                                                                                               | [Files](./apps/public-store)                                 |
| **Reputation Management** <br> ``` npm run start reputation-client```                                                                                             | [Files](./apps/reputation-client)                            |
| **Sales Center Client** <br> ```PID=ABC npm run start sales-center-client```                                                                                      | [Files and README.md](./apps/sales-center-client)            |
| Galaxy Sandbox <br> ``` npm run start sandbox```                                                                                                                  | [Files](./apps/sandbox)                                      |
| Shoppable Feed Client <br> ``` npm run start shoppable-feed-client```                                                                                             | [Files](./apps/shoppable-feed-client)                        |
| Snapshot Client <br> ``` npm run start snapshot-client```                                                                                                         | [Files and README.md](./apps/snapshot-client)                |
| **Social Marketing** <br> ```PID=ABC npm run start social-marketing-client```                                                                                     | [Files](./apps/social-marketing-client)                      |
| SRE Reporting Client <br> ``` npm run start sre-reporting-client```                                                                                               | [Files](./apps/sre-reporting-client)                         |
| Vendasta Center Client <br> ``` npm run start vendasta-center-client```                                                                                           | [Files](./apps/vendasta-center-client)                       |
| Vendor Center Client <br> ``` npm run start vendor-center-client```                                                                                               | [Files](./apps/vendor-center-client)                         |
| Web Crawler Client <br> ``` npm run start web-crawler-client ```                                                                                                  | [Files](./apps/web-crawler-client)                           |
| Webchat Client <br> ``` npm run start webchat-client ```                                                                                                          | [Files](./apps/webchat-client)                               |
| Website Pro Admin Center Client <br> ``` npm run start wsp-admin-center-client```                                                                                 | [Files and README.md](./apps/wsp-admin-center-client)        |
| Website Pro Portal Client <br> ``` PROXY_ENV=demo npm run start wsp-portal-client```                                                                              | [Files and README.md](./apps/wsp-portal-client)              |
| Developer Center Client <br> ``` npm run start developer-center-client```                                                                                         | [Files and README.md](./apps/developer-center-client)        |
| Sequences Client <br> ``` npm run start sequences-client```                                                                                                       | [Files](./apps/sequences-client)                             |
| CRM Shell Client <br> ``` npm run start shell-crm-client```                                                                                                       | [Files](./apps/shell-crm-client)                             |
| Billing Shell Client <br> ``` npm run start shell-billing-client```                                                                                               | [Files](./apps/shell-billing-client)                         |
| Configuration Management Shell Client <br> ``` npm run start shell-config-manager-client```                                                                             | [Files](./apps/shell-config-manager-client)                  |
<br>

